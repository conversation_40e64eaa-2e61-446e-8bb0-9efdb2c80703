<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Maintenance</title>
    <meta charset="utf-8" />
    <meta name="description" content="Login" />
    <meta name="keywords" content="Login" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />
  </head>
  <style>
      .header {
          position: fixed;
          width: 100%;
          background-color: #fff;
          z-index: 9999;
          top: 0;
      }

      @media (min-width: 992px) {
          .header {
              box-shadow: 1px 0 8px #7f7f7f;
          }
      }

      .menu-sp {
          display: none;
      }

      @media (max-width: 992px) {
          .menu-sp {
              display: block;
          }

          .menu-pc {
              display: none;
          }
      }

      .menu-pc {
          margin: 0 auto;
      }

      .menu-pc .logo-wrap {
          padding: 10px 0;
      }

      .menu-pc .link-header__customer {
          position: relative;
      }

      .menu-pc .link-header-login {
          padding: 14px 5px;
      }

      .header-wrap {
          display: flex;
          justify-content: space-between;
          height: 58px;
      }

      .header-wrap__left {
          display: flex;
      }

      .header-wrap__right {
          display: flex;
      }

      .banner {
          box-shadow: 0 2px 2px #e5e5e5;
          position: relative;
          margin-bottom: 35px;
      }

      .banner__img {
          display: block;
      }

      .banner__img a {
          display: block;
      }

      .banner img {
          width: 100%;
      }

      .banner__img-pc {
          width: 100%;
          max-height: 300px;
          overflow: hidden;
      }

      .auth {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
      }

      .auth__main {
          position: relative;
          width: 100%;
          max-width: 400px;
          padding: 0 10px;
      }

      .auth__content {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
      }

      .login-caution__avatar {
          width: 35px;
          height: 35px;
          margin-right: 10px;
          float: left;
      }

      .login-caution__avatar img {
          border-radius: 50%;
          max-width: 100%;
          overflow: hidden;
          height: 100%;
      }

      .login-caution__content {
          min-height: 60px;
          width: calc(100% - 100px);
          background-color: #e5e5e5;
          border: 1px solid #7f7f7f;
          border-radius: 20px;
          padding: 10px;
          position: relative;
          font-size: 12px;
          margin-left: 45px;
      }

      .login-caution__emoticon {
          width: calc(100% - 58px);
      }

      .login-caution__emoticon-images {
          width: 150px;
          float: right;
      }

      .login-caution__emoticon img {
          max-width: 100%;
      }

      @media (max-width: 992px) {
          .mobile-head-sp {
              position: fixed;
              background-color: #000;
              width: 100%;
              padding: 60px 0 20px;
              transition: .5s ease-in-out;
              top: -600px;
              z-index: 10;
          }

          .nav-toggle {
              position: fixed;
              z-index: 999;
              top: 16px;
              left: 20px;
              cursor: pointer;
              width: 30px;
              height: 25px;
          }

          .nav-toggle-box {
              position: relative;
          }

          .nav-toggle span {
              display: block;
              position: absolute;
              height: 3px;
              width: 30px;
              background: #000;
              left: 0;
              transition: .35s ease-in-out;
          }

          .nav-toggle span:nth-child(1) {
              top: 0;
          }

          .nav-toggle span:nth-child(2) {
              top: 10px;
          }

          .nav-toggle span:nth-child(3) {
              top: 20px;
          }

          .open .mobile-head-sp {
              transform: translateY(600px);
          }

          .open .nav-toggle span:nth-child(1) {
              top: 11px;
              transform: rotate(315deg);
              background: #fff;
          }

          .open .nav-toggle span:nth-child(2) {
              width: 0;
              left: 50%;
              background: #fff;
          }

          .open .nav-toggle span:nth-child(3) {
              top: 11px;
              transform: rotate(-315deg);
              background: #fff;
          }

          .header-menu-sp ul {
              display: flex;
              justify-content: space-between;
              padding: 0 20px;
              background: url("../images/bg_pc_navbody.png") repeat-x bottom left;
              margin-bottom: 0;
          }

          .header-menu-sp li {
              padding-bottom: 12px;
              padding-top: 8px;
          }

          .header-menu-sp a {
              color: #009cc3;
              padding: 2px 0;
              display: block;
              font-size: 17px;
              letter-spacing: 2px;
              position: relative;
              text-transform: uppercase;
          }
      }

      @media (max-width: 992px) and (max-width: 576px) {
          .header-menu-sp a {
              font-size: 12px;
          }
      }

      @media (max-width: 992px) {
          .header-menu-sp a:after {
              content: '';
              width: 100%;
              height: 2px;
              position: absolute;
              bottom: 0;
              left: 0;
              visibility: hidden;
              opacity: 0;
              transition: all .3s ease;
              background-color: #09c;
          }

          .header-menu-sp a:hover {
              text-decoration: none;
              color: #0076a5;
          }

          .header-menu-sp a:hover:after {
              visibility: visible;
              opacity: 1;
          }

          .nav-menu-sp {
              padding: 0;
              margin: 0 30px;
              list-style: none;
              position: static;
              right: 0;
              bottom: 0;
              font-size: 12px;
          }

          .nav-menu-sp > ul {
              padding: 0;
          }

          .nav-menu-sp li {
              list-style: none;
              float: none;
              position: static;
          }

          .nav-menu-sp .item-second a {
              color: #fff;
              font-size: 14px;
              padding: 10px 0;
              display: block;
              text-transform: uppercase;
          }

          .nav-menu-sp .item-main a {
              color: #009cc3;
              font-size: 16px;
              padding: 10px 0;
              display: block;
              text-transform: uppercase;
          }

          .logo-wrap-sp {
              position: absolute;
              top: 10px;
              left: 0;
              right: 0;
              margin: auto;
              width: 165px;
          }

          .header-top-sp {
              padding-bottom: 8px;
              position: relative;
              background: url("../images/bg_pc_navbody.png") repeat-x bottom left;
          }

          .header-top-sp .logo-wrap {
              width: 165px;
              margin: 0 auto;
              text-align: center;
              padding: 10px 0;
          }

          .header-top-sp .link-header {
              position: absolute;
              right: 20px;
              top: 0;
          }
      }

  </style>
  <body>
    <header class="header">
      <div class="header-container">
        <div class="menu-sp">
          <div class="header-top-sp">
            <div class="logo-wrap">
              <a class="logo" href="#">
                <img src="../static/images/<EMAIL>" alt="" height="35px">
              </a>
            </div>
          </div>
        </div>
        <div class="menu-pc">
          <div class="container">
            <div class="header-wrap">
              <div class="header-wrap__left">
                <div class="logo-wrap">
                  <a class="logo" href="#">
                    <img src="../static/images/<EMAIL>" alt="" height="35px">
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
    <div class="login">
      <div class="banner">
        <div class="banner__img visible-xs visible-sm">
          <img src="../static/images/login_banner_sp.png" alt="">
        </div>
        <div class="banner__img-pc visible-lg visible-md">
          <img src="../static/images/header-login.png" alt="">
        </div>
      </div>
      <div class="auth">
        <div class="auth__content">
          <div class="auth__main">
            <div class="login-caution">
              <div class="login-caution__item">
                <div class="login-caution__avatar">
                  <img src="../static/images/avatar-user.jpg" alt="">
                </div>
                <div class="login-caution__content">
                  <p>アップデート中・・・</p>
                  <p>少々御時間をおいてからアクセスをお願い致します。</p>
                </div>
                <div class="login-caution__emoticon">
                  <div class="login-caution__emoticon-images">
                    <img src="../static/images/ikaga_mojinashi.png" alt="">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.0/croppie.min.js" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
</html>
