# 01EG89H6SS2141VNGDDEHBMV4Q
import io
import time
from decimal import Decimal, ROUND_HALF_UP

import jwt
from PIL import Image
import os
import datetime
import json
import re
from itertools import groupby
import logging

from django.conf import settings
#apply rest_framework authtoken for api call
from rest_framework.authtoken.models import Token
#apply end
from django.contrib.auth import get_user_model, login, logout, authenticate, update_session_auth_hash
from django.contrib.auth.forms import (PasswordResetForm, SetPasswordForm, PasswordChangeForm)
from django.contrib.auth.hashers import make_password
from django.contrib.auth.views import PasswordResetCompleteView, PasswordResetConfirmView, \
    logout_then_login
from django.core import serializers as ser
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.urls import reverse_lazy, reverse
from django.core.exceptions import MultipleObjectsReturned
from django.db import transaction
from django.db.models import Q, Prefetch
from django.db.utils import load_backend
from django.http import HttpResponseRedirect, JsonResponse, request
from django.http.response import HttpResponse, HttpResponseBadRequest
from django.shortcuts import render, redirect
from django.template.loader import render_to_string
from django.template.response import TemplateResponse
from django.utils.html import strip_tags
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.views import View
from django.views.generic import DetailView
from django.views.generic import TemplateView, CreateView, UpdateView, ListView, FormView
from accounts import tasks
from accounts.forms import BlockListInlineForm, LoginUserForm, CreatorSettingForm, CreatorProfileForm, JoinUsForm
from accounts.models import AuthUser, BlockList, Creator, Skill, UserDevice, ProductUser, CreatorList, \
    CreatorListCreator, CreatorFile, ItemBlock, CreatorItemBlock, ProfileBlock, HeaderBlock, StatementBlock
from accounts.services import update_product_user_new_video, get_query_deadline_list_service, get_deadline_list_service, \
    get_query_deadline_this_month_service, get_query_deadline_next_month_service, check_email_in_system, \
    create_artist_invited, active_artist_invited, check_real_change_profile, get_all_artist, update_sale_content_in_list
from app.tasks import add_remove_member_into_offer_product, add_member_into_offer_creator
from app.util import get_download_link
from app.views import ManagerMixin
from app.models import OfferProduct, SaleContent, SaleContentVersion, AlbumVariation, AlbumVersion, HashTag, \
    SaleContentTag, OfferUser, SaleContentListWork, SaleContentSelection, ListBookMark, SceneTitleBookmark, \
    BookmarkListBookMarks
import stripe

from voice.constants import CONST_REGEX_EMAIL
from .accounts_services.update_creator_profile_services import check_edit_footer, update_edit_footer_block, \
    check_amount_item_social_in_footer, update_edit_profile_block_for_creator, update_edit_statement_block_for_creator, \
    update_edit_header_block_for_creator, update_edit_footer_block_for_creator, create_new_sale_content_for_creator, \
    update_sale_content_for_creator, check_sale_content_real_change_for_creator, crop_new_image
from .tasks import get_creator_profile, update_product_user_order, update_banner_resized, update_medium_small_avatar, \
    update_product_user_order_user, bookmark_sale_service

try:
    from django.contrib.sites.shortcuts import get_current_site
except ImportError:
    from django.contrib.sites.models import get_current_site
from django.template import loader

from . import forms
from . import models
from app.models import Product, Audio
from django.core.mail import send_mail
from django.contrib.auth.tokens import default_token_generator
from django.utils.encoding import force_bytes, force_str
from django.utils.translation import gettext as _
from accounts.services import get_task_creator_service

SECRET = settings.SECRET_KEY

LIST_STATEMENT_BLOCK = ['is_show_avatar', 'is_show_name', 'is_show_title', 'theme_jp', 'theme_en']
LIST_FIELD_HEADER = ['type_header', 'catchphrase_jp_1', 'catchphrase_jp_2', 'catchphrase_jp_3', 'catchphrase_en_1', \
                     'catchphrase_en_2', 'catchphrase_en_3', 'artist_name_jp', 'artist_name_en', 'title_jp', 'title_en',
                     'display_tag_line_1', 'display_tag_line_2']
LIST_FIELD_HEADER_IMAGE = ['logo', 'banner', 'key_visual_sp', 'key_visual_pc']
LIST_ITEM_MENU = ['title_jp', 'title_en', 'url', 'order']
LIST_ITEM_SOCIAL_LINK = ['type_social_link', 'url', 'order']


class Index(TemplateView):
    """
    ユーザー管理メニュー
    """
    template_name = "accounts/index.html"


class RequiredSuperMixin(ManagerMixin):
    def __has_permission(self):
        user = self.get_user()
        if not user or not user.is_authenticated:
            return False

        if user and user.role in [AuthUser.MASTERADMIN]:
            return True
        return False

    def dispatch(self, request, *args, **kwargs):
        if self.__has_permission():
            return super(RequiredSuperMixin, self).dispatch(request, args, **kwargs)
        return redirect("app:warning")


class RequiredCreatorMixin(ManagerMixin):
    def __has_permission(self):
        user = self.get_user()
        if not user or not user.is_authenticated:
            return False

        if user and user.role == AuthUser.CREATOR:
            try:
                if models.Creator.objects.get(user=user):
                    return True
            except models.Creator.DoesNotExist:
                return False
        return False

    def dispatch(self, request, *args, **kwargs):
        requested_creator = kwargs.get('pk')
        try:
            current_creator = request.user.user_creator.get(user=request.user.pk)
        except models.Creator.DoesNotExist:
            return redirect("app:warning")

        if current_creator:
            if str(current_creator.pk) != requested_creator:
                return redirect("app:warning")
        if self.__has_permission():
            return super(RequiredCreatorMixin, self).dispatch(request, args, **kwargs)
        return redirect("app:warning")


class List(RequiredSuperMixin, ListView):
    """
    ユーザー一覧
    """
    template_name = "accounts/list.html"
    model = models.AuthUser

    def get_context_data(self, **kwargs):
        user = self.request.user
        if user.role not in [AuthUser.MASTERADMIN, AuthUser.CURATOR]:
            return redirect('app:warning')
        active_users = models.AuthUser.objects.filter(is_active=True)
        context = {
            'user': user
        }
        if user.role == AuthUser.MASTERADMIN:
            master_admins = active_users.filter(role=AuthUser.MASTERADMIN)
            master_clients = active_users.filter(role=AuthUser.MASTERCLIENT)
            curators = active_users.filter(role=AuthUser.CURATOR)
            context.update({
                'master_admin': master_admins,
                'master_client': master_clients,
                'curator': curators
            })
        return context


class Regist(CreateView):
    """
    ユーザー登録
    """
    template_name = "accounts/regist.html"
    form_class = forms.UserCreationForm
    model = get_user_model()

    def get_success_url(self):
        success_url = reverse("accounts:accounts_list")
        object = self.object
        if object.role == AuthUser.CREATOR:
            creator = models.Creator.objects.create(user=self.object)
            creator_profile = models.CreatorProfile.objects.create(creator=creator, status='1')
            creator.last_published_version = creator_profile
            creator.save()
        return success_url

    def form_invalid(self, form):
        return render(self.request, "accounts/regist.html", {'form': form})

    def form_valid(self, form):
        if form.is_valid():
            user = form.save(commit=False)
            user.save()
            role = form.cleaned_data["role"]
            products = form.cleaned_data["products"]
            product_user_list = []
            if user.role != AuthUser.CURATOR:
                if user.role == AuthUser.MASTERADMIN:
                    products = Product.objects.all()
                for product in products:
                    if role == AuthUser.MASTERADMIN and product.contact_artist:
                        continue
                    user_max = update_product_user_order(user)
                    product_user = ProductUser.objects.create(user=user, product=product)
                    product_user.order = user_max
                    if role == AuthUser.MASTERCLIENT:
                        product_user.is_rating = True
                        product_user.is_favorite = True
                        product_user.order_user = update_product_user_order_user(product, 'master_client')
                    elif role == AuthUser.MASTERADMIN:
                        product_user.position = ProductUser.MASTERADMIN
                        product_user.order_user = update_product_user_order_user(product, 'master_admin')
                    elif role == AuthUser.CREATOR:
                        product_user.position = ProductUser.DIRECTOR
                        product_user.order_user = update_product_user_order_user(product, 'admin')
                    product_user.save()
                    if role == AuthUser.MASTERADMIN:
                        for offer in product.offer_product.all():
                            add_remove_member_into_offer_product(offer)
                        for offer in product.product_offers.filter(admin__role=AuthUser.MASTERADMIN):
                            add_member_into_offer_creator(offer)

                users = list()
                users.append(user)
                update_product_user_new_video(products, users)

        return super(Regist, self).form_valid(form)

    def get_context_data(self, **kwargs):
        context = {}
        user = self.request.user
        form = forms.UserCreationForm
        if user.role == AuthUser.MASTERADMIN:
            form.base_fields['role'].choices = [('master_admin', '管理者'),
                                                ('master_client', 'オーナー'),
                                                ('curator', 'キューレター')]
            form.base_fields['products'].queryset = Product.objects.all()
        elif user.role == AuthUser.CURATOR:
            form.base_fields['role'].choices = [('admin', 'アーティスト')]
            form.base_fields['products'].queryset = Product.objects.none()
        context.update({'form': form})

        return context

    def dispatch(self, request, *args, **kwargs):
        user = self.request.user
        if user.role not in [AuthUser.MASTERADMIN, AuthUser.CURATOR]:
            return redirect("app:warning")
        return super(Regist, self).dispatch(request, *args, **kwargs)


class Signup(FormView):
    template_name = "accounts/signup.html"
    form_class = forms.UserRegisterForm
    email_template_name = 'accounts/account_signup_email.html'
    subject_template_name = 'accounts/account_signup_subject.txt'
    model = get_user_model()
    success_url = reverse_lazy('accounts:password_signup_done')

    def get_context_data(self, **kwargs):
        context = super(Signup, self).get_context_data(**kwargs)
        next_url = self.request.GET.get('next')
        import urllib.parse

        if not next_url:
            next_url = self.request.GET.get('next_url')
        if next_url:
            if 'accounts/creator/' in next_url:
                playing_id = self.request.GET.get('playing_id', '')
                if playing_id:
                    next_url = next_url + '?playing_id=' + playing_id
                    social_next = '/accounts/handle_sns_signup_redirect'
                    if next_url:
                        social_next = social_next + urllib.parse.quote('?next_url=' + next_url, safe='')
                    context.update({'next_url': next_url, 'social_next': social_next})
                    return context
            next_url = urllib.parse.quote(next_url, safe='')
            if ('collection/bookmark_sale_content?' in next_url or \
                    '%2Fcollection%2Fbookmark_sale_content%3F' in next_url) and 'accounts' not in next_url:
                next_url = '/accounts' + next_url
        social_next = '/accounts/handle_sns_signup_redirect'
        if next_url:
            social_next = social_next + urllib.parse.quote('?next_url=' + next_url, safe='')
        context.update({'next_url': next_url, 'social_next': social_next})
        return context

    def form_valid(self, form):
        if form.is_valid():
            email = form.cleaned_data["email"]
            raw_password = form.cleaned_data["password1"]
            is_exits_email = AuthUser.objects.filter(email=email)
            if len(email) > 50:
                form.errors_mail = "メールアドレスの最大文字数は５０文字でございます。"
                return render(self.request, 'accounts/signup.html', {"form": form})
            elif is_exits_email:
                form.errors_mail = "このメールアドレスは既に登録されています。"
                return render(self.request, 'accounts/signup.html', {"form": form})
            else:
                sale = None
                user = AuthUser.objects.create(email=email,
                                               username=email,
                                               password=make_password(raw_password),
                                               last_name="姓",
                                               first_name="名",
                                               role="master_client",
                                               is_verify=False,
                                               is_active=False)
                next_url = self.request.GET.get('next_url', None)
                if next_url:
                    arr_url = next_url.split('sale_id=')
                    if len(arr_url) == 2 and '/collection/bookmark_sale_content?' in arr_url[0] and \
                            arr_url[1].isnumeric():
                        sale_id = arr_url[1]
                        sale = SaleContent.objects.filter(pk=sale_id).first()
                        if sale.child.exists():
                            sale = sale.child.first()
                            next_url = f'/collection/bookmark_sale_content?{sale.pk}'
                        bookmark_sale_service(user, sale)
                if not sale:
                    next_url = ''
                self.send_mail_confirm_create_account(user, next_url)
                return super(Signup, self).form_valid(form)

    def send_mail_confirm_create_account(self, user, next_url):
        context = {
            'user': user,
            'uidb64': urlsafe_base64_encode(force_bytes(user.pk)),
            'token': default_token_generator.make_token(user),
            'host': settings.HOST,
            'next_url': next_url
        }
        from_email = os.getenv('EMAIL_ADDRESS')
        html_message = loader.render_to_string(self.email_template_name, context).strip()
        body = strip_tags(html_message)
        subject = loader.render_to_string(self.subject_template_name, context).strip()
        try:
            send_mail(subject, message=body, from_email=from_email, recipient_list=[user.email], fail_silently=False, html_message=html_message)
        except Exception as e:
            raise e
        


class Update(UpdateView):
    """
    ユーザー情報更新
    """
    template_name = "accounts/update.html"
    form_class = forms.SelfUserChangeForm
    model = get_user_model()

    def get_context_data(self, **kwargs):
        context = {}
        user = self.request.user
        form = self.get_form()

        context.update({
            'form': form,
            'uidb64': urlsafe_base64_encode(force_bytes(user.pk)),
            'token': default_token_generator.make_token(user),
        })
        return context

    def get_initial(self):
        initial = super(Update, self).get_initial()
        reset_name = self.request.GET.get('reset_name')
        reset_name = True if reset_name and reset_name.lowner() == 'true' else False
        if reset_name:
            initial['first_name'] = ''
            initial['last_name'] = ''
        return initial

    def dispatch(self, request, *args, **kwargs):
        user = self.request.user
        if user.role == 'creator':
            return redirect("app:warning")
        elif user.role == 'master_client':
            next_url = request.GET.get('next_url')
            success_url = reverse_lazy("accounts:accounts_update_info", kwargs={"pk": user.pk})
            if next_url:
                success_url += '?next_url=' + next_url
            return redirect(success_url)
        return super(Update, self).dispatch(request, *args, **kwargs)

    def get_success_url(self):
        success_url = reverse("app:index")
        return success_url


class UpdateInfoView(UpdateView):
    """
    ユーザー情報更新
    """
    template_name = "accounts/account_info.html"
    form_class = forms.SelfUserInfoForm
    model = models.AuthUser

    def get_context_data(self, **kwargs):
        context = super(UpdateInfoView, self).get_context_data(**kwargs)
        current_user = self.request.user
        user_id = self.kwargs.get('pk', None)
        user_form = AuthUser.objects.filter(id=user_id).first()
        redirect_url = self.request.GET.get('next_url')

        if redirect_url and 'collection/bookmark_sale_content' in redirect_url:
            if '/accounts' not in redirect_url:
                redirect_url = '/accounts' + redirect_url
        context.update({
            # 'uidb64': urlsafe_base64_encode(force_bytes(current_user.pk)).decode(),
            'token': default_token_generator.make_token(current_user),
            'user': current_user,
            'user_form': user_form,
            'redirect_url': redirect_url
        })
        return context

    def get_initial(self):
        initial = super(UpdateInfoView, self).get_initial()
        reset_name = self.request.GET.get('reset_name')
        reset_name = True if reset_name and reset_name.lowner() == 'true' else False
        if reset_name:
            initial['first_name'] = ''
            initial['last_name'] = ''
        return initial

    def dispatch(self, request, *args, **kwargs):
        current_user = self.request.user
        user_id = kwargs.get('pk', None)
        user = AuthUser.objects.filter(pk=user_id).first()

        if user and user.role == AuthUser.MASTERCLIENT and (
                current_user == user or current_user.role == AuthUser.MASTERADMIN):
            return super(UpdateInfoView, self).dispatch(request, *args, **kwargs)
        return redirect('app:warning')

    def get_success_url(self):
        if self.request.user.role == AuthUser.MASTERCLIENT:
            success_url = self.request.GET.get('next_url')
            if not success_url:
                success_url = reverse("app:index")
        else:
            success_url = reverse('accounts:accounts_list')
        return success_url


def update_password(request):
    user_id = request.POST.get('user_id')
    if not user_id:
        return JsonResponse({'status': _('You do not have permission to change your password.')}, status=500)
    user = AuthUser.objects.filter(pk=user_id).first()
    if not user:
        return JsonResponse({'status': _('You do not have permission to change your password.')}, status=500)
    current_user = request.user
    current_pass = request.POST.get('old_password')
    if user.role not in [AuthUser.CREATOR, AuthUser.MASTERCLIENT] or (
            user.role == AuthUser.CREATOR and (current_user != user and current_user.role != AuthUser.CURATOR)) or (
            user.role == AuthUser.MASTERCLIENT and (current_user != user and current_user.role != AuthUser.MASTERADMIN)):
        return JsonResponse({'status': _('Password is required.')}, status=500)
    if not current_pass or current_pass and not user.check_password(current_pass):
        return JsonResponse({'status': _('The current password is incorrect.')}, status=500)
    new_pass = request.POST.get('new_password1')
    confirm_pass = request.POST.get('new_password2')
    if new_pass and confirm_pass:
        if new_pass == confirm_pass:
            form = PasswordChangeForm(user=user, data=request.POST)
            if form.is_valid():
                form.save()
                update_session_auth_hash(request, form.user)
                return JsonResponse({'status': _('I updated my password.')}, status=200)
            else:
                data = list(form.errors.items())
                return HttpResponseBadRequest(data, content_type='application/json')
        else:
            return JsonResponse({'status': _('The new password and the confirmation password are not the same.')},
                                status=500)
    return JsonResponse({'status': _('Something went wrong!')}, status=500)


def remove_current_user(request):
    user_id = request.POST.get('user_id')
    current_user = request.user
    if not user_id:
        return JsonResponse({}, status=500)
    user = AuthUser.objects.filter(pk=user_id).first()
    if not user or user.role not in [AuthUser.CREATOR, AuthUser.MASTERCLIENT]:
        return JsonResponse({}, status=500)
    if user.role == AuthUser.CREATOR and (current_user != user and current_user.role != AuthUser.CURATOR):
        return JsonResponse({}, status=500)
    if user.role == AuthUser.MASTERCLIENT and (current_user != user and current_user.role != AuthUser.MASTERADMIN):
        return JsonResponse({}, status=500)
    user.soft_delete()
    if user == current_user:
        logout_then_login(request)
    return JsonResponse({'is_current_user': user == current_user, 'user_role': current_user.role}, status=200)


class Setting(UpdateView):
    template_name = "accounts/setting.html"
    form_class = forms.UserSettingForm
    model = get_user_model()
    success_url = '/'

    def get_context_data(self, **kwargs):
        context = {}
        form = self.get_form()
        context.update({
            'form': form
        })
        return context

    def dispatch(self, request, *args, **kwargs):
        user = self.request.user
        if user.role == 'creator':
            return redirect("app:warning")
        return super(Setting, self).dispatch(request, *args, **kwargs)


class UpdateAdmin(UpdateView):
    """
    機能 ユーザー情報更新
    """
    template_name = "accounts/update_admin.html"
    form_class = forms.AdminUserChangeForm
    model = get_user_model()
    success_url = '/accounts/list'

    def form_valid(self, form):
        if form.is_valid():
            user = form.save(commit=False)
            user.save()
            role = form.cleaned_data["role"]
            products = form.cleaned_data["products"]
            if role == AuthUser.MASTERADMIN:
                products = Product.objects.all()
            added = Product.objects.filter(Q(pk__in=products), ~Q(pk__in=user.products.all()))
            if role == AuthUser.MASTERCLIENT:
                removed = Product.objects.filter(~Q(pk__in=products), Q(pk__in=user.products.all()))
                OfferUser.objects.filter(project=removed.values_list('product_id', flat=True), user=user).delete()
                pu = ProductUser.objects.filter(user=user, product__in=removed)
                for p in pu:
                    p.delete()
            if user.role == AuthUser.CURATOR:
                ProductUser.objects.filter(user=user).delete()
                OfferUser.objects.filter(user=user).delete()
            if added:
                if user.role != AuthUser.CURATOR:
                    for product in added:
                        if role == AuthUser.MASTERADMIN and product.contact_artist:
                            continue
                        user_max = update_product_user_order(user)
                        product_user, created = ProductUser.objects.get_or_create(user=user, product=product)
                        if created:
                            product_user.order = user_max
                        product_user.is_invited = False
                        if role == AuthUser.MASTERCLIENT:
                            product_user.is_rating = True
                            product_user.is_favorite = True
                            update_product_user_order_user(product, 'master_client')
                        elif role == AuthUser.MASTERADMIN:
                            product_user.position = ProductUser.MASTERADMIN
                            update_product_user_order_user(product, 'master_admin')
                        elif role == AuthUser.CREATOR:
                            product_user.position = ProductUser.DIRECTOR
                        product_user.save()

                        # update offer user
                        if role == AuthUser.MASTERADMIN:
                            for offer in product.offer_product.all():
                                add_remove_member_into_offer_product(offer)
                            for offer in product.product_offers.filter(admin__role=AuthUser.MASTERADMIN):
                                add_member_into_offer_creator(offer)

                    users = list()
                    users.append(user)
                    # update position product user, offer user
                    if user.role == AuthUser.MASTERCLIENT:
                        user.productuser_set.filter(~Q(position__in=[ProductUser.OWNER, ProductUser.REVIEWER])).update(
                            position=ProductUser.REVIEWER)
                        user.offeruser_set.filter(~Q(position__in=[OfferUser.OWNER])).update(
                            position=OfferUser.OWNER)
                    elif user.role == AuthUser.MASTERADMIN:
                        user.productuser_set.filter(~Q(position__in=[ProductUser.MASTERADMIN])).update(
                            position=ProductUser.MASTERADMIN)
                        user.offeruser_set.filter(~Q(position__in=[OfferUser.MASTER_ADMIN])).update(
                            position=OfferUser.MASTER_ADMIN)
                    update_product_user_new_video(added, users)

        return HttpResponseRedirect(self.get_success_url())

    def get_context_data(self, **kwargs):
        context = super(UpdateAdmin, self).get_context_data(**kwargs)
        user = self.request.user
        form = self.get_form()
        if user.role == AuthUser.MASTERADMIN:
            form.base_fields['role'].choices = [('master_admin', '管理者')]
            form.base_fields['products'].queryset = Product.objects.all()
        elif user.role == AuthUser.CURATOR:
            form.base_fields['role'].choices = [('curator', 'キューレター')]
            form.fields['products'].queryset = user.products.all()
        user_updated = models.AuthUser.objects.get(pk=self.kwargs.get('pk'))
        user_id = self.kwargs.get(self.pk_url_kwarg)
        context.update({'user_id': user_id, 'form': form, 'user_updated': user_updated})

        return context

    def dispatch(self, request, *args, **kwargs):
        current_user = self.request.user
        if current_user.role not in [AuthUser.MASTERADMIN, AuthUser.CURATOR]:
            return redirect("app:warning")
        try:
            requested_user = models.AuthUser.objects.get(pk=int(kwargs.get('pk')))
            if requested_user.role == 'creator':
                return redirect("app:warning")
        except models.AuthUser.DoesNotExist:
            return redirect("app:warning")
        return super(UpdateAdmin, self).dispatch(request, *args, **kwargs)


class Delete(FormView):
    form_class = forms.DeleteUser

    def get_success_url(self):
        """Return the URL to redirect to after processing a valid form."""
        if self.request.user.role == AuthUser.CURATOR:
            return str(reverse_lazy('accounts:curator_setting'))
        else:
            return str(reverse_lazy('accounts:accounts_list'))

    def form_valid(self, form):
        form.save()  # ユーサー削除処理
        return super(Delete, self).form_valid(form)

    def get_initial(self):
        from django.contrib.auth import get_user_model
        pk = self.kwargs.get('pk')
        user = get_user_model().objects.get(pk=pk)
        return {'username': user.username}


class Reset(FormView):
    case_sensitive = True
    form_class = PasswordResetForm
    template_name = 'accounts/password_reset_form.html'
    success_url = reverse_lazy('accounts:pwd_reset_done')
    email_template_name = 'accounts/password_reset_email.html'
    subject_template_name = 'accounts/password_reset_subject.txt'
    search_fields = ['email']

    def get_context_data(self, **kwargs):
        kwargs['url'] = self.request.get_full_path()
        return super(Reset, self).get_context_data(**kwargs)

    def get_site(self):
        return get_current_site(self.request)

    def send_notification(self):
        context = {
            'site': self.get_site(),
            'user': self.user,
            'uidb64': urlsafe_base64_encode(force_bytes(self.user.pk)),
            'token': default_token_generator.make_token(self.user),
            'secure': self.request.is_secure(),
            'host': settings.HOST,
            'avatar': self.user,
        }
        from_email = os.getenv('EMAIL_ADDRESS')
        html_message = loader.render_to_string(self.email_template_name, context).strip()
        body = strip_tags(html_message)
        subject = loader.render_to_string(self.subject_template_name, context).strip()
        try:
            send_mail(subject, message=body, from_email=from_email, recipient_list=[self.user.email],
                      fail_silently=False, html_message=html_message)
        except Exception as e:
            raise e

    def form_valid(self, form):
        self.email = form.cleaned_data['email']
        model = get_user_model()
        self.user = model.objects.filter(email=self.email).first()
        if form.is_valid():
            if self.user:
                self.send_notification()
                return super().form_valid(form)
            else:
                form.errors_mail = ["あなたのメールアドレスは登録されていません。"]
                return render(self.request, 'accounts/password_reset_form.html', {"form": form})


class UpdatePassword(PasswordResetConfirmView):
    post_reset_login = True
    post_reset_login_backend = 'django.contrib.auth.backends.ModelBackend'
    template_name = 'accounts/password_reset_confirm.html'

    def form_valid(self, form):
        super().form_valid(form)
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        """Return the URL to redirect to after processing a valid form."""
        if self.request.user.role == 'creator':
            return str(reverse_lazy('accounts:accounts_creator_info', kwargs={'pk': self.request.user.get_creator_id()}))
        else:
            return str(reverse_lazy('accounts:accounts_update', kwargs={'pk': self.request.user.id}))


class ResetPassword(PasswordResetConfirmView):
    post_reset_login = True
    template_name = 'accounts/password_reset_confirm.html'
    post_reset_login_backend = 'django.contrib.auth.backends.ModelBackend'

    def form_valid(self, form):
        super().form_valid(form)
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        """Return the URL to redirect to after processing a valid form."""
        return str('/')


class LoginUserView(FormView):
    form_class = LoginUserForm
    template_name = 'accounts/login.html'
    success_url = reverse_lazy('app:index')

    def get_context_data(self, **kwargs):
        context = super(LoginUserView, self).get_context_data(**kwargs)
        next_url = self.request.GET.get('next')
        import urllib.parse
        if not next_url:
            next_url = self.request.GET.get('next_url')
        if next_url:
            if 'accounts/creator/' in next_url:
                playing_id = self.request.GET.get('playing_id', '')
                if playing_id:
                    next_url = next_url + '?playing_id=' + playing_id
                    social_next = '/accounts/handle_sns_signup_redirect'
                    if next_url:
                        social_next = social_next + urllib.parse.quote('?next_url=' + next_url, safe='')
                    context.update({'next_url': next_url, 'social_next': social_next})
                    return context
            next_url = urllib.parse.quote(next_url, safe='')
            if ('collection/bookmark_sale_content?' in next_url or \
                    '%2Fcollection%2Fbookmark_sale_content%3F' in next_url) and 'accounts' not in next_url:
                next_url = '/accounts' + next_url
        social_next = '/accounts/handle_sns_signup_redirect'
        if next_url:
            social_next = social_next + urllib.parse.quote('?next_url=' + next_url, safe='')
        context.update({'next_url': next_url, 'social_next': social_next})
        return context

    def dispatch(self, request, *args, **kwargs):
        if self.request.user.is_authenticated:
            redirect_to = self.get_success_url()
            if redirect_to == self.request.path:
                raise ValueError(
                    "Redirection loop for authenticated user detected. Check that "
                    "your LOGIN_REDIRECT_URL doesn't point to a login page."
                )
            return HttpResponseRedirect(redirect_to)
        return super(LoginUserView, self).dispatch(request, *args, **kwargs)

    # Modify login_user to handle token creation
    def login_user(self, user, remember_me):
        # Set session expiration if "remember me" is not checked
        if not remember_me:
            self.request.session.set_expiry(0)

        try:
            # Log the user in using session-based login
            login(self.request, user, backend='django.contrib.auth.backends.ModelBackend')

            # Generate a token for the user
            token, created = Token.objects.get_or_create(user=user)

            # Save the token in the response cookies
            response = HttpResponseRedirect(self.get_success_url())
            response.set_cookie(
                'auth_token', 
                token.key, 
                httponly=True, 
                secure=False,  # Set to True if you're using HTTPS
                samesite='Lax'
            )

            self.log_device_user(user)

            return response

        except Exception as ex:
            raise ex

    def log_device_user(self, user):
        user_agent = self.request.user_agent
        if user_agent.is_mobile or user_agent.is_tablet:
            device = user_agent.device.family
        elif user_agent.is_pc:
            device = user_agent.browser.family

        province = self.request.COOKIES.get('province', None)
        ip = self.request.META['REMOTE_ADDR']
        user_device, created = UserDevice.objects.get_or_create(user=user, address=province, device=device, list_ip=ip)
        host = settings.HOST
        if created:
            tasks.send_email_when_fist_login.delay(user_id=user.id, device=device, province=province, host=host)

    # Modified form_valid to include token response
    def form_valid(self, form):
        filter = Q(is_active=True)
        filter &= Q(username__iexact=form.cleaned_data['username']) | Q(email__iexact=form.cleaned_data['username'])
        try:
            user = AuthUser.objects.get(filter)
            remember_me = form.cleaned_data['remember_me']
            next_url = self.request.GET.get('next_url', None)

            ip = self.request.META['REMOTE_ADDR']
            browser = self.request.user_agent.browser.family
            if browser == 'IE':
                self.success_url = reverse_lazy('accounts:login_caution')
            if user.is_check_ip:
                if ip in user.list_ip:
                    return self.login_user(user=user, remember_me=remember_me)  # Return the response with the token in cookies
                else:
                    form.add_error("error_user", "IPが無効です")
                    return super(LoginUserView, self).form_invalid(form)
            else:
                return self.login_user(user=user, remember_me=remember_me)  # Return the response with the token in cookies

        except Exception as e:
            print(e)
            pass
        return redirect('/')

    def get_success_url(self):
        next_path = self.request.GET.get('next_url')
        if next_path:
            return next_path
        return reverse_lazy('app:index')

def logout_handler(request):
    logout(request)
    return redirect('accounts:accounts_login')

class CreatorView(DetailView):
    template_name = "accounts/creator/creator_index.html"
    model = models.Creator

    def get(self, request, *args, **kwargs):
        creators = models.Creator.objects.filter(user=kwargs.get('pk'), user__is_active=True)
        context = get_creator_profile(request, creators)
        share_link_id = request.GET.get('playing_id', None)
        if share_link_id:
            sale = SaleContent.objects.filter(pk=share_link_id)
            if sale.exists():
                context.update({'share_link_album': sale.first() })
        if '?next' in context:
            return redirect(context)
        if context != 'app:warning':
            return self.render_to_response(context)
        return redirect('app:warning')


class CreatorSocialMedia(RequiredCreatorMixin, UpdateView):
    template_name = "accounts/creator/creator_social_media.html"
    form_class = forms.CreatorSocialMediaForm
    model = models.Creator

    def get_context_data(self, **kwargs):
        user = self.request.user
        creator = models.Creator.objects.get(user=user)
        context = {}
        context.update({'creator': creator,
                        'form': self.get_form()
                        })
        return context

    def get_success_url(self):
        success_url = reverse("accounts:accounts_creator_profile", kwargs={"pk": self.get_object().pk})
        return success_url


class CreatorInfoView(UpdateView):
    """
    ユーザー情報更新
    """
    template_name = "accounts/account_info.html"
    form_class = forms.AccountCreatorForm
    model = models.AuthUser

    def get_context_data(self, **kwargs):
        context = super(CreatorInfoView, self).get_context_data(**kwargs)
        current_user = self.request.user
        user_id = self.kwargs.get('pk', None)
        user_form = AuthUser.objects.filter(id=user_id).first()
        redirect_url = self.request.GET.get('next', '')
        context.update({
            'uidb64': urlsafe_base64_encode(force_bytes(current_user.pk)),
            'token': default_token_generator.make_token(current_user),
            'page': 'account_artist',
            'user': current_user,
            'user_form': user_form,
            'redirect_url': f'?next={redirect_url}' if redirect_url else '',
        })
        return context

    def get_initial(self):
        initial = super(CreatorInfoView, self).get_initial()
        reset_name = self.request.GET.get('reset_name')
        reset_name = True if reset_name and reset_name.lowner() == 'true' else False
        if reset_name:
            initial['first_name'] = ''
            initial['last_name'] = ''
        return initial

    def dispatch(self, request, *args, **kwargs):
        current_user = self.request.user
        user_id = kwargs.get('pk', None)
        user = AuthUser.objects.filter(id=user_id).first()
        if user and (current_user == user or current_user.role == AuthUser.CURATOR):
            return super(CreatorInfoView, self).dispatch(request, *args, **kwargs)
        return redirect("app:warning")

    def get_success_url(self):
        if self.request.user.role == AuthUser.CREATOR:
            success_url = self.request.GET.get('next')
            if not success_url:
                success_url = reverse("app:index")
        else:
            success_url = reverse('accounts:curator_setting')
        return success_url


class CreatorInfo(UpdateView):
    template_name = "accounts/creator/creator_info.html"
    form_class = forms.CreatorInfoForm
    model = models.Creator

    def get(self, request, *args, **kwargs):
        try:
            creator = models.Creator.objects.get(pk=self.kwargs.get('pk'))
            user = creator.user
            current_user = self.request.user
            if current_user == user or current_user.role == AuthUser.CURATOR:
                return redirect(reverse_lazy('accounts:creator_info', kwargs={'pk': user.pk}))
        except:
            pass
        return redirect('app:warning')


class CreatorProfile(RequiredCreatorMixin, UpdateView):
    template_name = "accounts/creator/creator_profile.html"
    form_class = forms.CreatorProfileForm
    model = models.Creator

    def get_success_url(self):
        success_url = reverse("accounts:accounts_creator", kwargs={"pk": self.request.user.pk})
        return success_url

    def get_context_data(self, **kwargs):
        context = super(CreatorProfile, self).get_context_data(**kwargs)
        user = self.request.user
        creator = models.Creator.objects.get(user=user)
        form = self.get_form()
        form.fields["avatar"].initial = user.avatar
        context.update({'creator': creator,
                        'form': form,
                        'audio_files': creator.audio_creator.all()
                        })
        return context

    def form_valid(self, form):
        user = self.request.user
        user.x = form.cleaned_data["x"] if form.cleaned_data["x"] else 0.0
        user.y = form.cleaned_data["y"] if form.cleaned_data["y"] else 0.0
        user.width = form.cleaned_data["width"] if form.cleaned_data["width"] else user.width
        user.height = form.cleaned_data["height"] if form.cleaned_data["height"] else user.height
        user.avatar = form.cleaned_data["avatar"] if form.cleaned_data["avatar"] else user.avatar
        user.save()

        return super(CreatorProfile, self).form_valid(form)

    def form_invalid(self, form):
        return super(CreatorProfile, self).form_invalid(form)


# def update_creator_profile(request):
#     approve_field = 0
#     edited_field = 0
#     action = 'edit'
#     edit_type = ''
#     import json
#     data = json.loads(request.POST.get('json'))
#
#     # request.FILE avatar, banner, file_audio
#     avatar = request.FILES.get('avatar', None)
#     banner = request.FILES.get('banner', None)
#
#     profile_id = data.get('profile_id', None)
#     list_approve = data.get('list_approve')
#     list_edited = data.get('list_edited')
#     list_reject = data.get('list_unchanged')
#     edited_field = len(list_edited)
#     approve_field = len(list_approve)
#     reject_field = len(list_reject)
#     user = request.user
#     float_keys = ['x', 'y', 'width', 'height', 'x_banner', 'y_banner', 'width_banner', 'height_banner', 'price',
#                   'max_price', 'song_attribute1_min', 'song_attribute2_min', 'song_attribute1_max',
#                   'song_attribute2_max']
#     int_keys = ['song_attribute1_min', 'song_attribute2_min', 'song_attribute1_max', 'song_attribute2_max']
#     content_edited = dict((k, data[k]) for k in list_edited if k in data)
#
#     data_sale_contents = data.get('sale_contents')
#     list_sale_id = []
#     list_deleted = []
#     action = ''
#     for data_sale in data_sale_contents:
#         key = data_sale.get('id')
#         if data_sale.get('type') != 'deleted':
#             list_sale_id.append(key)
#         else:
#             list_deleted.append(key)
#
#     try:
#         creator_profile = models.CreatorProfile.objects.get(pk=profile_id)
#         creator = creator_profile.creator
#         modified = data.get('modified')
#
#
#         scheme = request.scheme
#         host = settings.HOST
#         url = reverse_lazy('accounts:accounts_creator', kwargs={'pk': creator.user.pk})
#         path = "{host}{url}".format(host=host, url=url)
#         receiver_ids = []
#
#         if request.user.role == 'curator' or request.user.user_creator.first() == creator:
#             last_creator_profile = creator.last_version
#             last_public_creator_profile = creator.last_published_version
#             last_modified = last_creator_profile.modified.timestamp() if last_creator_profile else last_public_creator_profile.modified.timestamp()
#
#             if last_modified > float(modified):
#                 return JsonResponse({'modified': 'true'}, status=500)
#
#             need_update_banner_resized = False
#             with transaction.atomic():
#                 delete_sale_content(list_deleted)
#                 # check real edit?
#                 count = check_edit_profile(request, creator_profile, list_edited, content_edited, data_sale_contents)
#                 if count < 1:
#                     if count < 0:
#                         return JsonResponse({'amount_social': 'over'}, status=500)
#                     else:
#                         if list_deleted:
#                             edit_type = 'delete'
#                             check_real_change_profile(creator, creator_profile)
#                         else:
#                             edit_type = 'nothing'
#                         return JsonResponse({'edit_type': edit_type}, status=200)
#                 if not last_creator_profile:
#                     if count > 0:
#                         id_public = last_public_creator_profile.pk
#                         # clone sale content from last_published_version creator profile
#                         last_creator_profile = last_public_creator_profile
#                         last_creator_profile.pk = None
#                         last_creator_profile.status = '2'
#                         # last_creator_profile.avatar = None
#                         last_creator_profile.banner = None
#                         last_creator_profile.owner = request.user
#                         last_creator_profile.save()
#                         last_public_creator_profile = models.CreatorProfile.objects.get(pk=id_public)
#
#                         if 'sale_content' not in list_edited:
#                             clone_sale_content_with_relation(last_public_creator_profile, last_creator_profile)
#                         else:
#                             sale_contents = last_public_creator_profile.content_profile.all()
#                             for sale_content in sale_contents:
#                                 if str(sale_content.id) not in list_sale_id:
#                                     if not sale_content.child.exists():
#                                         clone_sale_content(sale_content, last_creator_profile, 'clone')
#
#                             for data_sale_content in data_sale_contents:
#                                 change_type = data_sale_content.get('type')
#                                 if change_type == 'edited':
#                                     sale_id = data_sale_content.get('id')
#                                     sale_content = SaleContent.objects.get(pk=sale_id)
#                                     if check_sale_content_real_change(data_sale_content, request):
#                                         update_sale_content(request, data_sale_content, sale_content,
#                                                             last_creator_profile,
#                                                             float_keys, int_keys)
#                                     else:
#                                         if not sale_content.child.exists():
#                                             clone_sale_content(sale_content, last_creator_profile, 'clone')
#                                 if change_type == 'new':
#                                     create_new_sale_content(request, data_sale_content, last_creator_profile,
#                                                             float_keys, int_keys)
#
#                         update_attribute_creator_profile(request, content_edited, last_creator_profile,
#                                                          last_public_creator_profile, False, float_keys)
#
#                         last_creator_profile.save()
#                         creator.last_version = last_creator_profile
#                         creator.save()
#
#                 elif count > 0:
#                     update_attribute_creator_profile(request, content_edited, last_creator_profile,
#                                                      last_public_creator_profile, True, float_keys)
#
#                     if 'sale_content' in list_edited:
#                         for data_sale_content in data_sale_contents:
#                             change_type = data_sale_content.get('type')
#                             if change_type == 'edited':
#                                 sale_id = data_sale_content.get('id')
#                                 sale_content = SaleContent.objects.get(pk=sale_id)
#                                 sale_content.save()
#                                 if check_sale_content_real_change(data_sale_content, request):
#                                     update_sale_content(request, data_sale_content, sale_content, last_creator_profile,
#                                                         float_keys, int_keys)
#                                 else:
#                                     clone_sale_content(sale_content, last_creator_profile, 'clone')
#                             elif change_type == 'new':
#                                 create_new_sale_content(request, data_sale_content, last_creator_profile,
#                                                         float_keys, int_keys)
#
#                     last_creator_profile.owner = user
#                     last_creator_profile.save()
#
#                 #send mail
#                 if count > 0:
#                     type = 'edit'
#                     receiver_ids = []
#                     if request.user.role == 'curator':
#                         if creator.notification == 'immediately':
#                             receiver_ids.append(creator.user.pk)
#                     elif request.user.role == AuthUser.CREATOR:
#                         receiver_ids = list(
#                             models.AuthUser.objects.filter(setting_mail='now', role='curator',
#                                                            is_active=True).values_list('pk', flat=True))
#                     if receiver_ids:
#                         tasks.send_mail_when_update_profile.delay(receiver_ids, scheme, host, path, type)
#
#                 if list_deleted and approve_field == 0 and count == 0:
#                     action = 'delete'
#
#             return JsonResponse({'action': action}, status=200)
#     except:
#         pass
#     return JsonResponse({}, status=500)


def update_creator_profile(request):
    import json
    data = json.loads(request.POST.get('json'))

    profile_id = data.get('profile_id', None)
    list_edited = data.get('list_edited')
    float_keys = ['x', 'y', 'width', 'height', 'x_banner', 'y_banner', 'width_banner', 'height_banner', 'price',
                  'max_price', 'song_attribute1_min', 'song_attribute2_min', 'song_attribute1_max',
                  'song_attribute2_max']
    int_keys = ['song_attribute1_min', 'song_attribute2_min', 'song_attribute1_max', 'song_attribute2_max']
    content_edited = dict((k, data[k]) for k in list_edited if k in data)
    action = ''
    html = ''

    try:
        creator_profile = models.CreatorProfile.objects.get(pk=profile_id)
        creator = creator_profile.creator
        context = {}
        key_edited = ''

        if request.user.role == AuthUser.CURATOR or request.user.user_creator.first() == creator:
            last_public_creator_profile = creator.last_published_version
            context_html = {'public_profile': None,
                            'creator_profile': last_public_creator_profile,
                            'user_creator': creator.user,
                            'user': request.user,
                            'is_checker': True,
                            'can_edit': True}
            if 'album' in list_edited:
                data_sale_contents = content_edited.get('album')
                list_delete_sale = data_sale_contents.get('list_deleted')
                if 'sale_content' in list_delete_sale:
                    data_sale_contents = data_sale_contents.get('sale_contents')
                    for data_sale_content in data_sale_contents:
                        change_type = data_sale_content.get('type')
                        if change_type == 'deleted':
                            sale_id = data_sale_content.get('id')
                            sale_content = SaleContent.objects.filter(pk=sale_id)
                            if sale_content.exists():
                                target = sale_content[0]
                                target.child.all().delete()
                                parent = target.parent
                                if parent:
                                    parent.delete()
                                SaleContent.objects.filter(pk=sale_id).delete()
                                action = 'delete'
                                key_edited = 'sale_content'

            with transaction.atomic():
                for key in list_edited:
                    if key == 'profile_text':
                        if update_edit_profile_block_for_creator(last_public_creator_profile, content_edited):
                            key_edited = 'profile_text'
                            action = 'edited'
                            html = render_to_string('accounts/creator/_text_profile.html', context_html)
                            break
                    if key == 'statement':
                        if update_edit_statement_block_for_creator(last_public_creator_profile, content_edited):
                            key_edited = 'statement'
                            action = 'edited'
                            html = render_to_string('accounts/creator/_statement.html', context_html)
                            break
                    if key == 'header':
                        if update_edit_header_block_for_creator(request, last_public_creator_profile, content_edited):
                            key_edited = 'header'
                            action = 'edited'
                            html = render_to_string('accounts/creator/_header.html', context_html)
                            break
                    if key == 'footer':
                        if update_edit_footer_block_for_creator(last_public_creator_profile, content_edited):
                            key_edited = 'footer'
                            action = 'edited'
                            html = render_to_string('accounts/creator/_footer.html', context_html)
                            break
                    if key == 'album':
                        data_sale_contents = content_edited.get('album')
                        list_edited_sale = data_sale_contents.get('list_edited')
                        if 'sale_content' in list_edited_sale:
                            data_sale_contents = data_sale_contents.get('sale_contents')
                            for data_sale_content in data_sale_contents:
                                change_type = data_sale_content.get('type')
                                if change_type == 'edited':
                                    sale_id = data_sale_content.get('id')
                                    sale_content = SaleContent.objects.get(pk=sale_id)
                                    if check_sale_content_real_change_for_creator(data_sale_content, request) > 0:
                                        sale_content = update_sale_content_for_creator(request, data_sale_content,
                                                                                       sale_content,
                                                                                       float_keys, int_keys)

                                        is_upload_file = False
                                        sale_content_version = sale_content.last_published_version
                                        if not sale_content_version.sale_youtube_link:
                                            is_upload_file = True
                                        key_edited = 'sale_content'
                                        action = 'edited'
                                        context_html.update({'sale_content': sale_content, 'is_edit': True, 'is_upload_device': is_upload_file})
                                        html = render_to_string('accounts/creator/_sale_content.html', context_html)
                                        context.update({'sale_id': sale_id})
                                elif change_type == 'new':
                                    sale_content = create_new_sale_content_for_creator(request, data_sale_content,
                                                                                       last_public_creator_profile,
                                                                                       float_keys, int_keys)
                                    key_edited = 'album'
                                    action = 'edited'
                                    context.update({
                                        'album_new_id': sale_content.pk
                                    })
                                    context_html.update({'sale_content': sale_content})
                                    html = render_to_string('accounts/creator/_sale_content.html', context_html)

            if key_edited != '':
                last_public_creator_profile.save()
            context.update({
                'html': html,
                'block': key_edited,
                'action': action
            })
            return JsonResponse(context, status=200)
    except:
        pass
    return JsonResponse({}, status=500)


def is_edit_profile(request, profile, list_edited, content_edited, data_sale_contents):
    for (key, value) in content_edited.items():
        current_value = getattr(profile, key)
        if key not in 'avatar, banner':
            if value != current_value:
                return True
    avatar = request.FILES.get('avatar')
    banner = request.FILES.get('banner')
    if avatar or banner:
        return True
    if 'sale_content' in list_edited:
        for data_sale_content in data_sale_contents:
            change_type = data_sale_content.get('type')
            if change_type == 'edited':
                sale_id = data_sale_content.get('id')
                sale_content = SaleContent.objects.get(pk=sale_id)
                if sale_content.last_version:
                    sale_content_version = sale_content.last_version
                else:
                    sale_content_version = sale_content.last_published_version
                for (key, value) in data_sale_content.items():
                    if key == 'id':
                        file_name = 'sale_content_image_' + value
                        audio_name = 'sale_content_audio_' + value
                        img = request.FILES.get(file_name, None)
                        audio = request.FILES.get(audio_name, None)
                        if img or audio:
                            return True

                    elif key in ('start_time', 'end_time'):
                        if value != '':
                            value = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M')
                        else:
                            value = None
                        current_value = getattr(sale_content_version, key)
                        if value != current_value:
                            return True

                    elif key not in \
                            ('image', 'id', 'type', 'start_time', 'end_time', 'tags', 'x', 'y', 'width', 'height'):
                        if key in \
                                ('song_attribute1_min', 'song_attribute2_min', 'song_attribute1_max',
                                 'song_attribute2_max', 'price', 'max_price'):
                            if value == '':
                                value = 0
                            else:
                                value = int(value)
                        current_value = getattr(sale_content_version, key)
                        if value != current_value:
                            return True

            elif change_type == 'new':
                return True
    return False


def check_edit_profile(request, profile, list_edited, content_edited, data_sale_contents):
    is_edit_header = check_edit_header(request, profile, content_edited)
    if is_edit_header:
        return 1
    if 'footer' in list_edited and check_edit_footer(profile, content_edited.get('footer')):
        if not check_amount_item_social_in_footer(profile, content_edited.get('footer')):
            return -1
        return 1

    profile_block = profile.get_block_profile()

    statement_block = profile.get_block_statement()

    for (key, value) in content_edited.items():
        if key in ['section_name_en', 'section_name_jp', 'is_link_menu', 'content_en', 'content_jp']:
            if key == 'is_link_menu':
                value = True if value == 'true' else False
            if getattr(profile_block, key) != value:
                return 1
        elif key in LIST_STATEMENT_BLOCK:
            if key in ['is_show_avatar', 'is_show_name', 'is_show_title']:
                value = True if value == 'true' else False
            if getattr(statement_block, key) != value:
                return 1
            continue

    if 'sale_content' in list_edited:
        for data_sale_content in data_sale_contents:
            change_type = data_sale_content.get('type')
            if change_type == 'edited':
                if check_sale_content_real_change(data_sale_content, request) == 1:
                    return 1
            elif change_type == 'new':
                return 1
    return 0


def check_edit_header(request, profile, content_edited):
    banner = request.FILES.get('banner')
    logo = request.FILES.get('logo')
    key_visual_pc = request.FILES.get('key_visual_pc')
    key_visual_sp = request.FILES.get('key_visual_sp')
    if banner or logo or key_visual_pc or key_visual_sp:
        return 1
    header_block = profile.get_block_header()
    for (key, value) in content_edited.items():
        if key in LIST_FIELD_HEADER:
            if key in ['display_tag_line_1', 'display_tag_line_2']:
                value = True if value == 'true' else False
            if getattr(header_block, key) != value:
                return 1
    return 0


def check_sale_content_real_change(data_sale_content, request):
    sale_id = data_sale_content.get('id')
    sale_content = SaleContent.objects.get(pk=sale_id)
    if sale_content.last_version:
        sale_content_version = sale_content.last_version
    else:
        sale_content_version = sale_content.last_published_version
    for (key, value) in data_sale_content.items():
        if key == 'id':
            file_name = 'sale_content_image_' + value
            audio_name = 'sale_content_audio_' + value
            img = request.FILES.get(file_name)
            audio = request.FILES.get(audio_name)
            if img or audio:
                return 1
        elif key in ('start_time', 'end_time'):
            if value != '':
                value = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M')
            else:
                value = None
            current_value = getattr(sale_content_version, key)
            if value != current_value:
                return 1
        elif key == 'tags_content':
            new_tag = value.strip()
            new_tag_arr = new_tag.split('#')
            new_tag_arr.sort()
            new_tag_str = ' '.join([f'{str(elem)}' for elem in list(new_tag_arr)]).strip()
            current_tag = ' '.join([f'{str(elem)}' for elem in list(
                sale_content_version.tags.all().order_by('tag_name').values_list('tag_name', flat=True))])
            if new_tag_str != current_tag:
                return 1
        elif key == 'customizable_sale_setting':
            sale_content_version.customizable_sale_setting = value
            sale_content_version.save()
            continue
        elif key not in \
                ('image', 'id', 'type', 'start_time', 'end_time', 'tags', 'x', 'y', 'width', 'height', 'tags_content'):
            if key in \
                    ('song_attribute1_min', 'song_attribute2_min', 'song_attribute1_max',
                     'song_attribute2_max', 'price', 'max_price'):
                if value == '':
                    value = 0
                if key in \
                        ('price', 'max_price'):
                    value = float(value)
                else:
                    value = int(value)
            current_value = getattr(sale_content_version, key)
            if value != current_value:
                return 1
    return 0


def approve_field_creator_profile(request):
    import json
    data = json.loads(request.POST.get('json'))
    try:
        profile_id = data.get('profile_id', None)
        list_approve = data.get('list_approve')
        approve_field = len(list_approve)
        content_approve = dict((k, data[k]) for k in list_approve if k in data)
        data_sale_contents = data.get('sale_contents')
        upgrade = data.get('upgrade')
        modified = data.get('modified')
        context = {}

        with transaction.atomic():
            creator_profile = models.CreatorProfile.objects.get(pk=profile_id)
            creator = creator_profile.creator
            if request.user.role == 'curator' or request.user.user_creator.first() == creator:
                last_creator_profile = creator.last_version
                last_public_creator_profile = creator.last_published_version
                last_modified = last_creator_profile.modified.timestamp() if last_creator_profile else last_public_creator_profile.modified.timestamp()

                if last_modified > float(modified):
                    return JsonResponse({'modified': 'true'}, status=500)
                if last_creator_profile and last_public_creator_profile and creator_profile == last_creator_profile and approve_field > 0:
                    if upgrade:
                        if not creator_profile.banner and last_public_creator_profile.banner:
                            creator_profile.banner = last_public_creator_profile.banner
                        creator_profile.status = '1'
                        creator_profile.save()
                        last_public_creator_profile.status = '3'
                        last_public_creator_profile.save()
                        tasks.update_medium_small_avatar.delay(str(creator_profile.creator.user.pk))
                        creator.last_published_version = creator_profile
                        creator.last_version = None
                        creator.save()
                        last_item_block = creator_profile.blocks.filter(
                            item_block__type_block=ItemBlock.FOOTER_BLOCK).first()
                        last_item_block.item_block.footer_block.item_social.all().update(parent=None)
                        last_item_block.item_block.footer_block.item_footer.all().update(parent=None)

                        sale_contents = creator_profile.content_profile.all()
                        for sale_content in sale_contents:
                            update_sale_content_in_list(sale_content.parent, sale_content)
                            sale_content.parent = None
                            sale_last_version = sale_content.last_version
                            if sale_last_version:
                                sale_content.last_published_version = sale_last_version
                                sale_content.last_version = None
                            sale_content.save()

                        scheme = request.scheme
                        host = settings.HOST
                        url = reverse_lazy('accounts:accounts_creator', kwargs={'pk': creator.user.pk})
                        path = "{host}{url}".format(host=host, url=url)
                        receiver_ids = []

                        type = 'approve'
                        if request.user.role == 'curator':
                            if creator.notification == 'immediately':
                                receiver_ids.append(creator.user.pk)
                                tasks.send_mail_when_update_profile.delay(receiver_ids, scheme, host, path, type)
                        return JsonResponse({'modified': creator_profile.modified.timestamp(),
                                             'upgrade': True }, status=200)
                    else:
                        old_sale_content = 0
                        sale_content_html = ''
                        for (key, value) in content_approve.items():
                            if key == 'profile_quote':
                                last_item_block = last_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.PROFILE_BLOCK).first()
                                public_item_block = last_public_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.PROFILE_BLOCK).first()
                                if last_item_block and last_item_block != public_item_block:
                                    last_public_creator_profile.blocks.filter(
                                        item_block__type_block=ItemBlock.PROFILE_BLOCK).delete()
                                    CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile,
                                                                    item_block=last_item_block.item_block)
                                continue
                            if key == 'theme_quote':
                                last_item_block = last_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.STATEMENT_BLOCK).first()
                                public_item_block = last_public_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.STATEMENT_BLOCK).first()
                                if last_item_block and last_item_block != public_item_block:
                                    last_public_creator_profile.blocks.filter(
                                        item_block__type_block=ItemBlock.STATEMENT_BLOCK).delete()
                                    CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile,
                                                                    item_block=last_item_block.item_block)
                                statement_update = render_to_string('accounts/creator/_statement.html', {
                                    'public_profile': last_public_creator_profile,
                                    'creator_profile': last_creator_profile,
                                    'user_creator': creator.user,
                                    'user': request.user,
                                    'is_checker': True,
                                    'can_edit': True
                                })
                                context.update({'statement_update': statement_update})
                                continue
                            if key == 'banner':
                                last_item_block = last_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.HEADER_BLOCK).first()
                                public_item_block = last_public_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.HEADER_BLOCK).first()
                                if last_item_block and last_item_block != public_item_block:
                                    last_public_creator_profile.blocks.filter(
                                        item_block__type_block=ItemBlock.HEADER_BLOCK).delete()
                                    CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile,
                                                                    item_block=last_item_block.item_block)
                                header_text_update = render_to_string('accounts/creator/_header.html', {
                                    'public_profile': last_public_creator_profile,
                                    'creator_profile': last_creator_profile,
                                    'user_creator': creator.user,
                                    'user': request.user,
                                    'is_checker': True,
                                    'can_edit': True
                                })
                                context.update({'header_text_update': header_text_update})
                                continue

                            if key == 'footer':
                                last_item_block = last_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.FOOTER_BLOCK).first()
                                public_item_block = last_public_creator_profile.blocks.filter(
                                    item_block__type_block=ItemBlock.FOOTER_BLOCK).first()
                                if last_item_block and last_item_block != public_item_block:
                                    last_public_creator_profile.blocks.filter(
                                        item_block__type_block=ItemBlock.FOOTER_BLOCK).delete()
                                    CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile,
                                                                    item_block=last_item_block.item_block)
                                    last_item_block.item_block.footer_block.item_social.all().update(parent=None)
                                    last_item_block.item_block.footer_block.item_footer.all().update(parent=None)
                                footer_text_update = render_to_string('accounts/creator/_footer.html', {
                                    'public_profile': last_public_creator_profile,
                                    'creator_profile': last_creator_profile,
                                    'user_creator': creator.user,
                                    'user': request.user,
                                    'is_checker': True,
                                    'can_edit': True
                                })
                                context.update({'footer_text_update': footer_text_update})
                                continue

                            if key in 'avatar':
                                continue
                            value = getattr(last_creator_profile, key)
                            setattr(last_public_creator_profile, key, value)

                        last_creator_profile.save()
                        last_public_creator_profile.save()
                        if 'sale_content' in list_approve:
                            for data_sale_content in data_sale_contents:
                                change_type = data_sale_content.get('type')
                                if change_type == 'approved':
                                    sale_id = data_sale_content.get('id')
                                    sale_content = SaleContent.objects.get(pk=sale_id)
                                    old_sale_content = sale_id
                                    new_sale_content_pk = approve_sale_content(sale_content,
                                                                               last_public_creator_profile,
                                                                               creator_profile)
                                    new_sale_content = SaleContent.objects.get(pk=new_sale_content_pk)
                                    sale_content_html = render_to_string('accounts/creator/_sale_content.html',
                                                                         {'sale_content': new_sale_content,
                                                                          'user': request.user})

                        context.update({'modified': last_creator_profile.modified.timestamp(),
                                        'old_sale_content': old_sale_content,
                                        'sale_content_html': sale_content_html})
                        return JsonResponse(context, status=200)
    except:
        return JsonResponse({}, status=500)
    return JsonResponse({}, status=500)


def clone_sale_content_with_relation(last_public_creator_profile, last_creator_profile):
    sale_contents = last_public_creator_profile.content_profile.all()
    for sale_content in sale_contents:
        clone_sale_content(sale_content, last_creator_profile, 'clone')


def update_attribute_creator_profile(request, dict_attribute, creator_profile, public_profile, has_last_version,
                                     float_keys):
    update_edit_profile_block(public_profile, creator_profile, dict_attribute, has_last_version)
    update_edit_header_block(request, public_profile, creator_profile, dict_attribute, has_last_version)
    update_edit_statement_block(public_profile, creator_profile, dict_attribute, has_last_version)
    update_edit_footer_block(public_profile, creator_profile, dict_attribute, has_last_version)
    for (key, value) in dict_attribute.items():
        if key in float_keys and key not in ['x', 'y', 'width', 'height']:
            if value == '':
                value = 0
            else:
                value = round(float(value), 2)
        elif key in ['avatar', 'banner', 'section_name_en', 'section_name_jp', 'content_jp', 'content_en',
                     'is_link_menu'] or key in LIST_FIELD_HEADER or key in LIST_FIELD_HEADER_IMAGE:
            continue
        setattr(creator_profile, key, value)
    creator_profile.save()


def check_edit_profile_block(item_block, dict_attribute):
    profile_block = item_block.profile_block
    for (key, value) in dict_attribute.items():
        if key in ['section_name_en', 'section_name_jp', 'is_link_menu', 'content_jp', 'content_en']:
            if key == 'is_link_menu':
                value = True if value == 'true' else False
            if value != getattr(profile_block, key):
                return 1
    return 0


def check_edit_header_block(request, item_block, dict_attribute):
    banner = request.FILES.get('banner')
    logo = request.FILES.get('logo')
    key_visual_pc = request.FILES.get('key_visual_pc')
    key_visual_sp = request.FILES.get('key_visual_sp')
    if banner or logo or key_visual_pc or key_visual_sp:
        return 1
    header_block = item_block.header_block
    for (key, value) in dict_attribute.items():
        if key in LIST_FIELD_HEADER:
            if key in ['display_tag_line_1', 'display_tag_line_2']:
                value = True if value == 'true' else False
            if getattr(header_block, key) != value:
                return 1
    return 0


def update_edit_profile_block(public_profile, creator_profile, dict_attribute, has_last_version):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.PROFILE_BLOCK).last().item_block
    last_block = None
    if has_last_version:
        last_block = creator_profile.blocks.filter(item_block__type_block=ItemBlock.PROFILE_BLOCK).last().item_block
        item_block = last_block
    else:
        item_block = public_block
    count_change = check_edit_profile_block(item_block, dict_attribute)
    if count_change < 1:
        if not has_last_version:
            CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
        return
    if not has_last_version or has_last_version and last_block == public_block:
        if last_block:
            creator_profile.blocks.filter(item_block__type_block=ItemBlock.PROFILE_BLOCK).delete()
        profile_block = ProfileBlock.objects.create()
        item_block = ItemBlock.objects.create(type_block=ItemBlock.PROFILE_BLOCK, profile_block=profile_block)
        CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
    profile_block = item_block.profile_block
    for (key, value) in dict_attribute.items():
        if key in ['section_name_en', 'section_name_jp', 'content_jp', 'content_en', 'is_link_menu']:
            if key == 'is_link_menu':
                value = True if value == 'true' else False
            setattr(profile_block, key, value)
    profile_block.save()
    return 0


def update_edit_header_block(request, public_profile, creator_profile, dict_attribute, has_last_version):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.HEADER_BLOCK).last().item_block
    last_block = None
    if has_last_version:
        last_block = creator_profile.blocks.filter(item_block__type_block=ItemBlock.HEADER_BLOCK).last().item_block
        item_block = last_block
    else:
        item_block = public_block
    count_change = check_edit_header_block(request, item_block, dict_attribute)
    banner = request.FILES.get('banner')
    logo = request.FILES.get('logo')
    key_visual_pc = request.FILES.get('key_visual_pc')
    key_visual_sp = request.FILES.get('key_visual_sp')
    current_header = item_block.header_block
    if count_change < 1:
        if not has_last_version:
            CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
        return
    if not has_last_version or has_last_version and last_block == public_block:
        if last_block:
            creator_profile.blocks.filter(item_block__type_block=ItemBlock.HEADER_BLOCK).delete()
        header_block = HeaderBlock.objects.create()
        item_block = ItemBlock.objects.create(type_block=ItemBlock.HEADER_BLOCK, header_block=header_block)
        CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
    header_block = item_block.header_block
    update_file_into_header(banner, 'banner', header_block, current_header)
    update_file_into_header(logo, 'logo', header_block, current_header)
    update_file_into_header(key_visual_pc, 'key_visual_pc', header_block, current_header)
    update_file_into_header(key_visual_sp, 'key_visual_sp', header_block, current_header)
    for key in LIST_FIELD_HEADER:
        setattr(header_block, key, getattr(current_header, key))
    for (key, value) in dict_attribute.items():
        if key in LIST_FIELD_HEADER:
            if key in ['display_tag_line_1', 'display_tag_line_2']:
                value = True if value == 'true' else False
            setattr(header_block, key, value)

    header_block.save()
    return 0


def check_edit_statement_block(item_block, dict_attribute):
    statement_block = item_block.statement_block
    for (key, value) in dict_attribute.items():
        if key in LIST_STATEMENT_BLOCK:
            if key in ['is_show_avatar', 'is_show_name', 'is_show_title']:
                value = True if value == 'true' else False
            if value != getattr(statement_block, key):
                return 1
    return 0


def update_edit_statement_block(public_profile, creator_profile, dict_attribute, has_last_version):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.STATEMENT_BLOCK).last().item_block
    last_block = None
    if has_last_version:
        last_block = creator_profile.blocks.filter(item_block__type_block=ItemBlock.STATEMENT_BLOCK).last().item_block
        item_block = last_block
    else:
        item_block = public_block
    count_change = check_edit_statement_block(item_block, dict_attribute)
    current_statement = item_block.statement_block
    if count_change < 1:
        if not has_last_version:
            CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
        return
    if not has_last_version or has_last_version and last_block == public_block:
        if last_block:
            creator_profile.blocks.filter(item_block__type_block=ItemBlock.STATEMENT_BLOCK).delete()
        statement_block = StatementBlock.objects.create()
        item_block = ItemBlock.objects.create(type_block=ItemBlock.STATEMENT_BLOCK, statement_block=statement_block)
        CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
    statement_block = item_block.statement_block
    for key in LIST_STATEMENT_BLOCK:
        setattr(statement_block, key, getattr(current_statement, key))
    for (key, value) in dict_attribute.items():
        if key in LIST_STATEMENT_BLOCK:
            if key in ['is_show_avatar', 'is_show_name', 'is_show_title']:
                value = True if value == 'true' else False
            setattr(statement_block, key, value)
    statement_block.save()
    return 0


def update_file_into_header(file, attr_name, header_block, current_header):
    if not file:
        file = getattr(current_header, attr_name)
    setattr(header_block, attr_name, file)


def approve_attribute_creator_profile(dict_attribute, creator_profile, last_version):
    for (key, value) in dict_attribute.items():
        if key in 'avatar, banner':
            continue
        value = getattr(last_version, key)
        setattr(creator_profile, key, value)
    creator_profile.save()


# approve sale content
def approve_sale_content(sale_content, profile, last_profile):
    id_public = sale_content.pk
    last_publish = sale_content.last_published_version
    last_version = sale_content.last_version
    creator = profile.creator
    if last_version:
        sale_content_version = last_version
    elif last_publish:
        sale_content_version = last_publish
    else:
        sale_content_version = None
    if sale_content_version:
        # create new sale content
        new_sale_content = sale_content
        new_sale_content.pk = None
        new_sale_content.last_version = None
        new_sale_content.parent = None
        new_sale_content.profile = profile
        new_sale_content.save()

        sale_content = SaleContent.objects.get(pk=id_public)

        # update sale content in new work, selection
        update_sale_content_in_list(sale_content, new_sale_content)

        album_variations = sale_content_version.album.all()
        salecontent_tag = sale_content_version.salecontenttag_set.all()
        tags = HashTag.objects.filter(pk__in=salecontent_tag.values_list('tag_id', flat=True))

        # create sale content version
        new_sale_content_version = sale_content_version

        new_sale_content_version.sale = new_sale_content
        new_sale_content_version.pk = None
        new_sale_content_version.save()

        for tag in tags:
            SaleContentTag.objects.get_or_create(sale_content=new_sale_content_version, tag=tag)

        # update last_published_version for new sale content
        new_sale_content.last_published_version = new_sale_content_version
        new_sale_content.save()

        for album_variation in album_variations:
            if album_variation.last_version:
                album_version = album_variation.last_version
            else:
                album_version = album_variation.last_published_version

            # create new album variation
            new_album_variation = album_variation

            new_album_variation.pk = None
            new_album_variation.sale_content = new_sale_content_version
            new_album_variation.save()

            if album_version:
                # create album version
                new_album_version = album_version
                if album_variation.last_version and not album_variation.last_version.file:
                    if album_variation.last_published_version and album_variation.last_published_version.file:
                        new_album_version.file = album_variation.last_published_version.file
                new_album_version.pk = None
                new_album_version.album = new_album_variation
                new_album_version.save()

                # update last_published_version for album variation
                new_album_variation.last_published_version = new_album_version
                new_album_variation.last_version = None
                new_album_variation.save()
        if sale_content.parent:
            clone_sale_content(new_sale_content, last_profile, 'approved')
            if sale_content != sale_content.parent:
                parent_sale_content = sale_content.parent
                parent_profile = parent_sale_content.profile
                if parent_profile == creator.last_published_version:
                    parent_sale_content.delete()
        sale_content.delete()
        return new_sale_content.pk


# create new sale content
def create_new_sale_content(request, data_sale_content, last_creator_profile, float_keys, int_keys):
    file_name = 'sale_content_image_' + data_sale_content.get('id')
    audio_name = 'sale_content_audio_' + data_sale_content.get('id')

    # create new sale content
    new_sale_content = SaleContent.objects.create(profile=last_creator_profile)
    new_sale_content.order = last_creator_profile.content_profile.all().order_by('-order').first().order + 1
    new_sale_content.parent = new_sale_content
    new_sale_content.save()

    new_sale_content_version = SaleContentVersion.objects.create(sale=new_sale_content)

    # update attribute sale content version
    update_attribute_sale_content(data_sale_content, new_sale_content_version, float_keys, int_keys)

    new_sale_content_version.save()

    img = request.FILES.get(file_name, None)
    if img and not new_sale_content_version.default_thumbnail:
        new_sale_content_version.image = img
        crop_new_image('image', new_sale_content_version.image, new_sale_content_version.image.name,
                       new_sale_content_version, new_sale_content_version.x,
                       new_sale_content_version.y, new_sale_content_version.height, new_sale_content_version.width)

    new_sale_content.last_published_version = new_sale_content_version
    new_sale_content.last_version = new_sale_content_version
    new_sale_content.save()
    # create audio
    audio = request.FILES.get(audio_name, None)
    if audio:
        album = AlbumVariation.objects.create(sale_content=new_sale_content_version)
        album_version = AlbumVersion.objects.create(album=album, file=audio)
        album_version.save()
        album.last_published_version = album_version
        album.last_version = album_version
        album.save()


# edited sale content
def update_sale_content(request, data_sale_content, sale_content, profile, float_keys, int_keys):
    # clone sale_content
    id_public = sale_content.pk
    file_name = 'sale_content_image_' + str(id_public)
    audio_name = 'sale_content_audio_' + str(id_public)
    new_sale_content = sale_content
    if sale_content.profile != profile:
        new_sale_content.pk = None
        new_sale_content.profile = profile
        new_sale_content.save()

    sale_content = SaleContent.objects.get(pk=id_public)
    if not sale_content.parent:
        new_sale_content.parent = sale_content
        new_sale_content.save()
    update_sale_content_in_list(sale_content, new_sale_content)
    last_publish = sale_content.last_published_version
    last_version = sale_content.last_version
    if last_version:
        sale_content_version = last_version
    elif last_publish:
        sale_content_version = last_publish

    album_variations = sale_content_version.album.all()

    new_sale_content_version = sale_content_version
    new_sale_content_version.pk = None

    if sale_content.profile != profile:
        new_sale_content_version.sale = new_sale_content
    else:
        new_sale_content_version.sale = sale_content
    new_sale_content_version.save()

    update_attribute_sale_content(data_sale_content, new_sale_content_version, float_keys, int_keys)

    img = request.FILES.get(file_name, None)
    if img:
        new_sale_content_version.image = img
        new_sale_content_version.default_thumbnail = None
        crop_new_image('image', new_sale_content_version.image, new_sale_content_version.image.name,
                       new_sale_content_version, new_sale_content_version.x,
                       new_sale_content_version.y, new_sale_content_version.height, new_sale_content_version.width)
    new_sale_content_version.save()

    if sale_content.profile != profile:
        new_sale_content.last_version = new_sale_content_version
        new_sale_content.save()
    else:
        sale_content.last_version = new_sale_content_version
        sale_content.save()

    # create audio
    audio = request.FILES.get(audio_name, None)
    for album_variation in album_variations:
        if album_variation.last_version:
            album_version = album_variation.last_version
        else:
            album_version = album_variation.last_published_version

        if album_version:
            # create new album variation
            new_album_variation = album_variation
            new_album_variation.pk = None
            new_album_variation.sale_content = new_sale_content_version
            new_album_variation.save()

            # create album version
            new_album_version = album_version
            if audio:
                new_album_version.file = audio
            new_album_version.pk = None
            new_album_version.album = new_album_variation
            new_album_version.save()

            # update last_published_version for album variation
            new_album_variation.last_published_version = new_album_version
            if audio:
                new_album_variation.last_version = new_album_version
            else:
                new_album_variation.last_version = None
            new_album_variation.save()


# clone same sale content
def clone_sale_content(sale_content, profile, type):
    sale_content_version = None
    if sale_content.last_version:
        sale_content_version = sale_content.last_version
    elif sale_content.last_published_version:
        sale_content_version = sale_content.last_published_version
    sale_content_id = sale_content.pk
    if sale_content_version:
        # create new sale content
        new_sale_content = sale_content
        new_sale_content.pk = None
        new_sale_content.profile = profile
        new_sale_content.save()
        sale_content = SaleContent.objects.get(pk=sale_content_id)
        if not sale_content.parent:
            new_sale_content.parent = sale_content
            new_sale_content.save()
            # update sale content in new work, selection
        update_sale_content_in_list(sale_content, new_sale_content)

        album_variations = sale_content_version.album.all()

        salecontent_tag = sale_content_version.salecontenttag_set.all()
        tags = HashTag.objects.filter(pk__in=salecontent_tag.values_list('tag_id', flat=True))

        # create sale content version
        new_sale_content_version = sale_content_version
        new_sale_content_version.sale = new_sale_content
        new_sale_content_version.pk = None
        new_sale_content_version.save()

        for tag in tags:
            SaleContentTag.objects.get_or_create(sale_content=new_sale_content_version, tag=tag)

        # update last_published_version for new sale content
        new_sale_content.last_published_version = new_sale_content_version
        new_sale_content.last_version = None
        new_sale_content.save()

        for album_variation in album_variations:
            if album_variation.last_version:
                album_version = album_variation.last_version
            elif album_variation.last_published_version:
                album_version = album_variation.last_published_version
            # create new album variation
            new_album_variation = album_variation
            new_album_variation.pk = None
            new_album_variation.last_version = None
            new_album_variation.last_published_version = None
            new_album_variation.sale_content = new_sale_content_version
            new_album_variation.save()

            if album_version:
                # create album version
                new_album_version = album_version
                new_album_version.pk = None
                new_album_version.album = new_album_variation
                new_album_version.save()
                # update last_published_version for album variation
                new_album_variation.last_published_version = new_album_version
                new_album_variation.save()


# def crop_new_image(key, value, name, object_active, x, y, height, width):
#     avatar = Image.open(io.BytesIO(value.read())).convert("RGBA")
#     area = (x, y, width + x, height + y)
#     crop_avatar = avatar.crop(area)
#     output = io.BytesIO()
#     white_background = Image.new("RGBA", crop_avatar.size, "WHITE")
#     white_background.paste(crop_avatar, (0, 0), crop_avatar)
#     white_background.convert('RGBA').save(output, format='PNG', quality=100)
#     image = InMemoryUploadedFile(output, 'FileField', name, 'image/png',
#                                                 output.getbuffer().nbytes, None)
#
#     setattr(object_active, key, image)
#     object_active.save()


def update_attribute_sale_content(dict_attribute, new_sale_content_version, float_keys, int_keys):
    song_attribute1_min = dict_attribute['song_attribute1_min']
    song_attribute1_max = dict_attribute['song_attribute1_max']
    song_attribute2_min = dict_attribute['song_attribute2_min']
    song_attribute2_max = dict_attribute['song_attribute2_max']

    if not song_attribute1_min:
        song_attribute1_min = 2
    else:
        song_attribute1_min = int(song_attribute1_min)
    if not song_attribute1_max:
        song_attribute1_max = 4
    else:
        song_attribute1_max = int(song_attribute1_max)
    if not song_attribute2_min:
        song_attribute2_min = 2
    else:
        song_attribute2_min = int(song_attribute2_min)
    if not song_attribute2_max:
        song_attribute2_max = 4
    else:
        song_attribute2_max = int(song_attribute2_max)

    if song_attribute1_max < song_attribute1_min:
        temp = song_attribute1_max
        song_attribute1_max = song_attribute1_min
        song_attribute1_min = temp

    if song_attribute2_max < song_attribute2_min:
        temp = song_attribute1_max
        song_attribute2_max = song_attribute2_min
        song_attribute2_min = temp

    dict_attribute['song_attribute1_min'] = song_attribute1_min
    dict_attribute['song_attribute1_max'] = song_attribute1_max
    dict_attribute['song_attribute2_min'] = song_attribute2_min
    dict_attribute['song_attribute2_max'] = song_attribute2_max

    for (key, value) in dict_attribute.items():
        if key in float_keys:
            if value == '':
                value = 0
            else:
                if key in int_keys:
                    value = int(value)
                else:
                    value = round(float(value), 2)
        elif key in ('image', 'id', 'type'):
            continue
        elif key in ('start_time', 'end_time'):
            if value != '':
                value = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M')
            else:
                value = None
        elif key == 'tags_content':
            txt = value
            list_tag = re.findall("#[々〆〤一-龠ぁ-ゔァ-ヴーａ-ｚＡ-Ｚ０-９a-zA-Z0-9]{1,60}", txt)
            list_new_tag = []
            for v in list_tag:
                v = v.replace('#', '')
                list_new_tag.append(v)
                tag = HashTag.objects.filter(tag_name=v)
                if tag.exists():
                    tag = tag[0]
                else:
                    tag = HashTag.objects.create(tag_name=v)
                if not SaleContentTag.objects.filter(sale_content=new_sale_content_version, tag=tag).exists():
                    SaleContentTag.objects.create(sale_content=new_sale_content_version, tag=tag)
            SaleContentTag.objects.filter(Q(sale_content=new_sale_content_version) & ~Q(tag__tag_name__in=list_new_tag)).delete()
            continue
        # elif key == 'default_thumbnail':
        #     new_sale_content_version.image = None
        setattr(new_sale_content_version, key, value)
    new_sale_content_version.save()


def delete_sale_content(list_deleted):
    for id in list_deleted:
        sale_content = SaleContent.objects.filter(pk=id)
        if sale_content.exists():
            target = sale_content[0]
            child_sales = target.child.all()
            if target.parent:
                target.parent.delete()
            if child_sales.exists():
                child_sales.delete()
            target.delete()


class CreatorSetting(UpdateView):
    template_name = "accounts/creator/creator_setting.html"
    form_class = forms.CreatorSettingForm
    model = models.Creator

    def get(self, request, *args, **kwargs):
        current_user = request.user
        user_id = self.kwargs.get('pk')
        if not user_id:
            return redirect('app:warning')
        creator_form = Creator.objects.filter(id=user_id).first()
        if not user_id:
            return redirect('app:warning')
        user_form = creator_form.user
        if current_user.role == AuthUser.CURATOR or current_user.role == AuthUser.CREATOR and current_user == user_form:
            return super(CreatorSetting, self).get(request, *args, **kwargs)

        return redirect('app:warning')

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        form_blocklist = self.get_blocklist_form()

        if form.is_valid() and form_blocklist.is_valid():
            form_blocklist.save()
            return super(CreatorSetting, self).post(request, *args, **kwargs)
        else:
            return self.form_invalid(form, form_blocklist)

    def get_success_url(self):
        if self.request.user.role == AuthUser.CURATOR:
            return reverse('accounts:curator_setting')

        success_url = reverse("accounts:accounts_creator", kwargs={"pk": self.request.user.pk})
        if self.object.slug:
            success_url = reverse('app:creator_info', kwargs={'slug': self.object.slug})
        return success_url

    def get_context_data(self, **kwargs):
        context = super(CreatorSetting, self).get_context_data(**kwargs)
        current_user = self.request.user
        creator_id = self.kwargs.get('pk', None)
        user_form = Creator.objects.filter(id=creator_id).first().user
        form = kwargs.get('form', self.get_form())
        form_blocklist = kwargs.get('form_blocklist', self.get_blocklist_form())
        toppage_user = AuthUser.get_artist_landing_page()
        is_toppage_user = False
        if toppage_user.exists() and toppage_user[0].pk == user_form.pk:
            is_toppage_user = True
            form.initial['direct_contact'] = 'production'
            if not user_form.user_creator.first().direct_contact == 'production':
                user_form.user_creator.update(direct_contact='production')
        context.update({
            'form': form,
            'creator': user_form.user_creator.first(),
            'host': settings.HOST,
            'skills': Skill.get_choses(user_form),
            'form_blocklist': form_blocklist,
            'current_user': current_user,
            'user_form': user_form,
            'is_toppage_user': is_toppage_user
        })

        return context

    def get_blocklist_form(self):
        queryset = BlockList.objects.filter(creator=self.object) \
                             .order_by('-created')
        kwargs = {
            'instance': self.object,
            'queryset': queryset
        }

        if self.request.method == 'POST':
            kwargs.update({'data': self.request.POST})

        return BlockListInlineForm(
            **kwargs,
        )

    def form_invalid(self, form, form_blocklist):
        return self.render_to_response(
            self.get_context_data(
                form=form,
                form_blocklist=form_blocklist
            )
        )


def accounts_check_valid_slug(request):
    slug_name = request.GET.get('slug_name')
    regex = '^[a-zA-Z0-9_-]+$'
    list_keyword = ['index.html', 'about.html', 'about', 'topics.html', 'topics', 'service.html', 'service',
                    'ownershelp.html', 'ownershelp', 'ownershelp_200810_sendfile.html',
                    'ownershelp_200810_sendfile',
                    'partnerhelp.html', 'partnerhelp', 'privacypolicy.html', 'privacypolicy', 'termsofservice.html',
                    'termsofservice', 'updateinfo.html', 'updateinfo',
                    'tokushoho.html', 'tokushoho', 'scene', 'top', 'creators', 'home', 'introduction',
                    'admin_review', 'offer_creator', 'gallery', 'gallery.html',
                    'get_sale_content_download_link', 'get_product_file_download_link', 'get_list_admin',
                    'add_edit_admin_product', 'update_sale_content_index',
                    'get_list_random_samples', 'get_content_contract_modal', 'direct', 'product', 'accounts', 'project',
                    'products', 'sale_contents', 'projects', 'scenes', 'offers', 'offer', 'message', 'messages',
                    'messenger', 'messengers', 'mileages']

    if len(slug_name) > 50:
        return JsonResponse({'message': '入力したものが正しくありません。'}, status=500)
    creator_id = request.GET.get('creator_id')
    creator_slug = models.Creator.objects.filter(slug=slug_name).first()
    creator = models.Creator.objects.filter(pk=creator_id).first()
    if creator_slug and creator_slug != creator:
        return JsonResponse({'message': 'このリンクがすでに存在しています。'}, status=500)
    elif slug_name in list_keyword:
        return JsonResponse({'message': 'このリンクが利用できません。'}, status=500)
    elif creator_slug and not re.search(regex, slug_name, flags=re.UNICODE):
        return JsonResponse({'message': '半角の英数字、アンダースコア、ハイフン以外は使用できません。'}, status=500)

    return JsonResponse({}, status=200)


class JoinUs(FormView):
    form_class = JoinUsForm
    template_name = 'accounts/creator/join_us.html'
    success_url = reverse_lazy('accounts:join_us_completed')
    email_template_name = 'accounts/creator/email_join_us_email.html'

    def send_email_join_us(self, creator, questions):
        host = settings.HOST
        creator_avt = tasks.get_user_url(creator.user, host, 'small')
        context = {'creator': creator, 'questions': questions, 'host': host, 'creator_avt': creator_avt }
        from_email = os.getenv('EMAIL_ADDRESS')
        html_message = loader.render_to_string(self.email_template_name, context).strip()
        body = strip_tags(html_message)
        subject = "【SOREMO】 Registration complete"
        try:
            send_mail(subject=subject, message=body, from_email=from_email, fail_silently=False,
                      recipient_list=[creator.user.email], html_message=html_message)
        except Exception as e:
            raise e

    def form_invalid(self, form):
        return super(JoinUs, self).form_invalid(form)

    def form_valid(self, form):
        user = models.AuthUser.objects.create(email=form.cleaned_data.get('email'),
                                              username=form.cleaned_data.get('email'),
                                              last_name=form.cleaned_data.get('last_name'),
                                              first_name=form.cleaned_data.get('first_name'), is_active=False,
                                              role='creator')

        creator = models.Creator.objects.create(dob=form.cleaned_data.get('dob'), phone=form.cleaned_data.get('phone'),
                                                policy=form.cleaned_data.get('policy'),
                                                role_creator=form.cleaned_data.get('role_creator'),
                                                user_id=user.id,
                                                question=form.cleaned_data.get('question')
                                                )
        creator_profile = models.CreatorProfile.objects \
            .create(creator=creator, status='1',
                    banner=form.cleaned_data.get('banner'),
                    official_site=form.cleaned_data.get('official_site'),
                    twitter_link=form.cleaned_data.get('twitter_link'),
                    instagram_link=form.cleaned_data.get('facebook_link'),
                    stage_name=form.cleaned_data.get('stage_name'),
                    stage_name_en=form.cleaned_data.get('stage_name_en'),
                    theme_quote=form.cleaned_data.get('theme_quote'),
                    profile_quote=form.cleaned_data.get('profile_quote'),
                    youtube_link=form.cleaned_data.get('youtube_link'),
                    )

        creator_profile.x = form.cleaned_data["x"] if form.cleaned_data["x"] else creator_profile.x
        creator_profile.y = form.cleaned_data["y"] if form.cleaned_data["y"] else creator_profile.y
        creator_profile.width = form.cleaned_data["width"] if form.cleaned_data["width"] else creator_profile.width
        creator_profile.height = form.cleaned_data["height"] if form.cleaned_data["height"] else creator_profile.height
        creator_profile.avatar = form.cleaned_data["image"] if form.cleaned_data["image"] else creator_profile.avatar

        user.x = form.cleaned_data["x"] if form.cleaned_data["x"] else user.x
        user.y = form.cleaned_data["y"] if form.cleaned_data["y"] else user.y
        user.width = form.cleaned_data["width"] if form.cleaned_data["width"] else user.width
        user.height = form.cleaned_data["height"] if form.cleaned_data["height"] else user.height
        user.avatar = form.cleaned_data["image"] if form.cleaned_data["image"] else user.avatar
        user.save()

        questions = transform_questions(json.loads(creator.question))
        self.send_email_join_us(creator, questions)
        return redirect(self.get_success_url())


class JoinUsComplete(TemplateView):
    template_name = "accounts/creator/join_us_complete.html"


class EmailTemplateWeb(DetailView):
    template_name = "accounts/creator/email_join_us_web.html"
    model = models.Creator
    context_object_name = "user_not_active"

    def get_creator(self):
        obj = self.model.objects.get(pk=self.kwargs.get('pk'))
        if obj and obj.user.is_active is False:
            return obj, True
        return obj, False

    def get_object(self, queryset=None):
        obj, not_active = self.get_creator()
        if obj and not_active:
            return obj

    def get_context_data(self, **kwargs):
        context = super(EmailTemplateWeb, self).get_context_data(**kwargs)
        questions = transform_questions(json.loads(self.get_creator()[0].question))
        context.update({'questions': questions})
        return context

    def dispatch(self, request, *args, **kwargs):
        try:
            creator, not_active = self.get_creator()
            if not creator or not not_active:
                return redirect("app:warning")
        except self.model.DoesNotExist:
            return redirect("app:warning")
        return super(EmailTemplateWeb, self).dispatch(request, *args, **kwargs)


def password_signup_done(request, template_name='accounts/password_signup_done.html'):
    return TemplateResponse(request, template_name)


def password_signup_complete(request, template_name='accounts/password_signup_complete.html'):
    return TemplateResponse(request, template_name)


def login_caution(request, template_name='accounts/login_caution.html'):
    return TemplateResponse(request, template_name)


def invite_pwd_change(request, pidb64, uidb64, token):
    template_name = 'accounts/password_reset_confirm.html'
    try:
        user_pk = force_str(urlsafe_base64_decode(uidb64))
        product_pk = force_str(urlsafe_base64_decode(pidb64))
        user = AuthUser.objects.get(pk=user_pk)
        product = Product.objects.get(pk=product_pk)
        product_user = ProductUser.objects.get(product=product, user=user)
    except (TypeError, ValueError, OverflowError, AuthUser.DoesNotExist, ProductUser.DoesNotExist):
        user = None
    if user is not None and default_token_generator.check_token(user, token):
        validlink = True
        title = 'Enter new password'
        form = SetPasswordForm(user, request.POST)
        next_url = request.GET.get('next_url', None)
        if request.method == 'POST':
            if form.is_valid():
                form.save()
                with transaction.atomic():
                    user.is_active=True
                    user.is_verify=True
                    user.save()
                    product_user.is_invited=False
                    last_member = ProductUser.objects.filter(user__role=AuthUser.MASTERCLIENT, is_invited=False,
                                                             user__is_active=True,
                                                             product=product_user.product).order_by('order_user').last()
                    product_user.order_user = last_member.order_user + 1 if last_member else 0
                    product_user.save()
                new_user = authenticate(username=user.username, password=form.cleaned_data['new_password1'],)
                login(request, new_user)
                success_url = reverse_lazy('accounts:accounts_update', kwargs={'pk': user.pk})
                if next_url:
                    success_url += f'?next_url={next_url}'
                return redirect(success_url)
        else:
            form = SetPasswordForm(user)
        context = {
            'form': form,
            'title': title,
            'validlink': validlink,
            'next_url': next_url
        }
        return TemplateResponse(request, template_name, context)
    return redirect('app:warning')


def check_email_register(request):
    return JsonResponse({'existed': models.AuthUser.objects.filter(email=request.POST.get('email')).exists()})


def validate_block_list(request):
    def check_exist_record(creator_param, company_name_param, ids):
        return models.BlockList.objects.filter(
                    creator=creator_param, company_name=company_name_param
                ).exclude(id__in=ids).exists()

    if request.method == "POST":
        creator = models.Creator.objects.get(user=request.user.id)
        company_name = request.POST.get('param[companyName]')
        ids = request.POST.getlist('param[ids][]', [])
        ids = [int(id) for id in ids]

        if not company_name:
            return JsonResponse({'status': '400', 'message': 'この項目は必須です。'}, status=400)

        if check_exist_record(creator, company_name, ids):
            return JsonResponse({'existed': '同じ会社・ユーザーがすでに存在しています。'})

        return JsonResponse({'successfully': {}})


def transform_questions(questions):
    def take_value_answer(dictionary, answer, list_question):
        val = dictionary.get(answer)
        return "{}: {}".format(answer, list_question[-1][val]) if val else answer

    questions_with_text = {'鍵盤楽器': 'keyboard', '管楽器': 'wind', '事務所': 'company'}
    for item in questions[:-1]:
        if item['question']['multi']:
            for index, value in enumerate(item['answer'], start=0):
                value_answer = item['question']['answer'][int(value)]
                item['answer'][index] = take_value_answer(questions_with_text, value_answer, questions)
        else:
            value_answer = item['question']['answer'][int(item['answer'])]
            item['answer'] = take_value_answer(questions_with_text, value_answer, questions)
    return questions


class CreatorProfileUpdateAdmin(UpdateView):
    template_name = "accounts/creator/creator_profile.html"
    form_class = forms.CreatorProfileUpdateAdminForm
    model = models.Creator

    def get_success_url(self):
        success_url = f'/accounts/creator_profile/update_admin/{self.request.user.pk}'
        return success_url

    def dispatch(self, request, *args, **kwargs):
        if self.request.user.role == 'admin':
            return super(CreatorProfileUpdateAdmin, self).dispatch(request, *args, **kwargs)
        else:
            return redirect("app:warning")

    def get_context_data(self, **kwargs):
        context = super(CreatorProfileUpdateAdmin, self).get_context_data(**kwargs)
        form = self.get_form()
        context.update({'form': form,
                        'audio_files': context['creator'].audio_creator.all()
                        })
        return context

    def form_valid(self, form):
        # user = self.request.user
        # user.x = form.cleaned_data["x"] if form.cleaned_data["x"] else 0.0
        # user.y = form.cleaned_data["y"] if form.cleaned_data["y"] else 0.0
        # user.width = form.cleaned_data["width"] if form.cleaned_data["width"] else user.width
        # user.height = form.cleaned_data["height"] if form.cleaned_data["height"] else user.height
        # user.avatar = form.cleaned_data["avatar"] if form.cleaned_data["avatar"] else user.avatar
        # user.save()

        return super(CreatorProfileUpdateAdmin, self).form_valid(form)

    def form_invalid(self, form):
        return super(CreatorProfileUpdateAdmin, self).form_invalid(form)


def activate_account(request, uidb64, token, backend='django.contrib.auth.backends.ModelBackend'):
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = models.AuthUser.objects.get(pk=uid)
        pass
    except(TypeError, ValueError, OverflowError, AuthUser.DoesNotExist):
        user = None
    if user is not None and default_token_generator.check_token(user, token):
        user.is_active = True
        user.save()
        login(request, user, backend='django.contrib.auth.backends.ModelBackend')
        next_url = request.GET.get('next_url')
        success_url = reverse_lazy('accounts:accounts_update', kwargs={'pk': user.id}) + '?reset_name=True'
        if user.role == AuthUser.MASTERCLIENT and next_url:
            return redirect(f'{success_url}&next_url={next_url}')
        return redirect(success_url)
    else:
        return redirect('app:warning')


def check_user_login(request, pidb64, uidb64):
    try:
        user_pk = force_str(urlsafe_base64_decode(uidb64))
        product_pk = force_str(urlsafe_base64_decode(pidb64))
        user = AuthUser.objects.get(pk=user_pk)
        product = Product.objects.get(pk=product_pk)
        product_user = ProductUser.objects.get(product=product, user=user)
    except (TypeError, ValueError, OverflowError, AuthUser.DoesNotExist, ProductUser.DoesNotExist):
        user = None
    if user is not None:
        if request.user == user:
            product_user.is_invited = False
            product_user.save()
            return redirect(reverse_lazy('app:top_project_detail', kwargs={"pk": product.pk}))
        else:
            return redirect(reverse_lazy('app:top_page'))
    else:
        return redirect(reverse_lazy('app:top_page'))


class RequiredUserMixin(ManagerMixin):
    def __has_permission(self):
        user = self.get_user()
        if not user or not user.is_authenticated:
            return False
        return True

    def dispatch(self, request, *args, **kwargs):
        requested_user = kwargs.get('pk')
        if str(request.user.pk) == requested_user:
            return super(RequiredUserMixin, self).dispatch(request, args, **kwargs)
        else:
            return redirect("app:warning")


class PaymentInfo(RequiredUserMixin, DetailView):
    template_name = "accounts/payment_info.html"
    model = models.AuthUser

    def get_context_data(self, **kwargs):
        context = super(PaymentInfo, self).get_context_data(**kwargs)
        context.update({"customer_pk": settings.CUSTOMER_PK, "list_card": self.get_list_card(),
                        "list_transaction": self.get_list_transaction()})
        return context

    def get_list_card(self):
        current_user = self.request.user
        if current_user.is_stripe_validated:
            stripe.api_key = settings.CUSTOMER_SK
            list_card = stripe.Customer.list_sources(
                current_user.stripe_customer,
                object="card"
            )
            return list_card.data
        return []

    def get_list_transaction(self):
        current_user = self.request.user
        if current_user.is_stripe_validated:
            stripe.api_key = settings.CUSTOMER_SK
            # list_transaction = stripe.Customer.list_balance_transactions(
            list_transaction = stripe.Charge.list(
                customer=current_user.stripe_customer,
            )
            return list_transaction.data
        return []


def card_payment(request):
    if request.GET:
        return redirect(reverse_lazy('accounts:payment', kwargs={"pk": request.user.id}))
    stripe.api_key = settings.CUSTOMER_SK
    current_user = request.user
    token = request.POST.get('stripeToken', None)
    getJson = request.POST.get('getJson', False)
    if not token:
        return redirect('app:warning')
    try:
        if token and current_user.is_stripe_validated:
            # add a card for a customer
            card = stripe.Customer.create_source(
                current_user.stripe_customer,
                source=token
            )
            if getJson:
                return JsonResponse({"card_id": card.id})
        elif token:
            # create a customer with card info
            customer = stripe.Customer.create(
                source=token,
                email=current_user.email,
            )
            current_user.stripe_customer = customer.id
            current_user.is_stripe_validated = not customer.delinquent
            current_user.save()
            if getJson:
                return JsonResponse({"card_id": customer.default_source})
    except stripe.error.CardError as e:
        return JsonResponse({"message": e.user_message, "code": e.code, "http_status": e.http_status}, status=e.http_status)
    return redirect(reverse_lazy('accounts:payment', kwargs={"pk": request.user.id}))


def stripe_config(request):
    if request.method == 'GET':
        stripe_config = {'publicKey': settings.CUSTOMER_PK}
        return JsonResponse(stripe_config, safe=False)


def remove_card(request):
    if request.GET:
        return JsonResponse({'message': 'エラーが発生しました'}, status=500)
    stripe.api_key = settings.CUSTOMER_SK
    card_id = request.POST.get('card_id', None)
    current_user = request.user
    customer_id = current_user.stripe_customer
    if current_user.is_anonymous or not current_user.is_stripe_validated or not customer_id:
        return JsonResponse({'message': 'エラーが発生しました'}, status=500)
    try:
        deleted_card = stripe.Customer.delete_source(customer_id, card_id)
        if deleted_card.deleted:
            return JsonResponse({"message": "カードが削除しました。", "card_id": deleted_card.id}, status=200)
    except Exception as e:
        return JsonResponse({"message": "エラーが発生しました"}, status=500)
        pass


def find_creator(request):
    list_id = request.GET.get('list_id')
    if list_id:
        try:
            list_creator = CreatorList.objects.get(pk=list_id)
            creator_list = list_creator.creator_list_creator.all().values_list('pk', flat=True)
            creators_all = models.Creator.objects.filter(user__is_active=True)
            creators = creators_all.exclude(pk__in=creator_list)
            if creators.exists():
                context = {
                    'temp_list_creators': creators,
                    'list_creator': list_creator
                }
                creators_html = render_to_string('creator/_show_creators.html', context)
                return JsonResponse({'status': 'ok', 'creators_html': creators_html}, status=200)
            elif creators_all.exists():
                return JsonResponse({'status': 'existed'}, status=200)
        except:
            pass
    return JsonResponse({'status': 'none'}, status=200)


def add_composer_to_list(request):
    list_composer_id = request.POST.getlist("list_composer_id[]")
    list_id = int(request.POST.get("list_id"))
    creator_list_creators = []
    if list_id and request.user.role == 'curator':
        try:
            list = CreatorList.objects.get(pk=list_id)
            for composer_id in list_composer_id:
                new_listcreator_creator, created = CreatorListCreator.objects.get_or_create(creator_id=int(composer_id),
                                                                                            creator_list_id=list_id)
                if created:
                    new_listcreator_creator.order = CreatorListCreator.objects.filter(creator_list_id=list_id).order_by(
                        '-order').first().order + 1
                    new_listcreator_creator.save()
                creator_list_creators.append(str(new_listcreator_creator.pk))
            return JsonResponse({
                'data': "success",
                'creator_list_creators': creator_list_creators
            }, status=200)
        except:
            pass
    return JsonResponse({}, status=500)


def update_list_creator_index(request):
    arr_order = request.POST.getlist('arr_order[]', None)
    if arr_order:
        try:
            index = 0
            arr_order = list(reversed(arr_order))
            for list_id in arr_order:
                item = models.CreatorList.objects.filter(pk=list_id)
                if item.exists():
                    target = item[0]
                    target.order = index
                    target.save()
                    index += 1
            return JsonResponse({}, status=200)
        except:
            pass
    return JsonResponse({}, status=500)


def update_creator_index(request):
    list_id = request.POST.get('list_id', None)
    arr_order = request.POST.getlist('arr_order[]', None)
    if arr_order:
        try:
            creator_list = models.CreatorList.objects.get(id=list_id)
            index = 0
            arr_order = list(reversed(arr_order))
            for creator_id in arr_order:
                item = models.CreatorListCreator.objects.filter(creator_id=creator_id, creator_list=creator_list)
                if item.exists():
                    target = item[0]
                    target.order = index
                    target.save()
                    index += 1

            return JsonResponse({}, status=200)
        except:
            pass
    return JsonResponse({}, status=500)


def delete_list_creator(request):
    list_id = request.POST.get('list_id', None)
    if list_id and request.user.role == 'curator':
        try:
            CreatorList.objects.get(pk=list_id).delete()
            return JsonResponse({'list_id': list_id}, status=200)
        except:
            pass
    return JsonResponse({}, status=500)


def update_list_creators(request):
    list_id = request.POST.get('list_id', None)
    title = request.POST.get('name').strip()
    description = request.POST.get('desc').strip()
    if list_id and title != '' and description != '' and request.user.role == 'curator':
        try:
            list_creator = CreatorList.objects.get(pk=list_id)
            list_creator.title = title
            list_creator.description = description
            list_creator.save()
            return JsonResponse({'data': "success", 'list_id': list_creator.pk, 'new_title': list_creator.title,
                                 'new_description': list_creator.description}, status=200)
        except:
            pass
    return JsonResponse({}, status=500)


def get_task_deadline(request):
    creator_id = request.GET.get('creator_id')
    if not creator_id:
        return JsonResponse({'status': '500'}, status=500)
    creator = Creator.objects.filter(pk=creator_id).first()
    if not creator:
        return JsonResponse({'status': '500'}, status=500)
    user = creator.user
    current_user = request.user

    if user.role not in [AuthUser.CREATOR, AuthUser.CURATOR]:
        return JsonResponse({'status': '403'}, status=403)

    try:
        group_offers = get_deadline_list_service(user)

        offer_this_month = get_query_deadline_this_month_service(user)
        offer_next_month = get_query_deadline_next_month_service(user)
        task_html = render_to_string(
            'accounts/_item_task.html',
            {'offer_this_month': offer_this_month, 'offer_next_month': offer_next_month, 'current_user': current_user},
        )
        return JsonResponse({'status': '200', 'group_offers': group_offers, 'task_html': task_html}, status=200)
    except Exception as e:
        logging.error(e)
        return JsonResponse({'status': '500'}, status=500)


def get_schedule_list(request):
    try:
        creator_id = request.GET.get('creator_id')
        if not creator_id:
            return JsonResponse({'status': '500'}, status=500)
        creator = Creator.objects.filter(pk=creator_id).first()
        if not creator:
            return JsonResponse({'status': '500'}, status=500)
        user = creator.user
        current_user = request.user

        if user.role not in [AuthUser.CREATOR, AuthUser.CURATOR]:
            return JsonResponse({'status': '403'}, status=403)

        creator = Creator.objects.get(user=user)
        schedules = creator.get_schedule_list()

        return JsonResponse({'status': '200', 'schedules': schedules}, status=200)
    except Exception as e:
        logging.error(e)
        return JsonResponse({'status': e}, status=500)


def curator_search_artist(request):
    keyword = request.GET.get('stage_name', '')
    skill_ids = request.GET.getlist('skill_ids[]')

    current_user = request.user
    if current_user.role == AuthUser.CURATOR:
        creators = Creator.objects.filter(Q(user__is_active=True))
        creator_stage_name = creators.filter(Q(user__stage_name__icontains=keyword) |
                                             Q(user__stage_name_en__icontains=keyword) |
                                             Q(user__fullname__icontains=keyword))
        if skill_ids:
            skills = Skill.objects.filter(pk__in=skill_ids)
            creator_stage_name = creator_stage_name.filter(skills__in=skills)
        artists = AuthUser.objects.filter(pk__in=creator_stage_name.values_list('user_id'))
        list_count = artists.count()
        total_page_search = list_count / 15 if list_count % 15 == 0 else int(list_count / 15) + 1
        artists = get_all_artist(0, keyword, skill_ids)
        html = render_to_string('accounts/curator_setting/_account_management_account_info.html',
                                {'artists': artists, 'list_count': list_count})
        return JsonResponse({'html': html,
                             'total_page_search': total_page_search}, status=200)
    return JsonResponse({}, status=500)


def get_all_skill():
    skills = Skill.objects.all().values_list('id', 'name', 'group')
    result = {}
    for skill in skills:
        res = [*skill[0:2], skill[2].lower().replace(' ', '_')]
        result.setdefault(skill[2], []).append(res)

    return result.items()


class CuratorSetting(TemplateView):
    template_name = "accounts/curator_setting/account_management.html"

    def get_context_data(self, **kwargs):
        context = {}
        artists = AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR)
        list_count = artists.count()
        all_artist = False if list_count > 15 else True

        total_page = list_count / 15 if list_count % 15 == 0 else int(list_count / 15) + 1
        artists = get_all_artist(0)
        creator_invitings = Creator.objects.filter(is_invited=True)
        artist_invitings = AuthUser.objects.filter(pk__in=creator_invitings.values_list('user_id'))

        if artist_invitings.exists():
            for artist_inviting in artist_invitings:
                encoded_token = jwt.encode({'member_pk': artist_inviting.pk}, SECRET, algorithm='HS256')
                artist_inviting.jwt_token = encoded_token
                artist_inviting.signature = encoded_token.split('.')[-1]

        context.update({'artists': artists,
                        'skills': get_all_skill(),
                        'artist_invitings': artist_invitings,
                        'total_page': total_page,
                        'all_artist': all_artist,
                        'list_count': list_count})

        return context

    def get(self, *args, **kwargs):
        if self.request.user.role == AuthUser.CURATOR:
            return super(CuratorSetting, self).get(self)
        return redirect('app:warning')


def get_load_more_artist(request):
    if request.user.role != AuthUser.CURATOR:
        return JsonResponse({'status': '500'})
    keyword = request.GET.get('stage_name', '')
    skill_ids = request.GET.getlist('skill_ids[]')
    offset = request.GET.get('offset')
    offset = int(offset) if offset.isdigit() else 0
    artists = get_all_artist(offset, keyword, skill_ids)
    html = render_to_string('accounts/curator_setting/_list_artist.html',
                            {'artists': artists})
    return JsonResponse({'html': html}, status=200)


def check_email_exist_invite_artist(request):
    email_value = request.POST.get('email_user')
    if AuthUser.objects.filter(email=email_value).exists():
        return JsonResponse({}, status=500)
    users = AuthUser.objects.filter(email=email_value).exists()
    if not users:
        return JsonResponse({}, status=200)

    return JsonResponse({}, status=200)


class InviteArtist(View):
    def get(self, *args, **kwargs):
        request = self.request
        if request.method == 'GET':
            jwt_token = request.GET.get('jwt')
            try:
                decoded_token = jwt.decode(jwt_token, SECRET, algorithms=['HS256']).get('data')
                user_invited_email = decoded_token.get('user_invited_email')
                user_invited_pk = decoded_token.get('user_invited_pk')
                auth_user = AuthUser.objects.get(pk=user_invited_pk, email=user_invited_email)
                uidb64 = urlsafe_base64_encode(force_bytes(auth_user.pk))
                token = default_token_generator.make_token(auth_user)
                if request.user.is_authenticated:
                    return redirect('app:index')

                elif not auth_user.is_active:
                    change_pwd_path = '/accounts/curator/invite/password/change/{uidb64}/{token}'.format(uidb64=uidb64,
                                                                                                         token=token)
                    return redirect(change_pwd_path)
            except jwt.exceptions.ExpiredSignature or AuthUser.DoesNotExist:
                return redirect('app:warning')
        return redirect('app:warning')

    def post(self, *args, **kwargs):
        if self.request.method == 'POST':
            user_params = self.request.POST
            current_user = self.request.user
            if not current_user.is_authenticated or current_user.role != AuthUser.CURATOR:
                return JsonResponse({}, status=500)

            email = user_params.get('email')

            if not check_email_in_system(email):
                return JsonResponse({}, status=500)

            user_invited = AuthUser.objects.filter(email=email).first()
            if user_invited and user_invited.role != AuthUser.CREATOR:
                return JsonResponse({}, status=500)

            if not user_invited:
                user_invited = create_artist_invited(user_params, email)
                stage_name = user_params.get('stage_name', '')
                stage_name_en = user_params.get('stage_name_en', '')
                if stage_name:
                    user_invited.fullname = stage_name
                    user_invited.save()
                elif stage_name_en:
                    user_invited.fullname = stage_name_en
                    user_invited.save()
            if user_invited:
                creator = user_invited.user_creator.first()
                if creator:
                    creator.is_invited = True
                    creator.save()
                if not creator:
                    creator = Creator.objects.create(user=user_invited)
                    creator_profile = models.CreatorProfile.objects.create(creator=creator, status='1', is_invited=True)
                    creator.last_published_version = creator_profile
                    creator.save()
                self.send_invite_artist(user_invited)
                encoded_token = jwt.encode({'member_pk': user_invited.pk}, SECRET, algorithm='HS256')
                user_invited.jwt_token = encoded_token
                user_invited.signature = encoded_token.split('.')[-1]
                html = render_to_string('accounts/curator_setting/_account_management_list_invited.html',
                                        {'artist_inviting': user_invited})

                return JsonResponse({'html': html}, status=200)

        return JsonResponse({}, status=500)

    def put(self, *args, **kwargs):
        request = self.request
        if request.method == 'PUT':
            user_params = request.GET
            decoded_token = jwt.decode(user_params.get('jwt'), SECRET, algorithms=['HS256'])
            user_pk = decoded_token.get('member_pk')
            current_user = self.request.user
            if user_pk:
                if current_user.is_anonymous or current_user.role != AuthUser.CURATOR:
                    return JsonResponse({}, status=500)
                try:
                    user = AuthUser.objects.get(pk=user_pk)
                    self.send_invite_artist(user)
                    return JsonResponse({}, status=200)
                except AuthUser.DoesNotExist:
                    return JsonResponse({}, status=500)
        return redirect('app:warning')

    def delete(self, *args, **kwargs):
        if self.request.method == "DELETE":
            user_params = self.request.GET
            decoded_token = jwt.decode(user_params.get('jwt'), SECRET, algorithms=['HS256'])
            user_pk = decoded_token.get('member_pk')
            current_user = self.request.user
            if user_pk:
                try:
                    if current_user.is_anonymous or current_user.role != AuthUser.CURATOR:
                        return JsonResponse({}, status=500)
                    auth_user = AuthUser.objects.get(pk=user_pk)
                    with transaction.atomic():
                        creator = auth_user.user_creator.first()
                        if creator:
                            creator.is_invited = False
                            creator.save()
                        auth_user.soft_delete()

                    return JsonResponse({}, status=200)
                except ProductUser.DoesNotExist or AuthUser.DoesNotExist:
                    return JsonResponse({}, status=500)
        return redirect('app:warning')

    def generate_jwt_token_invite_artist(self, user_invited_id, user_invited_email):
        data = {'user_invited_pk': user_invited_id, 'user_invited_email': user_invited_email}
        payload = {'exp': int(time.time()) + 86400, 'data': data}
        return jwt.encode(payload, SECRET, algorithm='HS256')

    def send_invite_artist(self, user_invited):
        scheme = self.request.scheme
        host = settings.HOST
        url = '/accounts/curator/invite_artist'
        path = "{host}{url}".format(host=host, url=url, scheme=scheme)
        jwt_token = self.generate_jwt_token_invite_artist(user_invited.id, user_invited.email)
        tasks.send_invitation_artist_email.delay(sender_id=self.request.user.pk,
                                                 user_invited_id=user_invited.pk,
                                                 path="{path}?jwt={token}".format(path=path, token=jwt_token))
        return


def curator_invite_pwd_change(request, uidb64, token):
    template_name = 'accounts/password_reset_confirm.html'
    try:
        user_pk = force_str(urlsafe_base64_decode(uidb64))
        user = AuthUser.objects.get(pk=user_pk)
    except (TypeError, ValueError, OverflowError, AuthUser.DoesNotExist, ProductUser.DoesNotExist):
        user = None
    if user is not None and default_token_generator.check_token(user, token):
        validlink = True
        title = 'Enter new password'
        form = SetPasswordForm(user, request.POST)
        if request.method == 'POST':
            if form.is_valid():
                form.save()
                with transaction.atomic():
                    active_artist_invited(user)
                new_user = authenticate(username=user.username, password=form.cleaned_data['new_password1'],)
                login(request, new_user)
                return redirect(reverse_lazy('accounts:creator_info', kwargs={'pk': user.pk}))
        else:
            form = SetPasswordForm(user)
        context = {
            'form': form,
            'title': title,
            'validlink': validlink,
        }
        return TemplateResponse(request, template_name, context)
    return redirect('app:warning')


def edit_evaluation_point_artist(request):
    user_id = request.POST.get('user_id')
    evaluation_point = float(request.POST.get('evaluation_point', '0').replace(',', ''))
    if evaluation_point < 0:
        evaluation_point = 0

    current_user = request.user
    if not user_id:
        return JsonResponse({}, status=500)
    user = AuthUser.objects.filter(pk=user_id, role=AuthUser.CREATOR).first()
    old_evaluation_point = user.evaluation_point
    if not user or current_user.role != AuthUser.CURATOR:
        return JsonResponse({}, status=500)
    user.evaluation_point = evaluation_point
    user.save()
    # Update total point
    AuthUser.change_balance(user.pk, [('total_point', (- old_evaluation_point + evaluation_point))])
    return JsonResponse({'user_id': user_id}, status=200)


def curator_upload_file(request):
    file = request.POST.get('file')
    user_id = request.POST.get('user_id')
    if not user_id:
        return JsonResponse({}, status=500)
    user = AuthUser.objects.filter(pk=user_id).first()
    if not user:
        return JsonResponse({}, status=500)
    current_user = request.user
    if current_user.role != AuthUser.CURATOR or not user.user_creator.first():
        return JsonResponse({}, status=500)
    curator_file_link = ''
    creator = user.user_creator.first()
    if file:
        creator.curator_file = file
        creator.curator_file_name = request.POST.get('file_name')
        creator.save()
        curator_file_link = creator.curator_file.url
    elif request.POST.get('delete') == 'true':
        creator.curator_file = None
        creator.curator_file_name = None
        creator.save()
    return JsonResponse({'curator_file_name': creator.curator_file_name, 'curator_file_link': curator_file_link},
                        status=200)


def curator_approve_reject_file(request):
    status = request.POST.get('status')
    user_id = request.POST.get('user_id')
    if not user_id or not status or status not in ['reject', 'accept']:
        return JsonResponse({}, status=500)
    user = AuthUser.objects.filter(pk=user_id).first()
    current_user = request.user
    if not user or current_user.role != AuthUser.CURATOR or user and not user.user_creator.first():
        return JsonResponse({}, status=500)
    creator = user.user_creator.first()
    files = creator.idetity_files.filter(creator=creator)
    if not files.exists():
        return JsonResponse({}, status=500)
    elif status == 'reject':
        creator.creator_file_status = '3'
    if status == 'accept':
        creator.creator_file_status = '2'
    creator.save()
    return JsonResponse({}, status=200)


def curator_file_download_link(request):
    user_id = request.POST.get('user_id')
    if not user_id:
        return JsonResponse({}, status=500)
    user = AuthUser.objects.filter(role=AuthUser.CREATOR, pk=user_id).first()
    if not user or user and not user.user_creator.first():
        return JsonResponse({}, status=500)
    creator = user.user_creator.first()
    url = get_download_link(creator.curator_file.name, creator.curator_file_name)
    return JsonResponse({'url': url}, status=200)


def artist_create_files(request):
    file = request.POST.get('key_file')
    file_name = request.POST.get('file_name')
    try:
        if file:
            created_file = CreatorFile.objects.create(file=file, real_name=file_name)
            return JsonResponse({str(created_file.pk): file_name})
    except:
        pass
    return JsonResponse({'status': "error"})


def uploadFile(request):
    list_file_remove = request.POST.getlist('list_file_remove')
    list_file_id = request.POST.get('list_file_id')
    user_id = request.POST.get('user_id')
    if not user_id:
        return JsonResponse({}, status=500)
    user = AuthUser.objects.filter(pk=user_id, role=AuthUser.CREATOR).first()
    if not user or user and not user.user_creator.first():
        return JsonResponse({}, status=500)
    creator = user.user_creator.first()
    is_edit_file = False

    if len(list_file_remove) > 0 and list_file_remove[0] != '':
        remove_id = list_file_remove[0].split(',')
        CreatorFile.objects.filter(file_id__in=remove_id).delete()
        is_edit_file = True

    if list_file_id:
        list_file_id = list_file_id.split(",")
        list_files = CreatorFile.objects.filter(pk__in=list_file_id)
        list_files.update(owner=request.user, creator=creator)
        is_edit_file = True
    if is_edit_file:
        creator.creator_file_status = '1' if creator.idetity_files.exists() else '0'
        creator.save()
    return JsonResponse({}, status=200)


def get_artist_files(request):
    user_id = request.POST.get('user_id')
    if not user_id:
        return JsonResponse({}, status=500)
    user = AuthUser.objects.filter(pk=user_id, role=AuthUser.CREATOR).first()
    current_user = request.user
    if not user or user and not user.user_creator.first() or current_user.role != AuthUser.CURATOR:
        return JsonResponse({}, status=500)
    html = render_to_string('accounts/curator_setting/_list_artist_file_modal.html', {'artist': user})
    return JsonResponse({'html': html}, status=200)


def bookmark_sale_content(request):
    sale_id = request.GET.get('sale_id', None)
    user = request.user
    url = f'{reverse_lazy("app:index")}'
    if user.is_authenticated and user.role not in [AuthUser.CREATOR, AuthUser.MASTERCLIENT] or not sale_id:
        return redirect(url)
    sale = SaleContent.objects.filter(pk=sale_id).first()
    if not sale:
        return redirect(url)
    bookmark_sale_service(user, sale)
    url = f'{reverse_lazy("app:get_bookmarked")}'
    return redirect(url)


def handle_sns_signup_redirect(request):
    import datetime
    created_time = datetime.datetime.now() - request.user.created
    next = request.GET.get('next_url', None)
    if created_time.total_seconds() < 20 and request.user.role == AuthUser.MASTERCLIENT:
        if next:
            next = f'?next_url={next}'
        else:
            next = ''
        redirect_url = reverse_lazy("accounts:accounts_update_info", kwargs={"pk": request.user.pk})
        return redirect(f'{redirect_url}{next}')
    elif next:
        return redirect(next)
    return redirect('app:index')
