import datetime
import logging
import os
import re
import redis

import vlc
import time
from celery.schedules import crontab
from celery.task.base import periodic_task
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.mail import send_mail
from django.db import transaction
from django.db.models import Prefetch, Q, F
from django.template import loader
from django.urls import reverse, reverse_lazy
from django.utils.html import strip_tags

from accounts.accounts_services.update_creator_profile_services import get_list_topics_artist
from common.base_tasks import make_new_version_of_img
from accounts.models import AuthUser, ProductUser, Creator, CreatorProfile
from app.models import Product, Scene, SceneComment, PreviewVideo, PreviewScene, Variation, SceneTitle, ProductScene, \
    OfferMessage, OfferCreator, MessageFile, OfferUser, OfferProject, SaleContent, SaleContentListWork, UserOnlineStatus, \
    SceneTitleBookmark, ListBookMark, BookmarkListBookMarks, ListWork, TopicGallery, SceneCommentFile, ProductCommentFile
from app.tasks import get_user_url, sendFileToACRFileScanning, get_object_link
from voice.celery import app

redis_instance = redis.StrictRedis(host=settings.REDIS_HOST,
                                   port=settings.REDIS_PORT, db=0)
LAST_SEND_MAIL = 'LAST_TIME'


@app.task
def send_email_when_fist_login(user_id, device, province, host):
    subject = "【SOREMO】新しいサインインがありました"
    user = AuthUser.objects.get(id=user_id)
    from_email = os.getenv('EMAIL_ADDRESS')

    context = {
        'user': user,
        'device': device,
        'province': province,
        'host': host,
        'last_login': user.last_login.strftime('%Y/%m/%d/ %H:%M')
    }

    html_message = loader.render_to_string('accounts/email-template-view4.html', context).strip()
    body = strip_tags(html_message)
    send_mail(subject, message=body, from_email=from_email,
              recipient_list=[user.email], fail_silently=False, html_message=html_message)


@periodic_task(run_every=crontab(hour=23, minute=59))
def clear_user_not_verify():
    # remove user not verify if link verify expired (1 day)
    today = datetime.datetime.now()
    yesterday = today - datetime.timedelta(days=1)
    try:
        AuthUser.objects.filter(is_verify=False, created__lte=yesterday).delete()
    except Exception as e:
        pass

@periodic_task(run_every=crontab(hour='*', minute='*/15'))
def update_status_online():
    # update status AFK user each 1 hour (1 hour)
    try:
        now = datetime.datetime.now()
        range_check_time = now - datetime.timedelta(minutes=15)
        online_list = UserOnlineStatus.objects.filter(status=True, modified__lte=range_check_time)
        online_list.update(status=False)
    except Exception as e:
        pass

@periodic_task(run_every=crontab(hour='*', minute=0))
def check_send_daily_mail_each_hour():
    time_ago = datetime.datetime.now() - datetime.timedelta(days=1)
    noti_hours = str(datetime.datetime.now().hour) + ':00'
    list_user_master_client = AuthUser.objects.filter(is_active=True, setting_mail='on', noti_hours=noti_hours,
                                        role=AuthUser.MASTERCLIENT)
    list_user_master_admin = AuthUser.objects.filter(is_active=True, setting_mail='on', noti_hours=noti_hours,
                                                      role=AuthUser.MASTERADMIN)
    list_user_admin = AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR, user_creator__notification='one_day',
                                              user_creator__hours=noti_hours)
    list_user_curator = AuthUser.objects.filter(is_active=True, role='curator', setting_mail='on',
                                                noti_hours=noti_hours)

    # send mail scene
    list_user_send_mail(list_user_master_client, time_ago)
    list_user_send_mail(list_user_admin, time_ago)

    # send mail offer creator
    offer_creator_send_multi_noti(list_user_admin, time_ago)
    offer_creator_send_multi_noti(list_user_master_admin, time_ago)


@periodic_task(run_every=crontab(hour='*', minute='*/15'))
def check_send_mail_15_minutes():
    last_time = redis_instance.get(LAST_SEND_MAIL)
    if last_time:
        last_time = last_time.decode("utf-8")
        last_time = datetime.datetime.strptime(last_time, '%Y-%m-%d %H:%M:%S.%f')
    else:
        last_time = datetime.datetime.now()
    time_ago = last_time
    last_time = str(datetime.datetime.now())
    redis_instance.set(LAST_SEND_MAIL, last_time)
    list_user = AuthUser.objects.filter(is_active=True, setting_mail='now', role=AuthUser.MASTERCLIENT)
    list_user_admin = AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR, user_creator__notification='immediately')
    list_user_send_mail(list_user, time_ago)
    list_user_send_mail(list_user_admin, time_ago)


@periodic_task(run_every=crontab(hour='*', minute='*/2'))
def get_result_acr():
    try:
        scmfiles = SceneCommentFile.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[0, 1])
        pdcmfiles = ProductCommentFile.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[0, 1])
        scene = Scene.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[0, 1])
        for scm in scmfiles:
            if scm.acr_status == '2':
                getACRResultForFile(scm)
                time.sleep(1)

        for pdcm in pdcmfiles:
            if pdcm.acr_status == '2':
                getACRResultForFile(pdcm)
                time.sleep(1)

        for sc in scene:
            if sc.acr_status == '2':
                getACRResultForFile(sc)
                time.sleep(1)
    except Exception as e:
        pass
    return True


@periodic_task(run_every=crontab(hour='*', minute='0'))
def get_result_acr_hourly():
    try:
        scmfiles = SceneCommentFile.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[2, 3])
        pdcmfiles = ProductCommentFile.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[2, 3])
        scene = Scene.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[2, 3])
        for scm in scmfiles:
            if scm.acr_status == '2':
                getACRResultForFile(scm)
                time.sleep(1)

        for pdcm in pdcmfiles:
            if pdcm.acr_status == '2':
                getACRResultForFile(pdcm)
                time.sleep(1)

        for sc in scene:
            if sc.acr_status == '2':
                getACRResultForFile(sc)
                time.sleep(1)
    except Exception as e:
        pass
    return True


@periodic_task(run_every=crontab(hour=23, minute=59))
def get_result_acr_daily():
    try:
        scmfiles = SceneCommentFile.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[4,5])
        pdcmfiles = ProductCommentFile.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[4,5])
        scene = Scene.objects.filter(acr_status='2', acr_filescanning_id__isnull=False, acr_result_check_count__in=[4,5])
        for scm in scmfiles:
            if scm.acr_status == '2':
                getACRResultForFile(scm)
                time.sleep(1)

        for pdcm in pdcmfiles:
            if pdcm.acr_status == '2':
                getACRResultForFile(pdcm)
                time.sleep(1)

        for sc in scene:
            if sc.acr_status == '2':
                getACRResultForFile(sc)
                time.sleep(1)
    except Exception as e:
        pass
    return True


def list_user_send_mail(list_user, time_ago):
    from_email = os.getenv('EMAIL_ADDRESS')
    template = 'accounts/email-template-view5.html'

    host = settings.HOST
    comments = SceneComment.objects.filter(created__gt=time_ago)
    scenes = Scene.objects.filter(created__gt=time_ago)
    if comments.exists() or scenes.exists():
        for user in list_user:
            products = user.products.filter(Q(productuser__is_invited=False) &\
                                            ((Q(productuser__notification='on') &\
                                              Q(productuser__user__role=AuthUser.MASTERCLIENT) |\
                                              Q(productuser__is_owner='1') |\
                                              Q(productuser__position__in=[ProductUser.DIRECTOR, ProductUser.PRODUCER]))
                                            )).distinct()
            product_scenes = ProductScene.objects.filter(product_scene__in=products)
            if user.role == 'admin':
                user_exclude = AuthUser.objects.filter(role='admin').values_list('id', flat=True)
            else:
                user_exclude = AuthUser.objects.filter(role='master_client').values_list('id', flat=True)

            owner_comment = comments.filter(scene__product__in=products).exclude(
                user_id__in=user_exclude).values_list('scene_id', flat=True)

            comment_scene = comments.filter(scene__product__in=products).exclude(
                user_id__in=user_exclude).values_list('scene_id', flat=True)
            scene_comment = Scene.objects.filter(pk__in=comment_scene).values_list('title_id', flat=True)
            title_comment = comments.filter(scene_title__product_scene__in=product_scenes).exclude(
                user_id__in=user_exclude).values_list('scene_title_id', flat=True)

            title_comment_act = []
            scene_comment_act = []
            if scene_comment:
                scene_comment_act = [str(item) for item in scene_comment]
            if title_comment:
                title_comment_act = [str(item) for item in title_comment]

            title_act_comment = scene_comment_act + title_comment_act

            owner_scene = scenes.filter(product__in=products).exclude(owner_id__in=user_exclude) \
                .values_list('scene_id', flat=True)
            title_scene = scenes.filter(product__in=products).exclude(owner_id__in=user_exclude) \
                .values_list('title_id', flat=True)
            owner_scene_act = []
            if owner_scene:
                owner_scene_act = [str(item) for item in owner_scene]
            title_scene_act = []
            if title_scene:
                title_scene_act = [str(item) for item in title_scene]

            title_act = title_scene_act + title_act_comment

            if len(title_act_comment) > 0 and len(owner_scene_act) == 0:
                subject = "【SOREMO】新しいメッセージが届いています。"
            elif len(owner_scene_act) > 0 and len(title_act_comment) == 0:
                subject = "【SOREMO】新しい演出が届いています。"
            else:
                subject = "【SOREMO】新しいお知らせ"

            product_user = user.products.filter(productuser__is_active=True, product_id__in=products)

            if user.role not in ['master_admin', 'admin']:
                ps_id = SceneTitle.objects.filter(pk__in=title_act).values_list('product_scene', flat=True)
            else:
                ps_id = SceneTitle.objects.filter(pk__in=title_act_comment).values_list('product_scene', flat=True)

            pss = ProductScene.objects.filter(pk__in=ps_id)
            product_ids = []
            for ps in pss:
                product_ids.append(ps.product_scene.first().pk)
            list_product = product_user.filter(pk__in=product_ids)

            list_product_query = list_product.prefetch_related(
                Prefetch('scene_list', queryset=ProductScene.objects.filter(pk__in=ps_id).prefetch_related(
                    Prefetch('title_product_scene',
                             queryset=SceneTitle.objects.filter(pk__in=title_act).prefetch_related(
                                 Prefetch('scene_title', queryset=Scene.objects.filter(
                                     (Q(created__gt=time_ago)) &
                                     Q(scene_id__in=owner_scene_act) & Q(title__isnull=False)).prefetch_related(
                                     Prefetch('other_versions',
                                              queryset=Scene.objects.order_by('-created'))).select_related('title'))
                             )))))

            if list_product_query and len(title_act) > 0:
                context = {
                    'user': user,
                    'products': list_product_query,
                    'secure': False,
                    'host': host,
                    'user_exclude_list': list(user_exclude) if user_exclude else None,
                    'subject': subject.replace('【SOREMO】', ''),
                    'time_ago': time_ago
                }

                html_message = loader.render_to_string(template, context).strip()
                body = strip_tags(html_message)
                send_mail(subject=subject, message=body, from_email=from_email,
                          recipient_list=[user.email], fail_silently=False, html_message=html_message)


def offer_creator_send_multi_noti(list_user, time_ago):
    from_email = os.getenv('EMAIL_ADDRESS')
    template = 'emails/email-template-multi-noti.html'
    host = settings.HOST
    subject = '【SOREMO】新しいお知らせが届いています。'

    message_modified = OfferMessage.objects.filter(created__gt=time_ago, type_message='1')
    offer_modified = OfferCreator.objects.filter(modified__gt=time_ago)
    try:
        if message_modified.exists() or offer_modified.exists():
            for user in list_user:
                product_created = None
                product_uploaded = None
                product_new_message = None
                product_accepted = None
                product_done = None
                user_name = user.get_display_name()
                user_avt = get_user_url(user, host, 'medium')
                products = user.products.all()

                offer_users = OfferUser.objects.filter(offer__type_offer=OfferProject.OFFER_CREATOR, user=user)
                offer_admins = offer_users.filter(position=OfferUser.ADMIN)
                offer_creators = offer_users.filter(position=OfferUser.CREATOR)

                messages = message_modified.filter(Q(offer_id__in=offer_users.values_list(
                    'offer__offer_creator__pk', flat=True))).exclude(user=user)

                file_uploaded = messages.filter(has_file=True, user=F('offer__creator'))
                new_messages = messages.exclude(pk__in=file_uploaded.values_list('pk'))

                # product new message
                if new_messages.exists():
                    offer_new_message = new_messages.values('offer').distinct()

                    product_new_message = products.filter(
                        product_offers__in=offer_new_message).distinct().prefetch_related(
                        Prefetch('product_offers', queryset=OfferCreator.objects.filter(
                            pk__in=offer_new_message.values_list('offer_id', flat=True)).prefetch_related(
                            Prefetch('message_offer', queryset=OfferMessage.objects.filter(
                                pk__in=new_messages.values_list('pk', flat=True)).exclude(user=user)))))

                list_name_artist = ''

                # product has production file
                if file_uploaded.exists():
                    offer_updloaded = OfferCreator.objects.filter(pk__in=file_uploaded.values('offer')).filter(
                        pk__in=offer_admins.values_list('offer__offer_creator__pk'))

                    product_uploaded = products.filter(
                        product_offers__in=offer_updloaded).distinct().prefetch_related(
                        Prefetch('product_offers', queryset=OfferCreator.objects.filter(admin=user,
                                                                                        pk__in=offer_updloaded.values_list(
                                                                                            'pk', flat=True))))

                # product has offer accept
                offer_accepted = offer_modified.filter(status='2').filter(
                    pk__in=offer_admins.values_list('offer__offer_creator__pk'))
                product_accepted = products.filter(
                    product_offers__in=offer_accepted).distinct().prefetch_related(
                    Prefetch('product_offers', queryset=OfferCreator.objects.filter(
                        pk__in=offer_accepted.values_list('pk', flat=True))))
                if product_accepted.exists():
                    list_creator_id = offer_accepted.values_list('creator_id', flat=True)
                    list_creator = AuthUser.objects.filter(pk__in=list_creator_id).distinct()
                    list_name = []
                    for creator in list_creator:
                        list_name.append(creator.get_display_name())
                    list_name_artist = ', '.join(list_name)

                # product has offer done
                offer_done = offer_modified.filter(status='4', creator=user)

                product_done = products.filter(product_offers__in=offer_done).distinct().prefetch_related(
                    Prefetch('product_offers',
                             queryset=OfferCreator.objects.filter(
                                 pk__in=offer_done.values_list('pk', flat=True))))

                # product has offer create
                offer_created = offer_modified.filter(status='1', creator=user)
                product_created = products.filter(product_offers__in=offer_created).distinct().prefetch_related(
                    Prefetch('product_offers',
                             queryset=OfferCreator.objects.filter(
                                 pk__in=offer_created.values_list('pk', flat=True))))

                context = {
                    'user': user,
                    'user_avt': user_avt,
                    'product_created': product_created,
                    'product_uploaded': product_uploaded,
                    'product_new_message': product_new_message,
                    'product_accepted': product_accepted,
                    'product_done': product_done,
                    'host': host,
                    'subject': subject,
                    'url_page': reverse('app:messenger_waiting'),
                    'user_name': user_name,
                    'list_name_artist': list_name_artist
                }

                html_message = loader.render_to_string(template, context).strip()
                body = strip_tags(html_message)
                if product_created or product_uploaded or product_new_message or product_accepted or product_done:
                    send_mail(subject=subject, message=body, from_email=from_email,
                              recipient_list=[user.email], fail_silently=False, html_message=html_message)
    except Exception as e:
        logging.error(e)


def update_creator_profile_once_noti(list_user, time_ago):
    from_email = os.getenv('EMAIL_ADDRESS')
    template = 'accounts/email_update_creator_profile.html'
    host = settings.HOST

    for user in list_user:
        user_avt = get_user_url(user, host, 'medium')
        type = ''
        if user.role == AuthUser.CREATOR:
            creator = user.user_creator.first()
            url = reverse_lazy('accounts:accounts_creator', kwargs={'pk': creator.user.pk})
            path = "{host}{url}".format(host=host, url=url)
            if creator.last_published_version and creator.last_published_version.modified >= time_ago and \
                    creator.last_published_version.owner == user and not creator.last_version:
                profile = creator.creator_profile.filter(modified__gte=time_ago, status='3').order_by(
                    '-modified').last()
                if profile:
                    type = 'approve'
                    subject = '【SOREMO】プロフィールが承認されました'
            elif creator.last_version and creator.last_version.modified >= time_ago and \
                    creator.last_version.owner != user:
                type = 'edit'
                subject = '【SOREMO】プロフィール提案が届いています'

            if type != '':
                context = {
                    'recipient': user,
                    'recipient_avt': user_avt,
                    'host': host,
                    'type': type,
                    'url': path
                }

                html_message = loader.render_to_string(template, context).strip()
                body = strip_tags(html_message)
                send_mail(subject=subject, message=body, from_email=from_email,
                          recipient_list=[user.email], fail_silently=False, html_message=html_message)

        elif user.role == 'curator':
            creators_edit = Creator.objects.filter(last_version__isnull=False,
                                                   last_version__owner__role=AuthUser.CREATOR,
                                                   user__is_active=True,
                                                   last_version__modified__gte=time_ago)

            if creators_edit.exists():
                subject = '【SOREMO】プロフィール更新が届いています。'
                context = {
                    'recipient': user,
                    'recipient_avt': user_avt,
                    'host': host,
                    'creators_edit': creators_edit
                }

                html_message = loader.render_to_string('accounts/email_curator_creator_profile.html', context).strip()
                body = strip_tags(html_message)
                send_mail(subject=subject, message=body, from_email=from_email,
                          recipient_list=[user.email], fail_silently=False, html_message=html_message)
            else:
                break


# @periodic_task(run_every=crontab(hour=23, minute=59))
# def clear_old_profile_creator():
#     try:
#         from django.conf import settings
#         import boto3
#
#         s3 = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#                           aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
#
#         creators = Creator.objects.filter(user__is_active=True)
#         with transaction.atomic():
#             for creator in creators:
#                 last_published_version = creator.last_published_version
#                 last_version = creator.last_version
#                 list_id = []
#                 list_profile = []
#                 list_name = []
#                 if last_published_version:
#                     list_id.append(last_published_version.pk)
#                     list_profile.append(last_published_version)
#             if last_version:
#                 list_id.append(last_version.pk)
#                 list_profile.append(last_version)
#             old_profiles = creator.creator_profile.filter(status='3').exclude(pk__in=list_id)
#             if old_profiles.exists():
#                 new_profile = old_profiles.order_by('-modified').first()
#                 list_profile.append(new_profile)
#                 old_profiles = old_profiles.exclude(pk=new_profile.pk)
#                 if old_profiles.exists():
#                     for profile in list_profile:
#                         sale_contents = profile.content_profile.all()
#                         for sale_content in sale_contents:
#                             sale_content_versions = sale_content.sale_content.all()
#                             for sale_content_version in sale_content_versions:
#                                 albums = sale_content_version.album.all()
#                                 for album in albums:
#                                     album_versions = album.album_versions.all()
#                                     for album_version in album_versions:
#                                         file = album_version.file
#                                         if file:
#                                             file_name = file.name
#                                             list_name.append(file_name)
#
#                     for profile in old_profiles:
#                         sale_contents = profile.content_profile.all()
#                         for sale_content in sale_contents:
#                             sale_content_versions = sale_content.sale_content.all()
#                             for sale_content_version in sale_content_versions:
#                                 albums = sale_content_version.album.all()
#                                 for album in albums:
#                                     album_versions = album.album_versions.all()
#                                     for album_version in album_versions:
#                                         file = album_version.file
#                                         if file:
#                                             file_name = file.name
#                                             if file_name not in list_name and not Scene.original_objects.filter(
#                                                     movie=album_version.file).exists():
#                                                 album_version.file.delete(save=False)
#                                                 # s3.delete_object(
#                                                 #     Bucket=settings.AWS_STORAGE_BUCKET_NAME,
#                                                 #     Key=file_name,
#                                                 # )
#                         profile.delete()
#     except:
#         pass


# @periodic_task(run_every=crontab(hour=23, minute=59))
# def clear_old_message_file():
#     try:
#         from django.conf import settings
#         import boto3
#
#         s3 = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#                           aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
#
#         time_ago = datetime.datetime.now() - datetime.timedelta(days=1)
#         message_files = MessageFile.objects.filter(message_id__isnull=True, created__lte=time_ago)
#
#         messages = MessageFile.objects.exclude(pk__in=message_files.values_list('pk'))
#
#         with transaction.atomic():
#             for message_file in message_files:
#                 file = message_file.file
#                 if file:
#                     file_name = file.name
#                     if not messages.filter(file=message_file.file).exists() and not Scene.original_objects.filter(
#                             movie=message_file.file).exists():
#                         message_file.file.delete(save=False)
#                         # s3.delete_object(
#                         #     Bucket=settings.AWS_STORAGE_BUCKET_NAME,
#                         #     Key=file_name,
#                         # )
#                 message_file.delete()
#     except:
#         pass


## TODO delete
@app.task
def send_mail_when_update_profile(receiver_ids, scheme, host, path, type):
    from_email = os.getenv('EMAIL_ADDRESS')

    if type == 'edit':
        subject = '【SOREMO】プロフィール提案が届いています'
    elif type == 'approve':
        subject = '【SOREMO】プロフィールが承認されました'

    for receiver_id in receiver_ids:
        try:
            recipient = AuthUser.objects.get(pk=receiver_id)
            if recipient.role == 'curator' and type == 'edit':
                subject = '【SOREMO】プロフィール更新が届いています。'
            recipient_avt = get_user_url(recipient, host, 'medium')

            context = {
                'recipient': recipient,
                'recipient_avt': recipient_avt,
                'host': host,
                'scheme': scheme,
                'url': path,
                'type': type
            }

            html_message = loader.render_to_string('accounts/email_update_creator_profile.html', context).strip()

            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient.email],
                fail_silently=False,
                html_message=html_message
            )
        except ObjectDoesNotExist:
            print(receiver_id)


# @periodic_task(run_every=crontab(hour=23, minute=59))
# def check_deadline_offer_creator():
#     time_ago = datetime.datetime.now() - datetime.timedelta(days=1)
#     offers = OfferCreator.objects.filter(status='1', deadline__lte=time_ago)
#     if offers.exists():
#         offers.update(status='5')


def add_admin_to_products():
    admins = AuthUser.objects.filter(role='admin')
    products = Product.objects.all()
    for admin in admins:
        for product in products:
            ProductUser.objects.get_or_create(product=product, user=admin)

    return


def update_fps_for_scene():
    for scene in Scene.objects.filter(fps_movie__isnull=True).exclude(movie=''):
        if scene.movie:
            vlc_ins = vlc.Instance()
            player = vlc_ins.media_player_new()
            player.set_mrl(scene.movie.url)
            player.play()
            time.sleep(4)
            fps = player.get_fps()
            player.stop()
            scene.fps_movie = round(fps)
            scene.save()


def update_admin_role_master_admin():
    for user in AuthUser.objects.filter(role='admin'):
        user.role  = 'master_admin'
        user.save()


def check_error_scene():
    done_scenes = Scene.objects.filter(tag='2', product__isnull=False, title_id__isnull=False, product_scene__isnull=False)
    for scene in done_scenes:
        if scene.version:
            scenes = Scene.objects.filter(
                Q(scene_id=scene.version) | ~Q(scene_id=scene.scene_id) & Q(version=scene.version)).exclude(
                flag_tag=True)
            if scenes:
                print(scenes.values('pk'))
        else:
            scenes = Scene.objects.filter(version=scene.pk).exclude(flag_tag=True)
            if scenes:
                print(scenes.values('pk'))


def update_user_setting_mail():
    users = AuthUser.objects.filter(setting_mail='other')
    users.update(setting_mail='on')


def update_variation_name():
    scenes = Scene.objects.filter(version__isnull=True, title__isnull=False,
                                  product_scene__isnull=False, variation__isnull=True,
                                  product__isnull=False)
    count = 0
    for scene in scenes:
        try:
            if scene.movie:
                name = scene.movie.name
                name = re.sub(r"movie\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
                variation = name
            else:
                variation = 'Unknown'
            variation_obj = Variation.objects.create(name=variation, scene_title=scene.title)
            scene.variation = variation_obj
            scene.save()
            count += 1
            print(str(int(count*100/scenes.count())) + '%')
        except:
            print(scene.pk)


def update_scene_title_is_done():
    products = Product.objects.all()
    for product in products:
        print('__START_' + product.name + '__')
        product_scenes = product.scene_list.all()
        scene_titles = SceneTitle.objects.filter(product_scene__in=product_scenes, is_done=False).prefetch_related(
            'scene_title')
        for scene_title in scene_titles:
            top_scenes = scene_title.scene_title.filter(product_scene__isnull=False, version=None)
            scene_title.is_done = True
            for top_scene in top_scenes:
                if not top_scene.check_tag() == '2':
                    scene_title.is_done = False
                    break
            if scene_title.is_done:
                scene_title.save()
                print(scene_title.pk)


def init_variation_status():
    products = Product.objects.all()
    for product in products:
        print('__START_' + product.name + '__')
        product_scenes = product.scene_list.all()
        scene_titles = SceneTitle.objects.filter(product_scene__in=product_scenes).prefetch_related(
            'scene_title')
        for scene_title in scene_titles:
            try:
                if scene_title.is_done:
                    if scene_title.production_file:
                        scene_title.variations.all().update(status='6')
                    else:
                        scene_title.variations.all().update(status='5')
                else:
                    top_scenes = scene_title.scene_title.filter(product_scene__isnull=False, version=None)
                    for top_scene in top_scenes:
                        last_scene = top_scene.get_latest_version()
                        if last_scene == top_scene:
                            comments = SceneComment.objects.filter(
                                Q(scene=top_scene) & Q(user__role__in=['master_client']))

                            if comments.exists():
                                if comments.filter(resolved=False).exists():
                                    variation = top_scene.variation
                                    variation.status = '3'
                                    variation.save()
                                    continue
                                else:
                                    variation = top_scene.variation
                                    variation.status = '4'
                                    variation.save()
                                    continue
                            else:
                                if top_scene.ok:
                                    variation = top_scene.variation
                                    variation.status = '4'
                                    variation.save()
                                    continue
                                else:
                                    variation = top_scene.variation
                                    variation.status = '1'
                                    variation.save()
                                    continue
                        else:
                            comments = SceneComment.objects.filter(
                                (Q(scene=top_scene) | Q(scene_id__in=top_scene.other_versions.all())) &
                                Q(user__role__in=['master_client']))
                            if comments.exists():
                                if comments.filter(resolved=False).exists():
                                    variation = top_scene.variation
                                    variation.status = '3'
                                    variation.save()
                                    continue
                                else:
                                    variation = top_scene.variation
                                    variation.status = '4'
                                    variation.save()
                                    continue
                            else:
                                variation = top_scene.variation
                                variation.status = '2'
                                variation.save()
                                continue
            except:
                print(scene_title.pk)
        print('__END__' + product.name + '__')
    print('DONE')
    return


def init_scene_title_status():
    products = Product.objects.all()
    for product in products:
        print('__START_' + product.name + '__')
        product_scenes = product.scene_list.all()
        scene_titles = SceneTitle.objects.filter(product_scene__in=product_scenes).prefetch_related(
            'scene_title')
        for scene_title in scene_titles:
            check_scene_title_status(scene_title)
        print('__END__' + product.name + '__')
    print('DONE')
    return


def check_scene_title_status(scene_title):
    try:
        if scene_title.is_done:
            if scene_title.production_file:
                scene_title.status = '6'
                scene_title.save()
            else:
                scene_title.status = '5'
                scene_title.save()
        else:
            top_scenes = scene_title.scene_title.filter(product_scene__isnull=False, version=None)
            version_scenes = scene_title.scene_title.filter(product_scene__isnull=False, version__isnull=False).exists()
            has_comment = SceneComment.objects.filter(
                (Q(scene__in=top_scenes) | Q(scene_id__in=top_scenes.values('other_versions')) |
                 Q(scene_title_id=scene_title)) & Q(user__role='master_client')).exists()

            has_ok = scene_title.scene_title.filter(ok=True).exists()
            if not has_comment and not has_ok:
                if version_scenes:
                    scene_title.status = '2'
                    scene_title.save()
                else:
                    scene_title.status = '1'
                    scene_title.save()
            else:
                if has_comment:
                    last_comment_time = SceneComment.objects.filter(
                        Q(scene__in=top_scenes) | Q(scene_id__in=top_scenes.values('other_versions')) |
                        Q(scene_title_id=scene_title)).order_by('-created').first().created
                    has_new_scene = scene_title.scene_title.filter(ok=False,
                                                                   created__gt=last_comment_time).exists()
                    has_new_version = scene_title.scene_title.filter(ok=False, created__gt=last_comment_time,
                                                                     version__isnull=False).exists()
                    if has_new_scene and has_new_version:
                        scene_title.status = '2'
                        scene_title.save()
                    else:
                        has_unresolved = SceneComment.objects.filter(
                            (Q(scene__in=top_scenes) | Q(scene_id__in=top_scenes.values('other_versions')) |
                             Q(scene_title_id=scene_title)) &
                            Q(resolved=False)).exists()

                        if has_unresolved:
                            last_unresolved_comment = SceneComment.objects.filter(
                                (Q(scene__in=top_scenes) | Q(scene_id__in=top_scenes.values('other_versions')) |
                                 Q(scene_title_id=scene_title)) &
                                 Q(resolved=False)).order_by('-created').first()
                            if last_unresolved_comment.user.role == 'master_client':
                                scene_title.status = '3'
                                scene_title.save()
                            else:
                                scene_title.status = '2'
                                scene_title.save()
                        else:
                            has_version = scene_title.scene_title.filter(ok=False,
                                                                         version__isnull=False).exists()
                            if has_version:
                                scene_title.status = '2'
                                scene_title.save()
                            else:
                                scene_title.status = '4'
                                scene_title.save()
                else:
                    ok_time = scene_title.scene_title.filter(ok=True).order_by('-modified').first().modified
                    has_new_scene = scene_title.scene_title.filter(ok=False,
                                                                   created__gt=ok_time).exists()

                    has_new_version = scene_title.scene_title.filter(ok=False, created__gt=ok_time,
                                                                     version__isnull=False).exists()
                    if has_new_scene and has_new_version:
                        scene_title.status = '2'
                        scene_title.save()
                    else:
                        has_version = scene_title.scene_title.filter(ok=False, version__isnull=False).exists()
                        if has_version:
                            scene_title.status = '2'
                            scene_title.save()
                        else:
                            scene_title.status = '4'
                            scene_title.save()
    except:
        print(scene_title.pk)


def fix_duplicate_preview_video():
    products = Product.objects.filter(is_active=True)
    for product in products:
        print(product.name)
        scenes = Scene.objects.filter(product=product)
        for scene in scenes:
            owner_list = []
            pv = PreviewVideo.objects.filter(scene=scene)
            for i in pv:
                if i.owner_id in owner_list:
                    try:
                        i.delete()
                    except:
                        pass
                    print(scene.pk)
                else:
                    owner_list.append(i.owner_id)


def auto_use_last_scene(scene_title):
    last_scene = scene_title.scene_title.order_by('created').last()
    scene_title.production_file = last_scene.movie
    scene_title.status = '6'
    scene_title.save()


@app.task
def update_medium_small_avatar(profile_id):
    profile = AuthUser.objects.filter(id=profile_id).first()
    if not profile:
        return

    if profile.avatar:
        make_new_version_of_img('medium', 'medium_avatar', 'avatar', (180, 180), profile)
        make_new_version_of_img('small', 'small_avatar', 'avatar', (24, 24), profile)
        profile.save()


@app.task
def update_banner_resized(creator_profile_id):
    creator_profile = CreatorProfile.objects.filter(pk=creator_profile_id).first()
    if not creator_profile:
        return

    if creator_profile.banner:
        make_new_version_of_img('resized', 'banner_resized', 'banner', (1920, 384), creator_profile)
        creator_profile.save()


@app.task
def send_invitation_artist_email(sender_id, user_invited_id, path):
    host = settings.HOST
    sender = AuthUser.objects.get(pk=sender_id)
    user_invited = AuthUser.objects.get(pk=user_invited_id)
    user_invited_avt = get_user_url(user_invited, host, 'medium')

    subject = 'SOREMOサービスのご案内'
    context = {
        'sender_name': sender.get_display_name(),
        'user_invited': user_invited,
        'user_invited_avt': user_invited_avt,
        'path': path,
        'host': host,
    }

    html_message = loader.render_to_string('emails/email_curator_invite_artist.html', context).strip()
    from_email = os.getenv('EMAIL_ADDRESS')
    body = strip_tags(html_message)
    send_mail(
        subject=subject,
        message=body,
        from_email=from_email,
        recipient_list=[user_invited.email],
        fail_silently=False,
        html_message=html_message
    )


def get_creator_profile(request, creators):
    if not creators.exists():
        return 'app:warning'

    context = {}
    creator = creators[0]
    creator_profile = creator.last_published_version
    user_creator = creator.user
    creator_job_title = f' | {creator.user.position}' if creator.user.position else ''
    context.update({
        'user_creator': user_creator,
        'title_page': f'{creator.user.get_display_name()}{creator_job_title}'
    })

    user =request.user

    if not user.is_authenticated:
        return get_context_unauthenticated(context, creator, creator_profile, request)

    if user.user_creator.first() == creator or user.role == AuthUser.CURATOR:
        return get_context_edit_profile(context, user, creator, creator_profile)

    if not can_show_profile(creator, user):
        return 'app:warning'

    return get_context_only_view(context, user, creator, creator_profile)


def can_show_profile(creator, user):
    if user.role not in [
        AuthUser.MASTERADMIN, AuthUser.CURATOR,
        AuthUser.CREATOR, AuthUser.MASTERCLIENT]:
        return False

    if creator.show_profile in [Creator.PUBLIC, Creator.PRIVATE]:
        return True

    return creator.user.products.filter(
        pk__in=user.products.filter(productuser__is_invited=False)
    ).exists()


def get_context_unauthenticated(context, creator, creator_profile, request):
    if creator.show_profile != Creator.PUBLIC:
        toppage = AuthUser.get_artist_landing_page()
        if toppage.exists() and creator.user.pk != toppage[0].pk:
            playing_id = request.GET.get('playing_id', '')
            if playing_id:
                playing_id = '&playing_id=' + playing_id
            context = f'{reverse_lazy("accounts:accounts_login")}' \
                        f'?next={str(reverse_lazy("accounts:accounts_creator", kwargs={"pk": creator.user.pk}))}{playing_id}'
            return context

    sale_contents = creator_profile.content_profile.all().order_by('-order')
    context.update({
        'creator_profile': creator_profile,
        'user': None,
        'public_profile': None,
        'sale_contents': sale_contents
    })
    get_list_topics_artist(creator.user, context)

    return context


def get_context_edit_profile(context, user, creator, creator_profile):
    creator.update_last_published_version()

    context.update({
        'can_edit': True,
        'user': user,
        'creator': creator,
    })
    get_list_topics_artist(creator.user, context)
    sale_contents = creator_profile.content_profile.all().order_by('-order')
    context.update({
        'creator_profile': creator_profile,
        'public_profile': None,
        'sale_contents': sale_contents
    })

    return context


def get_context_only_view(context, user, creator, creator_profile):
    creator.update_last_published_version()
    sale_contents = creator_profile.content_profile.all().order_by('-order')

    context.update({
        'creator_profile': creator_profile,
        'user': user,
        'public_profile': None,
        'sale_contents': sale_contents
    })
    get_list_topics_artist(creator.user, context)
    return context


def update_product_user_order(user):
    last_pu = user.productuser_set.all().order_by('-order').first()
    return (last_pu.order + 1) if last_pu else 0


def update_product_user_order_user(product, role):
    last_pu = None
    if role == 'master_admin':
        last_pu = product.productuser_set.filter(
        Q(user__role=AuthUser.MASTERADMIN) | Q(position=ProductUser.PRODUCER)).order_by('-order').first()
    elif role == 'admin':
        last_pu = product.productuser_set.filter(
            Q(user__role=AuthUser.CREATOR) & Q(position=ProductUser.DIRECTOR)).order_by('-order').first()
    elif role == 'master_client':
        last_pu = product.productuser_set.filter(
            Q(user__role=AuthUser.MASTERCLIENT) & Q(position=ProductUser.REVIEWER) & Q(is_invited=False)).order_by(
            '-order').first()
    return (last_pu.order + 1) if last_pu else 0


def get_list_works(user, list_works):
    if user.is_authenticated:
        if user.role == AuthUser.CURATOR:
            list_works = list_works.order_by('-order').prefetch_related(
                Prefetch('salecontentlistwork_set',
                         queryset=SaleContentListWork.objects.filter(
                             Q(sale_content__profile__creator__user__is_active=True)
                         ).order_by('-order')))
        else:
            list_works = list_works.exclude().order_by('-order').prefetch_related(
                Prefetch('salecontentlistwork_set',
                         queryset=SaleContentListWork.objects.filter(
                             (Q(sale_content__profile__creator__user__is_active=True) &
                              (Q(sale_content__profile__creator__show_profile__in=[Creator.PUBLIC, Creator.PRIVATE]) |
                               (Q(sale_content__profile__creator__show_profile=Creator.PROJECT) &
                                Q(sale_content__profile__creator__user__products__in=user.products.filter(
                                    productuser__is_invited=False))))) |
                             Q(sale_content__profile__creator=user.user_creator.first())
                         ).order_by('-order').distinct()))
    else:
        list_works = list_works.exclude().order_by('-order').prefetch_related(
            Prefetch('salecontentlistwork_set', queryset=SaleContentListWork.objects
                     .filter(sale_content__profile__creator__user__is_active=True,
                             sale_content__profile__creator__show_profile='public').order_by('-order').distinct()
                     ))
    return list_works


def get_list_search_sale_content(user, sale_contents):
    if user.is_authenticated:
        sale_contents = sale_contents.filter(
            (Q(profile__creator__user__is_active=True) & (
                Q(profile__creator__show_profile__in=[Creator.PUBLIC, Creator.PRIVATE])))).distinct()
    else:
        sale_contents = sale_contents.filter(
            (Q(profile__creator__user__is_active=True) & (
                Q(profile__creator__show_profile=Creator.PUBLIC)))).distinct()
    return sale_contents


def bookmark_sale_service(user, sale):
    if user.role not in [AuthUser.CREATOR, AuthUser.MASTERCLIENT] or not sale:
        return
    item_sale = SceneTitleBookmark.objects.filter(type_bookmark=SceneTitleBookmark.BOOKMARK_SALE,
                                                  sale=sale, user=user).first()
    if item_sale:
        return
    if sale.child.exists():
        sale = sale.child.first()
        if SceneTitleBookmark.objects.filter(type_bookmark=SceneTitleBookmark.BOOKMARK_SALE,
                                             sale=sale, user=user).first():
            return

    item_mark, created = SceneTitleBookmark.objects.get_or_create(type_bookmark=SceneTitleBookmark.BOOKMARK_SALE,
                                                  sale=sale, user=user)
    if created:
        list_bookmark = user.list_bookmarks.all().first()
        if not list_bookmark:
            list_bookmark = ListBookMark.objects.create(title='お気に入り', user=user)
        if list_bookmark and item_mark:
            if item_mark.pk not in BookmarkListBookMarks.objects.filter(listbookmark=list_bookmark).values_list(
                'scenetitlebookmark__pk'):
                if not list_bookmark.item_ids.exists():
                    order = 1
                else:
                    order = max(list_bookmark.item_ids.values_list("order", flat=True)) + 1
                BookmarkListBookMarks.objects.create(scenetitlebookmark=item_mark,
                                                     listbookmark=list_bookmark, order=order)
    return


def getACRResultForFile(object):
    try:
        print('run_get_result_for', str(object.pk))
        import requests, json
        ACCESS_TOKEN = settings.ACR_ACCESS_TOKEN
        FS_KEY = settings.ACR_FS_KEY
        object.refresh_from_db()
        if not object.acr_filescanning_id:
            return False
        url = "https://api-v2.acrcloud.com/api/fs-containers/"+ FS_KEY + "/files/" + object.acr_filescanning_id

        payload={}
        headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + ACCESS_TOKEN
        }

        response = requests.request("GET", url, headers=headers, data=payload)
        result = json.loads(response.text)
        acr_filescanning = result['data'][0]['results']
        object_queryset = object.__class__.objects.filter(pk=object.pk)
        result_check_count = object_queryset.first().acr_result_check_count
        result_check_count = result_check_count + 1
        if result_check_count > 6:
            object_queryset.update(acr_result_check_count=result_check_count, acr_status='5')
            return False
        else:
            object_queryset.update(acr_result_check_count=result_check_count)
        if str(result['data'][0]['state']) == '1' and acr_filescanning:
            try:
                if acr_filescanning['music']:
                    for idx, item in enumerate(acr_filescanning['music']):
                        isrc = item['result']['external_ids']['isrc']
                        if isrc:
                            url = 'https://api.deezer.com/2.0/track/isrc:' + isrc
                            res_deezer =  requests.request("GET", url)
                            result_deezer = json.loads(res_deezer.text)
                            if result_deezer['album']['cover_medium']:
                                acr_filescanning['music'][idx].update({'cover_url': result_deezer['album']['cover_medium']})
            except:
                import sys
                print('error1', sys.exc_info()[1])
                pass
            try:
                if acr_filescanning['cover_songs']:
                    for idx, item in enumerate(acr_filescanning['cover_songs']):
                        isrc = item['result']['external_ids']['isrc']
                        if isrc:
                            url = 'https://api.deezer.com/2.0/track/isrc:' + isrc
                            res_deezer = requests.request("GET", url)
                            result_deezer = json.loads(res_deezer.text)
                            if result_deezer['album']['cover_medium']:
                                acr_filescanning['cover_songs'][idx].update({'cover_url': result_deezer['album']['cover_medium']})
            except:
                import sys
                print('error2', sys.exc_info()[1])
                pass
            acr_filescanning = json.dumps(acr_filescanning)
            object.__class__.objects.filter(pk=object.pk).update(acr_filescanning=acr_filescanning, acr_status='3')
            sendSignalGotResultForFile(object, 'active')
        else:
            if str(result['data'][0]['state']) == '-1':
                object.__class__.objects.filter(pk=object.pk).update(acr_status='4')
                sendSignalGotResultForFile(object, 'hide')
            elif str(result['data'][0]['state']) == '-2':
                # sendFileToACRFileScanning(str(object.pk), object.__class__.__name__)
                object.__class__.objects.filter(pk=object.pk).update(acr_status='5')
                sendSignalGotResultForFile(object, 'hide')
        return True
    except:
        import sys
        print('error all', sys.exc_info()[1])
        pass
    return False


def sendSignalGotResultForFile(file, acr_class):
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync

    file_class = file.__class__.__name__
    if file_class == 'Scene':
        product = file.product
    elif file_class == 'ProductCommentFile':
        product = file.message.project
    elif file_class == 'SceneCommentFile':
        scm = file.message
        if scm.scene:
            product = scm.scene.product
        elif scm.scene_title:
            product = scm.scene_title.product_scene.product_scene.first()
            if not product:
                return False
        else:
            return False
    pu_ids = ProductUser.objects.filter(product=product).exclude(position=ProductUser.MASTERADMIN).values_list('user__pk', flat=True)
    new_offer_message = {'type': 'offer_message', 'action': 'acr_result', 'file': str(file.pk), 'acr_class': acr_class}
    for id in pu_ids:
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            '{}'.format(id),
            new_offer_message
        )
