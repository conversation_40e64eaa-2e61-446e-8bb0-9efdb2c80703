# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-09-09 11:10
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0063_auto_20210907_1649'),
    ]

    operations = [
        migrations.AddField(
            model_name='authuser',
            name='admin_file',
            field=models.FileField(blank=True, null=True, upload_to='file'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='admin_file_name',
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AddField(
            model_name='authuser',
            name='file_status',
            field=models.CharField(choices=[('0', 'まだファイルをアップロードしない'), ('1', '確認中'), ('2', '承認済'), ('3', '拒否済')], default='0', max_length=20),
        ),
        migrations.Add<PERSON>ield(
            model_name='authuser',
            name='stage_name_en',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='芸名（英語表記）'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='type',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='肩書き'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='user_file',
            field=models.FileField(blank=True, null=True, upload_to='file'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='user_file_name',
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
    ]
