# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-01-07 11:38
from __future__ import unicode_literals

from django.db import migrations, models
import django_mysql.models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_authuser_products'),
    ]

    operations = [
        migrations.AddField(
            model_name='authuser',
            name='is_check_ip',
            field=models.BooleanField(default=False, verbose_name='ログイン前にIPを確認'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='list_ip',
            field=django_mysql.models.ListTextField(models.CharField(max_length=15, null=True), blank=True, default=None, help_text="各ip範囲の後に '、'を使用", max_length=66, null=True, size=6, verbose_name='リストIP'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='email',
            field=models.EmailField(default=None, max_length=50, null=True, verbose_name='メールアドレス'),
        ),
    ]
