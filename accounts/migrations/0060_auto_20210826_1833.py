# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-08-26 18:33
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0059_authuser_titles'),
    ]

    operations = [
        migrations.AddField(
            model_name='authuser',
            name='account_number',
            field=models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='口座番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='account_type',
            field=models.CharField(choices=[('normal', '普通'), ('temp', '当座'), ('reverse_tax', '納税準備預金'), ('saving', '貯蓄'), ('other', 'その他')], default='normal', max_length=50, verbose_name='口座種類'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='bank',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='銀行名'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='bank_branch',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='支店名・番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='bank_branch_number',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='口座種類・番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='city',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='市町村'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='city2',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='市町村'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='company_url',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='url'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='dob',
            field=models.DateTimeField(blank=True, max_length=50, null=True, verbose_name='生年月日'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='mansion',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='番地・部屋番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='mansion2',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='番地・部屋番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='phone',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='電話番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='post_number',
            field=models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='郵便番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='post_number2',
            field=models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='郵便番号'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='province',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='都道府県'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='province2',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='都道府県'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='stage_name',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='表示名'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='fullname',
            field=models.CharField(default=None, max_length=200, verbose_name='氏名'),
        ),
    ]
