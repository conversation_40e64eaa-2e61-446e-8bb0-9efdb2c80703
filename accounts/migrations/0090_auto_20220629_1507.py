# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-06-29 15:07
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0089_auto_20220617_1130'),
    ]

    operations = [
        migrations.CreateModel(
            name='FooterBlock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('copyright', models.CharField(default='', max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='ItemFooterMenu',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type_footer', models.CharField(choices=[('1', 'footer_1'), ('2', 'footer_2')], default='1', max_length=20)),
                ('title_jp', models.Char<PERSON>ield(default='メニュー', max_length=60)),
                ('title_en', models.CharField(default='メニュー', max_length=60)),
                ('url', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='URL')),
                ('order', models.IntegerField(default=0)),
                ('footer_block', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_footer', to='accounts.FooterBlock')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child', to='accounts.ItemFooterMenu')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='ItemSocial',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type_social_link', models.CharField(choices=[('home', 'official_site'), ('twitter', 'twitter_link'), ('fb', 'facebook_link'), ('insta', 'instagram_link'), ('youtube', 'youtube_link'), ('tiktok', 'tiktok_link'), ('note', 'note_link')], default='home', max_length=40)),
                ('url', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='URL')),
                ('order', models.IntegerField(default=0)),
                ('footer_block', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_social', to='accounts.FooterBlock')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child', to='accounts.ItemSocial')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.AlterField(
            model_name='itemblock',
            name='type_block',
            field=models.CharField(choices=[('1', 'profile_block'), ('2', 'header_block'), ('3', 'statement_block'), ('4', 'footer_block')], default='2', max_length=20),
        ),
        migrations.AddField(
            model_name='itemblock',
            name='footer_block',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='footer_block', to='accounts.FooterBlock'),
        ),
    ]
