# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-11-27 18:39
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0029_auto_20191119_1751'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlockList',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='会社名')),
                ('company_url', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='url')),
                ('reason', models.TextField(blank=True, null=True, verbose_name='理由')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
        ),
        migrations.AddField(
            model_name='creator',
            name='before_delivery',
            field=models.BooleanField(default=False, max_length=10, verbose_name='納期前日に通知を送る。'),
        ),
        migrations.AddField(
            model_name='creator',
            name='hours',
            field=models.CharField(blank=True, default='10:00', max_length=50, null=True, verbose_name='hours'),
        ),
        migrations.AddField(
            model_name='blocklist',
            name='creator',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_banned_creator', to='accounts.Creator'),
        ),
    ]
