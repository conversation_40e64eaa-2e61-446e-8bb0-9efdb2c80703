# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-09-16 11:55
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0064_auto_20210909_1110'),
    ]

    operations = [
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='', max_length=300)),
                ('group', models.CharField(default='', max_length=300)),
                ('order', models.IntegerField(default=0)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='schedulecreator',
            name='status',
            field=models.CharField(choices=[('1', 'off'), ('2', 'maybe'), ('3', 'normal')], default='2', max_length=20),
        ),
        migrations.AlterField(
            model_name='creator',
            name='show_profile',
            field=models.CharField(choices=[('public', '常に受けとる'), ('private', 'サービス内で公開'), ('project', 'プロジェクト内で限定公開')], default='public', max_length=20, verbose_name='プライバシーポリシー'),
        ),
        migrations.AddField(
            model_name='creator',
            name='skills',
            field=models.ManyToManyField(related_name='creator_skills', to='accounts.Skill'),
        ),
    ]
