# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2017-11-23 03:49
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0008_alter_user_username_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuthUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.Char<PERSON>ield(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON><PERSON>(max_length=30, unique=True, verbose_name='ユーザID')),
                ('last_name', models.Char<PERSON>ield(default=None, max_length=30, verbose_name='姓')),
                ('first_name', models.CharField(default=None, max_length=30, verbose_name='名')),
                ('email', models.EmailField(default=None, max_length=254, null=True, verbose_name='メールアドレス')),
                ('is_active', models.BooleanField(default=True, verbose_name='有効フラグ')),
                ('is_staff', models.BooleanField(default=False, verbose_name='管理サイトアクセス権限')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'ユーザ',
                'verbose_name_plural': 'ユーザ',
            },
        ),
    ]
