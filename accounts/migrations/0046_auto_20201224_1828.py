# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-12-24 18:28
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0045_auto_20201225_1300'),
    ]

    operations = [
        migrations.CreateModel(
            name='CreatorProfile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('banner', models.ImageField(blank=True, upload_to='images')),
                ('official_site', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='OFFICIAL SITE')),
                ('twitter_link', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='Twitter')),
                ('facebook_link', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='Facebook')),
                ('instagram_link', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='Instagram')),
                ('stage_name', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='芸名')),
                ('stage_name_en', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='芸名（英語表記）')),
                ('type', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='肩書き')),
                ('theme_quote', models.TextField(max_length=140, verbose_name='テーマ（140字以内）')),
                ('profile_quote', models.TextField(max_length=400, verbose_name='プロフィール（400字以内）')),
                ('x_banner', models.FloatField(default=0)),
                ('y_banner', models.FloatField(default=0)),
                ('width_banner', models.FloatField(default=0)),
                ('height_banner', models.FloatField(default=0)),
                ('avatar', models.ImageField(blank=True, upload_to='images')),
                ('x', models.FloatField(default=0)),
                ('y', models.FloatField(default=0)),
                ('width', models.FloatField(default=0)),
                ('height', models.FloatField(default=0)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('1', 'approve'), ('2', 'editing'), ('3', 'old'), ('4', 'reject')], default='2', max_length=20)),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='creator_profile', to='accounts.Creator')),
            ],
        ),
        migrations.AlterField(
            model_name='authuser',
            name='role',
            field=models.CharField(choices=[('master_admin', '管理者'), ('admin', 'ディレクター'), ('master_client', 'オーナー'), ('creator', 'アーティスト'), ('curator', 'キューレター')], default='master_client', max_length=20, verbose_name='権限'),
        ),
        migrations.AddField(
            model_name='creator',
            name='last_published_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='published_prole', to='accounts.CreatorProfile'),
        ),
        migrations.AddField(
            model_name='creator',
            name='last_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='last_profile', to='accounts.CreatorProfile'),
        ),
    ]
