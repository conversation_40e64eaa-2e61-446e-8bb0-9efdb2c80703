# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-05-31 13:03
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0087_auto_20220525_1557'),
    ]

    operations = [
        migrations.CreateModel(
            name='StatementBlock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_show_avatar', models.BooleanField(default=False, verbose_name='アバターを表示')),
                ('is_show_name', models.BooleanField(default=False, verbose_name='アーティスト名を表示')),
                ('is_show_title', models.BooleanField(default=False, verbose_name='タイトルを表示')),
                ('theme_jp', models.TextField(max_length=1000, verbose_name='アーティストステートメント')),
                ('theme_en', models.TextField(max_length=1000, verbose_name='アーティストステートメント')),
            ],
        ),
        migrations.AlterField(
            model_name='itemblock',
            name='type_block',
            field=models.CharField(choices=[('1', 'profile_block'), ('2', 'header_block'), ('3', 'statement_block')], default='2', max_length=20),
        ),
        migrations.AddField(
            model_name='itemblock',
            name='statement_block',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='statement_block', to='accounts.StatementBlock'),
        ),
    ]
