# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-07-22 13:15
from __future__ import unicode_literals

from django.db import migrations, models
import django_mysql.models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0035_auto_20200508_1221'),
    ]

    operations = [
        migrations.AddField(
            model_name='productuser',
            name='is_check_ip',
            field=models.BooleanField(default=False, verbose_name='ログイン前にIPを確認'),
        ),
        migrations.AddField(
            model_name='productuser',
            name='list_ip',
            field=django_mysql.models.ListTextField(models.CharField(max_length=15, null=True), blank=True, default=None, help_text="各ip範囲の後に '、'を使用", max_length=66, null=True, size=6, verbose_name='リストIP'),
        ),
    ]
