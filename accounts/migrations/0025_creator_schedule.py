# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-10-10 12:34
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_mysql.models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0024_auto_20191003_1555'),
    ]

    operations = [
        migrations.CreateModel(
            name='Creator',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dob', models.DateTimeField(blank=True, max_length=50, null=True, verbose_name='生年月日')),
                ('post_number', models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='郵便番号')),
                ('province', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='都道府県')),
                ('city', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='市区町村')),
                ('mansion', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='マンション名')),
                ('phone', models.IntegerField(blank=True, null=True, verbose_name='電話番号')),
                ('bank', models.IntegerField(blank=True, null=True, verbose_name='銀行名')),
                ('bank_branch', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='支店')),
                ('account_number', models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='口座番号')),
                ('checkout_setting', models.CharField(choices=[('five_day', '5日'), ('twenty_day', '20日')], default='five_day', max_length=20, verbose_name='自動振込')),
                ('next_checkout', models.BooleanField(default=False, max_length=10, verbose_name='30,000円未満は、次回にまとめる')),
                ('policy', models.TextField(verbose_name='案件受託についてのポリシー')),
                ('trading', models.CharField(choices=[('one', 1), ('two', 2), ('three', 3), ('four', 4), ('five', 5)], default='one', max_length=20, verbose_name='自動振込')),
                ('notification', models.CharField(choices=[('immediately', ' 常に受け取る'), ('one_day', '１日１回受け取る'), ('off', 'OFF')], default='immediately', max_length=20, verbose_name='お知らせ')),
                ('noti_dayoff', models.BooleanField(default=False, max_length=10, verbose_name='仕事不可日は、通知を受け取らない。')),
                ('banner', models.ImageField(blank=True, upload_to='images')),
                ('official_site', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='OFFICIAL SITE')),
                ('twitter_link', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='Twitter')),
                ('facebook_link', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='Facebook')),
                ('instagram_link', models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='Instagram')),
                ('stage_name', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='芸名')),
                ('stage_name_en', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='芸名（英語表記）')),
                ('role_creator', models.CharField(choices=[('composer', 'COMPOSER'), ('sound', 'SOUND DESIGNER'), ('voice', 'VOICE ACTOR'), ('narrator', 'NARRATOR'), ('vocalist', 'VOCALIST')], default='composer', max_length=20, verbose_name='自動振込')),
                ('theme_quote', models.TextField(max_length=140, verbose_name='テーマ（140字以内）')),
                ('profile_quote', models.TextField(max_length=400, verbose_name='プロフィール（400字以内）')),
                ('x_banner', models.FloatField(default=0)),
                ('y_banner', models.FloatField(default=0)),
                ('width_banner', models.FloatField(default=0)),
                ('height_banner', models.FloatField(default=0)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_creator', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Schedule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('list_day', django_mysql.models.ListTextField(models.CharField(max_length=2, null=True), blank=True, default=None, max_length=100, null=True, size=None)),
                ('month', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('year', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedule_creator', to='accounts.Creator')),
            ],
        ),
    ]
