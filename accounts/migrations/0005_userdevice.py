# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-01-24 15:20
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_mysql.models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_authuser_avatar'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserDevice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('address', models.CharField(blank=True, default='', max_length=100, null=True)),
                ('list_ip', django_mysql.models.ListTextField(models.CharField(max_length=15, null=True), blank=True, default=None, help_text="各ip範囲の後に '、'を使用", max_length=66, null=True, size=6, verbose_name='リストIP')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_log', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
