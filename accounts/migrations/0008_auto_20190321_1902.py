# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-03-21 19:02
from __future__ import unicode_literals

from django.db import migrations


def create_through_relations(apps, schema_editor):
    User = apps.get_model('accounts', 'AuthUser')
    ProductUser = apps.get_model('accounts', 'ProductUser')
    for user in User.objects.all():
        is_rating = True
        is_favorite = False
        if user.is_staff:
            is_rating = False
        for product in user.products.all():
            ProductUser(
                user=user,
                product=product,
                is_rating=is_rating,
                is_favorite=is_favorite,
            ).save()
    User.objects.filter(is_staff=True).update(role='admin')
    User.objects.filter(is_staff=False).update(role='client')


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0007_auto_20190321_1902'),
    ]

    operations = [
      migrations.RunPython(create_through_relations, reverse_code=migrations.RunPython.noop),
    ]
