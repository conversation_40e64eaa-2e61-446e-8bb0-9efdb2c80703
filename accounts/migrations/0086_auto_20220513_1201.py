# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-05-13 12:01
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0085_auto_20220422_1317'),
    ]

    operations = [
        migrations.CreateModel(
            name='CreatorItemBlock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator_profile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='blocks', to='accounts.CreatorProfile')),
            ],
        ),
        migrations.CreateModel(
            name='ItemBlock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type_block', models.Char<PERSON>ield(choices=[('1', 'profile_block'), ('2', 'header_block')], default='2', max_length=20)),
                ('order', models.IntegerField(default=0)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='ProfileBlock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('section_name_jp', models.CharField(default='', max_length=15)),
                ('section_name_en', models.CharField(default='', max_length=15)),
                ('is_link_menu', models.BooleanField(default=False, verbose_name='トップメニューにリンク')),
                ('content_jp', models.TextField(max_length=1000, verbose_name='テキスト')),
                ('content_en', models.TextField(max_length=1000, verbose_name='Text')),
            ],
        ),
        migrations.AddField(
            model_name='itemblock',
            name='profile_block',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='profile_block', to='accounts.ProfileBlock'),
        ),
        migrations.AddField(
            model_name='creatoritemblock',
            name='item_block',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.ItemBlock'),
        ),
    ]
