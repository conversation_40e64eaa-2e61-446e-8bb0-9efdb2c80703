# Generated by Django 4.2.16 on 2024-12-05 13:01

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0095_authuser_blocking_users_authuser_following_users_and_more'),
    ]

    operations = [
        migrations.DeleteModel(
            name='TestProfile',
        ),
        migrations.RemoveField(
            model_name='creator',
            name='city',
        ),
        migrations.RemoveField(
            model_name='creator',
            name='dob',
        ),
        migrations.RemoveField(
            model_name='creator',
            name='mansion',
        ),
        migrations.RemoveField(
            model_name='creator',
            name='phone',
        ),
        migrations.RemoveField(
            model_name='creator',
            name='post_number',
        ),
        migrations.RemoveField(
            model_name='creator',
            name='province',
        ),
        migrations.AlterField(
            model_name='authuser',
            name='account_number',
            field=models.CharField(blank=True, default='', max_length=10, null=True, verbose_name='account_number（口座番号）'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='authuser',
            name='affiliation_en',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='bank_branch_number',
            field=models.CharField(blank=True, default='', max_length=3, null=True, verbose_name='bank_branch_number（口座種類・番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='blocking_users',
            field=models.ManyToManyField(blank=True, related_name='blocked', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='city',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='city（市区町村・番地）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='city2',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='city2（市区町村・番地）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='enterprise',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='following_users',
            field=models.ManyToManyField(blank=True, related_name='followers', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='mansion',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='mansion（建物名・部屋番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='post_number',
            field=models.CharField(blank=True, default='', max_length=10, null=True, verbose_name='post_number（郵便番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='post_number2',
            field=models.CharField(blank=True, default='', max_length=10, null=True, verbose_name='post_number2（郵便番号）'),
        ),
    ]
