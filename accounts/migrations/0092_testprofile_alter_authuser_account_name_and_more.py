# Generated by Django 4.2.16 on 2024-11-29 12:55

from django.db import migrations, models
import django.db.models.deletion
import django_mysql.models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0256_alter_albumvariation_id_alter_albumversion_id_and_more'),
        ('accounts', '0091_authuser_invoice_register_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='TestProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.Char<PERSON><PERSON>(max_length=20)),
                ('medium_avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('stage_name', models.CharField(blank=True, max_length=100, null=True)),
                ('display_name', models.CharField(blank=True, max_length=100, null=True)),
                ('fullname', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('position', models.Char<PERSON><PERSON>(max_length=100)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('enterprise', models.CharField(max_length=100)),
                ('company_url', models.URLField()),
            ],
        ),
        migrations.AlterField(
            model_name='authuser',
            name='account_name',
            field=models.CharField(blank=True, default='', max_length=255, null=True, verbose_name='account_name（振込名義）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='account_number',
            field=models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='account_number（口座番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='account_type',
            field=models.CharField(choices=[('normal', '普通'), ('temp', '当座'), ('reverse_tax', '納税準備預金'), ('saving', '貯蓄'), ('other', 'その他')], default='normal', max_length=50, verbose_name='account_type（口座種類）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='bank',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='bank（銀行名）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='bank_branch',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='bank_branch（支店名・番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='bank_branch_number',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='bank_branch_number（口座種類・番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='city',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='city（市町村）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='city2',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='city2（市町村）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='company_url',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='company_url'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='display_name',
            field=models.CharField(blank=True, default=None, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='dob',
            field=models.DateTimeField(blank=True, max_length=50, null=True, verbose_name='dob（生年月日）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='email',
            field=models.EmailField(default=None, max_length=70, null=True, unique=True, verbose_name='email（メールアドレス）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='file_status',
            field=models.CharField(choices=[('0', '未提出'), ('1', '審査中'), ('2', '本人確認済み'), ('3', '要更新')], default='0', max_length=20),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='first_name',
            field=models.CharField(default='guest', max_length=30, verbose_name='first_name（名）[削除希望]'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='fullname',
            field=models.CharField(default=None, max_length=200, verbose_name='fullname（氏名）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='invoice_register_number',
            field=models.CharField(blank=True, max_length=30, null=True, verbose_name='invoice_register_number（登録番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='is_active（有効フラグ）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='is_check_ip',
            field=models.BooleanField(default=False, verbose_name='is_check_ip（ログイン前にIPを確認）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='is_staff',
            field=models.BooleanField(default=False, verbose_name='is_staff（管理サイトアクセス権限）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='last_name',
            field=models.CharField(default='user', max_length=30, verbose_name='last_name（姓）[削除希望]'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='mansion',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='mansion（番地・部屋番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='mansion2',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='mansion2（番地・部屋番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='noti_hours',
            field=models.CharField(blank=True, default='10:00', max_length=50, null=True, verbose_name='noti_hours（通知時間）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='phone',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='phone（電話番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='post_number',
            field=models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='post_number（郵便番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='post_number2',
            field=models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='post_number2（郵便番号）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='products',
            field=models.ManyToManyField(blank=True, through='accounts.ProductUser', to='app.product', verbose_name='products（プロジェクト）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='province',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='province（都道府県）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='province2',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='province2（都道府県）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='role',
            field=models.CharField(choices=[('master_admin', '①管理者'), ('admin', '③クリエイター'), ('master_client', '④クライアント'), ('curator', '②マネージャー')], default='master_client', max_length=20, verbose_name='role'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='setting_mail',
            field=models.CharField(choices=[('now', '今すぐ（15分ごとに通知）'), ('on', '1日1回まとめて通知'), ('off', 'オフ（受け取らない）')], default='on', max_length=20, verbose_name='setting_mail（お知らせ）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='stage_name',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='stage_name_en',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='stripe_customer',
            field=models.CharField(default=None, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='type',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='type（肩書き）[削除希望]'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='username',
            field=models.CharField(max_length=70, unique=True, verbose_name='username（ユーザID）[削除希望]'),
        ),
        migrations.AlterField(
            model_name='blocklist',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='account_number',
            field=models.CharField(blank=True, default='', max_length=50, null=True, verbose_name='account_number（口座番号）[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='account_type',
            field=models.CharField(choices=[('normal', '普通'), ('temp', '当座'), ('reverse_tax', '納税準備預金'), ('saving', '貯蓄'), ('other', 'その他')], default='normal', max_length=50, verbose_name='account_type（口座種類）[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='bank',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='銀行名[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='bank_branch',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='bank_branch（支店）[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='bank_branch_number',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='bank_branch_number（支店番号）[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='before_delivery',
            field=models.BooleanField(default=False, max_length=10, verbose_name='before_delivery（納期前日に通知を送る。）[復活可能性あり]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='checkout_setting',
            field=models.CharField(choices=[('five_day', '5日'), ('twenty_day', '20日')], default='five_day', max_length=20, verbose_name='checkout_setting（自動振込）[復活可能性あり]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='city',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='市区町村[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='hours',
            field=models.CharField(blank=True, default='10:00', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='creator',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='mansion',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='マンション名[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='next_checkout',
            field=models.BooleanField(default=False, max_length=10, verbose_name='next_checkout（30,000円未満は、次回にまとめる）[復活可能性あり]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='noti_dayoff',
            field=models.BooleanField(default=False, max_length=10, verbose_name='noti_dayoff（仕事不可日は、通知を受け取らない。）'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='notification',
            field=models.CharField(choices=[('immediately', 'すぐに受けとる'), ('one_day', '1日１回まとめて受けとる'), ('off', 'OFF')], default='immediately', max_length=20, verbose_name='notification（お知らせ）[AuthUserにもある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='phone',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='電話番号[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='policy',
            field=models.TextField(blank=True, null=True, verbose_name='policy（クライテリア）'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='profile_quote',
            field=models.TextField(verbose_name='profile_quote（プロフィール）'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='province',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='都道府県[不要。AuthUserにある]'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='role_creator',
            field=models.CharField(choices=[('composer', 'コンポーザー'), ('sound', 'サウンドデザイナー'), ('voice', '声優・ナレーター'), ('player', '演奏家'), ('vocalist', 'ボーカリスト'), ('audio engineer', 'オーディオエンジニア')], default='composer', max_length=20),
        ),
        migrations.AlterField(
            model_name='creator',
            name='show_profile',
            field=models.CharField(choices=[('public', 'オープン'), ('private', 'メンバーズオンリー'), ('project', 'プロジェクトオンリー')], default='project', max_length=20, verbose_name='show_profile'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='stage_name',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='creator',
            name='stage_name_en',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='creator',
            name='theme_quote',
            field=models.TextField(max_length=140, verbose_name='theme_quote（テーマ）'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='trading',
            field=models.CharField(choices=[('one', 1), ('two', 2), ('three', 3), ('four', 4), ('five', 5)], default='three', max_length=20, verbose_name='trading'),
        ),
        migrations.AlterField(
            model_name='creator',
            name='type',
            field=models.CharField(blank=True, default='', max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='creatoritemblock',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creatorlist',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creatorlistcreator',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creatorprofile',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creatorprofile',
            name='profile_quote',
            field=models.TextField(max_length=400, verbose_name='profile_quote（400字以内）'),
        ),
        migrations.AlterField(
            model_name='creatorprofile',
            name='stage_name',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='creatorprofile',
            name='stage_name_en',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='creatorprofile',
            name='theme_quote',
            field=models.TextField(max_length=140, verbose_name='theme_quote（140字以内）'),
        ),
        migrations.AlterField(
            model_name='creatorprofile',
            name='type',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='type（肩書き）[AuthUserのpositionとダブってる]'),
        ),
        migrations.AlterField(
            model_name='footerblock',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='headerblock',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='itemblock',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='itemfootermenu',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='itemsocial',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='productuser',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='productuser',
            name='is_check_ip',
            field=models.BooleanField(default=False, verbose_name='is_check_ip（IP制限の有無）'),
        ),
        migrations.AlterField(
            model_name='productuser',
            name='is_owner',
            field=models.CharField(choices=[('0', '無'), ('1', '有')], default='0', max_length=20, verbose_name='is_owner[未使用]'),
        ),
        migrations.AlterField(
            model_name='productuser',
            name='list_ip',
            field=django_mysql.models.ListTextField(models.CharField(max_length=15, null=True), blank=True, default=None, help_text="各ip範囲の後に '、'を使用", max_length=66, null=True, size=6, verbose_name='list_ip'),
        ),
        migrations.AlterField(
            model_name='productuser',
            name='notification',
            field=models.CharField(choices=[('on', 'ON'), ('off', 'OFF')], default='on', max_length=20, verbose_name='notification（プロジェクトごとのお知らせ）[復活可能性あり]'),
        ),
        migrations.AlterField(
            model_name='productuser',
            name='position',
            field=models.CharField(choices=[('master_admin', '管理者'), ('admin', '⑧ディレクター'), ('owner', '⑤プロジェクトオーナー'), ('creator', '⑨スタッフ'), ('client', '⑥レビュアー'), ('producer', '⑦プロデューサー')], default='client', max_length=20, verbose_name='position（権限）'),
        ),
        migrations.AlterField(
            model_name='productuser',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.product', verbose_name='product（プロジェクト）'),
        ),
        migrations.AlterField(
            model_name='profileblock',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='schedulecreator',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='skill',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='statementblock',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='userdevice',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
    ]
