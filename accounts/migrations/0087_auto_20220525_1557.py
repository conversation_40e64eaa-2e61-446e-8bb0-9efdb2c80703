# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-05-25 15:57
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0086_auto_20220513_1201'),
    ]

    operations = [
        migrations.CreateModel(
            name='HeaderBlock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type_header', models.CharField(choices=[('1', 'full_screen'), ('2', 'banner')], default='1', max_length=20)),
                ('logo', models.ImageField(blank=True, upload_to='images')),
                ('key_visual_pc', models.FileField(blank=True, null=True, upload_to='file')),
                ('key_visual_sp', models.FileField(blank=True, null=True, upload_to='file')),
                ('banner', models.FileField(blank=True, null=True, upload_to='file')),
                ('catchphrase_jp_1', models.CharField(default='', max_length=15)),
                ('catchphrase_jp_2', models.CharField(default='', max_length=15)),
                ('catchphrase_jp_3', models.CharField(default='', max_length=15)),
                ('catchphrase_en_1', models.CharField(default='', max_length=15)),
                ('catchphrase_en_2', models.CharField(default='', max_length=15)),
                ('catchphrase_en_3', models.CharField(default='', max_length=15)),
                ('display_tag_line_1', models.BooleanField(default=False, verbose_name='タグライン１を表示')),
                ('display_tag_line_2', models.BooleanField(default=False, verbose_name='タグライン２を表示')),
                ('artist_name_jp', models.CharField(default='', max_length=60)),
                ('artist_name_en', models.CharField(default='', max_length=60)),
                ('title_jp', models.CharField(default='', max_length=60)),
                ('title_en', models.CharField(default='', max_length=60)),
            ],
        ),
        migrations.AddField(
            model_name='itemblock',
            name='header_block',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='header_block', to='accounts.HeaderBlock'),
        ),
    ]
