# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-03-16 18:07
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0053_creatorprofile_youtube_link'),
    ]

    operations = [
        migrations.CreateModel(
            name='CreatorList',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, default='', max_length=100, null=True)),
                ('description', models.TextField(null=True)),
                ('order', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='CreatorListCreator',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.Creator')),
                ('creator_list', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.CreatorList')),
            ],
        ),
        migrations.AddField(
            model_name='creatorlist',
            name='creator_list_creator',
            field=models.ManyToManyField(through='accounts.CreatorListCreator', to='accounts.Creator'),
        ),
    ]
