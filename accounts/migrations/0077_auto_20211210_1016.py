# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-12-10 10:16
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0076_creator_is_invited'),
    ]

    operations = [
        migrations.CreateModel(
            name='CreatorFile',
            fields=[
                ('file_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(blank=True, max_length=1024, upload_to='file')),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AddField(
            model_name='creator',
            name='creator_file_status',
            field=models.CharField(choices=[('0', 'まだファイルをアップロードしない'), ('1', '確認中'), ('2', '承認済'), ('3', '拒否済')], default='0', max_length=20),
        ),
        migrations.AddField(
            model_name='creator',
            name='curator_file',
            field=models.FileField(blank=True, null=True, upload_to='file'),
        ),
        migrations.AddField(
            model_name='creator',
            name='curator_file_name',
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AddField(
            model_name='creatorfile',
            name='creator',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idetity_files', to='accounts.Creator'),
        ),
        migrations.AddField(
            model_name='creatorfile',
            name='owner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='idetity_files', to=settings.AUTH_USER_MODEL),
        ),
    ]
