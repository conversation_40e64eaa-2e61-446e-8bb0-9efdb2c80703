# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-03-21 19:02
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0026_product_information'),
        ('accounts', '0006_auto_20190130_1553'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_rating', models.BooleanField(default=False)),
                ('is_favorite', models.BooleanField(default=False)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.Product')),
            ],
        ),
        migrations.AddField(
            model_name='authuser',
            name='role',
            field=models.Char<PERSON>ield(choices=[('admin', '管理者'), ('master_client', 'マスタ・クライアント'), ('client', 'クライアント')], default='client', max_length=20, verbose_name='権限'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='products',
            field=models.ManyToManyField(to='app.Product', verbose_name='プロジェクト'),
        ),
        migrations.AddField(
            model_name='productuser',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
