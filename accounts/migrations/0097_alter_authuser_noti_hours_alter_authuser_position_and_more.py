# Generated by Django 4.2.16 on 2024-12-10 12:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0096_delete_testprofile_remove_creator_city_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='authuser',
            name='noti_hours',
            field=models.CharField(blank=True, default='10:00', max_length=50, null=True, verbose_name='noti_hours（配信時間）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='position',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='setting_mail',
            field=models.CharField(choices=[('now', '今すぐ（15分ごとに通知）'), ('on', '1日1回まとめて通知'), ('off', 'オフ（受け取らない）')], default='on', max_length=20, verbose_name='setting_mail（配信タイミング）'),
        ),
    ]
