# Generated by Django 4.2.16 on 2024-12-02 12:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0092_testprofile_alter_authuser_account_name_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='authuser',
            name='affiliation_en',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='enterprise',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='first_name',
            field=models.Char<PERSON>ield(default='guest', max_length=30, verbose_name='first_name（名）'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='last_name',
            field=models.Char<PERSON>ield(default='user', max_length=30, verbose_name='last_name（姓）'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='authuser',
            name='position',
            field=models.Char<PERSON><PERSON>(max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='authuser',
            name='type',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='username',
            field=models.CharField(max_length=70, unique=True, verbose_name='username（ユーザID）'),
        ),
    ]
