# Generated by Django 4.2.16 on 2024-12-02 18:21

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0094_userrelationship'),
    ]

    operations = [
        migrations.AddField(
            model_name='authuser',
            name='blocking_users',
            field=models.ManyToManyField(related_name='blocked', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='authuser',
            name='following_users',
            field=models.ManyToManyField(related_name='followers', to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='UserRelationship',
        ),
    ]
