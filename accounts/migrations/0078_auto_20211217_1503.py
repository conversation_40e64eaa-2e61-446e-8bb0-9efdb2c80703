# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-12-17 15:03
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0077_auto_20211210_1016'),
    ]

    operations = [
        migrations.AddField(
            model_name='productuser',
            name='current_budget',
            field=models.FloatField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(***************)]),
        ),
        migrations.AddField(
            model_name='productuser',
            name='is_super_producer',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productuser',
            name='usage_fee',
            field=models.FloatField(default=17.5),
        ),
    ]
