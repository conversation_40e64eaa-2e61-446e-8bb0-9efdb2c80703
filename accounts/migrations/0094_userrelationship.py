# Generated by Django 4.2.16 on 2024-12-02 17:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0093_authuser_affiliation_en_alter_authuser_enterprise_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserRelationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('blocked', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blocked', related_query_name='blocked', to=settings.AUTH_USER_MODEL)),
                ('blocking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blocking', related_query_name='blocking', to=settings.AUTH_USER_MODEL)),
                ('follower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='follower', related_query_name='follower', to=settings.AUTH_USER_MODEL)),
                ('following', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='following', related_query_name='following', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
