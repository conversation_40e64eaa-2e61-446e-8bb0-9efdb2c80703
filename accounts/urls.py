# 01EG89H6SS2141VNGDDEHBMV4Q
from django.urls import path, re_path
from django.contrib.auth import views
from django.urls import reverse_lazy
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import PasswordChangeView, PasswordChangeDoneView, PasswordResetView, PasswordResetDoneView, PasswordResetConfirmView, PasswordResetCompleteView
# from django.contrib.auth.views import password_change, password_change_done, password_reset, password_reset_done
# from django.contrib.auth.views import password_reset_confirm, password_reset_complete

from .views import Index, List, Regist, Update, UpdateInfoView, UpdateAdmin, Delete, Reset, LoginUserView, Signup, \
    password_signup_done, password_signup_complete, login_caution, CreatorInfo, CreatorSetting, CreatorProfile, \
    CreatorView, CreatorSocialMedia, Join<PERSON><PERSON>, Join<PERSON>sComplete, check_email_register, validate_block_list, \
    EmailTemplateWeb, invite_pwd_change, CreatorProfileUpdateAdmin, Setting, ResetPassword, \
    UpdatePassword, activate_account, check_user_login, PaymentInfo, stripe_config, card_payment, \
    remove_card, update_creator_profile, approve_field_creator_profile, accounts_check_valid_slug, find_creator, \
    add_composer_to_list, update_list_creator_index, update_creator_index, delete_list_creator, update_list_creators, \
    update_password, remove_current_user, CreatorInfoView, uploadFile, get_task_deadline, get_schedule_list, \
    CuratorSetting, curator_search_artist, check_email_exist_invite_artist, InviteArtist, curator_invite_pwd_change, \
    curator_upload_file, curator_approve_reject_file, \
    curator_file_download_link, artist_create_files, get_artist_files, edit_evaluation_point_artist, \
    get_load_more_artist, bookmark_sale_content, handle_sns_signup_redirect, logout_handler

from app.util import login_staff_required, login_curator_required, login_manager_account_required

app_name = 'accounts'

urlpatterns = [
    re_path(r'^login/?$', LoginUserView.as_view(), name='accounts_login'),
    re_path(r'^logout$', login_required(logout_handler), name='accounts_logout'),
    re_path(r'^$', login_required(Index.as_view()), name='accounts_index'),
    re_path(r'^list$', login_manager_account_required(List.as_view()), name='accounts_list'),
    re_path(r'^regist$', login_manager_account_required(Regist.as_view()), name='accounts_regist'),
    re_path(r'^signup$', Signup.as_view(), name='accounts_signup'),
    re_path(r'^login_caution', login_caution, {'template_name': 'accounts/login_caution.html'}, name='login_caution'),
    re_path(r'^update/(?P<pk>[0-9]+)/$', login_required(Update.as_view()), name='accounts_update'),
    re_path(r'^update_info/(?P<pk>[0-9]+)/$', login_required(UpdateInfoView.as_view()), name='accounts_update_info'),
    re_path(r'^update_admin/(?P<pk>[0-9]+)/$', login_staff_required(UpdateAdmin.as_view()), name='accounts_update_admin'),
    re_path(r'^creator/(?P<pk>[0-9]+)$', CreatorView.as_view(), name='accounts_creator'),
    re_path(r'^creator_info/(?P<pk>[0-9]+)/$', login_required(CreatorInfo.as_view()), name='accounts_creator_info'),
    re_path(r'^creator_account/(?P<pk>[0-9]+)/$', login_required(CreatorInfoView.as_view()), name='creator_info'),
    re_path(r'^uploadFile', login_required(uploadFile), name='uploadFile'),
    re_path(r'^accounts_check_valid_slug', login_required(accounts_check_valid_slug),
        name='accounts_check_valid_slug'),
    re_path(r'^creator_setting/(?P<pk>[0-9]+)/$', login_required(CreatorSetting.as_view()),
        name='accounts_creator_setting'),
    re_path(r'^setting/(?P<pk>[0-9]+)/$', login_required(Setting.as_view()), name='accounts_setting'),
    re_path(r'^creator_profile/(?P<pk>[0-9]+)/$', login_required(CreatorProfile.as_view()), name='accounts_creator_profile'),
    re_path(r'^creator_profile/update_admin/(?P<pk>[0-9]+)/$', login_required(CreatorProfileUpdateAdmin.as_view()),
        name='accounts_creator_profile_update_admin'),
    re_path(r'^creator_social_media/(?P<pk>[0-9]+)/$', login_required(CreatorSocialMedia.as_view()), name='accounts_creator_social_media'),
    re_path(r'^delete/(?P<pk>[0-9]+)/$', login_required(Delete.as_view()), name='accounts_delete'),
    re_path(r'^join_us$', JoinUs.as_view(), name='join_us'),
    re_path(r'^join_us_successfully$', JoinUsComplete.as_view(), name='join_us_completed'),
    re_path(r'^check_email_register$', check_email_register, name='check_email_register'),
    re_path(r'^ajax/block_list/validate$', validate_block_list, name='validate_block_list'),
    re_path(r'^email_join_us_web/(?P<pk>[0-9]+)/$', EmailTemplateWeb.as_view(), name='email_join_us_web'),
    re_path(r'^invite/password/change/(?P<pidb64>[0-9A-Za-z_\-]+)/(?P<uidb64>[0-9A-Za-z_\-]+)/(?P<token>.+)/$', invite_pwd_change, name='invite_pwd_change'),
    re_path(r'^check_user_login/(?P<pidb64>[0-9A-Za-z_\-]+)/(?P<uidb64>[0-9A-Za-z_\-]+)/$', login_required(check_user_login), {}, name='check_user_login'),
    re_path(r'^payment_info/(?P<pk>[0-9]+)/$', login_required(PaymentInfo.as_view()), name='payment'),
    re_path(r'^payment/config$', login_required(stripe_config), name='payment_config'),
    re_path(r'^payment/addcard$', login_required(card_payment), name='payment_add_card'),
    re_path(r'^payment/removecard$', login_required(remove_card), name='payment_remove_card'),
    re_path(r'^update_creator_profile$', update_creator_profile, name='update_creator_profile'),
    re_path(r'^approve_field_creator_profile$', approve_field_creator_profile, name='approve_field_creator_profile'),
    re_path(r'^find_creators$', login_curator_required(find_creator), name='find_creator'),
    re_path(r'^creators/add_composer_to_list', login_curator_required(add_composer_to_list), name="add_composer_to_list"),
    re_path(r'^creators/update_list_creator_index', login_curator_required(update_list_creator_index),
        name="update_list_creator_index"),
    re_path(r'^creators/update_creator_index', login_curator_required(update_creator_index), name="update_creator_index"),
    re_path(r'^creators/delete_list_creator', login_curator_required(delete_list_creator), name="delete_list_creator"),
    re_path(r'^creators/update_list_creators', login_curator_required(update_list_creators), name="update_list_creators"),
    # パスワード変更
    re_path(r'^update/password/$',
        PasswordChangeView.as_view(),
        {
            'post_change_redirect': reverse_lazy('accounts:pwd_change_done'),
            'template_name': 'accounts/update_password.html',
        },
        name='pwd_change'
        ),
    # パスワード変更完了
    re_path(r'^update/password/done/$',
        PasswordChangeDoneView.as_view(),
        {
            'template_name': 'accounts/update_password_done.html',
        },
        name='pwd_change_done'
        ),
    # リセット用の情報入力
    re_path(r'^password/reset/$', Reset.as_view(), name='pwd_reset'),

    # リセット用の情報入力完了
    re_path(r'^password/reset/done/$',
        PasswordResetDoneView.as_view(),
        {
            'template_name': 'accounts/password_reset_done.html',
        },
        name='pwd_reset_done'
        ),
    # send email signup success
    re_path(r'^password/signup/done/$',
        password_signup_done,
        {
            'template_name': 'accounts/password_signup_done.html',
        },
        name='password_signup_done'
        ),
    # 新規パスワードの入力
    re_path(r'^password/reset/confirm/(?P<uidb64>[0-9A-Za-z_\-]+)/(?P<token>.+)/$',
        UpdatePassword.as_view(),
        name='pwd_reset_confirm'
        ),

    re_path(r'^password/reset/(?P<uidb64>[0-9A-Za-z_\-]+)/(?P<token>.+)/$',
        ResetPassword.as_view(),
        name='pwd_reset'
        ),
    # パスワードリセット完了
    re_path(r'^password/reset/complete/$',
        PasswordResetCompleteView.as_view(),
        {
            'template_name': 'accounts/password_reset_complete.html',
        },
        name='pwd_reset_complete'
        ),
    # confirm signup password
    re_path(r'^password/signup/confirm/(?P<uidb64>[0-9A-Za-z_\-]+)/(?P<token>.+)/$',
        UpdatePassword.as_view(),
        name='pwd_signup_confirm'
        ),
    re_path(r'^password/signup/complete/$',
        password_signup_complete,
        {
            'template_name': 'accounts/password_signup_complete.html',
        },
        name='pwd_signup_complete'
        ),
    re_path(r'^signup/activate/(?P<uidb64>[0-9A-Za-z_\-]+)/(?P<token>.+)/$', activate_account,
        name='signup_activate'
        ),
    re_path(r'^update_password$', login_required(update_password), name='update_password'),
    re_path(r'^remove_current_user$', login_required(remove_current_user), name='remove_current_user'),
    re_path(r'^get_task_deadline$', login_required(get_task_deadline), name='get_task_deadline'),
    re_path(r'^get_schedule_list$', login_required(get_schedule_list), name='get_schedule_list'),
    re_path(r'^curator/curator_setting$', CuratorSetting.as_view(), name='curator_setting'),
    re_path(r'^curator/get_load_more_artist$', login_required(get_load_more_artist), name='get_load_more_artist'),
    re_path(r'^curator/curator_search_artist$', login_required(curator_search_artist),
        name='curator_search_artist'),
    re_path(r'^curator/check_email_exist_invite_artist$', login_required(check_email_exist_invite_artist),
        name='check_email_exist_invite_artist'),
    re_path(r'^curator/invite_artist', InviteArtist.as_view(), name='invite_artist'),

    re_path(r'^curator/invite/password/change/(?P<uidb64>[0-9A-Za-z_\-]+)/(?P<token>.+)/$', curator_invite_pwd_change,
        name='curator_invite_pwd_change'),
    re_path(r'^curator/edit_evaluation_point_artist$', login_required(edit_evaluation_point_artist), name='edit_evaluation_point_artist'),
    re_path(r'^curator/curator_upload_file$', login_required(curator_upload_file), name='curator_upload_file'),
    re_path(r'^curator/curator_approve_reject_file$', login_required(curator_approve_reject_file),
        name='curator_approve_reject_file'),
    re_path(r'^curator/curator_file_download_link$', login_required(curator_file_download_link),
        name='curator_file_download_link'),
    re_path(r'^curator/artist_create_files$', login_required(artist_create_files),
        name='artist_create_files'),
    re_path(r'^curator/get_artist_files$', login_required(get_artist_files),
        name='get_artist_files'),
    re_path(r'^collection/bookmark_sale_content$', login_required(bookmark_sale_content),
        name='bookmark_sale_content'),
    re_path(r'^handle_sns_signup_redirect$', login_required(handle_sns_signup_redirect),
        name='handle_sns_signup_redirect'),
]
