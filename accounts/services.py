# 01EG89H6SS2141VNGDDEHBMV4Q
import re
from itertools import groupby
from datetime import datetime

from django.db.models.expressions import F, Case, When
from app.models import Product, Scene, PreviewVideo, PreviewScene, \
    SceneComment, SceneTitle, OfferCreator, SaleContentListWork, SaleContentSelection, OfferProduct, SceneTitleBookmark
from accounts.models import <PERSON>th<PERSON><PERSON>, Block<PERSON>ist, Creator, ProductUser, ScheduleCreator, CreatorProfile, Skill, \
    ItemBlock, HeaderBlock, CreatorItemBlock, ProfileBlock, StatementBlock, FooterBlock
from django.db.models import Q
from app.util import to_hour, parser_date, get_end_date_of_month, get_start_date_of_next_month, get_start_time_today
from app.util import get_start_date_of_month, get_end_date_of_next_month

from voice.constants import CONST_REGEX_EMAIL
ARTIST_PER_PAGE = 15

LIST_STATEMENT_BLOCK = ['is_show_avatar', 'is_show_name', 'is_show_title', 'theme_jp', 'theme_en']


def update_product_user_new_video(products=False, users=False):
    if not products:
        products = Product.objects.all()

    for product in products:
        scenes = Scene.objects.filter(Q(product=product), ~Q(title=None), ~Q(product_scene=None))
        if not users:
            users = AuthUser.objects.filter(productuser__product=product)
        for user in users:
            is_new = False
            for scene in scenes:
                if scene.check_tag() != '2' and not scene.movie.name == '' and not PreviewVideo.objects.filter(scene=scene, owner_id=user.id).exists():
                    is_new = True
                    break
            if not is_new:
                comments = SceneComment.objects.exclude(owner_id=user.id).filter(scene__in=scenes,
                                                                                 owner_id__isnull=False,
                                                                                 scene__tag=1, scene__flag_tag=0)
                for comment in comments:
                    if not PreviewScene.objects.filter(comment=comment, owner_id=user.id).exists():
                        is_new = True
                        break

            pu = ProductUser.objects.filter(product=product, user=user).first()
            if pu and pu.has_new_video != is_new:
                pu.has_new_video = is_new
                pu.save()


def update_product_to_scene_title():
    scene_titles = SceneTitle.objects.all()
    count = 0
    count2 = 0
    for st in scene_titles:
        st_scene_list = st.scene_title.filter(product_scene_id__isnull=False)
        if st_scene_list.exists():
            scene1 = st_scene_list.first()
            scene2 = st_scene_list.last()
            if scene1.product_scene_id == scene2.product_scene_id:
                st.product_scene_id = scene1.product_scene_id
                st.save()
                print('ok')
            else:
                print(st.pk)
                print('case1')
                count += 1
        else:
            print(st.pk)
            print('case2')
            count2 += 1
    print(count)
    print(count2)


def get_task_creator_service(deadline, user):
    deadline = parser_date(deadline)
    offers = OfferCreator.objects.filter(status__in=['2', '3'],
                                deadline__year=deadline.year,
                                deadline__month=deadline.month,
                                deadline__day=deadline.day,
                                creator=user).order_by('deadline')
    result = {}
    for offer in offers:
        result.setdefault(to_hour(offer.deadline), []) \
                        .append(offer)
    return result.items()


def get_query_task_service(self, start_time, end_time):
    return OfferCreator.objects.filter(Q(status__in=['2', '3']),
                                       Q(deadline__range=[start_time, end_time]),
                                       (Q(creator=self) | Q(admin=self))).order_by('deadline')


def get_query_deadline_list_service(self):
    dt = datetime.now()
    today = get_start_time_today(dt)
    return get_query_task_service(self, today, get_end_date_of_next_month(dt))


def get_query_deadline_this_month_service(self):
    dt = datetime.now()
    today = get_start_time_today(dt)
    return get_query_task_service(self, today, get_end_date_of_month(dt))


def get_query_deadline_next_month_service(self):
    dt = datetime.now()
    return get_query_task_service(self, get_start_date_of_next_month(dt), get_end_date_of_next_month(dt))


def get_deadline_list_service(self):
    dt = datetime.now()
    result = {
        'thisMonth': [],
        'nextMonth': []
    }
    deadline_creator = get_query_deadline_list_service(self)
    for offer in deadline_creator:
        if offer.deadline.month == dt.month:
            result['thisMonth'].append(str(offer.deadline.day))
        else:
            result['nextMonth'].append(str(offer.deadline.day))

    return result


class UserDeleteableService():
    def __init__(self, user) -> None:
        self.user = user

    deleteable_dict = {
        AuthUser.MASTERADMIN: False,
        AuthUser.MASTERCLIENT: 'not_in_project_undone',
        AuthUser.CREATOR: 'not_in_project_undone_and_have_task_in_progress',
        AuthUser.CURATOR: True,
    }

    def process(self):
        deleteable = self.deleteable_dict[self.user.role]

        if type(deleteable) is bool:
            return deleteable
        else:
            return getattr(self, deleteable)()

    def not_have_task_in_progress(self):
        count_offers = OfferCreator.objects.filter(
            (Q(creator=self.user, admin__is_active=True) | Q(admin=self.user, creator__is_active=True)) &
            Q(status__in=OfferCreator.STATUS_IN_PROGRESS)
        ).count()
        return count_offers == 0

    def not_in_project_undone(self):
        return not self.user.productuser_set.\
                            filter(position__in=[ProductUser.REVIEWER, ProductUser.DIRECTOR, ProductUser.OWNER],
                                   is_invited=False, product__max_scene__gt=F('product__current_heart')).exists()

    def not_in_project_undone_and_have_task_in_progress(self):
        return self.not_in_project_undone() and self.not_have_task_in_progress()


def check_email_in_system(email):
    if not email:
        return False
    user_invited = AuthUser.objects.filter(email=email).exists()
    if user_invited:
        return False
    return True


def create_artist_invited(user_params, email):
    stage_name = user_params.get('stage_name', '')
    stage_name_en = user_params.get('stage_name_en', '')
    position = user_params.get('position', '')
    type = user_params.get('type', '')
    enterprise = user_params.get('enterprise', '')
    user_name = email
    if AuthUser.objects.filter(username=email).exists():
        count_user = AuthUser.objects.filter(email=email).count() + 1
        user_name = f'{email}+{count_user}'

    user_invited = AuthUser.objects.create(
        username=user_name, email=email, is_active=False,
        is_verify=False, role=AuthUser.CREATOR, stage_name=stage_name,
        stage_name_en=stage_name_en, position=position, type=type, enterprise=enterprise)

    creator = Creator.objects.create(user=user_invited)
    creator_profile = CreatorProfile.objects.create(creator=creator, status='1')
    creator.last_published_version = creator_profile
    creator.save()
    create_default_block(creator_profile)
    return user_invited


def create_default_block(creator_profile):
    # create header block
    header_block = HeaderBlock.objects.create(type_header=HeaderBlock.HEADER_BANNER)
    item_header_block = ItemBlock.objects.create(type_block=ItemBlock.HEADER_BLOCK, header_block=header_block)
    CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_header_block)
    # create profile block
    profile_block = ProfileBlock.objects.create()
    item_profile_block = ItemBlock.objects.create(type_block=ItemBlock.PROFILE_BLOCK, profile_block=profile_block)
    CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_profile_block)
    # create statement block
    statement_block = StatementBlock.objects.create(is_show_avatar=True, is_show_name=True, is_show_title=True)
    item_statement_block = ItemBlock.objects.create(type_block=ItemBlock.STATEMENT_BLOCK,
                                                    statement_block=statement_block)
    CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_statement_block)
    # create footer block
    footer_block = FooterBlock.objects.create()
    item_footer_block = ItemBlock.objects.create(type_block=ItemBlock.FOOTER_BLOCK, footer_block=footer_block)
    CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_footer_block)


def active_artist_invited(user):
    user.is_active = True
    user.is_verify = True
    user.save()
    creator = user.user_creator.first()
    if creator:
        creator.is_invited = False
        creator.save()
    return


def check_real_change_profile(creator, creator_profile):
    if creator_profile != creator.last_version:
        return
    list_key = ["official_site", "twitter_link", "facebook_link", "instagram_link", "youtube_link"]
    last_profile = creator.last_version
    if not last_profile:
        return
    public_profile = creator.last_published_version
    real_change = False

    last_profile_block = last_profile.get_block_profile()
    public_profile_block = public_profile.get_block_profile()
    for key in ['section_name_en', 'section_name_jp', 'is_link_menu', 'content_en', 'content_jp']:
        current_value = getattr(public_profile_block, key)
        new_value = getattr(last_profile_block, key)
        if current_value != new_value:
            real_change = True
            break

    last_statement_block = last_profile.get_block_statement()
    public_statement_block = public_profile.get_block_statement()
    for key in LIST_STATEMENT_BLOCK:
        current_value = getattr(public_statement_block, key)
        new_value = getattr(last_statement_block, key)
        if current_value != new_value:
            real_change = True
            break

    last_header_block = last_profile.get_block_header()
    public_header_block = public_profile.get_block_header()
    if last_header_block != public_header_block:
        real_change = True

    last_footer_block = last_profile.get_block_footer()
    public_footer_block = public_profile.get_block_footer()
    if last_footer_block != public_footer_block:
        real_change = True

    last_sale_contents = last_profile.content_profile.all()
    public_sale_contents = last_profile.content_profile.all()
    if last_sale_contents.count() != public_sale_contents.count():
        real_change = True
    else:
        for sale_content in public_sale_contents:
            if sale_content.last_version:
                real_change = True
                break
            audios = sale_content.last_published_version.album.all()
            for audio in audios:
                if audio.last_version:
                    real_change = True
                    break
    if real_change is False:
        if not creator_profile.banner and public_profile.banner:
            CreatorProfile.objects.filter(pk=last_profile.pk).update(banner=public_profile.banner,
                                                                     x_banner=public_profile.x_banner,
                                                                     y_banner=public_profile.y_banner,
                                                                     height_banner=public_profile.height_banner,
                                                                     width_banner=public_profile.width_banner,
                                                                     banner_resized=public_profile.banner_resized)
        CreatorProfile.objects.filter(pk=last_profile.pk).update(status='1')
        CreatorProfile.objects.filter(pk=public_profile.pk).update(status='3')
        Creator.objects.filter(pk=creator.pk).update(last_version=None)
        Creator.objects.filter(pk=creator.pk).update(last_published_version=last_profile)
        sale_contents = creator_profile.content_profile.all()
        for sale_content in sale_contents:
            update_sale_content_in_list(sale_content.parent, sale_content)
            sale_content.parent = None
            sale_last_version = sale_content.last_version
            if sale_last_version:
                sale_content.last_published_version = sale_last_version
                sale_content.last_version = None
            sale_content.save()
    return


def update_sale_content_in_list(sale_content, new_sale_content):
    SaleContentListWork.objects.filter(sale_content=sale_content).update(sale_content=new_sale_content)
    SaleContentSelection.objects.filter(sale_content=sale_content).update(sale_content=new_sale_content)
    OfferProduct.objects.filter(sale=sale_content).update(sale=new_sale_content)
    SceneTitleBookmark.objects.filter(sale=sale_content).update(sale=new_sale_content)


def get_all_artist(offset=0, keyword='', skill_ids=[]):
    creators = Creator.objects.filter(Q(user__is_active=True))
    if keyword or skill_ids:
        creators = creators.filter(Q(user__stage_name__icontains=keyword) |
                                   Q(user__stage_name_en__icontains=keyword) |
                                   Q(user__fullname__icontains=keyword))
        if skill_ids:
            skills = Skill.objects.filter(pk__in=skill_ids)
            creators = creators.filter(skills__in=skills)

    artists = AuthUser.objects.filter(pk__in=creators.values_list('user_id'), is_active=True, role=AuthUser.CREATOR)
    if artists:
        artist_ids = list(creators.order_by('-last_published_version__modified').values_list('user_id', flat=True))
        preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(artist_ids)])
        artists = artists.filter(pk__in=artist_ids).order_by(preserved)
        artists = artists[ARTIST_PER_PAGE * offset:][: ARTIST_PER_PAGE]
    return artists
