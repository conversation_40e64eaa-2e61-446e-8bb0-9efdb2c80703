# 01EG89H6SS2141VNGDDEHBMV4Q
from __future__ import unicode_literals

import datetime
import io
import re
import uuid
from decimal import Decimal, ROUND_HALF_UP

from PIL import Image
from django.conf import settings
from django.db import transaction
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.core.validators import MaxValueValidator
from django.db import models, transaction
from django.db.models import SlugField, Sum, F
from django.urls import reverse_lazy
from six import python_2_unicode_compatible
from django_mysql.models import ListTextField
from django.db import models

from common.base_models import BaseModelCropImg
from app.util import get_start_date_of_month, get_end_date_of_next_month, get_start_date_of_week, \
    get_range_date_of_this_week, get_range_date_of_four_week, get_range_date_of_next_week, \
    get_range_date_of_three_week, get_end_date_of_four_week, get_type_file
from mileages.models import MileageRank


def gen_password(length=8):
    from random import choice
    from string import ascii_letters, digits, punctuation
    chars = ascii_letters + digits + punctuation
    return ''.join([choice(chars) for i in range(length)])


@python_2_unicode_compatible
class AuthUserManager(BaseUserManager):
    def _create_user(self, username, email, password, last_name, first_name, role, **extra_fields):
        """
        ユーザ作成

        :param username: ユーザID
        :param email: メールアドレス
        :param password: パスワード
        :param last_name: 苗字
        :param first_name: 名前
        :return: AuthUserオブジェクト
        """
        if not email:
            raise ValueError('Users must have an email')
        if not username:
            raise ValueError('Users must have an username')

        user = self.model(username=username,
                          email=email,
                          password=password,
                          last_name=last_name,
                          first_name=first_name,
                          role=role,
                          **extra_fields)
        user.is_active = True
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, username, email, password=gen_password(32), last_name="master", first_name="client", role="master_client", **extra_fields):
        return self._create_user(username, email, password, last_name, first_name, role, **extra_fields)

    def create_superuser(self, username, email, password, last_name, first_name):
        """
        スーパーユーザ作成

        :param username: ユーザID
        :param email: メールアドレス
        :param password: パスワード
        :param last_name: 苗字
        :param first_name: 名前
        :return: AuthUserオブジェクト
        """
        user = self.create_user(username=username,
                                email=email,
                                password=password,
                                last_name=last_name,
                                first_name=first_name)
        user.is_staff = True
        user.is_superuser = True
        user.save(using=self._db)
        return user


@python_2_unicode_compatible
class AuthUser(AbstractBaseUser, PermissionsMixin, BaseModelCropImg):
    """
    ユーザ情報を管理する
    """

    class Meta:
        verbose_name = 'ユーザ'
        verbose_name_plural = 'ユーザ'

    def get_short_name(self):
        """
        ユーザの苗字を取得する

        :return: 苗字
        """
        return self.last_name

    def get_full_name(self):
        """
        ユーザのフルネームを取得する

        :return: 苗字 + 名前
        """
        return self.last_name + ' ' + self.first_name

    def get_display_name(self):
        if self.role == AuthUser.MASTERCLIENT and self.display_name:
            return self.display_name
        if self.role == AuthUser.CREATOR:
            if self.stage_name:
                return self.stage_name
            if self.stage_name_en:
                return self.stage_name_en
        return self.fullname

    def get_stage_name_en(self):
        return self.stage_name_en or ''

    def get_stage_name(self):
        return self.stage_name or self.stage_name_en or ''

    username = models.CharField(verbose_name='username（ユーザID）',
                                unique=True,
                                max_length=70) #削除希望
    last_name = models.CharField(verbose_name='last_name（姓）',
                                 max_length=30,
                                 default="user") #削除希望
    first_name = models.CharField(verbose_name='first_name（名）',
                                  max_length=30,
                                  default="guest") #削除希望
    email = models.EmailField(verbose_name='email（メールアドレス）',
                              null=True,
                              max_length=70,
                              default=None,
                              unique=True)
    is_active = models.BooleanField(verbose_name='is_active（有効フラグ）',
                                    default=True)
    is_staff = models.BooleanField(verbose_name='is_staff（管理サイトアクセス権限）',
                                   default=False)

    MASTERADMIN = 'master_admin'
    MASTERCLIENT = 'master_client'
    CLIENT = 'client'
    CREATOR = 'admin'
    CURATOR = 'curator'
    USER_ROLES = (
        (MASTERADMIN, '①管理者'),
        (CREATOR, '③クリエイター'),
        (MASTERCLIENT, '④クライアント'),
        (CURATOR, '②マネージャー')
    )

    MAIL_SETTINGS = (
        ('now', '今すぐ（15分ごとに通知）'),
        ('on', '1日1回まとめて通知'),
        ('off', 'オフ（受け取らない）'),
    )

    ACCOUNT_TYPE_CHOICES = (
        ('normal', '普通'),
        ('temp', '当座'),
        ('reverse_tax', '納税準備預金'),
        ('saving', '貯蓄'),
        ('other', 'その他')
    )
    STATUS_FILE = (
        ('0', '未提出'),
        ('1', '審査中'),
        ('2', '本人確認済み'),
        ('3', '要更新')
    )
    role = models.CharField(choices=USER_ROLES, default=MASTERCLIENT, max_length=20, verbose_name='role')

    is_check_ip = models.BooleanField(verbose_name='is_check_ip（ログイン前にIPを確認）',
                                      default=False)
    is_verify = models.BooleanField(default=True)
    list_ip = ListTextField(
        base_field=models.CharField(max_length=15, null=True),
        size=6, verbose_name='リストIP', null=True, blank=True,
        max_length=(6 * 11), default=None, help_text="各ip範囲の後に '、'を使用"
    )
    products = models.ManyToManyField('app.Product', verbose_name='products（プロジェクト）', through='ProductUser', blank=True)

    
    # 作成/更新日時
    modified = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    USERNAME_FIELD = 'username' # emailに変更希望
    REQUIRED_FIELDS = ['email', 'last_name', 'first_name']
    objects = AuthUserManager()
    x = models.FloatField(default=0)
    y = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)
    titles = models.ManyToManyField('app.SceneTitle', through='app.SceneTitleBookmark', blank=True)


    # アバター画像
    avatar = models.ImageField(blank=True, upload_to='images')
    medium_avatar = models.ImageField(blank=True, upload_to='images')
    small_avatar = models.ImageField(blank=True, upload_to='images')

    # アカウント名（芸名、表示名）
    stage_name = models.CharField(default='', max_length=100, null=True, blank=True, )
    stage_name_en = models.CharField(default='', max_length=100, null=True, blank=True, )
    display_name = models.CharField(max_length=200, default=None, null=True, blank=True) # If USER_ROLES === MASTERCLIENT, it can be consolidated into stage_name.

    position = models.CharField(max_length=255, null=True, blank=True) # title
    type = models.CharField(max_length=255, null=True, blank=True, ) # titele_en

    # 所属
    enterprise = models.CharField(max_length=255, null=True, blank=True) # affiliation（所属）
    affiliation_en = models.CharField(max_length=255, null=True, blank=True) # affiliation_en

    company_url = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='company_url') #未使用

    # 本人確認
    user_file = models.FileField(upload_to='file', blank=True, null=True)
    user_file_name = models.CharField(max_length=512, blank=True, null=True)
    file_status = models.CharField(choices=STATUS_FILE, default='0', max_length=20)

    # NDA確認
    admin_file = models.FileField(upload_to='file', blank=True, null=True)
    admin_file_name = models.CharField(max_length=512, blank=True, null=True)

    # mileages
    balance_reward = models.FloatField(default=0)
    evaluation_point = models.FloatField(default=0)
    total_point = models.FloatField(default=0)

    # balance
    balance_available = models.DecimalField(max_digits=30, decimal_places=2, default=0) # 残高
    balance_expected = models.DecimalField(max_digits=30, decimal_places=2, default=0) # 仕掛（進行中


    # 振込先銀行口座
    bank = models.CharField(null=True, blank=True, max_length=200, verbose_name='bank（銀行名）')
    bank_branch = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='bank_branch（支店名・番号）')
    bank_branch_number = models.CharField(default='', max_length=3, null=True, blank=True, verbose_name='bank_branch_number（口座種類・番号）')
    account_type = models.CharField(choices=ACCOUNT_TYPE_CHOICES, default='normal', max_length=50,
                                    verbose_name='account_type')
    account_number = models.CharField(default='', max_length=10, null=True, blank=True, verbose_name='account_number（口座番号）')
    account_name = models.CharField(default='', max_length=255, null=True, blank=True, verbose_name='account_holder')

    # クレジットカード情報
    stripe_customer = models.CharField(max_length=100, default=None, null=True)
    is_stripe_validated = models.BooleanField(verbose_name="Stripeの決済認証ステータス", default=False)

    #通知
    setting_mail = models.CharField(choices=MAIL_SETTINGS, default='on', max_length=20, verbose_name='setting_mail（配信タイミング）')
    noti_hours = models.CharField(default='10:00', null=True, max_length=50, blank=True, verbose_name="noti_hours（配信時間）")



    # 個人情報
    fullname = models.CharField(verbose_name='fullname（氏名）', max_length=200, default=None)
    dob = models.DateTimeField(max_length=50, null=True, blank=True, verbose_name='dob（生年月日）')
    phone = models.CharField(null=True, blank=True, max_length=50, verbose_name='phone（電話番号）')
    invoice_register_number = models.CharField(max_length=30, blank=True, null=True, verbose_name='invoice_register_number（登録番号）')

    # 請求先
    post_number = models.CharField(default='', max_length=10, null=True, blank=True, verbose_name='post_number（郵便番号）')
    province = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='province（都道府県）') # address1
    city = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='city（市区町村・番地）') # address2
    mansion = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='mansion（建物名・部屋番号）') #address3

    # 送付先
    only_address = models.BooleanField(default=True) # 請求先と同じ住所を使用するかどうか

    post_number2 = models.CharField(default='', max_length=10, null=True, blank=True, verbose_name='post_number2（郵便番号）')
    province2 = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='province2（都道府県）') #address1
    city2 = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='city2（市区町村・番地）') #address2
    mansion2 = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='mansion2（番地・部屋番号）') #address3






    # フォローしているユーザーのリスト
    following_users = models.ManyToManyField('self', symmetrical=False, related_name='followers', blank=True)
    # ブロックしているユーザーのリスト
    blocking_users = models.ManyToManyField('self', symmetrical=False, related_name='blocked', blank=True)

    def follow(self, user):
        """ユーザーをフォローする"""
        self.following_users.add(user)

    def unfollow(self, user):
        """ユーザーのフォローを解除する"""
        self.following_users.remove(user)

    def block(self, user):
        """ユーザーをブロックする"""
        self.blocking_users.add(user)

    def unblock(self, user):
        """ユーザーのブロックを解除する"""
        self.blocking_users.remove(user)






    def get_usage_fee_for_user(self):
        mileage_rank = self.get_mileage_for_artist()
        if mileage_rank:
            creator = self.user_creator.first()
            if creator and creator.direct_contact == Creator.AGENT:
                return mileage_rank.usage_fee + 5
            return mileage_rank.usage_fee
        return None   

    def get_mileage_for_artist(self):
        # マイナスポイントの場合は、デフォルトランクを返す
        if self.total_point < 0:
            mileage_rank = MileageRank.objects.filter(is_default=True).first()
            if not mileage_rank:
                # デフォルトランクが存在しない場合は、point=0の最小ランクを返す
                mileage_rank = MileageRank.objects.filter(point=0).order_by('usage_fee').first()
            return mileage_rank
        
        # 通常のロジック：total_point以下の最大ポイントを持つランクを取得
        mileage_rank = MileageRank.objects.filter(point__lte=self.total_point).order_by('-point').first()
        if not mileage_rank:
            # 該当するランクがない場合（point=0のランクも存在しない場合）
            mileage_rank = MileageRank.objects.filter(is_default=True).first()
            if not mileage_rank:
                # デフォルトランクも存在しない場合は、最小ポイントのランクを返す
                mileage_rank = MileageRank.objects.order_by('point').first()
        return mileage_rank

    def get_usage_fee_for_user_to_show(self):
        usage_fee = self.get_usage_fee_for_user()
        if usage_fee is None:
            return Decimal('0.00')
        return Decimal(usage_fee).quantize(Decimal('1.00'), ROUND_HALF_UP).normalize()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_avatar = self.avatar

    def __str__(self):
        return self.last_name + ' ' + self.first_name


    @property
    def full_address(self):
        data_address = list(filter(lambda item: item, [self.province, self.city, self.mansion]))
        return ''.join(data_address)

    def created_str(self):
        return self.created.strftime('%y/%m/%d %a %H:%M')

    def check_auth(self, product):
        if self.is_staff:
            return True
        if isinstance(product, str):
            product_id = product
        else:
            product_id = product.product_id
        return self.products.filter(product_id=product_id).exists()

    def get_delete_form(self):
        from .forms import DeleteUser
        return DeleteUser(initial={'username': self.username})

    def get_creator_id(self):
        if self.role == AuthUser.CREATOR:
            return str(Creator.objects.get(user=self).pk)
        return

    def save(self, *args, **kwargs):
        use_back_job = kwargs.pop('use_back_job', True)
        if self.created and not self.is_verify:
            # verify email
            self.is_verify = True

        need_update_medium_small_avatar = False
        if self.avatar and self.avatar != self._original_avatar:
            img, out_put = self.crop_img('avatar', self.x, self.y, self.width, self.height, ['medium_avatar', 'small_avatar'])
            need_update_medium_small_avatar = True
        if self.role not in [AuthUser.MASTERCLIENT, AuthUser.CREATOR] or not self.fullname:
            self.fullname = f'{self.last_name} {self.first_name}'

        if self.user_file and not self.user_file_name:
            name = self.user_file.name
            file_extension = ''
            if re.search(".[0-9a-zA-Z]{3,4}$", name):
                file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            self.user_file_name = name

        super(AuthUser, self).save(*args, **kwargs)
        if need_update_medium_small_avatar:
            from accounts.tasks import update_medium_small_avatar

            if use_back_job:
                update_medium_small_avatar.delay(self.pk)
            else:
                update_medium_small_avatar(self.pk)
            img.close()
            out_put.close()

    def product_user_list(self):
        return ProductUser.objects.filter(user=self)

    def soft_delete(self):
        self.is_active = False
        self.email = f'{self.email}_{datetime.datetime.now().strftime("%s")}'
        self.username = f'{self.username}_{datetime.datetime.now().strftime("%s")}'
        from social_django.models import UserSocialAuth
        user_socials = UserSocialAuth.objects.filter(user_id=self.pk)
        user_socials.delete()
        self.save()

    @classmethod
    def get_artist_landing_page(cls):
        return cls.objects.filter(role=AuthUser.CREATOR, is_active=True, email=settings.ARTIST_MAIL_LANDINGPAGE)

    @classmethod
    def get_master_admins(cls):
        return cls.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)

    @classmethod
    def change_balance(cls, user_id, fields):
        query_update = {}
        for data in fields:
            query_update[data[0]] = F(data[0]) + data[1]
        cls.objects.filter(pk=user_id).update(**query_update)

    def get_link_profile(self):
        if self.role == AuthUser.CREATOR:
            creator = self.user_creator.first()
            if creator:
                if creator.slug:
                    return reverse_lazy('app:creator_info', kwargs={'slug': creator.slug})
                else:
                    return reverse_lazy('accounts:accounts_creator', kwargs={'pk': self.pk})
        return '/'

    def is_producer_in_project(self):
        return ProductUser.objects.filter(product__is_active=True, product__offer_product__isnull=False, user=self,
                                          position=ProductUser.PRODUCER).exists()

    def get_profile_for_artist(self):
        if self.role == AuthUser.CREATOR:
            return self.user_creator.first().last_published_version
        return


class ProductUser(models.Model):
    NOTIFICATION_CHOICES = (
        ('on', 'ON'),
        ('off', 'OFF')
    )
    ROLE_CHOICES = (
        ('0', '無'),
        ('1', '有')
    )
    MASTERADMIN = 'master_admin'
    DIRECTOR = 'admin'
    OWNER = 'owner'
    REVIEWER = 'client'
    STAFF = 'creator'
    PRODUCER = 'producer'
    USER_ROLES = (
        (MASTERADMIN, '管理者'),
        (DIRECTOR, '⑧ディレクター'),
        (OWNER, '⑤プロジェクトオーナー'),
        (STAFF, '⑨スタッフ'),
        (REVIEWER, '⑥レビュアー'),
        (PRODUCER, '⑦プロデューサー'),
    )
    user = models.ForeignKey('AuthUser', on_delete=models.CASCADE)
    product = models.ForeignKey('app.Product', on_delete=models.CASCADE, verbose_name='product（プロジェクト）')
    is_rating = models.BooleanField(default=True)
    is_favorite = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    is_invited = models.BooleanField(default=False)
    has_new_video = models.BooleanField(default=True)
    is_check_ip = models.BooleanField(verbose_name='is_check_ip（IP制限の有無）',
                                      default=False)
    list_ip = ListTextField(
        base_field=models.CharField(max_length=15, null=True),
        size=6, verbose_name='list_ip', null=True, blank=True,
        max_length=(6 * 11), default=None, help_text="各ip範囲の後に '、'を使用"
    )
    order = models.IntegerField(default=0)
    notification = models.CharField(choices=NOTIFICATION_CHOICES, default='on', max_length=20,
                                    verbose_name='notification（プロジェクトごとのお知らせ）[復活可能性あり]')
    is_owner = models.CharField(choices=ROLE_CHOICES, default='0', max_length=20, verbose_name='is_owner[未使用]')
    budget = models.IntegerField(null=True, default='0')
    view_only = models.BooleanField(default=False)
    position = models.CharField(choices=USER_ROLES, default=REVIEWER, max_length=20, verbose_name='position（権限）')
    budget_offer = models.FloatField(null=True, default='0')
    is_super_producer = models.BooleanField(default=False)
    current_budget = models.FloatField(null=True, default=0, validators=[MaxValueValidator(999999999999999)])
    usage_fee = models.FloatField(default=17.5)
    order_user = models.IntegerField(default=0)
    rewarded = models.FloatField(null=True, default=0)

    def get_max_order_user(self):
        pu_producers = self.product.productuser_set.filter(product=self.product, user__is_active=True, is_invited=False,
                                   position__in=[ProductUser.MASTERADMIN,
                                                 ProductUser.PRODUCER]).exclude(
            pk=self.pk).order_by('order_user').last()

        return pu_producers.order_user + 1 if pu_producers else 0

    def save(self, *args, **kwargs):
        if self.user.role == AuthUser.MASTERADMIN:
            self.position = ProductUser.MASTERADMIN

        super(ProductUser, self).save(*args, **kwargs)

    def update_budget_artist(self, offer_status, new_reward, old_reward, offer):
        if offer.status == '1':
            return
        budget_offer = self.budget_offer if self.budget_offer else 0
        if offer_status == 'create':
            budget_offer += new_reward
        elif offer_status == 'update':
            budget_offer = budget_offer - old_reward + new_reward
        self.budget_offer = budget_offer
        self.save()

    @classmethod
    def update_available_budget(cls, project_user, new_reward):
        if not project_user or project_user and project_user.user.role != AuthUser.CREATOR:
            return
        budget_offer = project_user.budget_offer if project_user.budget_offer else 0
        budget_offer = budget_offer + new_reward
        project_user.budget_offer = budget_offer
        project_user.save()

    @classmethod
    def update_rewarded(cls, project_user, new_reward):
        # rewarded được sử dụng để order artist trong modal search artist staff credit
        if not project_user or project_user and project_user.user.role != AuthUser.CREATOR:
            return
        rewarded = project_user.rewarded if project_user.rewarded else 0
        rewarded = rewarded + new_reward
        project_user.rewarded = rewarded
        project_user.save()

class UserDevice(models.Model):
    user = models.ForeignKey('AuthUser', related_name='user_log', blank=True,
                             null=True, on_delete=models.CASCADE)
    device = models.CharField(max_length=20, default='', null=True, blank=True)
    address = models.CharField(max_length=100, default='', null=True, blank=True)
    list_ip = ListTextField(
        base_field=models.CharField(max_length=15, null=True),
        size=6, verbose_name='リストIP', null=True, blank=True,
        max_length=(6 * 11), default=None, help_text="各ip範囲の後に '、'を使用"
    )
    modified = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)


class CreatorList(models.Model):
    title = models.CharField(max_length=100, default='', null=True, blank=True)
    description = models.TextField(null=True)
    order = models.IntegerField(default=0)
    creator_list_creator = models.ManyToManyField(
        to="Creator",
        through='CreatorListCreator',
    )
    is_default = models.BooleanField(default=False)

    def to_string_id(self):
        return str(self.id)
    string_id = property(to_string_id)


class CreatorListCreator(models.Model):
    creator = models.ForeignKey("Creator", on_delete=models.CASCADE)
    creator_list = models.ForeignKey("CreatorList", on_delete=models.CASCADE)
    order = models.IntegerField(default=0)


class Creator(models.Model):
    PUBLIC = 'public'
    PRIVATE = 'private'
    PROJECT = 'project'
    MATCHING = 'matching'
    AGENT = 'agent'
    PRODUCTION = 'production'

    CHECKOUT_CHOICES = (
        ('five_day', '5日'),
        ('twenty_day', '20日'),
    )
    TRADING_CHOICES = (
        ('one', 1),
        ('two', 2),
        ('three', 3),
        ('four', 4),
        ('five', 5)
    )
    NOTIFICATION_CHOICES = (
        ('immediately', 'すぐに受けとる'),
        ('one_day', '1日１回まとめて受けとる'),
        ('off', 'OFF')
    )

    ROLE_CREATOR = (
        ('composer', 'コンポーザー'),
        ('sound', 'サウンドデザイナー'),
        ('voice', '声優・ナレーター'),
        ('player', '演奏家'),
        ('vocalist', 'ボーカリスト'),
        ('audio engineer', 'オーディオエンジニア')
    )

    SHOW_PROFILE = (
        ('public', 'オープン'),
        ('private', 'メンバーズオンリー'),
        ('project', 'プロジェクトオンリー')
    )

    ACCOUNT_TYPE_CHOICES = (
        ('normal', '普通'),
        ('temp', '当座'),
        ('reverse_tax', '納税準備預金'),
        ('saving', '貯蓄'),
        ('other', 'その他')
    )

    STATUS_FILE = (
        ('0', 'まだファイルをアップロードしない'),
        ('1', '確認中'),
        ('2', '承認済'),
        ('3', '拒否済')
    )

    DIRECT_CONTACT = (
        ('matching', 'マッチングスタイル'),
        ('agent', 'エージェントスタイル'),
        ('production', 'プロダクションスタイル')
    )

    user = models.ForeignKey('AuthUser', related_name='user_creator', on_delete=models.CASCADE)
    # dob = models.DateTimeField(max_length=50, null=True, blank=True, verbose_name='生年月日') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    # post_number = models.CharField(default='', max_length=50, null=True, blank=True, verbose_name='郵便番号') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    # province = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='都道府県[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    # city = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='市区町村[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    # mansion = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='マンション名[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    # phone = models.CharField(null=True, blank=True, max_length=100, verbose_name='電話番号[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    bank = models.CharField(null=True, blank=True, max_length=200, verbose_name='銀行名[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    bank_branch = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='bank_branch（支店）[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    bank_branch_number = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='bank_branch_number（支店番号）[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    account_type = models.CharField(choices=ACCOUNT_TYPE_CHOICES, default='normal', max_length=50,
                                    verbose_name='account_type（口座種類）[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    account_number = models.CharField(default='', max_length=50, null=True, blank=True, verbose_name='account_number（口座番号）[不要。AuthUserにある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    checkout_setting = models.CharField(choices=CHECKOUT_CHOICES, default='five_day', max_length=20,
                                        verbose_name='checkout_setting（自動振込）[復活可能性あり]')
    next_checkout = models.BooleanField(default=False, max_length=10, verbose_name='next_checkout（30,000円未満は、次回にまとめる）[復活可能性あり]')
    policy = models.TextField(verbose_name='policy（クライテリア）', null=True, blank=True)
    trading = models.CharField(choices=TRADING_CHOICES, default='three', max_length=20, verbose_name='trading')
    notification = models.CharField(choices=NOTIFICATION_CHOICES, default='immediately', max_length=20,
                                    verbose_name='notification（お知らせ）[AuthUserにもある]') # The same data exists in AuthUser, resulting in duplication. I request its removal.
    noti_dayoff = models.BooleanField(default=False, max_length=10, verbose_name='noti_dayoff（仕事不可日は、通知を受け取らない。）')
    before_delivery = models.BooleanField(default=False, max_length=10, verbose_name='before_delivery（納期前日に通知を送る。）[復活可能性あり]')
    hours = models.CharField(default='10:00', null=True, max_length=50, blank=True, )
    banner = models.ImageField(blank=True, upload_to='images')
    official_site = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='OFFICIAL SITE')
    twitter_link = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='Twitter')
    facebook_link = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='Facebook')
    instagram_link = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='Instagram')
    stage_name = models.CharField(default='', max_length=100, null=True, blank=True, ) #The same data exists in AuthUser, resulting in duplication. I request its removal.
    stage_name_en = models.CharField(default='', max_length=100, null=True, blank=True, ) #The same data exists in AuthUser, resulting in duplication. I request its removal.
    role_creator = models.CharField(choices=ROLE_CREATOR, default='composer', max_length=20, )
    type = models.CharField(default='', max_length=200, null=True, blank=True, )
    theme_quote = models.TextField(verbose_name='theme_quote（テーマ）', max_length=140)
    profile_quote = models.TextField(verbose_name='profile_quote（プロフィール）')
    x_banner = models.FloatField(default=0)
    y_banner = models.FloatField(default=0)
    width_banner = models.FloatField(default=0)
    height_banner = models.FloatField(default=0)
    question = models.TextField(default="", verbose_name="question", null=True, blank=True)
    modified = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)
    show_profile = models.CharField(choices=SHOW_PROFILE, default='project', max_length=20,
                                    verbose_name='show_profile')

    last_version = models.ForeignKey('accounts.CreatorProfile', related_name='last_profile', blank=True,
                                     null=True, on_delete=models.SET_NULL)
    last_published_version = models.ForeignKey('accounts.CreatorProfile', related_name='published_profile',
                                               blank=True, null=True, on_delete=models.SET_NULL)
    slug = SlugField(max_length=50, null=True, blank=True)
    skills = models.ManyToManyField('Skill', related_name='creator_skills', blank=True)
    is_assigneer = models.BooleanField(default=False)
    note_schedule = models.TextField(null=True, max_length=400, blank=True)
    is_direct = models.BooleanField(default=False) #use for check targeted users to add when create project
    usage_fee = models.FloatField(default=17.5)
    is_invited = models.BooleanField(default=False)
    creator_file_status = models.CharField(choices=STATUS_FILE, default='0', max_length=20)
    curator_file = models.FileField(upload_to='file', blank=True, null=True)
    curator_file_name = models.CharField(max_length=512, blank=True, null=True)
    direct_contact = models.CharField(choices=DIRECT_CONTACT, default='production', max_length=20, verbose_name='コンタクト')

    @classmethod
    def get_creator_landing_page(self):
        return self.objects.filter(user__is_active=True, user__email=settings.ARTIST_MAIL_LANDINGPAGE)


    def save(self, *args, **kwargs):
        # if self.banner:
        #     try:
        #         creator = Creator.objects.get(id=self.pk)
        #     except Creator.DoesNotExist:
        #         creator = None
        #
        #     if not creator or creator.banner != self.banner:
        #         banner = Image.open(io.BytesIO(self.banner.read()))
        #         area = (self.x_banner, self.y_banner,
        #                 self.width_banner + self.x_banner,
        #                 self.height_banner + self.y_banner)
        #         crop_banner = banner.crop(area)
        #         output = io.BytesIO()
        #         white_background = Image.new("RGBA", crop_banner.size, "WHITE")
        #         white_background.paste(crop_banner, (0, 0), crop_banner)
        #         white_background.convert('RGB').save(output, format='PNG')
        #         self.banner = InMemoryUploadedFile(output, 'FileField', self.banner.name, 'image/png',
        #                                            output.getbuffer().nbytes, None)
        self.is_direct = True if self.direct_contact == Creator.MATCHING and not self.is_landing_page() else False
        self.modified = datetime.datetime.now()
        super(Creator, self).save(*args, **kwargs)

    def content_published(self):
        return self.content_owner.filter(status='publish', parent__isnull=True)

    def get_public_stage_name(self):
        try:
            if self.last_published_version.stage_name:
                return self.last_published_version.stage_name

            elif self.last_published_version.stage_name_en:
                return self.last_published_version.stage_name_en
            else:
                return self.user.fullname
        except:
            return self.user.fullname

    def get_schedule_list(self):
        dt = datetime.datetime.now()
        result = {
            'thisMonth': {},
            'nextMonth': {}
        }
        schedules_list = ScheduleCreator.objects.filter( \
            creator=self,
            schedule__range=[get_start_date_of_month(dt), get_end_date_of_next_month(dt)]
        ).values('status', 'schedule')

        for schedule_creator in schedules_list:
            schedule = schedule_creator['schedule']
            status = schedule_creator['status']
            if schedule.month == dt.month:
                result['thisMonth'][str(schedule_creator["schedule"].day)] = int(status)
            else:
                result['nextMonth'][str(schedule_creator["schedule"].day)] = int(status)

        return result

    def update_last_published_version(self):
        if not self.last_published_version:
            creator_profile = CreatorProfile.objects.create(creator=self, status='1')
            self.last_published_version = creator_profile
            self.save()

    def get_schedule_list_info_model(self):
        dt = datetime.datetime.now()
        result = {
            'weekOne': [],
            'weekTwo': [],
            'weekThree': [],
            'weekFour': [],
        }
        schedules = ScheduleCreator.objects.filter( \
            creator=self, schedule__gte=get_start_date_of_week(dt), \
            schedule__lt=get_end_date_of_four_week(dt)).order_by('schedule')
        list_date = get_range_date_of_four_week(dt)
        list_schedule = list(schedules.values_list('schedule', flat=True))

        for schedule_creator in list_date:
            if schedule_creator in list_schedule:
                schedule = next((schedule for schedule in schedules if schedule.schedule == schedule_creator), None)
                res = [schedule.status, schedule_creator.day]
            else:
                res = ['0', schedule_creator.day]
            if schedule_creator in get_range_date_of_this_week(dt):
                result['weekOne'].append(res)
            elif schedule_creator in get_range_date_of_next_week(dt):
                result['weekTwo'].append(res)
            elif schedule_creator in get_range_date_of_three_week(dt):
                result['weekThree'].append(res)
            else:
                result['weekFour'].append(res)
        return result.items()

    def get_type_file_curator(self):
        if self.curator_file:
            name = self.curator_file.name
            return get_type_file(name)
        return 'other'

    def get_usage_fee(self):
        return Decimal(self.usage_fee).quantize(Decimal('1.00'), ROUND_HALF_UP).normalize()


    def is_landing_page(self):
        return self.user.email == settings.ARTIST_MAIL_LANDINGPAGE


class CreatorFile(models.Model):
    file_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    creator = models.ForeignKey('Creator', related_name="idetity_files", null=True, on_delete=models.SET_NULL)
    owner = models.ForeignKey('AuthUser', related_name="idetity_files", null=True, on_delete=models.SET_NULL)
    file = models.FileField(upload_to='file', blank=True, max_length=1024)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    modified = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    def get_type_file_artist(self):
        if self.file:
            name = self.file.name
            return get_type_file(name)
        return 'other'

class CreatorProfile(models.Model, BaseModelCropImg):
    VERSION_STATUS = (
        ('1', 'approve'),
        ('2', 'editing'),
        ('3', 'old'),
        ('4', 'reject'),
    )

    creator = models.ForeignKey('Creator', related_name='creator_profile', on_delete=models.CASCADE)
    banner = models.ImageField(blank=True, upload_to='images')
    banner_resized = models.ImageField(blank=True, upload_to='images')
    official_site = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='OFFICIAL SITE')
    twitter_link = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='Twitter')
    facebook_link = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='Facebook')
    instagram_link = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='Instagram')
    stage_name = models.CharField(default='', max_length=100, null=True, blank=True, )  # The same data exists in AuthUser, resulting in duplication. I request its removal.
    stage_name_en = models.CharField(default='', max_length=100, null=True, blank=True, )  # #The same data exists in AuthUser, resulting in duplication. I request its removal.
    type = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='type（肩書き）[AuthUserのpositionとダブってる]')
    theme_quote = models.TextField(verbose_name='theme_quote（140字以内）', max_length=140)
    profile_quote = models.TextField(verbose_name='profile_quote（400字以内）', max_length=400)
    x_banner = models.FloatField(default=0)
    y_banner = models.FloatField(default=0)
    width_banner = models.FloatField(default=0)
    height_banner = models.FloatField(default=0)
    avatar = models.ImageField(blank=True, upload_to='images')
    x = models.FloatField(default=0)
    y = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)
    modified = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)
    status = models.CharField(choices=VERSION_STATUS, default='2', max_length=20)
    owner = models.ForeignKey('AuthUser', related_name='profiles', blank=True,
                              null=True, on_delete=models.CASCADE)
    youtube_link = models.CharField(max_length=200, null=True, blank=True, verbose_name='Youtube')

    def save(self, *args, **kwargs):
        self.modified = datetime.datetime.now()
        super(CreatorProfile, self).save(*args, **kwargs)

    def get_stage_name(self):
        return self.creator.user.get_display_name()

    def get_block_profile(self):
        return self.blocks.filter(item_block__type_block=ItemBlock.PROFILE_BLOCK).last().item_block.profile_block

    def get_block_statement(self):
        return self.blocks.filter(item_block__type_block=ItemBlock.STATEMENT_BLOCK).last().item_block.statement_block

    def get_block_header(self):
        return self.blocks.filter(item_block__type_block=ItemBlock.HEADER_BLOCK).last().item_block.header_block

    def get_block_footer(self):
        block = self.blocks.filter(item_block__type_block=ItemBlock.FOOTER_BLOCK).last()
        if block:
            return block.item_block.footer_block


class ScheduleCreator(models.Model):
    OFF = '1'
    MAYBE = '2'
    NORMAL = '3'

    STATUS_CHOIES = (
        ('1',  'off'),
        ('2', 'maybe'),
        ('3', 'normal'),
    )

    creator = models.ForeignKey('Creator', related_name='schedule_creator', on_delete=models.CASCADE)
    schedule = models.DateTimeField(null=True, blank=True)
    status = models.CharField(choices=STATUS_CHOIES, default='2', max_length=20)


class BlockList(models.Model):
    creator = models.ForeignKey('Creator', related_name='company_banned_creator', on_delete=models.CASCADE)
    company_name = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='会社名')
    company_url = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='url')
    reason = models.TextField(null=True, blank=True, verbose_name='理由')
    modified = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)
# 241202 削除し、AuthUser.blocking_usersに統合を希望


class Skill(models.Model):
    name = models.CharField(max_length=300, default='')
    group = models.CharField(max_length=300, default='')
    order = models.IntegerField(default=0)

    class Meta:
        ordering = ["order"]

    @classmethod
    def get_choses(cls, user):
        skills = cls.objects.all().values_list("id", "name", "group")
        selected = Creator.objects.get(user=user).skills.all() \
                          .values_list('id', flat=True)
        result = {}
        for skill in skills:
            is_selected = skill[0] in selected
            res = [*skill[0:2], is_selected]
            result.setdefault(skill[2], []) \
                  .append(res)

        return result.items()


class ItemBlock(models.Model):
    PROFILE_BLOCK = '1'
    STATEMENT_BLOCK = '3'
    HEADER_BLOCK = '2'
    FOOTER_BLOCK = '4'
    TYPE_BLOCK = (
        ('1', 'profile_block'),
        ('2', 'header_block'),
        ('3', 'statement_block'),
        ('4', 'footer_block')
    )

    type_block = models.CharField(choices=TYPE_BLOCK, default='2', max_length=20)
    profile_block = models.OneToOneField('ProfileBlock', related_name='profile_block', blank=True, null=True,
                                         on_delete=models.CASCADE)
    header_block = models.OneToOneField('HeaderBlock', related_name='header_block', blank=True, null=True,
                                        on_delete=models.CASCADE)

    statement_block = models.OneToOneField('StatementBlock', related_name='statement_block', blank=True, null=True,
                                           on_delete=models.CASCADE)
    footer_block = models.OneToOneField('FooterBlock', related_name='footer_block', blank=True, null=True,
                                        on_delete=models.CASCADE)

    order = models.IntegerField(default=0)

    class Meta:
        ordering = ["order"]

    def get_item_block(self):
        if self.type_block == ItemBlock.PROFILE_BLOCK:
            return self.profile_block


class CreatorItemBlock(models.Model):
    creator_profile = models.ForeignKey('accounts.CreatorProfile', related_name='blocks', blank=True,
                                        null=True, on_delete=models.CASCADE)
    item_block = models.ForeignKey('accounts.ItemBlock', on_delete=models.CASCADE)


class ProfileBlock(models.Model):
    section_name_jp = models.CharField(max_length=15, default='')
    section_name_en = models.CharField(max_length=15, default='')
    is_link_menu = models.BooleanField(verbose_name='トップメニューにリンク', default=False)
    content_jp = models.TextField(verbose_name='テキスト', max_length=3000)
    content_en = models.TextField(verbose_name='Text', max_length=3000)


class StatementBlock(models.Model):
    is_show_avatar = models.BooleanField(verbose_name='アバターを表示', default=False)
    is_show_name = models.BooleanField(verbose_name='アーティスト名を表示', default=False)
    is_show_title = models.BooleanField(verbose_name='タイトルを表示', default=False)
    theme_jp = models.TextField(verbose_name='アーティストステートメント', max_length=1000)
    theme_en = models.TextField(verbose_name='アーティストステートメント', max_length=1000)


class HeaderBlock(models.Model):
    HEADER_FULL_SCREEN = '1'
    HEADER_BANNER = '2'
    TYPE_HEADER = (
        ('1', 'full_screen'),
        ('2', 'banner'),
    )
    type_header = models.CharField(choices=TYPE_HEADER, default='1', max_length=20)
    logo = models.ImageField(blank=True, upload_to='images')
    key_visual_pc = models.FileField(upload_to='file', blank=True, null=True)
    key_visual_sp = models.FileField(upload_to='file', blank=True, null=True)
    banner = models.FileField(upload_to='file', blank=True, null=True)
    catchphrase_jp_1 = models.CharField(max_length=15, default='')
    catchphrase_jp_2 = models.CharField(max_length=15, default='')
    catchphrase_jp_3 = models.CharField(max_length=15, default='')
    catchphrase_en_1 = models.CharField(max_length=15, default='')
    catchphrase_en_2 = models.CharField(max_length=15, default='')
    catchphrase_en_3 = models.CharField(max_length=15, default='')
    display_tag_line_1 = models.BooleanField(verbose_name='タグライン１を表示', default=False)
    display_tag_line_2 = models.BooleanField(verbose_name='タグライン２を表示', default=False)
    artist_name_jp = models.CharField(max_length=60, default='')
    artist_name_en = models.CharField(max_length=60, default='')
    title_jp = models.CharField(max_length=60, default='')
    title_en = models.CharField(max_length=60, default='')

    def get_catchphrase_jp(self):
        return f'{self.catchphrase_jp_1}{self.catchphrase_jp_2}{self.catchphrase_jp_3}'

    def get_key_visual_pc(self):
        if self.key_visual_pc:
            return self.key_visual_pc.url
        return ''

    def get_key_visual_sp(self):
        if self.key_visual_sp:
            return self.key_visual_sp.url
        return ''

    def get_banner(self):
        if self.banner:
            return self.banner.url
        return ''

    def get_logo(self):
        if self.logo:
            return self.logo.url
        return ''

    def get_key_visual_pc_name(self):
        if self.key_visual_pc:
            return self.key_visual_pc.name.replace('file/', '')
        return ''

    def get_key_visual_sp_name(self):
        if self.key_visual_sp:
            return self.key_visual_sp.name.replace('file/', '')
        return ''

    def get_banner_name(self):
        if self.banner:
            return self.banner.name.replace('file/', '')
        return ''

    def get_logo_name(self):
        if self.logo:
            return self.logo.name.replace('images/', '')
        return ''


class ItemFooterMenu(models.Model):
    FOOTER_1 = '1'
    FOOTER_2 = '2'
    TYPE_FOOTER = (
        ('1', 'footer_1'),
        ('2', 'footer_2'),
    )
    type_footer = models.CharField(choices=TYPE_FOOTER, default='1', max_length=20)
    title_jp = models.CharField(max_length=60, default='メニュー')
    title_en = models.CharField(max_length=60, default='メニュー')
    url = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='URL')
    order = models.IntegerField(default=0)
    footer_block = models.ForeignKey('FooterBlock', related_name='item_footer', null=False, on_delete=models.CASCADE)
    parent = models.ForeignKey('self', related_name='child', null=True, blank=True, on_delete=models.SET_NULL)

    class Meta:
        ordering = ['order']


class ItemSocial(models.Model):
    TYPE_SOCIAL_LINK = (
        ('home', 'official_site'),
        ('twitter', 'twitter_link'),
        ('fb', 'facebook_link'),
        ('insta', 'instagram_link'),
        ('youtube', 'youtube_link'),
        ('tiktok', 'tiktok_link'),
        ('note', 'note_link'),
    )
    type_social_link = models.CharField(choices=TYPE_SOCIAL_LINK, default='home', max_length=40)
    url = models.CharField(default='', max_length=200, null=True, blank=True, verbose_name='URL')
    order = models.IntegerField(default=0)
    footer_block = models.ForeignKey('FooterBlock', related_name='item_social', null=False, on_delete=models.CASCADE)
    parent = models.ForeignKey('self', related_name='child', null=True, blank=True, on_delete=models.SET_NULL)

    class Meta:
        ordering = ['order']


class FooterBlock(models.Model):
    copyright = models.CharField(max_length=200, default='')

    def get_footer_menu_first(self):
        return self.item_footer.filter(type_footer=ItemFooterMenu.FOOTER_1)

    def get_footer_menu_second(self):
        return self.item_footer.filter(type_footer=ItemFooterMenu.FOOTER_2)
