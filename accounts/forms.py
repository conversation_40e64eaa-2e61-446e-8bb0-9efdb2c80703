import datetime
import re

from django import forms
from django.contrib.auth import get_user_model, password_validation
from django.contrib.auth.forms import ReadOnlyPasswordHashField as _ReadOnlyPasswordHashField
from django.contrib.auth.forms import ReadOnlyPasswordHashWidget as _ReadOnlyPasswordHashWidget
from django.contrib.auth.forms import UserChangeForm as _UserChangeForm
from django.contrib.auth.forms import UserCreationForm as _UserCreationForm
from django.contrib.auth.forms import UsernameField
from django.db.models import Q
from django.forms import EmailField
from django.forms.models import inlineformset_factory
from django.http import Http404
from django.utils.translation import gettext_lazy

from accounts.models import AuthUser, BlockList, Creator, Skill


class ReadOnlyPasswordHashWidget(_ReadOnlyPasswordHashWidget):
    template_name = 'accounts/widgets/read_only_password_hash.html'


class ReadOnlyPasswordHashField(_ReadOnlyPasswordHashField):
    widget = ReadOnlyPasswordHashWidget


class UserCreationForm(_UserCreationForm):
    username = forms.CharField(max_length=50, required=True, label='ユーザID',
                               widget=forms.TextInput(attrs={'placeholder': 'ユーザID'}))
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(attrs={'placeholder': 'メールアドレス'}))

    class Meta:
        model = get_user_model()
        fields = ("username", 'email', 'last_name', 'first_name', 'role', 'products',
                  'is_check_ip', 'list_ip', 'setting_mail')
        field_classes = {'username': UsernameField}
        widgets = {
            'setting_mail': forms.RadioSelect(),
        }

    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError(
                self.error_messages['password_mismatch'],
                code='password_mismatch',
            )
        self.instance.username = self.cleaned_data.get('username')
        self.instance.email = self.cleaned_data.get('email')
        self.instance.first_name = self.cleaned_data.get('first_name')
        self.instance.last_name = self.cleaned_data.get('last_name')
        password_validation.validate_password(self.cleaned_data.get('password2'), self.instance)
        return password2


class UserRegisterForm(forms.Form):
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(attrs={'placeholder': 'メールアドレス', 'class': 'input-box'}))
    password1 = forms.CharField(required=True, label="Password", widget=forms.PasswordInput(attrs={'placeholder': '新しいパスワード', 'class': 'input-box'}))
    password2 = forms.CharField(required=True, label="Password Confirm", widget=forms.PasswordInput(attrs={'placeholder': 'パスワードを確認する', 'class': 'input-box'}))

    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError(
                self.error_messages['password_mismatch'],
                code='password_mismatch',
            )
        return password2


class SelfUserChangeForm(_UserChangeForm):
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(attrs={'placeholder': 'メールアドレス'}))
    x = forms.FloatField(widget=forms.HiddenInput())
    y = forms.FloatField(widget=forms.HiddenInput())
    width = forms.FloatField(widget=forms.HiddenInput())
    height = forms.FloatField(widget=forms.HiddenInput())
    enterprise = forms.CharField(max_length=255, required=False, label='enterprise', widget=forms.TextInput(attrs={'placeholder': '会社名'}))
    position = forms.CharField(max_length=255, required=False, label='position', widget=forms.TextInput(attrs={'placeholder': '役職'}))

    class Meta:
        model = get_user_model()
        fields = ('last_name', 'first_name', 'email', 'password', 'avatar', 'x', 'y', 'width', 'height',
                  'enterprise', 'position')
        field_classes = {'username': UsernameField}

    def clean(self):
        super(SelfUserChangeForm, self).clean()
        if 'email' in self.changed_data:
            from social_django.models import UserSocialAuth
            user_socials = UserSocialAuth.objects.filter(user_id=self.instance.pk)
            user_socials.delete()


class SelfUserInfoForm(_UserChangeForm):
    display_name = forms.CharField(label='姓', max_length=60, required=False,
                                   widget=forms.TextInput(attrs={'placeholder': 'ぽっちんぷりん'}))
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(attrs={'placeholder': 'メールアドレス'}))
    fullname = forms.CharField(max_length=60, required=True,
                               widget=forms.TextInput(attrs={'placeholder': '姓 名'}))
    x = forms.FloatField(widget=forms.HiddenInput())
    y = forms.FloatField(widget=forms.HiddenInput())
    width = forms.FloatField(widget=forms.HiddenInput())
    height = forms.FloatField(widget=forms.HiddenInput())
    enterprise = forms.CharField(max_length=255, required=False, label='enterprise',
                                 widget=forms.TextInput(attrs={'placeholder': '@soremo'}))
    position = forms.CharField(max_length=255, required=False, label='position',
                               widget=forms.TextInput(attrs={'placeholder': 'コンポーザー'}))
    phone = forms.CharField(max_length=30, required=False, label='phone',
                            widget=forms.TextInput(attrs={'placeholder': '03-6457-1780'}))
    post_number = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '135-0064'}))
    post_number2 = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '135-0064'}))
    city = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '江東区青海'}))
    city2 = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '江東区青海'}))
    province = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '東京都'}))
    province2 = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '東京都'}))
    mansion = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '2-7-4 theSOHO1226'}))
    mansion2 = forms.CharField(required=False, widget=forms.TextInput(attrs={'placeholder': '2-7-4 theSOHO1226'}))

    dob = forms.DateTimeField(required=False, input_formats=['%Y/%m/%d'],
                              widget=forms.TextInput(attrs={'placeholder': 'yyyy/mm/dd'}))

    invoice_register_number = forms.CharField(max_length=30, required=False,
                                              widget=forms.TextInput(attrs={'placeholder': 'T1234567890123'}))

    class Meta:
        model = get_user_model()
        fields = ('last_name', 'first_name', 'display_name', 'email', 'password', 'avatar', 'x', 'y', 'width', 'height',
                  'enterprise', 'position', 'fullname', 'company_url', 'post_number', 'province',
                  'city', 'mansion', 'post_number2', 'province2', 'city2', 'mansion2', 'phone', 'only_address', 'dob',
                  'invoice_register_number')
        field_classes = {'username': UsernameField}
        widgets = {
            'display_name': forms.TextInput(attrs={'placeholder': 'ぽっちんぷりん'}),
            'company_url': forms.TextInput(attrs={'placeholder': 'https://soremo.jp'}),
        }

    def clean(self):
        super(SelfUserInfoForm, self).clean()
        phone = self.cleaned_data.get("phone")
        dob = self.cleaned_data.get("dob")
        if dob and dob >= datetime.datetime.now():
            self.add_error('dob', "Must past time")
        if phone and not re.search('^[0-9-]*$', phone):
            self.add_error('phone', "contain number")
        if 'email' in self.changed_data:
            from social_django.models import UserSocialAuth
            user_socials = UserSocialAuth.objects.filter(user_id=self.instance.pk)
            user_socials.delete()


class AccountCreatorForm(SelfUserInfoForm):
    stage_name = forms.CharField(max_length=60, required=False,
                                 widget=forms.TextInput(attrs={'placeholder': 'ジョン・レノン'}))
    stage_name_en = forms.CharField(max_length=60, required=False,
                                    widget=forms.TextInput(attrs={'placeholder': 'John Lennon'}))
    type = forms.CharField(max_length=60, required=False,
                           widget=forms.TextInput(attrs={'placeholder': 'Composer'}))

    class Meta(SelfUserInfoForm.Meta):
        model = AuthUser
        fields = ('first_name', 'display_name', 'email', 'password', 'avatar', 'x', 'y', 'width', 'height',
                  'enterprise', 'position', 'fullname', 'company_url', 'post_number', 'province',
                  'city', 'mansion', 'post_number2', 'province2', 'city2', 'mansion2', 'phone', 'only_address', 'dob',
                  'stage_name',
                  'bank_branch_number', 'account_type', 'account_name',
                  'bank', 'bank_branch', 'account_number', 'stage_name_en', 'type', 'user_file',
                  'invoice_register_number'
                  )
        widgets = {
            'account_number': forms.TextInput(
                attrs={'class': 'form-control', 'autocomplete': 'off', 'pattern': '[0-9]+',
                       'title': 'Enter Numbers Only ', 'placeholder': '7030397'}),
            'bank': forms.TextInput(attrs={'placeholder': '楽天銀行'}),
            'bank_branch': forms.TextInput(attrs={'placeholder': 'ロック支店'}),
            'bank_branch_number': forms.TextInput(attrs={'placeholder': '202'}),
            'account_name': forms.TextInput(attrs={'placeholder': 'カ）ソレモ'}),
        }


class UserSettingForm(forms.ModelForm):
    class Meta:
        model = get_user_model()
        fields = ('setting_mail', 'noti_hours',)
        widgets = {
            'password': forms.HiddenInput(),
        }


class AdminUserChangeForm(_UserChangeForm):
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(attrs={'placeholder': 'メールアドレス'}))
    password = ReadOnlyPasswordHashField(
        label=gettext_lazy("Password"),
        help_text=gettext_lazy(
            "Raw passwords are not stored, so there is no way to see this "
            "user's password, but you can change the password using "
            "<a href=\"../password/\">this form</a>."
        ),
    )

    class Meta:
        model = get_user_model()
        fields = ('last_name', 'first_name', 'email', 'role',
                  'products', 'password', 'is_check_ip', 'list_ip')
        field_classes = {'username': UsernameField}
        widgets = {
            'password': forms.HiddenInput(),
        }


class DeleteUser(forms.Form):
    username = forms.CharField(max_length=50, required=True, widget=forms.HiddenInput)

    def save(self):
        from django.contrib.auth import get_user_model
        username = self.cleaned_data['username']
        user = get_user_model().objects.get(username=username)
        user.soft_delete()


class LoginUserForm(forms.Form):
    password = forms.RegexField(max_length=20, min_length=8, required=False, regex=r"^[\w.@+-]+$",
                                widget=forms.PasswordInput)
    username = forms.CharField(max_length=50, required=False)
    error_user = forms.CharField(max_length=255, required=False)
    error_pass = forms.CharField(max_length=255, required=False)
    remember_me = forms.BooleanField(required=False)

    def is_exist_user_with_username(self, username):
        try:
            return AuthUser.objects.filter(Q(username__iexact=username) | Q(email__iexact=username)).first() is not None
        except Exception as ex:
            return False

    def get_exist_user_by_username(self, username):
        try:
            return AuthUser.objects.get(Q(username__iexact=username) | Q(email__iexact=username))
        except:
            raise Http404()

    def is_valid_user(self, username, password):
        if self.is_exist_user_with_username(username=username):
            user = self.get_exist_user_by_username(username=username)
            return user.check_password(raw_password=password)
        return False

    def clean_username(self):
        username = self.cleaned_data.get('username', None)
        user = AuthUser.objects.filter(Q(username__iexact=username) | Q(email__iexact=username)).first()
        if not user:
            self.add_error("error_user", "入力したメールアドレスは正しくありません。")
        return username

    def clean(self):
        cleaned_data = super(LoginUserForm, self).clean()
        username = cleaned_data.get('username', None)
        password = cleaned_data.get('password', None)
        if not self.is_valid_user(username=username, password=password):
            if not self.errors:
                self.add_error("error_pass", "入力したパスワードは正しくありません。")
        return cleaned_data


class CreatorInfoForm(forms.ModelForm):
    last_name = forms.CharField(label='姓', max_length=30, required=True)
    first_name = forms.CharField(label='名', max_length=30, required=True)
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(attrs={'placeholder': 'メールアドレス'}))
    dob = forms.DateTimeField(input_formats=['%Y/%m/%d'], required=True)

    class Meta:
        model = Creator
        fields = (
            # 'post_number', 'province', 'city', 'mansion', 'phone', 
                  'bank_branch_number', 'account_type',
                  'bank', 'bank_branch', 'account_number', 'checkout_setting', 'next_checkout')
        widgets = {
            'checkout_setting': forms.RadioSelect(),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'autocomplete': 'off', 'pattern': r'^([0-9]+-*)+\d',
                                             'maxlength': '30', 'title': '数字またはダッシュのみを入力してください', 'required': True}),
            'account_number': forms.TextInput(
                attrs={'class': 'form-control', 'autocomplete': 'off', 'pattern': '[0-9]+',
                       'title': 'Enter Numbers Only ', 'required': True}),
            'post_number': forms.TextInput(),
            'province': forms.TextInput(attrs={'required': True}),
            'city': forms.TextInput(attrs={'required': True}),
            'bank': forms.TextInput(attrs={'required': True}),
            'bank_branch': forms.TextInput(attrs={'required': True}),
            'bank_branch_number': forms.TextInput(attrs={'required': True}),
        }


class CreatorProfileForm(forms.ModelForm):
    avatar = forms.ImageField(required=False, widget=forms.FileInput)
    banner = forms.ImageField(required=False, widget=forms.FileInput)
    x = forms.FloatField(widget=forms.HiddenInput(), required=False)
    y = forms.FloatField(widget=forms.HiddenInput(), required=False)
    width = forms.FloatField(widget=forms.HiddenInput(), required=False)
    height = forms.FloatField(widget=forms.HiddenInput(), required=False)
    x_banner = forms.FloatField(widget=forms.HiddenInput(), required=False)
    y_banner = forms.FloatField(widget=forms.HiddenInput(), required=False)
    width_banner = forms.FloatField(widget=forms.HiddenInput(), required=False)
    height_banner = forms.FloatField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = Creator
        fields = ('avatar', 'banner', 'stage_name', 'stage_name_en', 'theme_quote', 'profile_quote', 'x_banner',
                'y_banner', 'width_banner', 'height_banner', 'type')


class CreatorSettingForm(forms.ModelForm):
    class Meta:
        model = Creator
        fields = ('policy', 'trading', 'notification', 'noti_dayoff', 'before_delivery',
                  'hours', 'show_profile', 'slug', 'skills', 'is_assigneer', 'note_schedule', 'direct_contact')
        widgets = {
            'notification': forms.RadioSelect(),
            'show_profile': forms.RadioSelect(),
            'note_schedule': forms.Textarea(
                attrs={
                    'class': 'form-textarea, id: form-control, cols=40, rows=10',
                    'cols': "40",
                    'rows': "3",
                    'placeholder': '水曜日は、休日にしています。土曜日は、できれば仕事をしたいです。'
                }
            ),
            'direct_contact': forms.RadioSelect(),
        }

    def clean(self):
        list_keyword = ['index.html', 'about.html', 'about', 'topics.html', 'topics', 'service.html', 'service',
                        'ownershelp.html', 'ownershelp', 'ownershelp_200810_sendfile.html',
                        'ownershelp_200810_sendfile',
                        'partnerhelp.html', 'partnerhelp', 'privacypolicy.html', 'privacypolicy', 'termsofservice.html',
                        'termsofservice', 'updateinfo.html', 'updateinfo',
                        'tokushoho.html', 'tokushoho', 'scene', 'top', 'creators', 'home', 'introduction',
                        'admin_review', 'offer_creator', 'gallery', 'gallery.html',
                        'get_sale_content_download_link', 'get_product_file_download_link', 'get_list_admin',
                        'add_edit_admin_product', 'update_sale_content_index',
                        'get_list_random_samples', 'get_content_contract_modal', 'direct', 'product', 'accounts',
                        'project',
                        'products', 'sale_contents', 'projects', 'scenes', 'offers', 'offer', 'message', 'messages',
                        'messenger', 'messengers', 'mileages']
        super(CreatorSettingForm, self).clean()
        slug_name = self.cleaned_data.get('slug')
        if slug_name:
            if len(slug_name) < 2 or len(slug_name) > 50:
                self.add_error('slug', '入力したものが正しくありません。')
            else:
                creator = self.instance
                creator_slug = Creator.objects.filter(slug=slug_name).first()
                if creator_slug and creator != creator_slug:
                    self.add_error('slug', 'このリンクがすでに存在しています。')
                elif slug_name in list_keyword:
                    self.add_error('slug', 'このリンクが利用できません。')

    def save(self, *args, **kwargs):
        instance = super(CreatorSettingForm, self).save(*args, **kwargs)
        user = instance.user
        if instance.notification == 'immediately':
            user.setting_mail = 'now'
        elif instance.notification == 'one_day':
            user.setting_mail = 'on'
        elif instance.notification == 'off':
            user.setting_mail = 'off'
        user.noti_hours = instance.hours
        user.save()
        return instance


BlockListInlineForm = inlineformset_factory(
    Creator, BlockList, extra=20, can_delete=True,
    fields=('company_name', 'reason')
)


class CreatorSocialMediaForm(forms.ModelForm):
    class Meta:
        model = Creator
        fields = ('official_site', 'twitter_link', 'facebook_link', 'instagram_link')


class JoinUsForm(forms.ModelForm):
    last_name = forms.CharField(label='姓', max_length=30, required=True)
    first_name = forms.CharField(label='名', max_length=30, required=True)
    email = forms.EmailField(max_length=50, required=True, label='メールアドレス')
    question = forms.CharField(required=True)
    dob = forms.DateTimeField(input_formats=['%Y/%m/%d'])
    image = forms.ImageField(required=True, widget=forms.FileInput)
    x = forms.FloatField(widget=forms.HiddenInput(), required=False)
    y = forms.FloatField(widget=forms.HiddenInput(), required=False)
    width = forms.FloatField(widget=forms.HiddenInput(), required=False)
    height = forms.FloatField(widget=forms.HiddenInput(), required=False)
    youtube_link = forms.CharField(label='Youtube', max_length=200, required=False)

    class Meta:
        model = Creator
        fields = (
            # 'phone',
            'policy', 'official_site', 'twitter_link', 'facebook_link', 'instagram_link',
            'stage_name', 'stage_name_en', 'theme_quote', 'profile_quote', 'role_creator'
        )
        widgets = {
            'phone': forms.TextInput(attrs={'autocomplete': 'off', 'pattern': '[0-9]+', 'title': 'Enter Numbers Only ',
                                            'maxlength': '20', 'required': 'true'}),
            'policy': forms.Textarea(attrs={'class': 'form-textarea form-control'})
        }


class CreatorProfileUpdateAdminForm(forms.ModelForm):
    avatar = forms.ImageField(required=False, widget=forms.HiddenInput())
    banner = forms.ImageField(required=False, widget=forms.HiddenInput())
    stage_name = forms.CharField(widget=forms.TextInput(attrs={'disabled': 'true'}))
    stage_name_en = forms.CharField(widget=forms.TextInput(attrs={'disabled': 'true'}))
    theme_quote = forms.CharField(widget=forms.TextInput(attrs={'disabled': 'true'}))
    profile_quote = forms.CharField(widget=forms.TextInput(attrs={'disabled': 'true'}))
    type = forms.CharField(widget=forms.TextInput(attrs={'disabled': 'true'}))
    acoustic = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    sound = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    realistic = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    elegant = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    gender = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    key_note = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    age = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    low_high = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = Creator
        fields = ('avatar', 'banner', 'stage_name', 'stage_name_en', 'theme_quote', 'profile_quote', 'type')
