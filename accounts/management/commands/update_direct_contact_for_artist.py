from django.core.management.base import BaseCommand
from django.db.models import Sum

from accounts.models import ProductUser, AuthUser, Creator

# UPDATE LOGIC
# is_direct = ON ~ direct_contact = matching; is_direct=OFF ~ direct_contact = agent/production
# option1: Matching: ON (same as before)
# option2: Agent: OFF, usage = rank usage fee + 5% (new)
# option3: Production: OFF (same as before)

# UPDATE DATA


def update_direct_contact_for_artist():
    try:
        creators = Creator.objects.filter()
        creators.filter(is_direct=True).update(direct_contact=Creator.MATCHING)
        creators.filter(is_direct=False).update(direct_contact=Creator.PRODUCTION)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update direct contact for artist"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_direct_contact_for_artist()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
