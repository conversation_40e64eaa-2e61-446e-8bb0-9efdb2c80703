from django.core.management.base import BaseCommand

from accounts.models import AuthUser


def update_username_for_acc_inactive():
    try:
        users = AuthUser.objects.filter(is_active=False, role=AuthUser.MASTERCLIENT)
        for user in users:
            email = user.email
            user_name = user.username
            last_string = email[-11:]
            if last_string.startswith('_') and email[-10:].isnumeric() and not user_name.endswith(last_string):
                user_name += last_string
                print(user.pk)
                try:
                    user.username = user_name
                    user.save()
                except:
                    continue
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update user name for acc inactive"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_username_for_acc_inactive()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
