from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from app.models import Sale<PERSON>ontentVersion, SaleContent, SaleContentListWork, ListWork, SaleContentSelection
from accounts.models import Creator


def update_parent_sale_content():
    try:
        with transaction.atomic():
            creators = Creator.objects.filter(last_version__isnull=False)
            sale_contents = SaleContent.objects.filter(profile__creator__pk__in=creators.values_list('pk', flat=True))
            for sale_content in sale_contents:
                if sale_content.child.last():
                    SaleContentListWork.objects.filter(sale_content=sale_content.parent).update(
                        sale_content=sale_content.child.last())
                    SaleContentSelection.objects.filter(sale_content=sale_content.parent).update(
                        sale_content=sale_content.child.last())

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update hash tag from description to tag field for sale content"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_parent_sale_content()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
