from django.core.management.base import BaseCommand
from accounts.models import Auth<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HeaderBlock, CreatorItemBlock


def update_header_block():
    try:
        users = AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR)
        for user in users:
            creator = user.user_creator.first()
            last_creator_profile = creator.last_version
            last_public_creator_profile = creator.last_published_version
            item_block = None
            if last_public_creator_profile:
                public_block = HeaderBlock.objects.create(banner=last_public_creator_profile.banner,
                                                          key_visual_pc=last_public_creator_profile.banner,
                                                          type_header=HeaderBlock.HEADER_FULL_SCREEN)
                item_block = ItemBlock.objects.create(type_block=ItemBlock.HEADER_BLOCK,
                                                      header_block=public_block)
                CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile, item_block=item_block)
            if last_creator_profile:
                banner = last_creator_profile.banner
                if banner:
                    last_block = HeaderBlock.objects.create(banner=banner, key_visual_pc=banner,
                                                            type_header=HeaderBlock.HEADER_FULL_SCREEN)
                    last_item_block = ItemBlock.objects.create(type_block=ItemBlock.HEADER_BLOCK,
                                                               header_block=last_block)
                    CreatorItemBlock.objects.create(creator_profile=last_creator_profile, item_block=last_item_block)
                elif item_block:
                    CreatorItemBlock.objects.create(creator_profile=last_creator_profile, item_block=item_block)

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update header block"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_header_block()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
