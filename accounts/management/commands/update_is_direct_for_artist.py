from django.core.management.base import BaseCommand

from accounts.models import AuthUser, Creator

#update is_direct for artist has profile as toppage
def update_is_direct_for_artist():
    try:
        users = AuthUser.get_artist_landing_page()
        for user in users:
            if user.user_creator.exists():
                creator = user.user_creator.first()
                try:
                    creator.is_direct = True if creator.direct_contact == Creator.MATCHING and not creator.is_landing_page() else False
                    creator.save()
                except:
                    print('error:', creator.user.email)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update_is_direct_for_artist"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_is_direct_for_artist()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
