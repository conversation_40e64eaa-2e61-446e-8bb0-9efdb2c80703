from django.core.management.base import BaseCommand

from accounts.models import Auth<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CreatorItem<PERSON><PERSON>, StatementBlock, HeaderBlock, ProfileBlock


# update statement block
def update_statement_block():
    try:
        update_header_for_artist_not_active()
        update_profile_for_artist_not_active()
        users = AuthUser.objects.filter(role=AuthUser.CREATOR)
        for user in users:
            creator = user.user_creator.first()
            if creator:
                last_creator_profile = creator.last_version
                last_public_creator_profile = creator.last_published_version
                item_block = None
                if not last_public_creator_profile or last_public_creator_profile.blocks.filter(
                        item_block__type_block=ItemBlock.STATEMENT_BLOCK).exists():
                    continue
                if last_public_creator_profile:
                    theme_quote = last_public_creator_profile.theme_quote
                    public_block = StatementBlock.objects.create(theme_jp=theme_quote, is_show_avatar=True,
                                                                 is_show_name=True, is_show_title=True)
                    item_block = ItemBlock.objects.create(type_block=ItemBlock.STATEMENT_BLOCK,
                                                          statement_block=public_block)
                    CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile, item_block=item_block)
                if last_creator_profile:
                    theme_quote = last_creator_profile.theme_quote
                    if last_public_creator_profile.theme_quote != theme_quote:
                        last_block = StatementBlock.objects.create(theme_jp=theme_quote, is_show_avatar=True,
                                                                   is_show_name=True, is_show_title=True)
                        last_item_block = ItemBlock.objects.create(type_block=ItemBlock.STATEMENT_BLOCK,
                                                                   statement_block=last_block)
                        CreatorItemBlock.objects.create(creator_profile=last_creator_profile,
                                                        item_block=last_item_block)
                    elif item_block:
                        CreatorItemBlock.objects.create(creator_profile=last_creator_profile, item_block=item_block)
    except:
        print("Error! Try again!")


def update_header_for_artist_not_active():
    users = AuthUser.objects.filter(role=AuthUser.CREATOR)
    for user in users:
        creator = user.user_creator.first()
        if creator:
            last_public_creator_profile = creator.last_published_version
            if last_public_creator_profile and not last_public_creator_profile.blocks.filter(
                    item_block__type_block=ItemBlock.HEADER_BLOCK).exists():
                public_block = HeaderBlock.objects.create(type_header=HeaderBlock.HEADER_FULL_SCREEN)
                item_block = ItemBlock.objects.create(type_block=ItemBlock.HEADER_BLOCK,
                                                      header_block=public_block)
                CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile, item_block=item_block)
    return


def update_profile_for_artist_not_active():
    users = AuthUser.objects.filter(role=AuthUser.CREATOR)
    for user in users:
        creator = user.user_creator.first()
        if creator:
            last_public_creator_profile = creator.last_published_version
            if last_public_creator_profile and not last_public_creator_profile.blocks.filter(
                    item_block__type_block=ItemBlock.PROFILE_BLOCK).exists():
                public_block = ProfileBlock.objects.create()
                item_block = ItemBlock.objects.create(type_block=ItemBlock.PROFILE_BLOCK,
                                                      profile_block=public_block)
                CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile, item_block=item_block)
    return


class Command(BaseCommand):
    help = "update statement block"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_statement_block()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
