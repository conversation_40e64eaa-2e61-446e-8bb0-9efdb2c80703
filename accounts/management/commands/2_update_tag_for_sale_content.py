from django.core.management.base import BaseCommand
from django.db import transaction

from app.models import SaleContentVersion, SaleContent


def update_sale_content_version():
    try:
        with transaction.atomic():
            sale_contents = SaleContent.objects.filter()
            for sale_content in sale_contents:
                # Update show thumbnail for sale content
                last_version = sale_content.last_version
                last_published_version = sale_content.last_published_version
                if last_version:
                    show_thumbnail = 'color'
                    if last_version and last_version.image:
                        show_thumbnail = 'image'
                    elif last_version and last_version.default_thumbnail:
                        show_thumbnail = 'color'
                    elif last_published_version and last_published_version.image:
                        show_thumbnail = 'image'
                    elif last_published_version and last_published_version.default_thumbnail:
                        show_thumbnail = 'color'
                    last_version.show_thumbnail = show_thumbnail
                    SaleContentVersion.objects.filter(pk=last_version.pk).update(show_thumbnail=show_thumbnail)

                if last_published_version.image:
                    last_published_version.show_thumbnail = 'image'
                    SaleContentVersion.objects.filter(pk=last_published_version.pk).update(show_thumbnail='image')
                else:
                    last_published_version.show_thumbnail = 'color'
                    SaleContentVersion.objects.filter(pk=last_published_version.pk).update(show_thumbnail='color')
    except:
        print("Error! Try again!")


def get_tags_content_for_sale_content(sale_content):
    tags = sale_content.tags.order_by('tag_name')
    tags_content = ''
    for tag in tags:
        tags_content += f'#{tag}'
    return tags_content


class Command(BaseCommand):
    help = "update hash tag from description to tag field for sale content"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_sale_content_version()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
