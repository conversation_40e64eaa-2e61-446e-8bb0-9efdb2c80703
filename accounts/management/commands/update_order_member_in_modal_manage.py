
from django.core.management.base import BaseCommand
from django.db.models import Q, When, Case

from accounts.models import ProductUser, AuthUser
from app.models import Product


def update_order_member_in_modal_manage():
    try:
        projects = Product.objects.all()
        for project in projects:
            print(project.pk)
            project_users = project.productuser_set.filter(~Q(position=ProductUser.STAFF))
            directors = project_users.filter(user__role=AuthUser.CREATOR, position=ProductUser.DIRECTOR)
            owners = project_users.filter(user__role=AuthUser.MASTERCLIENT, position=ProductUser.OWNER)
            members = project_users.filter(user__role=AuthUser.MASTERCLIENT, position=ProductUser.REVIEWER)
            order_member(members)
            order_member(owners)
            master_producer = project_users.filter(position=ProductUser.PRODUCER, user__is_active=True,
                                                   is_super_producer=True)
            master_admins = None
            if not master_producer.exists():
                master_admins = project_users.filter(
                    Q(user__role=AuthUser.MASTERADMIN) | Q(position=ProductUser.PRODUCER)).order_by('-user__role')
            else:
                producers = project_users.filter(position=ProductUser.PRODUCER, user__is_active=True,
                                                 is_super_producer=False)
                list_admin_ids = list(master_producer.values_list('pk', flat=True)) + list(project_users.filter(
                    Q(user__role=AuthUser.MASTERADMIN)).values_list('pk', flat=True)) + list(producers.values_list('pk', flat=True))
                preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(list_admin_ids)])
                master_admins = project_users.filter(
                    Q(user__role=AuthUser.MASTERADMIN) | Q(position=ProductUser.PRODUCER)).filter(pk__in=list_admin_ids).order_by(preserved)
            order_member(master_admins)
            order_member(directors)
    except:
        print("Error! Try again!")


def order_member(members):
    for order, project_user in enumerate(members):
        project_user.order_user = order + 1
        project_user.save()


class Command(BaseCommand):
    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_order_member_in_modal_manage()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
