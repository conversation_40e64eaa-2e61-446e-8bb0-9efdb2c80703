from django.core.management.base import BaseCommand
from django.db.models import Sum

from accounts.models import ProductUser, AuthUser


# update rewarded
def update_rewarded_creator():
    try:
        product_users = ProductUser.objects.filter(user__is_active=True, user__role=AuthUser.CREATOR)
        product_users.update(rewarded=0)
        for pu in product_users:
            if pu.user.role == AuthUser.CREATOR:
                offers = pu.product.product_offers.filter(creator=pu.user).exclude(status__in=['1', '5'])
                if offers.exists():
                    total_offers = offers.aggregate(Sum('reward'))
                    total_offers = total_offers['reward__sum'] if total_offers['reward__sum'] else 0
                    pu.rewarded = total_offers
                    pu.save()
                offer_project = pu.product.offer_product.first()
                if pu.is_super_producer and offer_project.condition in ['3', '4', '5', '6']:
                    pu.rewarded += pu.product.total_budget
                    pu.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update rewarded creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_rewarded_creator()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
