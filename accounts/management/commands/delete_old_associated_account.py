from django.core.management.base import BaseCommand
from accounts.models import AuthUser
from social_django.models import UserSocialAuth

def delete_old_associated_account():
    try:
        user_socials = UserSocialAuth.objects.all()
        for user in user_socials:
            target = AuthUser.objects.filter(id=user.user_id, is_active=False)
            if target.exists():
                user.delete()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "delete_old_associated_account"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        delete_old_associated_account()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
