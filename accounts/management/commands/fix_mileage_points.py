from django.core.management.base import BaseCommand
from django.db.models import Sum, Q
from django.db import transaction

from accounts.models import AuthUser
from app.models import OfferCreator


class Command(BaseCommand):
    help = "Fix mileage points calculation for all artists"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without actually updating the database',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='Fix only specific user by ID',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        user_id = options.get('user_id')
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be saved"))
        
        # 対象ユーザーの取得
        if user_id:
            artists = AuthUser.objects.filter(pk=user_id, role=AuthUser.CREATOR)
            if not artists.exists():
                self.stdout.write(self.style.ERROR(f"User {user_id} not found or not a creator"))
                return
        else:
            artists = AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR)
        
        self.stdout.write(f"Processing {artists.count()} artists...")
        
        fixed_count = 0
        error_count = 0
        
        with transaction.atomic():
            for artist in artists:
                try:
                    old_balance_reward = artist.balance_reward
                    old_total_point = artist.total_point
                    
                    # オファーの集計
                    offers = OfferCreator.original_objects.filter(
                        Q(status='4') & Q(project__is_active=True) & (Q(admin=artist) | Q(creator=artist))
                    )
                    
                    # 支払った報酬（adminとして）
                    offer_admins = offers.filter(admin=artist)
                    total_offer_admins = offer_admins.aggregate(Sum('reward'))['reward__sum'] or 0
                    
                    # 受け取った報酬（creatorとして）
                    offer_creators = offers.filter(creator=artist)
                    total_offer_creators = offer_creators.aggregate(Sum('reward'))['reward__sum'] or 0
                    
                    # 新しい値の計算
                    new_balance_reward = total_offer_creators - total_offer_admins
                    new_total_point = new_balance_reward + artist.evaluation_point
                    
                    # 変更があった場合のみ更新
                    if old_balance_reward != new_balance_reward or old_total_point != new_total_point:
                        self.stdout.write(
                            f"\nUser: {artist.get_full_name()} (ID: {artist.pk})"
                        )
                        self.stdout.write(
                            f"  Old: balance_reward={old_balance_reward}, total_point={old_total_point}"
                        )
                        self.stdout.write(
                            f"  New: balance_reward={new_balance_reward}, total_point={new_total_point}"
                        )
                        self.stdout.write(
                            f"  Details: received={total_offer_creators}, paid={total_offer_admins}, "
                            f"evaluation={artist.evaluation_point}"
                        )
                        
                        if not dry_run:
                            artist.balance_reward = new_balance_reward
                            artist.total_point = new_total_point
                            artist.save()
                        
                        fixed_count += 1
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"\nError processing user {artist.pk}: {str(e)}")
                    )
                    error_count += 1
            
            if dry_run:
                # dry-runモードではロールバック
                transaction.set_rollback(True)
        
        # サマリー
        self.stdout.write("\n" + "="*50)
        self.stdout.write(f"Total artists processed: {artists.count()}")
        self.stdout.write(f"Fixed: {fixed_count}")
        self.stdout.write(f"Errors: {error_count}")
        self.stdout.write(f"Unchanged: {artists.count() - fixed_count - error_count}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("\nDRY RUN completed - no changes were saved")) 