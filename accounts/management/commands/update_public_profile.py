from django.core.management.base import BaseCommand
from django.db import transaction
import logging
from accounts.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Creator, CreatorProfile

from accounts.services import update_sale_content_in_list


def update_public_profile():
    try:
        creators = Creator.objects.filter(last_published_version__isnull=False,
                                          last_version__isnull=False)
        users = AuthUser.objects.filter(role=AuthUser.CREATOR, pk__in=creators.values_list('user__id', flat=True))
        with transaction.atomic():
            for user in users:
                creator = user.user_creator.first()
                creator_profile = creator.last_version
                last_public_creator_profile = creator.last_published_version
                if not creator_profile or not last_public_creator_profile:
                    continue
                if not creator_profile.owner or creator_profile.owner and creator_profile.owner.role == AuthUser.CREATOR:

                    # update last_published_version
                    creator.last_published_version = creator_profile
                    CreatorProfile.objects.filter(pk=creator_profile.pk).update(status='1')
                    CreatorProfile.objects.filter(pk=last_public_creator_profile.pk).update(status='3')
                    Creator.objects.filter(pk=creator.pk).update(last_version=None,
                                                                 last_published_version=creator_profile)

                    # update object footer
                    last_item_block = creator_profile.blocks.filter(
                        item_block__type_block=ItemBlock.FOOTER_BLOCK).first()
                    if last_item_block:
                        last_item_block.item_block.footer_block.item_social.all().update(parent=None)
                        last_item_block.item_block.footer_block.item_footer.all().update(parent=None)

                    # update sale content
                    sale_contents = creator_profile.content_profile.all()
                    for sale_content in sale_contents:
                        update_sale_content_in_list(sale_content.parent, sale_content)
                        sale_content.parent = None
                        sale_last_version = sale_content.last_version
                        if sale_last_version:
                            sale_content.last_published_version = sale_last_version
                            sale_content.last_version = None
                        sale_content.save()
                else:
                    # update last_published_version
                    CreatorProfile.objects.filter(pk=creator_profile.pk).update(status='3')
                    Creator.objects.filter(pk=creator.pk).update(last_version=None)

                    # update object footer
                    creator_profile.blocks.filter(item_block__type_block=ItemBlock.FOOTER_BLOCK).delete()

                    # update sale content
                    sale_contents = creator_profile.content_profile.all()
                    for sale_content in sale_contents:
                        parent_sale = sale_content.parent
                        if parent_sale:
                            update_sale_content_in_list(sale_content, parent_sale)
                            parent_sale.last_version = None
                        sale_content.parent = None
                        sale_content.last_version = None
                        sale_content.save()
        return
    except Exception as e:
        print(e)
        logging.error(e)


class Command(BaseCommand):
    help = "update statement block"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_public_profile()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
