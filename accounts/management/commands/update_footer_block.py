from django.core.management.base import BaseCommand
from accounts.models import AuthUser, <PERSON><PERSON><PERSON><PERSON>, CreatorItem<PERSON><PERSON>, FooterBlock, ItemSocial

LIST_FIELD_FOOTER = ['official_site', 'facebook_link', 'twitter_link', 'instagram_link', 'youtube_link']


def update_footer_block():
    try:
        users = AuthUser.objects.filter(role=AuthUser.CREATOR)
        for user in users:
            creator = user.user_creator.first()
            if not creator:
                continue
            if not user.is_active and not creator.is_invited:
                continue
            last_creator_profile = creator.last_version
            last_public_creator_profile = creator.last_published_version
            if not last_public_creator_profile:
                continue
            item_block = None
            if last_public_creator_profile:
                public_block = FooterBlock.objects.create()
                count_link = 0
                for key in LIST_FIELD_FOOTER:
                    current_value = getattr(last_public_creator_profile, key)
                    if current_value:
                        key = covert_social_link(key)
                        ItemSocial.objects.create(footer_block=public_block, url=current_value, type_social_link=key,
                                                  order=count_link)
                        count_link += 1

                item_block = ItemBlock.objects.create(type_block=ItemBlock.FOOTER_BLOCK,
                                                      footer_block=public_block)
                CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile, item_block=item_block)
            if last_creator_profile:
                has_change = False
                for key in LIST_FIELD_FOOTER:
                    if getattr(last_public_creator_profile, key) != getattr(last_creator_profile, key):
                        has_change = True
                        break
                if has_change:
                    last_block = FooterBlock.objects.create()
                    count = 0
                    for key in LIST_FIELD_FOOTER:
                        new_value = getattr(last_creator_profile, key)
                        if new_value:
                            key = covert_social_link(key)
                            new_item = ItemSocial.objects.create(footer_block=last_block, url=new_value,
                                                                 type_social_link=key, order=count)
                            parent_item = ItemSocial.objects.filter(footer_block=last_block,
                                                                    type_social_link=key).first()
                            if not parent_item:
                                parent_item = new_item
                            new_item.parent = parent_item
                            new_item.save()
                            count += 1
                    item_block = ItemBlock.objects.create(type_block=ItemBlock.FOOTER_BLOCK,
                                                          footer_block=last_block)
                    CreatorItemBlock.objects.create(creator_profile=last_creator_profile, item_block=item_block)
                elif item_block:
                    CreatorItemBlock.objects.create(creator_profile=last_creator_profile, item_block=item_block)
    except:
        print("Error! Try again!")


def covert_social_link(key):
    if key == 'twitter_link':
        return 'twitter'
    elif key == 'facebook_link':
        return 'fb'
    elif key == 'instagram_link':
        return 'insta'
    elif key == 'youtube_link':
        return 'youtube'
    return 'home'


class Command(BaseCommand):
    help = "update footer block"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_footer_block()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
