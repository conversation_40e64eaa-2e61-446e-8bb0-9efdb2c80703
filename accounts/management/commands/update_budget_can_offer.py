from django.core.management.base import BaseCommand
from django.db.models import Q, Case, When, Sum, F, Prefetch

from accounts.models import ProductUser, AuthUser

# update available budget for artist
from app.models import OfferCreator
from app.services import update_budget_offer


def update_budget_can_offer():
    try:
        product_users = ProductUser.objects.filter(user__role=AuthUser.CREATOR, product__is_active=True)
        update_budget_offer(product_users)

        users = AuthUser.objects.filter(role=AuthUser.CREATOR)
        for user in users:
            # update balance_expected = offer nhận in progress (2, 3) - offer đi in progress (1,2,3)
            balance_expected = 0
            offers_inprogress = OfferCreator.original_objects.filter(
                (Q(status__in=['1', '2', '3']) & Q(admin=user)) | (
                        Q(status__in=['2', '3']) & Q(creator=user))).exclude(Q(payment_status=True) |
                                                                             Q(project__is_active=False))

            balance_expected = offers_inprogress.aggregate(total_reward=Sum(
                Case(When(admin=user, then=-1 * F('reward')),
                     default='reward'))).get('total_reward', 0) if offers_inprogress else 0
            user.balance_expected = balance_expected

            # update balance_available = tổng các task checked hiện thị khi toggle OFF
            product_users = user.productuser_set.filter(product__is_active=True)
            balance_available = 0
            for product_user in product_users:
                current_user = product_user.user
                project = product_user.product
                offers = OfferCreator.original_objects.filter(project=project).filter(
                    (Q(status=OfferCreator.STATUS_COMPLETED) & Q(admin=current_user)) | (
                            Q(status=OfferCreator.STATUS_COMPLETED) & Q(creator=current_user))).exclude(
                    (Q(payment_status=True) & Q(creator_payment_request_id__isnull=True) & Q(
                        admin_payment_request_id__isnull=True)) | (Q(payment_status=True) & Q(creator=current_user)) | \
                    (Q(admin=current_user) & Q(admin_payment_request_id__isnull=False)) | \
                    (Q(creator=current_user) & Q(creator_payment_request_id__isnull=False)))
                budget_offer = offers.aggregate(total_reward=Sum(
                    Case(When(admin=current_user, then=-1 * F('reward')),
                         default='reward'))).get('total_reward', 0) if offers else 0
                balance_available += budget_offer
            user.balance_available = balance_available
            user.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update budget can offer for artist"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_budget_can_offer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
