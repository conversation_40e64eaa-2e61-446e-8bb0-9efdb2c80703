from django.core.management.base import BaseCommand
from django.db.models import Sum

from accounts.models import Auth<PERSON><PERSON>, It<PERSON><PERSON>lock, ProfileBlock, CreatorItemBlock


# update profile block
def update_profile_block():
    try:
        users = AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR)
        for user in users:
            creator = user.user_creator.first()
            last_creator_profile = creator.last_version
            last_public_creator_profile = creator.last_published_version
            item_block = None
            if last_public_creator_profile:
                content = last_public_creator_profile.profile_quote
                public_block = ProfileBlock.objects.create(section_name_jp='Profile', content_jp=content)
                item_block = ItemBlock.objects.create(type_block=ItemBlock.PROFILE_BLOCK,
                                                      profile_block=public_block)
                CreatorItemBlock.objects.create(creator_profile=last_public_creator_profile, item_block=item_block)
            if last_creator_profile:
                content = last_creator_profile.profile_quote
                if last_public_creator_profile.profile_quote != content:
                    last_block = ProfileBlock.objects.create(section_name_jp='Profile', content_jp=content)
                    last_item_block = ItemBlock.objects.create(type_block=ItemBlock.PROFILE_BLOCK,
                                                               profile_block=last_block)
                    CreatorItemBlock.objects.create(creator_profile=last_creator_profile, item_block=last_item_block)
                elif item_block:
                    CreatorItemBlock.objects.create(creator_profile=last_creator_profile, item_block=item_block)


    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update profile block"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_profile_block()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
