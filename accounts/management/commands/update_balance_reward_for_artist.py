from django.core.management.base import BaseCommand
from django.db.models import Sum, Q

from accounts.models import ProductUser, AuthUser
from app.models import OfferCreator


# update rewarded
# offer nhan - offer di + (offer owner - offer usage fee) <=> offer nhan - offer di (offer accepted)
def update_balance_reward_for_artist():
    try:
        artists = AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR)
        artists.update(balance_reward=0, total_point=0)
        for artist in artists:
            offers = OfferCreator.original_objects.filter(
                Q(status='4') & Q(project__is_active=True) & (Q(admin=artist) | Q(creator=artist)))
            if offers:
                offer_admins = offers.filter(admin=artist)
                total_offer_admins = offer_admins.aggregate(Sum('reward'))
                total_offer_admins = total_offer_admins['reward__sum'] if total_offer_admins['reward__sum'] else 0
                offer_creators = offers.filter(creator=artist)
                total_offer_creators = offer_creators.aggregate(Sum('reward'))
                total_offer_creators = total_offer_creators['reward__sum'] if total_offer_creators['reward__sum'] else 0
                artist.balance_reward = total_offer_creators - total_offer_admins
                artist.total_point = total_offer_creators - total_offer_admins + artist.evaluation_point
                artist.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update balance reward creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_balance_reward_for_artist()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
