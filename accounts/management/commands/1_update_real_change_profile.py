
from django.core.management.base import BaseCommand
from django.db import transaction

from accounts.models import Creator, CreatorProfile
from app.models import SaleContentVersion


def update_artist_info():
    with transaction.atomic():
        try:
            # Update sale content
            # SaleContentVersion.objects.update(sale_type='4')

            # check real change sale content
            creators = Creator.objects.filter(last_version__isnull=False, last_published_version__isnull=False)
            list_key = ["official_site", "twitter_link", "facebook_link", "instagram_link", "youtube_link",
                        "theme_quote", "profile_quote"]
            for creator in creators:
                last_profile = creator.last_version
                public_profile = creator.last_published_version
                real_change = False
                if last_profile.banner:
                    real_change = True
                    break
                for key in list_key:
                    current_value = getattr(public_profile, key)
                    new_value = getattr(last_profile, key)
                    if current_value != new_value:
                        real_change = True
                        break

                last_sale_contents = last_profile.content_profile.all()
                public_sale_contents = last_profile.content_profile.all()
                if last_sale_contents.count() != public_sale_contents.count():
                    real_change = True
                else:
                    for sale_content in public_sale_contents:
                        if sale_content.last_version:
                            real_change = True
                            break
                        audios = sale_content.last_published_version.album.all()
                        for audio in audios:
                            if audio.last_version:
                                real_change = True
                                break
                if real_change is False:
                    if not last_profile.banner and public_profile.banner:
                        CreatorProfile.objects.filter(pk=last_profile.pk).update(banner=public_profile.banner,
                                                                                 x_banner=public_profile.x_banner,
                                                                                 y_banner=public_profile.y_banner,
                                                                                 height_banner=public_profile.height_banner,
                                                                                 width_banner=public_profile.width_banner,
                                                                                 banner_resized=public_profile.banner_resized)
                    CreatorProfile.objects.filter(pk=public_profile.pk).update(status='3')
                    CreatorProfile.objects.filter(pk=last_profile.pk).update(status='1')
                    Creator.objects.filter(pk=creator.pk).update(last_version=None)
                    Creator.objects.filter(pk=creator.pk).update(last_published_version=last_profile)

        except:
            print("Error! Try again!")


class Command(BaseCommand):
    help = "update data for aritst"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_artist_info()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
