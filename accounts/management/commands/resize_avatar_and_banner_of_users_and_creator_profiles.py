from django.core.management.base import BaseCommand

from accounts.models import AuthUser, CreatorProfile
from accounts.tasks import update_medium_small_avatar, update_banner_resized


def resize_avatar_of_user():
    user_ids = AuthUser.objects.filter(is_active=True).values_list('id', flat=True)
    for user_id in user_ids:
        update_medium_small_avatar.delay(user_id)
    return len(user_ids)


def resize_banner_of_creator_profile():
    creator_profile_ids = CreatorProfile.objects.filter(published_profile__isnull=False).values_list('id', flat=True)
    for creator_id in creator_profile_ids:
        update_banner_resized.delay(creator_id)
    return len(creator_profile_ids)


class Command(BaseCommand):
    help = 'Set back job to resize avatar and banner of users and creator profiles'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start set back job to resize__________')
        num_users = resize_avatar_of_user()
        self.stdout.write(f'=============> Set {num_users} done for {AuthUser.__name__}')
        num_creator_profiles = resize_banner_of_creator_profile()
        self.stdout.write(f'=============> Set {num_creator_profiles} done for {CreatorProfile.__name__}')
        self.stdout.write(self.style.SUCCESS('__________set done!__________'))
