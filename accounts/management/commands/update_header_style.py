from django.core.management.base import BaseCommand
from accounts.models import AuthUser, <PERSON>er<PERSON><PERSON>

def update_header_block():
    try:
        users = AuthUser.objects.filter(role=AuthUser.CREATOR)
        for user in users:
            print(str(user.pk))
            creator = user.user_creator.first()
            if not creator:
                continue
            if not user.is_active and not creator.is_invited:
                continue
            last_creator_profile = creator.last_version
            last_public_creator_profile = creator.last_published_version
            if not last_public_creator_profile:
                continue
            public_header_block = None
            if last_public_creator_profile:
                public_header_block = last_public_creator_profile.get_block_header()
                if not public_header_block.banner and not public_header_block.key_visual_pc and \
                        not public_header_block.key_visual_sp:
                    public_header_block.type_header = HeaderBlock.HEADER_BANNER
                    public_header_block.save()

            if last_creator_profile:
                last_header_block = last_creator_profile.get_block_header()
                if last_header_block != last_public_creator_profile:
                    if not last_header_block.banner and not last_header_block.key_visual_pc and \
                            not last_header_block.key_visual_sp:
                        last_header_block.type_header = HeaderBlock.HEADER_BANNER
                        last_header_block.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update type style for header block"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_header_block()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
