# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-27 15:48+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: accounts/forms.py:196
msgid "Password"
msgstr ""

#: accounts/forms.py:198
msgid ""
"Raw passwords are not stored, so there is no way to see this user's "
"password, but you can change the password using <a href=\"../password/"
"\">this form</a>."
msgstr ""

#: accounts/templates/accounts/_member_item.html:13
#: accounts/templates/accounts/_member_item.html:15
#: accounts/templates/accounts/_member_item.html:17
msgid "Change"
msgstr "変更"

#: accounts/templates/accounts/_member_item.html:21
#: accounts/templates/accounts/_member_item.html:27
msgid "delete"
msgstr "削除"

#: accounts/templates/accounts/account_info.html:53
msgid "Account information"
msgstr "アカウント情報"

#: accounts/templates/accounts/account_info.html:79
msgid "Register image"
msgstr "画像を登録"

#: accounts/templates/accounts/account_info.html:93
msgid "To register"
msgstr "登録する"

#: accounts/templates/accounts/account_info.html:111
#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:28
msgid "Artist name"
msgstr "アーティスト名"

#: accounts/templates/accounts/account_info.html:112
msgid "Let's set the stage name and nickname to be written on the work credit."
msgstr "作品クレジットに表記する芸名やニックネームを設定しましょう。"

#: accounts/templates/accounts/account_info.html:127
#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:42
msgid "Title account"
msgstr "タイトル"

#: accounts/templates/accounts/account_info.html:128
msgid "Explain your role in SOREMO."
msgstr "SOREMOでのあなたの役割を説明しましょう。"

#: accounts/templates/accounts/account_info.html:143
#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:55
msgid "Organization name"
msgstr "組織名"

#: accounts/templates/accounts/account_info.html:151
msgid "Showing name"
msgstr "表示名"

#: accounts/templates/accounts/account_info.html:152
msgid "You can freely set stage names and nicknames."
msgstr "芸名やニックネームを自由に設定できます。"

#: accounts/templates/accounts/account_info.html:159
msgid "Job title / charge"
msgstr "役職・担当"

#: accounts/templates/accounts/account_info.html:168
msgid "Company name / URL"
msgstr "会社名・URL"

#: accounts/templates/accounts/account_info.html:289
msgid "Concluded"
msgstr "締結済"

#: accounts/templates/accounts/account_info.html:528
#: accounts/templates/accounts/creator/creator_index.html:276
#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:63
#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:126
#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:183
#: accounts/templates/accounts/password_reset_form.html:226
msgid "cancel"
msgstr "キャンセル"

#: accounts/templates/accounts/creator/creator_index.html:271
msgid "Do you really want to delete this?"
msgstr ""

#: accounts/templates/accounts/creator/creator_index.html:277
msgid "yes"
msgstr ""

#: accounts/templates/accounts/creator/creator_setting.html:47
msgid "Description heading"
msgstr "あなただけのワークスタイルを自在にデザインしましょう。"

#: accounts/templates/accounts/creator/creator_setting.html:58
msgid "Heading trade-off slide"
msgstr "トレードオフスライダー"

#: accounts/templates/accounts/creator/creator_setting.html:59
msgid "Description trade-off slide"
msgstr ""
"オファーを増やしたいときは「左」、条件をよくしていきたいときは「右」へ。こま"
"めに今の希望を調整しましょう。"

#: accounts/templates/accounts/creator/creator_setting.html:71
msgid "Trade-off left"
msgstr "オファーを増やしたい"

#: accounts/templates/accounts/creator/creator_setting.html:72
msgid "Trade-off right"
msgstr "条件を良くしたい"

#: accounts/templates/accounts/creator/creator_setting.html:82
msgid "Description policy"
msgstr "お仕事の選択の基準を伝えましょう。"

#: accounts/templates/accounts/creator/creator_setting.html:95
msgid "Description schedule"
msgstr "あなたの予定を伝えて、期日を配慮してもらいましょう。"

#: accounts/templates/accounts/creator/creator_setting.html:122
msgid "Switch label"
msgstr "アーティストからの再々委託も請ける"

#: accounts/templates/accounts/creator/creator_setting.html:126
msgid "Description switch label"
msgstr ""
"※ONにすると、利用設定（トレードオフスライダー、クライテリア、スケジュール）が"
"アーティストにも開示されます。了承の上、ONにしてください。"

#: accounts/templates/accounts/creator/creator_setting.html:141
msgid "URL"
msgstr "覚えやすい固有のURLを設定し、QRコードを生成しましょう。"

#: accounts/templates/accounts/creator/creator_setting.html:164
msgid "Description security"
msgstr "プロフィールとサンプルの公開範囲をコントロールしましょう。"

#: accounts/templates/accounts/creator/creator_setting.html:236
msgid "Description notification"
msgstr "オファーやメッセージのメール通知タイミングを設定できます。"

#: accounts/templates/accounts/creator/creator_setting.html:281
msgid "Description block list"
msgstr "御取引しない会社・ユーザー名を設定ください。"

#: accounts/templates/accounts/creator/creator_setting.html:290
msgid "Label company name"
msgstr "会社名・ユーザー名"

#: accounts/templates/accounts/creator/creator_setting.html:299
msgid "Label reason"
msgstr "理由（任意)"

#: accounts/templates/accounts/creator/creator_setting.html:321
msgid "Add block list"
msgstr "ブロックリストを追加"

#: accounts/templates/accounts/creator/creator_setting.html:343
msgid "Note"
msgstr ""
"※追加更新するには、作品をプロフィールサイトにアップロードし、キュレーターの承"
"認を得る必要があります。"

#: accounts/templates/accounts/curator_setting/_account_management_account_info.html:10
#: accounts/templates/accounts/list.html:58
msgid "Artist"
msgstr "アーティスト"

#: accounts/templates/accounts/curator_setting/_account_management_account_info.html:12
msgid "Trading performance"
msgstr "取引実績"

#: accounts/templates/accounts/curator_setting/_account_management_account_info.html:13
msgid "Consideration results"
msgstr "対価実績"

#: accounts/templates/accounts/curator_setting/_account_management_list_invited.html:14
msgid "Cancel the invitation"
msgstr "招待を取り消す"

#: accounts/templates/accounts/curator_setting/_account_management_list_invited.html:15
msgid "Resend invitation"
msgstr "招待を再送信"

#: accounts/templates/accounts/curator_setting/_account_management_search_artist.html:11
#, fuzzy
#| msgid "Artist name"
msgid "Search by artist name"
msgstr "アーティスト名"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:12
#: accounts/templates/accounts/curator_setting/account_management.html:26
msgid "Invite an artist"
msgstr "アーティストを招待"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:15
msgid "Enter the information of the artist you want to invite."
msgstr "招待したいアーティストの情報を入力しましょう。"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:22
#: accounts/templates/accounts/list.html:70
msgid "email address"
msgstr "メールアドレス"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:65
#, fuzzy
#| msgid "Resend invitation"
msgid "Send invitation email"
msgstr "招待を再送信"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:79
msgid "Upload NDA"
msgstr "NDAをアップロード"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:82
msgid "Upload and share your contract with the artist you've signed."
msgstr "締結したアーティストとの契約書をアップロードして共有しましょう。"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:144
msgid "Approval of identity verification documents"
msgstr "本人確認書類の承認"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:147
msgid ""
"Check and approve the content, or email the artist why and what to do about "
"the disapproval."
msgstr ""
"内容を確認して承認するか、非承認の理由と対策をアーティストにメールしましょ"
"う。"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:160
msgid "Not approve"
msgstr "承認しない"

#: accounts/templates/accounts/curator_setting/_account_management_setting_modal.html:162
msgid "recognize"
msgstr "承認"

#: accounts/templates/accounts/curator_setting/account_management.html:32
msgid "In reception"
msgstr "招待中"

#: accounts/templates/accounts/list.html:51
msgid "Administrator"
msgstr "管理者"

#: accounts/templates/accounts/list.html:53
msgid "Owner"
msgstr "オーナー"

#: accounts/templates/accounts/list.html:55
msgid "Curator"
msgstr "キュレーター"

#: accounts/templates/accounts/list.html:60
msgid "Add account"
msgstr "アカウント追加"

#: accounts/templates/accounts/list.html:68
msgid "User name"
msgstr "ユーザーネーム"

#: accounts/templates/accounts/list.html:69
msgid "Full name"
msgstr "氏名"

#: accounts/templates/accounts/list.html:71
msgid "Registered Date"
msgstr "登録日時"

#: accounts/templates/accounts/login.html:183
msgid "remember me"
msgstr "サインインしたままにする"

#: accounts/templates/accounts/login.html:187
msgid "forgot password link"
msgstr "パスワードを忘れた場合は"

#: accounts/templates/accounts/login.html:189
msgid "forgot password link1"
msgstr "こちら"

#: accounts/templates/accounts/login.html:200
#: accounts/templates/accounts/signup.html:187
msgid "Text join social"
msgstr "or"

#: accounts/templates/accounts/login.html:200
#: accounts/templates/accounts/signup.html:187
msgid "Text join social1"
msgstr "SNSアカウントで情報を簡単入力"

#: accounts/templates/accounts/login.html:215
msgid "register link"
msgstr "アカウントをお持ちでない場合は"

#: accounts/templates/accounts/login.html:217
msgid "register link1"
msgstr "サインアップ"

#: accounts/templates/accounts/login.html:217
msgid "register link2"
msgstr "へ"

#: accounts/templates/accounts/password_reset_confirm.html:154
#: accounts/templates/accounts/password_reset_form.html:202
msgid "Forgot pass confirm title"
msgstr "パスワード再設定"

#: accounts/templates/accounts/password_reset_confirm.html:159
msgid "Placeholder new password"
msgstr "パスワード"

#: accounts/templates/accounts/password_reset_confirm.html:165
msgid "Placeholder confirm password"
msgstr "パスワード（確認）"

#: accounts/templates/accounts/password_reset_confirm.html:169
msgid "With the start of use"
msgstr "利用開始をもって"

#: accounts/templates/accounts/password_reset_confirm.html:169
msgid "terms of service"
msgstr "利用規約"

#: accounts/templates/accounts/password_reset_confirm.html:170
msgid "privacy policy"
msgstr "プライバシーポリシー"

#: accounts/templates/accounts/password_reset_confirm.html:170
msgid "It is considered that you have agreed to"
msgstr "に同意したものとみなします"

#: accounts/templates/accounts/payment_info.html:135
msgid "Add a new card"
msgstr "新しいカードを追加"

#: accounts/templates/accounts/payment_info.html:288
msgid "Register"
msgstr "登録"

#: accounts/views.py:448 accounts/views.py:451
msgid "You do not have permission to change your password."
msgstr "パスワード変更権限がありません。"

#: accounts/views.py:457
msgid "Password is required."
msgstr "パスワードは必須です。"

#: accounts/views.py:459
msgid "The current password is incorrect."
msgstr "現在のパスワードが正しくありません。"

#: accounts/views.py:468
msgid "I updated my password."
msgstr "パスワードを更新しました。"

#: accounts/views.py:473
msgid "The new password and the confirmation password are not the same."
msgstr "新しいパスワードと確認パスワードが一致しません。"

#: accounts/views.py:475
msgid "Something went wrong!"
msgstr "エラーが発生しました"

#~ msgid "Direct transaction"
#~ msgstr "直取引"

#~ msgid "Use material"
#~ msgstr "利用料"

#~ msgid "Registration date"
#~ msgstr "登録日"

#~ msgid "update"
#~ msgstr "更新"
