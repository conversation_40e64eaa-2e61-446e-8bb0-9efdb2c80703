from channels.generic.websocket import AsyncWebsocketConsumer
from django.conf import settings
import json
import redis
class UserConsumer(AsyncWebsocketConsumer):

    async def online_user(self):
        user = self.scope['user']
        user_id = user.pk
        users_rd = self.redis_instance.get("user_online")
        if users_rd and users_rd.decode('UTF-8'):
            list_rd = users_rd.decode('UTF-8').split(",")
            self.dict_online = list_rd
        if user.is_authenticated:
            self.dict_online.append(str(user_id))
        list_online = ','.join(str(x) for x in self.dict_online)
        self.redis_instance.set("user_online", list_online)
        content = {
            'command': 'user_online',
            'on_ids': list_online
        }
        await self.send_status(content)

    async def offline_user(self):
        user = self.scope['user']
        user_id = user.pk

        users_rd = self.redis_instance.get("user_online")
        list_rd = users_rd.decode('UTF-8').split(",")
        list_rd.remove(str(user_id))
        list_online = ','.join(str(x) for x in list_rd)
        self.redis_instance.set("user_online", list_online)
        content = {
            'command': 'user_online',
            'on_ids': list_online
        }
        await self.send_status(content)

    async def connect(self):
        self.dict_online = []
        user = self.scope['user']
        self.redis_instance = redis.StrictRedis(host=settings.REDIS_HOST,
                                                port=settings.REDIS_PORT, db=0)

        await self.channel_layer.group_add("users", self.channel_name)
        if user.is_authenticated:
            await self.accept()
            await self.online_user()
        else:
            self.disconnect()


    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard("users", self.channel_name)
        await self.offline_user()

    async def receive(self, text_data=None, bytes_data=None):
        data = json.loads(text_data)

    async def send_status(self, message):
        await self.channel_layer.group_send("users",
            {
                "type": "user_status",
                "message": message
            }
        )
    async def user_status(self, event):
        message = event['message']
        await self.send(text_data=json.dumps(message))


