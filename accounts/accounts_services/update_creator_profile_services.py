import datetime
import io
import re
import json

from PIL import Image
import os
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Q

from accounts.models import ItemSocial, ItemFooterMenu, ItemBlock, CreatorItemBlock, FooterBlock
from app.app_services.topic_artist_services import prefetch_topics, LOAD_MORE_LIST
from app.models import TopicGallery, SaleContent, SaleContentVersion, AlbumVariation, AlbumVersion, HashTag, \
    SaleContentTag

LIST_STATEMENT_BLOCK = ['is_show_avatar', 'is_show_name', 'is_show_title', 'theme_jp', 'theme_en']
LIST_FIELD_HEADER = ['type_header', 'catchphrase_jp_1', 'catchphrase_jp_2', 'catchphrase_jp_3', 'catchphrase_en_1', \
                     'catchphrase_en_2', 'catchphrase_en_3', 'artist_name_jp', 'artist_name_en', 'title_jp', 'title_en',
                     'display_tag_line_1', 'display_tag_line_2']
LIST_FIELD_HEADER_IMAGE = ['logo', 'banner', 'key_visual_sp', 'key_visual_pc']
LIST_ITEM_MENU = ['title_jp', 'title_en', 'url', 'order']
LIST_ITEM_SOCIAL_LINK = ['type_social_link', 'url', 'order']


def check_edit_footer(profile, content_edited):
    footer_block = profile.get_block_footer()
    if content_edited.get('copyright') != footer_block.copyright:
        return 1
    # check delete item
    if content_edited.get('list_delete_item_menu', []):
        return 1
    if content_edited.get('list_delete_item_social', []):
        return 1
    if content_edited.get('list_delete_item_sub', []):
        return 1

    # check edit item footer menu
    content_menu_footer = content_edited.get('item_menu')
    for data_item_menu in content_menu_footer:
        change_type = data_item_menu.get('type')
        if change_type == 'new':
            return 1
        elif change_type == 'edited':
            if check_real_edit_item_menu(data_item_menu) == 1:
                return 1

    # check edit social link
    content_social_link = content_edited.get('social_link')
    for data_social_link in content_social_link:
        change_type = data_social_link.get('type')
        if change_type == 'new':
            return 1
        elif change_type == 'edited':
            if check_real_edit_social_link(data_social_link) == 1:
                return 1
    return 0


def check_real_edit_item_menu(data_item_menu):
    item_id = data_item_menu.get('id')
    item_menu = ItemFooterMenu.objects.filter(pk=item_id).first()
    if not item_menu:
        return 0
    for (key, value) in data_item_menu.items():
        if key in LIST_ITEM_MENU:
            if key == 'order':
                value = int(value)
            if value != getattr(item_menu, key):
                return 1
    return 0


def check_real_edit_social_link(data_social_link):
    item_id = data_social_link.get('id')
    item_social = ItemSocial.objects.filter(pk=item_id).first()
    if not item_social:
        return 0
    for (key, value) in data_social_link.items():
        if key in LIST_ITEM_SOCIAL_LINK:
            if key == 'order':
                value = int(value)
            if value != getattr(item_social, key):
                return 1
    return 0


def update_edit_footer_block(public_profile, creator_profile, dict_attribute, has_last_version):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.FOOTER_BLOCK).last().item_block
    dict_attribute = dict_attribute.get('footer')
    last_block = None
    if has_last_version:
        last_block = creator_profile.blocks.filter(item_block__type_block=ItemBlock.FOOTER_BLOCK).last().item_block
        item_block = last_block
    else:
        item_block = public_block
    count_change = 0 if not dict_attribute else check_edit_footer_block(item_block, dict_attribute)
    current_footer = item_block.footer_block
    if count_change < 1:
        if not has_last_version:
            CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
        return
    if not has_last_version or has_last_version and last_block == public_block:
        if last_block:
            creator_profile.blocks.filter(item_block__type_block=ItemBlock.FOOTER_BLOCK).delete()
        footer_block = FooterBlock.objects.create()
        item_block = ItemBlock.objects.create(type_block=ItemBlock.FOOTER_BLOCK, footer_block=footer_block)
        CreatorItemBlock.objects.create(creator_profile=creator_profile, item_block=item_block)
    footer_block = item_block.footer_block

    # update copyright
    footer_block.copyright = dict_attribute.get('copyright')

    # update item menu
    list_item_menu_edited = []
    content_menu_footer = dict_attribute.get('item_menu')
    list_item_menu_deleted = dict_attribute.get('list_delete_item_menu', []) + dict_attribute.get(
        'list_delete_item_sub', [])
    list_item_menu_deleted = remove_element_in_list_delete(list_item_menu_deleted)
    ItemFooterMenu.objects.filter(pk__in=list_item_menu_deleted, parent__isnull=False).delete()

    for data_item_menu in content_menu_footer:
        key = data_item_menu.get('id')
        if data_item_menu.get('type') != 'deleted':
            list_item_menu_edited.append(key)

    item_menus = current_footer.item_footer.filter()
    for item_menu in item_menus:
        if str(item_menu.pk) not in list_item_menu_edited and str(item_menu.pk) not in list_item_menu_deleted:
            clone_item_menu(item_menu, footer_block)

    for data_item_menu in content_menu_footer:
        change_type = data_item_menu.get('type')
        if change_type == 'new':
            create_new_item_menu(footer_block, data_item_menu)
        elif change_type == 'edited':
            item_id = data_item_menu.get('id')
            if not item_id.isnumeric():
                continue
            item_menu = ItemFooterMenu.objects.filter(pk=item_id).first()
            if item_menu and check_real_edit_item_menu(data_item_menu) == 1:
                update_attr_item_menu(footer_block, item_menu, data_item_menu)

    # update item social
    list_item_social_edited = []
    list_item_social_deleted = dict_attribute.get('list_delete_item_social', [])
    list_item_social_deleted = remove_element_in_list_delete(list_item_social_deleted)
    ItemSocial.objects.filter(pk__in=list_item_social_deleted, parent__isnull=False).delete()
    content_social_footer = dict_attribute.get('social_link')
    for data_item_social in content_social_footer:
        key = data_item_social.get('id')
        if data_item_social.get('type') != 'deleted':
            list_item_social_edited.append(key)

    item_socials = current_footer.item_social.filter()
    for item_social in item_socials:
        if str(item_social.pk) not in list_item_social_edited and str(item_social.pk) not in list_item_social_deleted:
            clone_item_menu(item_social, footer_block)

    for data_item_social in content_social_footer:
        change_type = data_item_social.get('type')
        if change_type == 'new':
            create_new_item_social(footer_block, data_item_social)
        elif change_type == 'edited':
            item_id = data_item_social.get('id')
            if not item_id.isnumeric():
                continue
            item_social = ItemSocial.objects.filter(pk=item_id).first()
            if item_social and check_real_edit_social_link(data_item_social) == 1:
                update_attr_item_social(footer_block, item_social, data_item_social)

    footer_block.save()
    return 0


def clone_item_menu(item_menu, footer_block):
    if item_menu.parent:
        return
    new_item_menu = item_menu
    new_item_menu.pk = None
    new_item_menu.footer_block = footer_block
    new_item_menu.save()
    new_item_menu.parent = item_menu
    new_item_menu.save()


def create_new_item_menu(footer_block, data_item_menu):
    item_menu = ItemFooterMenu.objects.create(footer_block=footer_block)
    for (key, value) in data_item_menu.items():
        if key in LIST_ITEM_MENU or key == 'type_footer':
            if key == 'order':
                value = int(value)
            setattr(item_menu, key, value)
    item_menu.save()


def update_attr_item_menu(item_menu, data_item_menu):
    new_item_menu = item_menu
    for (key, value) in data_item_menu.items():
        if key in LIST_ITEM_MENU:
            if key in 'order':
                value = int(value)
            setattr(new_item_menu, key, value)
    new_item_menu.save()


def create_new_item_social(footer_block, data_item_social):
    item_social = ItemSocial.objects.create(footer_block=footer_block)
    for (key, value) in data_item_social.items():
        if key in LIST_ITEM_SOCIAL_LINK:
            if key == 'order':
                value = int(value)
            setattr(item_social, key, value)
    item_social.save()


def update_attr_item_social(item_social, data_item_social):
    new_item_social = item_social
    for (key, value) in data_item_social.items():
        if key in LIST_ITEM_SOCIAL_LINK:
            if key == 'order':
                value = int(value)
            setattr(new_item_social, key, value)
    new_item_social.save()


def check_edit_footer_block(item_block, content_edited):
    footer_block = item_block.footer_block
    # check delete item
    if content_edited.get('list_delete_item_menu', []):
        return 1
    if content_edited.get('list_delete_item_social', []):
        return 1
    if content_edited.get('list_delete_item_sub', []):
        return 1
    if content_edited.get('copyright') != footer_block.copyright:
        return 1

    # check edit item footer menu
    content_menu_footer = content_edited.get('item_menu')
    for data_item_menu in content_menu_footer:
        change_type = data_item_menu.get('type')
        if change_type == 'new':
            return 1
        elif change_type == 'edited':
            if check_real_edit_item_menu(data_item_menu) == 1:
                return 1

    # check edit social link
    content_social_link = content_edited.get('social_link')
    for data_social_link in content_social_link:
        change_type = data_social_link.get('type')
        if change_type == 'new':
            return 1
        elif change_type == 'edited':
            if check_real_edit_social_link(data_social_link) == 1:
                return 1
    return 0


def check_amount_item_social_in_footer(profile, dict_attribute):
    item_block = profile.blocks.filter(item_block__type_block=ItemBlock.FOOTER_BLOCK).last().item_block
    current_footer = item_block.footer_block
    amount_item = 0
    list_item_social_edited = []
    list_item_social_deleted = dict_attribute.get('list_delete_item_social', [])
    list_item_social_deleted = remove_element_in_list_delete(list_item_social_deleted)
    amount_item -= ItemSocial.objects.filter(pk__in=list_item_social_deleted, parent__isnull=False).count()
    content_social_footer = dict_attribute.get('social_link')
    for data_item_social in content_social_footer:
        key = data_item_social.get('id')
        if data_item_social.get('type') != 'deleted':
            list_item_social_edited.append(key)
            amount_item += 1
    item_socials = current_footer.item_social.filter()
    for item_social in item_socials:
        if str(item_social.pk) not in list_item_social_edited and str(item_social.pk) not in list_item_social_deleted:
            amount_item += 1
    return False if amount_item > 8 else True


def remove_element_in_list_delete(list_delete):
    for id in list_delete:
        if not id.isnumeric():
            list_delete.remove(id)
    return list_delete


def get_list_topics_artist(artist, context):
    topics = TopicGallery.objects.filter(is_deleted=False, user=artist).order_by('-order')
    list_count = topics.count()
    topic_ids = list(topics.values_list('pk', flat=True))
    topic_ids = list(map(lambda x: str(x), topic_ids))
    topics = topics[:LOAD_MORE_LIST]
    total_page = list_count / LOAD_MORE_LIST if list_count % LOAD_MORE_LIST == 0 else int(
        list_count / LOAD_MORE_LIST) + 1
    context.update({
        'topics': topics,
        'topic_ids': topic_ids,
        'total_page': total_page,
    })


def check_real_edit_profile_block_for_creator(item_block, dict_attribute):
    profile_block = item_block.profile_block
    for (key, value) in dict_attribute.items():
        if key in ['section_name_en', 'section_name_jp', 'is_link_menu', 'content_jp', 'content_en']:
            if key == 'is_link_menu':
                value = True if value == 'true' else False
            if value != getattr(profile_block, key):
                return 1
    return 0


def update_edit_profile_block_for_creator(public_profile, dict_attribute):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.PROFILE_BLOCK).last()
    item_block = public_block.item_block
    dict_attribute = dict_attribute.get('profile_text')
    count_change = 0 if not dict_attribute else check_real_edit_profile_block_for_creator(item_block, dict_attribute)
    if count_change < 1:
        return False
    profile_block = item_block.profile_block
    for (key, value) in dict_attribute.items():
        if key in ['section_name_en', 'section_name_jp', 'content_jp', 'content_en', 'is_link_menu']:
            if key == 'is_link_menu':
                value = True if value == 'true' else False
            setattr(profile_block, key, value)
    profile_block.save()
    return True


def check_real_edit_statement_block_for_creator(item_block, dict_attribute):
    statement_block = item_block.statement_block
    for (key, value) in dict_attribute.items():
        if key in LIST_STATEMENT_BLOCK:
            if key in ['is_show_avatar', 'is_show_name', 'is_show_title']:
                value = True if value == 'true' else False
            if value != getattr(statement_block, key):
                return 1
    return 0


def update_edit_statement_block_for_creator(public_profile, dict_attribute):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.STATEMENT_BLOCK).last().item_block
    item_block = public_block
    dict_attribute = dict_attribute.get('statement')
    count_change = 0 if not dict_attribute else check_real_edit_statement_block_for_creator(item_block, dict_attribute)
    if count_change < 1:
        return False
    current_statement = item_block.statement_block
    for (key, value) in dict_attribute.items():
        if key in LIST_STATEMENT_BLOCK:
            if key in ['is_show_avatar', 'is_show_name', 'is_show_title']:
                value = True if value == 'true' else False
            setattr(current_statement, key, value)
    current_statement.save()
    return True


def check_edit_header_block_for_creator(request, item_block, dict_attribute):
    banner = request.FILES.get('banner')
    logo = request.FILES.get('logo')
    key_visual_pc = request.FILES.get('key_visual_pc')
    key_visual_sp = request.FILES.get('key_visual_sp')
    list_delete = json.loads(request.POST.get("json"))["header"]["list_deleted_image"]
    if banner or logo or key_visual_pc or key_visual_sp:
        return 1
    if list_delete:
        return 1
    header_block = item_block.header_block
    for (key, value) in dict_attribute.items():
        if key in LIST_FIELD_HEADER:
            if key in ['display_tag_line_1', 'display_tag_line_2']:
                value = True if value == 'true' else False
            if getattr(header_block, key) != value:
                return 1
    return 0


def update_edit_header_block_for_creator(request, public_profile, dict_attribute):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.HEADER_BLOCK).last().item_block
    item_block = public_block
    dict_attribute = dict_attribute.get('header')
    count_change = 0 if not dict_attribute else check_edit_header_block_for_creator(request, item_block, dict_attribute)
    if count_change < 1:
        return False
    current_header = item_block.header_block
    list_delete = json.loads(request.POST.get("json"))["header"]["list_deleted_image"]
    for (key, value) in dict_attribute.items():
        if key in LIST_FIELD_HEADER:
            if key in ['display_tag_line_1', 'display_tag_line_2']:
                value = True if value == 'true' else False
            setattr(current_header, key, value)
    for key in LIST_FIELD_HEADER_IMAGE:
        if request.FILES.get(key):
            setattr(current_header, key, request.FILES.get(key))
        elif key in list_delete:
            setattr(current_header, key, '')
    current_header.save()
    return True


def update_edit_footer_block_for_creator(public_profile, dict_attribute):
    public_block = public_profile.blocks.filter(item_block__type_block=ItemBlock.FOOTER_BLOCK).last().item_block
    dict_attribute = dict_attribute.get('footer')
    item_block = public_block
    count_change = 0 if not dict_attribute else check_edit_footer_block(item_block, dict_attribute)
    if count_change < 1:
        return False
    footer_block = item_block.footer_block

    # update copyright
    footer_block.copyright = dict_attribute.get('copyright')

    # update item menu
    content_menu_footer = dict_attribute.get('item_menu')
    list_item_menu_deleted = dict_attribute.get('list_delete_item_menu', []) + dict_attribute.get(
        'list_delete_item_sub', [])
    list_item_menu_deleted = remove_element_in_list_delete(list_item_menu_deleted)
    ItemFooterMenu.objects.filter(pk__in=list_item_menu_deleted).delete()

    for data_item_menu in content_menu_footer:
        change_type = data_item_menu.get('type')
        if change_type == 'new':
            create_new_item_menu(footer_block, data_item_menu)
        elif change_type == 'edited':
            item_id = data_item_menu.get('id')
            if not item_id.isnumeric():
                continue
            item_menu = ItemFooterMenu.objects.filter(pk=item_id).first()
            if item_menu and check_real_edit_item_menu(data_item_menu) == 1:
                update_attr_item_menu(item_menu, data_item_menu)

    # update item social
    list_item_social_deleted = dict_attribute.get('list_delete_item_social', [])
    list_item_social_deleted = remove_element_in_list_delete(list_item_social_deleted)
    ItemSocial.objects.filter(pk__in=list_item_social_deleted).delete()
    content_social_footer = dict_attribute.get('social_link')

    for data_item_social in content_social_footer:
        change_type = data_item_social.get('type')
        if change_type == 'new':
            create_new_item_social(footer_block, data_item_social)
        elif change_type == 'edited':
            item_id = data_item_social.get('id')
            if not item_id.isnumeric():
                continue
            item_social = ItemSocial.objects.filter(pk=item_id).first()
            if item_social and check_real_edit_social_link(data_item_social) == 1:
                update_attr_item_social(item_social, data_item_social)

    footer_block.save()
    return True


def create_new_sale_content_for_creator(request, data_sale_content, last_creator_profile, float_keys, int_keys):
    file_name = 'sale_content_image_' + data_sale_content.get('id')
    audio_name = 'sale_content_audio_' + data_sale_content.get('id')

    # create new sale content
    new_sale_content = SaleContent.objects.create(profile=last_creator_profile)
    new_sale_content.order = last_creator_profile.content_profile.all().order_by('-order').first().order + 1
    new_sale_content.save()

    new_sale_content_version = SaleContentVersion.objects.create(sale=new_sale_content)

    # update attribute sale content version
    new_sale_content_version = update_attribute_sale_content(data_sale_content, new_sale_content_version, float_keys, int_keys)

    img = request.FILES.get(file_name, None)
    if img:
        new_sale_content_version.image = img
        crop_new_image('image', new_sale_content_version.image, new_sale_content_version.image.name,
                       new_sale_content_version, new_sale_content_version.x,
                       new_sale_content_version.y, new_sale_content_version.height, new_sale_content_version.width)

    new_sale_content.last_published_version = new_sale_content_version
    new_sale_content.save()
    # create audio
    audio = request.FILES.get(audio_name, None)
    if audio:
        album = AlbumVariation.objects.create(sale_content=new_sale_content_version)
        album_version = AlbumVersion.objects.create(album=album, file=audio)
        album_version.save()
        album.last_published_version = album_version
        album.save()
    new_sale_content.refresh_from_db()
    return new_sale_content


def update_sale_content_for_creator(request, data_sale_content, sale_content, float_keys, int_keys):
    id_public = sale_content.pk
    file_name = 'sale_content_image_' + str(id_public)
    audio_name = 'sale_content_audio_' + str(id_public)
    sale_content_version = sale_content.last_published_version

    sale_content_version = update_attribute_sale_content(data_sale_content, sale_content_version, float_keys, int_keys)
    sale_content.refresh_from_db()

    img = request.FILES.get(file_name, None)
    sale_content_version.image = img
    if img:
        crop_new_image('image', sale_content_version.image, sale_content_version.image.name,
                       sale_content_version, sale_content_version.x,
                       sale_content_version.y, sale_content_version.height, sale_content_version.width)
        sale_content_version.save()

    # update audio
    audio = request.FILES.get(audio_name, None)
    if audio:
        album_variations = sale_content_version.album.all()
        if album_variations:
            for album_variation in album_variations:
                album_version = album_variation.last_published_version
                if album_version and audio:
                    album_version.file = audio
                    album_version.save()
        else:
            album = AlbumVariation.objects.create(sale_content=sale_content_version)
            album_version = AlbumVersion.objects.create(album=album, file=audio)
            album_version.save()
            album.last_published_version = album_version
            album.save()
            sale_content_version.refresh_from_db()
    else:
        if sale_content_version.sale_youtube_link:
            album_variations = sale_content_version.album.all()
            album_variations.delete()
    sale_content_version.refresh_from_db()
    return sale_content

def check_sale_content_real_change_for_creator(data_sale_content, request):
    sale_id = data_sale_content.get('id')
    sale_content = SaleContent.objects.filter(pk=sale_id).first()
    if not sale_content:
        return 0
    sale_content_version = sale_content.last_published_version
    for (key, value) in data_sale_content.items():
        if key == 'id':
            file_name = 'sale_content_image_' + value
            audio_name = 'sale_content_audio_' + value
            img = request.FILES.get(file_name)
            audio = request.FILES.get(audio_name)
            if img or audio:
                return 1
        elif key in ('start_time', 'end_time'):
            if value != '':
                value = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M')
            else:
                value = None
            current_value = getattr(sale_content_version, key)
            if value != current_value:
                return 1
        elif key == 'tags_content':
            new_tag = value.strip()
            new_tag_arr = new_tag.split('#')
            new_tag_arr.sort()
            new_tag_str = ' '.join([f'{str(elem)}' for elem in list(new_tag_arr)]).strip()
            current_tag = ' '.join([f'{str(elem)}' for elem in list(
                sale_content_version.tags.all().order_by('tag_name').values_list('tag_name', flat=True))])
            if new_tag_str != current_tag:
                return 1
        elif key == 'customizable_sale_setting':
            return 1
        elif key not in \
                ('image', 'id', 'type', 'start_time', 'end_time', 'tags', 'x', 'y', 'width', 'height', 'tags_content'):
            if key in \
                    ('song_attribute1_min', 'song_attribute2_min', 'song_attribute1_max',
                     'song_attribute2_max', 'price', 'max_price'):
                if value == '':
                    value = 0
                if key in \
                        ('price', 'max_price'):
                    value = float(value)
                else:
                    value = int(value)
            current_value = getattr(sale_content_version, key)
            if value != current_value:
                return 1
    return 0


def crop_new_image(key, value, name, object_active, x, y, height, width):
    avatar = Image.open(io.BytesIO(value.read())).convert("RGBA")
    area = (x, y, width + x, height + y)
    crop_avatar = avatar.crop(area)
    output = io.BytesIO()
    white_background = Image.new("RGBA", crop_avatar.size, "WHITE")
    white_background.paste(crop_avatar, (0, 0), crop_avatar)
    white_background.convert('RGBA').save(output, format='PNG', quality=100)
    image = InMemoryUploadedFile(output, 'FileField', name, 'image/png',
                                                output.getbuffer().nbytes, None)

    setattr(object_active, key, image)
    object_active.save()


def update_attribute_sale_content(dict_attribute, new_sale_content_version, float_keys, int_keys):
    song_attribute1_min = dict_attribute['song_attribute1_min']
    song_attribute1_max = dict_attribute['song_attribute1_max']
    song_attribute2_min = dict_attribute['song_attribute2_min']
    song_attribute2_max = dict_attribute['song_attribute2_max']

    if not song_attribute1_min:
        song_attribute1_min = 2
    else:
        song_attribute1_min = int(song_attribute1_min)
    if not song_attribute1_max:
        song_attribute1_max = 4
    else:
        song_attribute1_max = int(song_attribute1_max)
    if not song_attribute2_min:
        song_attribute2_min = 2
    else:
        song_attribute2_min = int(song_attribute2_min)
    if not song_attribute2_max:
        song_attribute2_max = 4
    else:
        song_attribute2_max = int(song_attribute2_max)

    if song_attribute1_max < song_attribute1_min:
        temp = song_attribute1_max
        song_attribute1_max = song_attribute1_min
        song_attribute1_min = temp

    if song_attribute2_max < song_attribute2_min:
        temp = song_attribute1_max
        song_attribute2_max = song_attribute2_min
        song_attribute2_min = temp

    dict_attribute['song_attribute1_min'] = song_attribute1_min
    dict_attribute['song_attribute1_max'] = song_attribute1_max
    dict_attribute['song_attribute2_min'] = song_attribute2_min
    dict_attribute['song_attribute2_max'] = song_attribute2_max

    for (key, value) in dict_attribute.items():
        if key in float_keys:
            if value == '':
                value = 0
            else:
                if key in int_keys:
                    value = int(value)
                else:
                    value = round(float(value), 2)
        elif key in ('image', 'id', 'type'):
            continue
        elif key in ('start_time', 'end_time'):
            if value != '':
                value = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M')
            else:
                value = None
        elif key == 'tags_content':
            txt = value
            list_tag = re.findall("#[々〆〤一-龠ぁ-ゔァ-ヴーａ-ｚＡ-Ｚ０-９a-zA-Z0-9]{1,60}", txt)
            list_new_tag = []
            for v in list_tag:
                v = v.replace('#', '')
                list_new_tag.append(v)
                tag = HashTag.objects.filter(tag_name=v)
                if tag.exists():
                    tag = tag[0]
                else:
                    tag = HashTag.objects.create(tag_name=v)
                if not SaleContentTag.objects.filter(sale_content=new_sale_content_version, tag=tag).exists():
                    SaleContentTag.objects.create(sale_content=new_sale_content_version, tag=tag)
            SaleContentTag.objects.filter(
                Q(sale_content=new_sale_content_version) & ~Q(tag__tag_name__in=list_new_tag)).delete()
            continue
        setattr(new_sale_content_version, key, value)
    new_sale_content_version.save()
    new_sale_content_version.refresh_from_db()
    return new_sale_content_version
