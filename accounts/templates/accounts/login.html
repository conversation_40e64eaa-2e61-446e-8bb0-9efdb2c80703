{% extends "base_nofooter.html" %}
{% load widget_tweaks %}
{% load bootstrap3 %}
{% load static compress %}
{% block extrahead %}
{% load i18n %}
    <script>
      $('html').attr('prefix', 'og: http://ogp.me/ns# fb: http://ogp.me/ns/ fb# website: http://ogp.me/ns/website#')
    </script>
    <meta property="og:url" content="https://soremo.jp/" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="SOREMO" />
    <meta property="og:description" content="動画を送るとぴったりの音を返してくれるWEBサービス" />
    <meta property="og:site_name" content="SOREMO" />
    <meta property="og:image" content="{{ request.scheme }}://{{ request.META.HTTP_HOST }}{% static 'images/OGP_logo.png' %}" />
    <meta property="og:image:width" content="400" />
    <meta property="og:image:height" content="230" />

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.css" rel="stylesheet"/>
    {% compress css %}
    <link rel="stylesheet" href="{% static 'css/login.css' %}"/>
    <style>
        .banner__img-pc {
            transition: background 2.0s ease-in-out;
        }

        .img1 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201801.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img2 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201808.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img3 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201811.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img4 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201901.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img5 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201904.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img6 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201908.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img7 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        @media (max-width: 576px) {
            .banner__img-pc {
                display: none;
            }

            .img1 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201801.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img2 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201808.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img3 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201811.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img4 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201901.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img5 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201904.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img6 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201908.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img7 {
                background-color: transparent;
                background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }
        }
    </style>
    {% endcompress %}
{% endblock %}
{% block content %}
    <div class="login">
        <div class="banner">
            <div class="bxslider">
                <div class="banner__img visible-xs visible-sm img0">
                </div>
                <div class="banner__img-pc img0"></div>
            </div>
        </div>
        <div class="auth">
            <div class="auth__content">
                <div class="auth__main">
                    <div class="auth__form">
                        <form method="post" action="

                                {% url 'accounts:accounts_login' %}{% if next_url %}?next_url={{ next_url }}{% endif %}" style="display:flex; flex-direction: column; gap:12px">
                            {% csrf_token %}

                            <div class="form-group">
                                {{ form.username|attr:"placeholder:メールアドレス"|add_class:"input-box input-user-name-signin" }}
                                {% if form.errors.error_user %}
                                    <div class="error-messager valid_mail">
                                        {{ form.errors.error_user }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                {{ form.password|attr:"placeholder:パスワード"|add_class:"input-box input-password-signin" }}
                                {% if form.errors.error_pass %}
                                    <div class="error-messager valid_mail">
                                        {{ form.errors.error_pass }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <div class="checkbox">
                                    {% render_field form.remember_me type="checkbox"|add_class:"signin-checkbox" %}
                                    <label for="checkbox">{% trans "remember me" %}</label>
                                </div>
                            </div>
                            <div class="form-group text-center" style="padding: 0;">
                                {% buttons %}
                                    <input type="submit" class="btn button button--submit auth__form-button btn-signin-form"
                                           value="サインイン"
                                           onclick="this.disabled=true,this.form.submit();"
                                           style="font-size: 16px;"/>
                                {% endbuttons %}
                            </div>
                            <div class="form-group" style="text-align: center;">
                                <div class="auth__form-forgotpass">{% trans "forgot password link" %}
                                    <a href="/accounts/password/reset/">
                                        {% trans "forgot password link1" %}</a>
                                </div>
                            </div>

                            <!-- <div class="form-group join-form__social-title text-center">OR</div>
                            <div class="login-social form-group"
                                 style="display: flex;justify-content: center;flex-wrap: wrap;">


                                <div class="join-form__social-list">
                                    <a class="join-form__social-network google"
                                       href="{% url 'social:begin' 'google-oauth2' %}?next={{ social_next }}"></a>
                                    <a class="join-form__social-network facebook"
                                       href="{% url 'social:begin' 'facebook' %}?next={{ social_next }}"></a>
                                </div>
                            </div> -->

                        </form>
                        <!-- <hr>
                        <div class="form-group form-bottom">
                            <div class="auth__form-register" style="text-align:center;">{% trans "register link" %}
                                <a href="{% url 'accounts:accounts_signup' %}{% if next_url %}?next_url={{ next_url }}{% endif %}">
                                    {% trans "register link1" %}</a>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{% static 'js/random.js' %}"></script>
    <script>
        //Array of images which you want to show: Use path you want.
        let images = ['img1', 'img2', 'img3', 'img4', 'img5', 'img6', 'img7'];
        removeNavbar()
        randomImageBackground(images);

        $(document).ready(function() {
            rezizeAuth();
            $(window).on('resize', () => {
                rezizeAuth()
            })

            $('.input-user-name-signin').on('input', function() {
                checkRequired();
            });

            $('.input-password-signin').on('input', function () {
                if ($('.input-password-signin').val().trim().length < 8) {
                    if (!$('.error-pass').length) {
                        $('<div class="errorlist error-pass">' +
                            '8文字以上を入力してください。' +
                            '</div>').insertAfter($('.input-password-signin'))
                    }
                } else {
                    $('.error-pass').remove()
                }
                checkRequired();
            });

            checkRequired();

            function checkRequired() {
                btn_disable_condition = $('.input-user-name-signin').val().trim() !== '' && $('.input-password-signin').val().trim() !== '' && $('.input-password-signin').val().trim().length >= 8;
                $('.btn-signin-form').toggleClass('btn--disabled', !btn_disable_condition);
            }
        });
    </script>
    <script src="{% url 'javascript-catalog' %}"></script>
{% endblock %}
