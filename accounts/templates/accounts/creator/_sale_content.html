{% load util %}
{% load static %}
<div class="sample-audio-item rel {% if can_edit %}editable{% endif %}"
     data-sale-content-id="{{ sale_content.pk }}"
     data-sale-youtube-link="{{ sale_content.last_published_version|has_sale_youtube_link }}"
     data-bookmark-state="{% if sale_content.child.first %}{{ sale_content.child.first|is_bookmarked_sale:user }}{% else %}{{ sale_content|is_bookmarked_sale:user }}{% endif %}"
     data-sale-id="{% if sale_content.child.first %}{{ sale_content.child.first.pk }}{% else %}{{ sale_content.pk }}{% endif %}">
    {% if is_edit %}
        {% if not is_upload_device %}
            <div class="sale-youtube-video sample-audio-thumbnail" data-file-type="{{ file_type }}"
                 style="{{ sale_content|get_thumbnail_sale_content:user }}"
                 id="youtube-video-{{ sale_content.last_published_version.pk }}"
                 data-id-element="youtube-video-{{ sale_content.last_published_version.pk }}"
                 data-video-url="{{ sale_content.last_published_version.sale_youtube_link }}">
            </div>
        {% else %}
            {% for album_variation in sale_content|get_audios:user %}
                {% with album_variation|get_file_type:user as file_type %}
                    <div class="sample-audio-thumbnail" style="{{ sale_content|get_thumbnail_sale_content:user }}"
                         data-file-type="{{ file_type }}">
                        <div class="sample-audio-playpause-button {% if file_type != 'audio' %}btn-preview-album{% endif %}"></div>
                        <audio loop src="{{ album_variation|get_audio:user }}"
                               preload="metadata"
                               data-album="{{ album_variation.pk }}"
                               data-name="{{ album_variation|get_audio_name:user }}"
                               data-album-name="{{ sale_content|get_title_sale_content:user }}"
                               data-file-type="{{ file_type }}"></audio>
                        {% if file_type == 'movie' %}
                            {% with video_url=album_variation|get_audio:user %}
                            {% with video_info=video_url|get_video_url_with_fallback %}
                            <video preload="none"
                                   style="width: 100%; height: 100%; max-height: 100%; background: white; display: none;"
                                   data-video-src="{{ video_info.url }}"
                                   data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                                   data-fallback-src="{% if video_url %}{{ video_url }}{% endif %}">
                                <source src="{{ video_info.url }}">
                            </video>
                            {% endwith %}
                            {% endwith %}
                        {% endif %}
                        <input type="file" class="hide new-audio-file">
                    </div>
                {% endwith %}
            {% endfor %}
        {% endif %}
    {% else %}
        {% if sale_content.last_published_version.sale_youtube_link %}
            <div class="sale-youtube-video sample-audio-thumbnail" data-file-type="{{ file_type }}"
                 style="{{ sale_content|get_thumbnail_sale_content:user }}"
                 id="youtube-video-{{ sale_content.last_published_version.pk }}"
                 data-id-element="youtube-video-{{ sale_content.last_published_version.pk }}"
                 data-video-url="{{ sale_content.last_published_version.sale_youtube_link }}">
            </div>
        {% else %}
  {% for album_variation in sale_content|get_audios:user %}
    {% with album_variation|get_file_type:user as file_type %}
      <div class="sample-audio-thumbnail" style="{{ sale_content|get_thumbnail_sale_content:user }}"
           data-file-type="{{ file_type }}">
        <div class="sample-audio-playpause-button {% if file_type != 'audio' %}btn-preview-album{% endif %}"></div>
        <audio loop src="{{ album_variation|get_audio:user }}"
               preload="metadata"
               data-album="{{ album_variation.pk }}"
               data-name="{{ album_variation|get_audio_name:user }}"
               data-album-name="{{ sale_content|get_title_sale_content:user }}"
               data-file-type="{{ file_type }}"></audio>
        {% if file_type == 'movie' %}
          {% with video_url=album_variation|get_audio:user %}
          {% with video_info=video_url|get_video_url_with_fallback %}
          <video preload="none" style="width: 100%; height: 100%; max-height: 100%; background: white; display: none;"
                 data-video-src="{{ video_info.url }}"
                 data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                 data-fallback-src="{% if video_url %}{{ video_url }}{% endif %}">
            <source src="{{ video_info.url }}">
          </video>
          {% endwith %}
          {% endwith %}
        {% endif %}
        <input type="file" class="hide new-audio-file">
      </div>
    {% endwith %}
  {% endfor %}
  {% endif %}
{% endif %}


  <div class="sample-audio-info">
    {% with price=sale_content|get_price_sale_content sale_type=sale_content|get_type_sale_content %}
      <div class="sample-audio-title"
           data-sale-youtube-link="{{ sale_content|get_sale_youtube_link }}"
           data-desc="{{ sale_content|get_description_sale_content }}"
           data-type="{{ sale_content|get_type_sale_content }}"
           data-title="{{ sale_content|get_title_sale_content:user }}"
           data-price="{{ price }}"
           data-content-type="{{ sale_content|get_content_type }}"
           data-attribute-min1="{{ sale_content|get_song_attr1_min }}"
           data-attribute-min2="{{ sale_content|get_song_attr2_min }}"
           data-attribute-max1="{{ sale_content|get_song_attr1_max }}"
           data-attribute-max2="{{ sale_content|get_song_attr2_max }}"
           data-default-color="{{ sale_content|get_default_color }}"
           data-auctions-start-time="{{ sale_content|get_auction_start_time }}"
           data-auctions-end-time="{{ sale_content|get_auction_end_time }}"
           data-background="{{ sale_content|get_thumbnail_sale_content:user }}"
              {{ sale_content|get_auction_price:'data-auctions-start-price=' }}
              {{ sale_content|get_auction_max_price:'data-auctions-end-price=' }}
           data-show-thumbnail="{{ sale_content|get_show_thumbnail:user }}"

           data-content-desc="{{ sale_content|get_description_sale_content }}"
           data-old-type="{{ sale_content|get_type_sale_content }}"
           data-content-title="{{ sale_content|get_title_sale_content:user }}"
           data-content-price="{{ price }}"
           data-content-content-type="{{ sale_content|get_content_type }}"
           data-content-attribute-min1="{{ sale_content|get_song_attr1_min }}"
           data-content-attribute-min2="{{ sale_content|get_song_attr2_min }}"
           data-content-attribute-max1="{{ sale_content|get_song_attr1_max }}"
           data-content-attribute-max2="{{ sale_content|get_song_attr2_max }}"
           data-content-default-color="{{ sale_content|get_default_color }}"
           data-content-content-auctions-start-time="{{ sale_content|get_auction_start_time }}"
           data-content-auctions-end-time="{{ sale_content|get_auction_end_time }}"
           data-content-background="{{ sale_content|get_thumbnail_sale_content:user }}"
              {{ sale_content|get_auction_price:'data-content-auctions-start-price=' }}
              {{ sale_content|get_auction_max_price:'data-content-auctions-end-price=' }}

           data-created-year="{{ sale_content|get_created_year }}"
           data-credit="{{ sale_content|get_credit }}"
           data-hashtag="{{ sale_content|get_hash_tag_in_sale_content:user }}"
           data-content-show-thumbnail="{{ sale_content|get_show_thumbnail:user }}"
           data-customizable-sale-setting="{{ sale_content|get_customizable_sale_setting:user }}"
           data-sale-type="{{ sale_content.last_published_version.sale_type }}"
      >{{ sale_content|get_title_sale_content:user }}</div>
      <div class="sample-audio-sale-type">{{ sale_content|get_sale_type_text:user }}</div>
    {% endwith %}
    <div class="drag-sample-audio-btn drag-button">
      <i class="fas fa-arrows-alt"></i>
    </div>
    <div class="delete-sample-audio-btn delete-button">
      <i class="icon icon--sicon-trash"></i>
    </div>
    <div class="edit-sample-audio-btn edit-button">
      <i class="icon icon--sicon-pencil"></i>
    </div>
  </div>

</div>
