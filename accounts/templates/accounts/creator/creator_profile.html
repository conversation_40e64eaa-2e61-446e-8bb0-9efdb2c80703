{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load user_agents %}
{% load static %}
{% load util compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
    {% compress css %}
  <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
  <link href="{% static 'css/creator_profile.css' %}" rel="stylesheet">
  <style>
    {% if request|is_pc %}
      .audio-player {
        padding: 0 15vw;
      }
    {% endif %}
  </style>
    {% endcompress %}
{% endblock %}
{% block content %}
  <form method="post" id="creator_profile_form"
        action="{% if user.role == 'creator' %}{% url 'accounts:accounts_creator_profile' object.pk %}{% elif user.role == 'admin' %}{% url 'accounts:accounts_creator_profile_update_admin' object.pk %}{% endif %}"
        enctype='multipart/form-data'>
    {% csrf_token %}
    <main class="profile profile--edit">
      <div class="profile__content">
        <div class="container">
          <div class="profile__page-title">プロフィール</div>
        </div>
        <div class="profile__cover-image"
             style="background-image: {% if creator.banner %}url({{ creator.banner.url }}){% else %}url({% static 'images/profile-cover.png' %}){% endif %}">
          {% if user.role == 'creator' %}
            <a class="button button--background button--background-gray button--round" href="#" role="button">
              <label for="id_banner">バナー画像を選択</label>
            </a>
            {{ form.banner|append_attr:"style: display: none;" }}
            {{ form.x_banner }}
            {{ form.y_banner }}
            {{ form.width_banner }}
            {{ form.height_banner }}
          {% endif %}
        </div>
        <div class="container">
          <div class="profile__top-edit">
            <div class="profile__avatar background-avt">
              <img class="profile__avatar-img" src="{{ user|get_avatar:'medium' }}" alt="">
            </div>
            {% if user.role == 'creator' %}
              <div class="profile__avatar-upload">
                <a class="button button--background button--background-gray button--round" href="#"
                   role="button">
                  <label for="id_avatar">プロフィール画像を選択</label>
                </a>
                {{ form.avatar|append_attr:"style: display: none;" }}
              </div>
              <div class="profile__avatar-link">
                <a class="header-button header-button--next"
                   href="{% url 'accounts:accounts_creator_social_media' object.pk %}">
                  リンクを追加
                </a>
              </div>
            {% endif %}
          </div>
        </div>
        {% if user.role == 'creator' %}
          {{ form.x }}
          {{ form.y }}
          {{ form.width }}
          {{ form.height }}
        {% endif %}
        <div class="profile__audio">
          <div class="container sample-audio-container">
            {% for file in audio_files %}
              <div class="sample-audio-item">
                <div class="sample-audio-thumbnail">
                    <div class="sample-audio-playpause-button"></div>
                    <audio src="{{ file.audio.url }}" preload="metadata"></audio>
                </div>
              </div>
            {% endfor %}
            <div class="sample-audio-item">
              <div class="sample-audio-thumbnail upload-new-audio">
              </div>
            </div>
          </div>
          <div class="profile__edit-other">
            <div class="container">
              <div class="form-group">
                <label for="stage-name">芸名</label>
                {{ form.stage_name|add_class:"form-control"|append_attr:"placeholder:ソレモ光" }}
              </div>
              <div class="form-group">
                <label for="stage-name-en">芸名（英語表記）</label>
                {{ form.stage_name_en|add_class:"form-control"|append_attr:"placeholder:Soremo Hikaru" }}
              </div>
              <div class="form-group select-container select_role">
                <label>職種</label>
                <div id="creator_role">{{ creator.user.type }}</div>
              </div>
              <div class="form-group">
                <label for="id_professional_creator_title">肩書き</label>
                {{ form.type|add_class:"form-control"|append_attr:"placeholder:例:クラシック作曲家" }}
              </div>
              <div class="form-group form-customize" rows="5">
                <label for="philosophy-quote">テーマ（140字以内）</label>
                <div class="form-textarea" id="philosophy-quote">
                  {{ form.theme_quote|add_class:"form-control"|append_attr:"placeholder:たくさんの方が心地よくなる音を届けます。" }}
                </div>
              </div>
              <div class="form-group form-customize profile-quote" rows="5">
                <label for="profile-quote">プロフィール（400字以内）</label>
                <div class="form-textarea" id="profile-quote">
                  {{ form.profile_quote|add_class:"form-control"|append_attr:"placeholder:アメリカで生まれ育ち、ロックの影響をうける。" }}
                </div>
              </div>
            </div>
          </div>
          {% if user.role == 'creator' %}
            <div class="account__action">
              <div class="account__form-group">
                {% buttons %}
                  <input type="submit"
                         class="button button--gradient button--gradient-primary button--round border-width-button"
                         role="button" value="変更を申請"/>
                {% endbuttons %}
              </div>
            </div>
          {% endif %}
        </div>
    </main>
    <div class="modal fade" id="modalCrop">
      <div class="modal-dialog" style="transform: translate(0,10%);">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
            <h4 class="modal-title">画像を登録</h4>
          </div>
          <div class="modal-body">
            <img src="" id="image" style="max-width: 100%;" alt="">
          </div>
          <div class="modal-footer">
            <div class="btn-group pull-left" role="group">
              <button type="button" class="btn btn-default js-zoom-in">
                <span class="glyphicon glyphicon-zoom-in"></span>
              </button>
              <button type="button" class="btn btn-default js-zoom-out">
                <span class="glyphicon glyphicon-zoom-out"></span>
              </button>
            </div>
            <button type="button" class="btn btn-primary js-crop-and-upload">登録する</button>
          </div>
        </div>
      </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
  {% compress js inline %}
    {% if request|is_pc %}
      <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    {% else %}
      <script type="text/javascript" src="{% static 'js/wavesurfer.js' %}"></script>
    {% endif %}
  {% endcompress %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  {% compress js inline %}
    <script src="{% static 'js/main.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
    <script src="{% static 'js/cropper.min.js' %}"></script>
    <script src="{% static 'js/main_cropping.js' %}"></script>
    <script src="{% static 'js/creator_profile.js' %}"></script>
  {% endcompress %}
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
  </form>
{% endblock content %}
