{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load widget_tweaks %}
{% load static compress %}
{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css"/>

    {% if request.user_agent.is_pc %}
        <script type="text/javascript" src='https://cdnjs.cloudflare.com/ajax/libs/wavesurfer.js/1.3.2/wavesurfer.min.js'></script>
    {% else %}
        <script type="text/javascript" src="{% static 'js/wavesurfer.js' %}"></script>
    {% endif %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
        <script src="{% static 'js/cropper.min.js' %}"></script>
    {% endcompress %}
    {% compress css %}
    <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'css/join_us.css' %}" />
    {% endcompress %}

{% endblock extrahead %}
{% block content %}
    <main>
        <div class="join-form">
            <div class="join-form__content">
                <div class="container">
                    <div class="join-form__page-title">JOIN US</div>
                    <div class="step-intro">
                        <div class="join-form__intro">
                            <p>クリエイター登録は、完全審査制。</p>
                            <p>承認されたクリエイターは、次のことができます。</p>
                            <p class="join-form__intro-title">【クライアントワーク】</p>
                            <p>
                                あなただけの才能をアピールし、つながりを創り、広く制作依頼をうけることができます。取引折衝、契約、納品、チェックバック、検収、相互評価、入金まで、全ての取引をオンラインで完結。</p>
                            <p>生み出したコンテンツのクレジットは保証され、将来の制作依頼につなげていきます。</p>
                            <p class="join-form__intro-title">【コンテンツ販売】</p>
                            <p>あなたのオリジナルコンテンツの利用権を販売し、ロイヤリティ収入を受け取ることができます。</p>
                            <p>アカウント内情報は、全てを自分で管理でき、その価値を自分で決めることができます。</p>
                        </div>
                        <div class="join-form__action">
                            <div class="join-form__form-group"><a
                                    class="button button--gradient button--gradient-primary button--round"
                                    href="javascript:void(0)"
                                    role="button">応募する</a>
                            </div>
                        </div>
                    </div>
                    <form action="{% url 'accounts:join_us' %}" method="POST" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="step-select-role slide-out-right join-form--select-role">
                            <div class="join-form__role-title">募集職種を選んでください。</div>
                            <div class="join-form__role-list">
                                <div class="join-form__role-item" data-value="0" data-select="composer">
                                    <div class="join-form__role-item-title">コンポーザー</div>
                                    <div class="join-form__role-item-info">作編曲家はこちら。インストのBGM、テーマソングの楽曲制作を受託できます。</div>
                                </div>
                                <div class="join-form__role-item" data-value="1" data-select="sound">
                                    <div class="join-form__role-item-title">サウンドデザイナー</div>
                                    <div class="join-form__role-item-info">効果音、音響効果制作、ソニックブランディング（サウンドロゴなど）の制作を受託したい方。</div>
                                </div>
                                <div class="join-form__role-item" data-value="2" data-select="voice">
                                    <div class="join-form__role-item-title">ボイスアクター</div>
                                    <div class="join-form__role-item-info">声優・ナレーターの方々。</br>
                                        キャラクターボイス（CV）、ナレーションをお願いします。
                                        宅録環境での収録、スタジオでの実演。
                                        御希望に応じて、オーダーを御請けできます。</div>
                                </div>
                                <div class="join-form__role-item" data-value="3" data-select="vocalist">
                                    <div class="join-form__role-item-title">ボーカリスト</div>
                                    <div class="join-form__role-item-info">ボーカリストの方。</div>
                                </div>
                                <div class="join-form__role-item" data-value="4" data-select="narrator">
                                    <div class="join-form__role-item-title">プレイヤー</div>
                                    <div class="join-form__role-item-info">楽器演奏をご依頼。</div>
                                </div>
                                <div class="join-form__role-item" data-value="5" data-select="vocalist">
                                    <div class="join-form__role-item-title">オーディオエンジニア</div>
                                    <div class="join-form__role-item-info">スタジオのレコーディング、音楽のミックス、マスタリング、ポスプロ作業などをご依頼できる方。</div>
                                </div>
                            </div>
                            <div class="join-form__action">
                                <div class="join-form__form-group"><a
                                        class="button button--gradient button--gradient-primary button--round disabled"
                                        href="javascript:void(0)" role="button">登録</a>
                                </div>
                            </div>
                        </div>

                        <div class="step-input-info slide-out-right">
                            <div class="join-form__steps">
                                <div class="join-form__step-list">
                                    <a class="join-form__step current step1" data-value="1" href="javascript:void(0)">1</a>
                                    <a class="join-form__step disabled step2" data-value="2" href="javascript:void(0)">2</a>
                                    <a class="join-form__step disabled step3" data-value="3" href="javascript:void(0)">3</a>
                                </div>
                                <div class="join-form__step-title">アカウント情報の登録</div>
                            </div>
                            <div class="step-input-1" data-value="1">
                                <div class="join-form__social-login">
                                    <div class="join-form__social-title">SNSアカウントで情報を簡単入力</div>
                                    <div class="join-form__social-list"><a class="join-form__social-network apple"
                                                                           href="#"></a><a
                                            class="join-form__social-network google" href="#"></a><a
                                            class="join-form__social-network facebook" href="#"></a></div>
                                    <div class="join-form__social-title">OR</div>
                                </div>
                                <div class="join-form__input-form">
                                    <div class="join-form__form-group">
                                        <div class="form-group">
                                            <label for="email">メールアドレス</label>
                                            {{ form.email|add_class:"form-control" }}
                                        </div>
                                        <div class="account__column-2">
                                            <div class="form-group">
                                                <label for="last-name">姓</label>
                                                {{ form.last_name|add_class:"form-control" }}
                                            </div>
                                            <div class="form-group">
                                                <label for="first-name">名</label>
                                                {{ form.first_name|add_class:"form-control" }}
                                            </div>
                                        </div>
                                        <div class="account__notice">※本名（プロフィールページには掲載はされません）</div>
                                        <div class="form-group">
                                            <label for="birthday">生年月日</label>
                                            {{ form.dob|add_class:"form-control dob-datepicker" }}
                                        </div>
                                        <div class="form-group">
                                            <label for="phone-number">電話番号</label>
                                            {{ form.phone|add_class:"form-control" }}
                                        </div>
                                    </div>
                                </div>
                                <div class="join-form__action">
                                    <div class="join-form__form-group"><a
                                            class="button button--gradient button--gradient-primary button--round disabled"
                                            href="javascript:void(0)" role="button">次へ</a>
                                    </div>
                                </div>
                            </div>

                            <div class="step-input-2 slide-out-right" data-value="2" style="margin-top: -30px">
                                <div class="profile__top-edit">
                                    <div class="profile__avatar">
                                        <img class="profile__avatar-img" src="{% static 'images/default-avatar-creator.png' %}" alt="">
                                            {{form.x}}
                                            {{form.y}}
                                            {{form.width}}
                                            {{form.height}}
                                            {{form.image|add_class:"hidden"}}
                                    </div>
                                    <div class="profile__avatar-upload"><label
                                            class="button button--background button--background-gray button--round button--small"
                                            href="javascript:void(0)" role="button" for="id_image">プロフィール画像を選択</label>
                                    </div>
                                </div>

                                <div class="profile__edit-other">
                                    <div class="container">
                                        <div class="account__form-group">
                                            <div class="form-group">
                                                <label for="stage-name">芸名</label>
                                                {{ form.stage_name|add_class:"form-control" }}
                                            </div>
                                            <div class="form-group">
                                                <label for="stage-name-en">芸名（英語表記）</label>
                                                {{ form.stage_name_en|add_class:"form-control" }}
                                            </div>
                                            <div class="form-group form-customize" rows="5">
                                                <label for="philosophy-quote">テーマ（140字以内）</label>
                                                <div class="form-textarea" id="philosophy-quote">
                                                    {{ form.theme_quote|add_class:"form-control" }}
                                                </div>
                                            </div>
                                            <div class="form-group form-customize textarea-large" rows="5">
                                                <label for="profile-quote">プロフィール（400字以内）</label>
                                                <div class="form-textarea" id="profile-quote">
                                                    {{ form.profile_quote|add_class:"form-control" }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="account__profile-social">
                                            <div class="account__sub-title">LINK</div>
                                            <div class="account__form-group">
                                                <div class="form-group">
                                                    <label for="id_official_site">OFFICIAL SITE</label>
                                                    {{ form.official_site|add_class:"form-control"|append_attr:"pattern:https?://.+" }}
                                                </div>
                                                <div class="form-group">
                                                    <label for="id_twitter_link">Twitter</label>
                                                    {{ form.twitter_link|add_class:"form-control"|append_attr:"pattern:https?://.+" }}
                                                </div>
                                                <div class="form-group">
                                                    <label for="id_facebook_link">Facebook</label>
                                                    {{ form.facebook_link|add_class:"form-control"|append_attr:"pattern:https?://.+" }}
                                                </div>
                                                <div class="form-group">
                                                    <label for="id_instagram_link">Instagram</label>
                                                    {{ form.instagram_link|add_class:"form-control"|append_attr:"pattern:https?://.+" }}
                                                </div>
                                                <div class="form-group">
                                                    <label for="id_youtube_link">Youtube</label>
                                                    {{ form.youtube_link|add_class:"form-control"|append_attr:"pattern:https?://.+" }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-5">
                                    <div class="join-form__action">
                                        <div class="join-form__form-group"><a
                                                class="button button--gradient button--gradient-primary button--round disabled"
                                                href="javascript:void(0)" role="button">次へ</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="step-input-3 slide-out-right" data-value="3" style="margin-top: -60px">
                                <div class="join-form__input-form">
                                    <div class="join-form__form-group">
                                        <div class="form-group form-customize" rows="5">
                                            <label for="offer-policy">案件受託についてのポリシー</label>
                                            <div class="form-textarea" id="offer-policy">
                                                {{ form.policy }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="join-form__qa">
                                        <div class="join-form__sub-title">Q&A</div>
                                    </div>
                                </div>
                                <div class="join-form__action">
                                    <div class="join-form__form-group">
                                        <button class="button button--gradient button--gradient-primary button--round
                                                       disabled" role="button" type="submit" style="border: none">
                                            応募する
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="hidden">
                            {{ form.role_creator }}

                            {{ form.question }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="modal fade" id="modalCrop">
            <div class="modal-dialog" style="transform: translate(0,10%);">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        <h4 class="modal-title">画像登録</h4>
                    </div>
                    <div class="modal-body">
                        <img src="" id="image" style="max-width: 100%;">
                    </div>
                    <div class="modal-footer">
                        <div class="btn-group pull-left" role="group">
                            <button type="button" class="btn btn-default js-zoom-in">
                                <span class="glyphicon glyphicon-zoom-in"></span>
                            </button>
                            <button type="button" class="btn btn-default js-zoom-out">
                                <span class="glyphicon glyphicon-zoom-out"></span>
                            </button>
                        </div>
                        <button type="button" class="btn btn-primary js-crop-and-upload">登録する</button>
                    </div>
                </div>
            </div>
        </div>
    </main>
    {% compress js inline %}
    <script src="{% static 'js/join_us.js' %}"></script>
    {% endcompress %}
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
{% endblock content %}
