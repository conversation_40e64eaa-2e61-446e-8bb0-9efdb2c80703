{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util %}
{% load i18n %}
{% load compress %}

{% block extrahead %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" />
{% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}" />
    <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
{% endcompress %}
{% compress js inline %}
    <script src="{% static 'js/cropper.min.js' %}"></script>
{% endcompress %}
{% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}" />
    <link href="{% static 'css/creator_setting.css' %}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{% static 'css/components/card.css' %}" />
{% endcompress %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
{% endblock%}

{% block content %}
<div class="user-info">
  <div class="container">
    <div class="user-info__wrap">
      <ul class="nav flex-column user-info__tabs-list">
        <li class="nav-item">
          <a class="nav-link" href="#tab_1">利用設定</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#tab_2">プロフィール</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#tab_3">お知らせ</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#tab_4">ブロックリスト</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#tab_5">ポジション</a>
        </li>
      </ul>
    </div>

    <div class="user-info__main account__info" id="tab_1">
      <div class="user-info__heading">
        <h3>利用設定</h3>
        <p class="account__field-text">{% trans "Description heading" %}</p>
      </div>

      <div class="user-info__content">
        <form method="post" action="{% url 'accounts:accounts_creator_setting' object.pk %}"
          enctype="multipart/form-data" class="user-info__form row" data-id="{{object.pk}}" id="creator_settings__form">
          {% csrf_token %}

          <div class="account__form-group form-group col-sm-12" id="tab_1">
            <div class="col-sm-12 form-group">
              <div class="account__form-group-wrap">
                <p class="account__field-label">{% trans "Heading trade-off slide" %}</p>
                <p class="account__field-hint">{% trans "Description trade-off slide" %}
                </p>
                <div class="account__tradeoff">
                  <div class="account__trade-slider">
                    {% for option in form.trading %}
                    <div class="account__trade-item {% if option.data.selected %}active{% endif %}"
                      data-option="{{option.data.value}}">
                    </div>
                    {% endfor %}
                  </div>
                  {{ form.trading|append_attr:"style:display:none;" }}
                  <div class="account__trade-label">
                    <div class="account__label-left">{% trans "Trade-off left" %}</div>
                    <div class="account__label-right">{% trans "Trade-off right" %}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-sm-12 form-group">
              <div class="account__form-group-wrap">
                <label class="col-sm-12" for="offer-policy">
                  <p class="account__field-label">クライテリア</p>
                  <p class="account__field-hint">{% trans "Description policy" %}
                  </p>
                  <div class="form-textarea" id="offer-policy">
                    {{ form.policy|append_attr:"class: form-textarea, id: form-control, cols: 40, rows: 8" }}
                  </div>
                </label>
              </div>
            </div>

            <div class="col-sm-12 form-group" style="margin-bottom: 0;">
              <div class="account__form-group-wrap">
                <label class="col-sm-12" for="offer-policy">
                  <p class="account__field-label">スケジュール</p>
                  <p class="account__field-hint">{% trans "Description schedule" %}</p>
                  <div class="col-md-12 form-group">
                    <div class="form-textarea" id="note-schedule">
                      {{form.note_schedule}}
                    </div>
                  </div>

                  <div class="col-md-12 form-group" style="margin: 8px -15px 16px;">
                    <div class="col-md-5 col-sm-12">
                      <div class="mcalendar mcalendar--small mcalendar__this_month"></div>
                    </div>
                    <div class="col-md-5 col-sm-12">
                      <div class="mcalendar mcalendar--small mcalendar__next_month"></div>
                    </div>
                  </div>
                  <div class="col-sm-12 form-group account__field-tasks {% if current_user.role == 'curator' %}cannot-check{% endif %}"
                       id="dealline_task">
                  </div>

                  <div class="col-sm-12 form-group">
                    <div class="account__form-group-wrap" style="margin-top: 8px;">
                      <div class="form-check custom-switch">
                        <label class="form-check-label">
                          <div class="form-check-group">
                            <input class="form-check-input switch-checkbox" type="checkbox" name="{{form.is_assigneer.name}}"
                              id="{{form.is_assigneer.attrs.id}}" {% if form.is_assigneer.value %}checked{% endif %}/><span class="switch-slider"></span>
                          </div>
                          <span class="switch-label" style="color: #000000">{% trans "Switch label" %}
                          </span>
                        </label>
                      </div>
                      <p class="account__field-text">{% trans "Description switch label" %}
                      </p>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <div class="account__form-group form-group col-sm-12" id="tab_2">
            <h3 class="account__form-heading">プロフィールサイト</h3>

            <div class="col-sm-12 form-group">
              <label class="col-sm-12" for="">
                <span class="account__field-label">URL</span>
                <p class="account__field-description">{% trans "URL" %}
                </p>
                <div class="account__form-group-link">
                  <div class="input-group form-group">
                    <span class="input-group-addon" id="basic-addon3">{{ host }}/</span>
                    {{ form.slug|append_attr:"class: form-control input-group-slug"|append_attr:"aria-describedby:basic-addon3"|append_attr:"pattern:[a-zA-Z0-9_-]{2,50}"|append_attr:"id:id_slug" }}
                  </div>
                  <div class="account__copy-link btn btn--primary {% if not form.slug.value %}disable{% endif %}">
                    <img src="/static/images/file_icon.png">
                    <span>コピー</span>
                  </div>
                </div>
                <span class="errorlist"></span>
              </label>
            </div>

            <div class="col-sm-12 form-group">
              <div id="qrcode"></div>
            </div>

            <div class="col-sm-12 form-group">
              <label class="col-sm-12" for="">
                <span class="account__field-label">セキュリティ</span>
                <p class="account__field-hint">{% trans "Description security" %}
                </p>
                <div class="account__sub-group">
                  {% for choice in form.show_profile %}
                  <div class="account__form-multi">
                    <label class="input-radio">
                      <input type="radio" name="{{ choice.data.name }}" {% if choice.data.selected %}checked{% endif %}
                        value="{{ choice.data.value }}" index="{{ choice.data.index }}" required="{{ choice.data.attrs.required}}"
                             id="{{ choice.data.attrs.id }}" />{{ choice.data.label }}
                      <div class="check-mark"></div>
                    </label>
                    <p class="account__field-text">{{ choice.data.value|get_description_show_profile }}</p>
                  </div>
                  {% endfor %}
                </div>
              </label>
            </div>

            <div class="col-sm-12 form-group">
              <label class="col-sm-12" for="">
                <span class="account__field-label">コンタクト</span>
                <p class="account__field-hint">プロフィールサイトでのお問い合わせの対応方法を選びましょう。 </p>
                <div class="account__sub-group">
                  <div class="account__form-multi card-container">
                  {% for choice in form.direct_contact %}
                    <input type="radio" name="direct_contact" {% if choice.data.selected %}checked{% endif %}
                        value="{{ choice.data.value }}" index="{{ choice.data.index }}" id="{{ choice.data.attrs.id }}"/>

                    {% if forloop.counter0 == 0 %}
                      <label class="input-radio-card {% if is_toppage_user %}btn--disabled{% endif %}" for="id_direct_contact_0">
                        <div class="input-radio-card-img"
                             style="background-image: url({% static 'images/direct_transaction_matching_bg.png' %})"></div>
                        <div class="input-radio-card-content">
                          <div class="input-radio-card-content-title heading--16">マッチングスタイル</div>
                          <div class="input-radio-card-content-description caption--11">
                            ご自身でオーナーと直接やりとりできます。SOREMOは、システムのみを提供します。
                          </div>
                          <div class="input-radio-card-content-highlight caption--11">利用料： {{ object.user|get_usage_fee_in_setting:'' }}%</div>
                        </div>
                      </label>
                    {% elif forloop.counter0 == 1 %}

                    <label class="input-radio-card {% if is_toppage_user %}btn--disabled{% endif %}" for="id_direct_contact_1">
                      <div class="input-radio-card-img" style="background-image: url({% static 'images/direct_transaction_agent_bg.png' %})"></div>
                      <div class="input-radio-card-content">
                        <div class="input-radio-card-content-title heading--16">エージェントスタイル</div>
                        <div class="input-radio-card-content-description caption--11">オーナーとは直接契約頂きます。SOREMOが窓口となって、オーナーとのやりとりを代行します。</div>
                        <div class="input-radio-card-content-highlight caption--11">利用料： {{ object.user|get_usage_fee_in_setting:'agent' }}%</div>
                      </div>
                    </label>
                    {% else %}

                    <label class="input-radio-card" for="id_direct_contact_2">
                      <div class="input-radio-card-img" style="background-image: url({% static 'images/direct_transaction_production_bg.png' %})"></div>
                      <div class="input-radio-card-content">
                        <div class="input-radio-card-content-title heading--16">プロダクションスタイル</div>
                        <div class="input-radio-card-content-description caption--11">SOREMOがオーナーと契約します。オーナーとの契約状況に関わらず、お仕事が完了したら、その対価が支払われます。</div>
                        <div class="input-radio-card-content-highlight caption--11">都度提示</div>
                      </div>
                    </label>
                    {% endif %}

                      {% endfor %}
                  </div>
                </div>
              </label>
            </div>

          </div>

          <div class="account__form-group form-group col-sm-12" id="tab_3">
            <h3 class="account__form-heading">お知らせ</h3>
            <p class="account__field-hint">{% trans "Description notification" %}
            </p>
            <div class="col-sm-12 form-group" style="margin-bottom: 40px;">
              <label class="col-sm-12" for="">
                <div class="account__sub-group">
                  <div class="account__form-multi">
                    {% for choice in form.notification %}
                      {% if choice.data.value == "one_day" %}
                        <div class="account__form-multi account__form-flex">
                          <label class="input-radio">
                            <input type="radio" name="{{ choice.data.name }}" {%if choice.data.selected%}checked{%endif%}
                              value={{choice.data.value}} index={{choice.data.index}} required={{choice.data.attrs.required}}
                              id={{choice.data.attrs.id}}/>{{ choice.data.label }}
                            <div class="check-mark"></div>
                          </label>
                        <div class="notification-time">
                          <select class="input-time" name={{form.hours.name}} id="time" value="{{creator.hours}}">
                              {% for i in 24|make_list %}
                                {% with forloop.counter|option_time as option_time %}
                                  <option value="{{ option_time }}" {% if option_time == creator.hours %}selected="selected"{% endif %}>
                                    {{ option_time }}
                                  </option>
                                {% endwith %}
                              {% endfor %}
                          </select>
                          </div>
                        </div>
                      {% else %}
                        <label class="input-radio">
                          <input type="radio" name="{{ choice.data.name }}" {%if choice.data.selected%}checked{%endif%}
                            value={{choice.data.value}} index={{choice.data.index}} required={{choice.data.attrs.required}}
                            id={{choice.data.attrs.id}}/>{{ choice.data.label }}
                          <div class="check-mark"></div>
                        </label>
                      {% endif %}
                    {% endfor %}
                  </div>
                </div>
              </label>
            </div>
          </div>

          <div class="account__form-group form-group col-sm-12" id="tab_4">
            <h3 class="account__form-heading">ブロックリスト</h3>
            {{ form_blocklist.management_form }}
            <p class="account__field-hint" id="block_list__hint">{% trans "Description block list" %}
            </p>
            {% for item in form_blocklist %}
              {% if item.company_name.value %}
                <div class="col-md-12 form-group form-group-wrap blocklist__edit" data-index="{{forloop.counter0}}">
                  <div class="form-group-content">
                    <div class="col-md-4 col-sm-12">
                      <label for="id_company" class="label_company">
                        {% if forloop.counter == 1 %}
                          <p class="account__field-text" style="margin-bottom: 8px;">{% trans "Label company name" %}</p>
                        {% endif %}
                        <input type="text" name="{{item.company_name.html_name}}" value="{{item.company_name.value}}" placeholder="株式会社コレモ"
                          class="form-control account__input-text maxlength-company input__company_name" id="id_company_{{item.pk}}">
                      </label>
                    </div>
                    <div class="col-md-4 col-sm-12">
                      <label for="id_reason" class="label_reason">
                        {% if forloop.counter == 1 %}
                          <p class="account__field-text" style="margin-bottom: 8px;">{% trans "Label reason" %}</p>
                        {% endif %}
                        <input type="text" name="{{item.reason.html_name}}" value="{{item.reason.value}}" placeholder="競合規制のため"
                          class="form-control account__input-text input__reason" id="id_reason_{{item.pk}}">
                      </label>
                    </div>
                    {{ item.id }}
                    {{ item.creator }}
                    <input type="checkbox" name="{{item.DELETE.html_name}}" id="{{item.DELETE.id_for_label}}" style="display: none;" class="delete__checkbox"/>
                    <div class="col-sm-12 form-group-action account__blocklist--unblock hide-action-delete">
                      <i class="delete-row icon icon--sicon-trash"></i>
                    </div>
                  </div>
                </div>
              {% endif %}
            {% endfor %}

            <div class="col-md-12 form-group form-group-wrap" id="form-group-wrap__add-button">
              <div class="account__add" id="button__add_blocklist">
                <div class="account__add-row">
                  <div class="account__add-icon account__blocklist__add-button">
                    <i class="icon icon--sicon-add-cirlce"></i>
                    <p>{% trans "Add block list" %}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

            <div class="account__form-group form-group col-sm-12" id="tab_5">
            <h3 class="account__form-heading">ポジション</h3>
            {% for group in skills %}
              <div class="col-md-12 form-group form-group-wrap">
                <p class="account__field-label">{{group.0}}</p>
                <div class="account__field-list-selected">
                {% for skill in group.1 %}
                  <span class="account__field-item {% if skill.2 %}selected{% endif %}" data-id="{{skill.0}}">{{skill.1}}</span>
                  <input type="checkbox" name="skills" value="{{skill.0}}" id="skill_{{skill.0}}" hidden {% if skill.2 %}checked{% endif %}>
                {% endfor %}
                </div>
              </div>
            {% endfor %}
            <div class="col-md-12 form-group form-group-wrap" style="margin-top: 8px;">
              <input type="button" class="btn btn--secondary" value="更新を申請" id="" disabled>
              <p class="account__field-text">{% trans "Note" %}</p>
            </div>
          </div>

          <div class="user-info__submit col-sm-12 acc_action">
            {% buttons %}
            <input type="submit" value="OK" id="btn__ok" class="btn btn--primary" />
            {% endbuttons %}
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
{% block extra_script %}

<script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}"; </script>
{% comment %} <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script> {% endcomment %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
<script src="{% url 'javascript-catalog' %}"></script>
    {% compress js inline %}
<script type="text/javascript" src="{% static 'js/creator_setting.js' %}"></script>
<script type="text/javascript" src="{% static 'js/qrcode.js' %}"></script>
    {% endcompress %}
{% endblock %}
