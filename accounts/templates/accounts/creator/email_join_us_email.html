{% load static %}
{% load bootstrap3 %}
{% load util %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Email Template Email</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
    <style type="text/css">
      @font-face {
        font-family: AxisRound;
        font-weight: 700;
        src: url('../fonts/AxisRound100StdN-B.otf');
      }

      @font-face {
        font-family: AxisRound;
        font-weight: 400;
        src: url('../fonts/AxisRound100StdN-R.otf');
      }

      @media only screen and (max-width: 620px) {
        body {
          font-size: 12px !important;
        }

        .email-title {
          font-size: 18px !important;
        }

        .email-sub-title {
          font-size: 14px !important;
        }

        .email {
          background-size: 23% auto !important;
        }

        .email-text {
          font-size: 12px;
        }

        .email-info {
          font-size: 12px;
        }

        .email-info-detail {
          font-size: 12px;
        }

        .email-qa {
          font-size: 12px;
        }

        .email-footer {
          font-size: 12px;
        }

        .email-policy {
          font-size: 12px !important;
        }
      }

    </style>
  </head>
  <body style="margin: 0; font-family: AxisRound, Hiragino Sans, Noto Sans Japanese, Yu Gothic, Meiryo, Hiragino Kaku Gothic Pro; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
    <main class="email" style="font-size: 13px; background-image: url(&quot;{% static 'images/email-bg.png'%}&quot;);
          background-repeat-x: no-repeat; background-position: right -20px top 0; background-size: 15vw auto;">
      <div class="email-container" style="max-width: 1170px; margin: 0 auto; padding: 0 15vw 0 15px;">
        <div class="email-logo" style="padding-top: 25px;"><img src="{{host}}/static/images/soremo-favi2_01.png" height="45px" width="45px" style="border-radius: 50%"></div>
        <h1 class="email-title" style="font-size: 24px; font-weight: 400; line-height: 1.1; color: #333; margin: 0 0 25px; padding-top: 25px;">応募を受け付けました</h1>
        <p class="email-text" style="margin-bottom: 8px">ご応募ありがとうございました。</p>
        <p class="email-text" style="margin-bottom: 8px">内容を確認し、改めて弊社よりご連絡いたします。</p>
        <h5 class="email-sub-title" style="font-size: 16px; font-weight: 400; line-height: 1.1; padding: 20px 0 10px; margin: 10px 0 10px; color: #585858;">登録した情報：</h5>
        <table class="email-info" role="presentation" border="0" cellpadding="0" cellspacing="0">
          <tr>
            <td style="width: 100px; padding-right: 25px">
              <img src="{{ creator_avt }}"
                   alt="User Avatar" border="0" style="width: 100px; max-width: 100px; border-radius: 50%;">
            </td>
            <td>
              <p style="margin: 0 0 8px;">{{creator.user.username}}</p>
              <p style="margin: 0 0 8px;">{{creator.user.email}}</p>
              <p style="margin: 0 0 8px;">{{creator.dob|date:"d.m.Y"}}</p>
              <p style="margin: 0;">{{creator.user.type}}</p>
            </td>
          </tr>
          <tr></tr>
          <tr>
            <td style="padding-top: 10px;">
              <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                <tr>
                    <td><a class="social-network" href="#" style="text-decoration: none;"><img src="{% static 'images/icon-home-email.svg' %}" alt="" style="width: 20px; margin-right: 6px;"></a></td>
                    {% if creator.facebook_link %}<td><a class="social-network" href="{{creator.facebook_link}}" style="text-decoration: none;"><img src="{% static 'images/icon-facebook-email.svg' %}" alt="" style="width: 20px; margin-right: 5px;"></a></td>{% endif %}
                    {% if creator.twitter_link %}<td><a class="social-network" href="{{creator.twitter_link}}" style="text-decoration: none;"><img src="{% static 'images/icon-twitter-email.svg' %}" alt="" style="width: 20px; margin-right: 5px;"></a></td>{% endif %}
                    {% if creator.instagram_link %}<td><a class="social-network" href="{{creator.instagram_link}}" style="text-decoration: none;"><img src="{% static 'images/icon-instagram-email.svg' %}" alt="" style="width: 20px; margin-right: 5px;"></a></td>{% endif %}
                </tr>
              </table>
            </td>
          </tr>
        </table>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin-top: 15px;">
          <tr>
            <td style="width: 30px; vertical-align: top;">
              <p style="margin: 0 0 10px"><img src="{% static 'images/icon-music-email.svg' %}" alt="" style="width: 20px;"></p>
            </td>
             <td style="vertical-align: top;">
                {% for audio in creator.audio_creator.all %}
                  <span><p style="pointer-events: none; text-decoration: none; background-color: #019CC6; color: #fff; padding: 3px 20px; border-radius: 15px; font-size: 10px; word-break: keep-all; margin: 0 0 6px;">{{ audio }}</p></span>
                 {% endfor %}
              </td>
          </tr>
        </table>
        <table class="email-info-detail" role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin: 30px 0 30px">
          <tr>
            <td style="width: 15%; min-width: 115px; padding: 0 15px 15px 0; vertical-align: top;">電話番号：</td>
            <td style="width: 85%; vertical-align: top;">{{creator.phone|default:"n/a"}}</td>
          </tr>
          <tr></tr>
          <tr>
            <td style="width: 15%; min-width: 115px; padding: 0 15px 15px 0; vertical-align: top;">芸名：</td>
            <td style="width: 85%; vertical-align: top;">{{creator.stage_name|default:"n/a"}}</td>
          </tr>
          <tr></tr>
          <tr>
            <td style="width: 15%; min-width: 115px; padding: 0 15px 15px 0; vertical-align: top;">芸名（英語）：</td>
            <td style="width: 85%; vertical-align: top;">{{creator.stage_name_en|default:"n/a"}}</td>
          </tr>
          <tr></tr>
          <tr>
            <td style="width: 15%; min-width: 115px; padding: 0 15px 15px 0; vertical-align: top;">マニフェスト：</td>
            <td style="width: 85%; vertical-align: top; padding: 0 0 15px;">“{{creator.theme_quote|default:"n/a"}}”</td>
          </tr>
          <tr></tr>
          <tr>
            <td style="width: 15%; min-width: 115px; padding: 0 15px 15px 0; vertical-align: top;">プロフィール：</td>
            <td style="width: 85%; vertical-align: top;">“{{creator.profile_quote|default:"n/a"}}”</td>
          </tr>
          <tr></tr>
        </table>
        <h6 style="font-size: 14px; font-weight: 400; line-height: 1.1; margin: 20px 0 10px;">案件受託についてのポリシー</h6>
        <p class="email-policy" style="font-size: 14px; padding-left: 15px;">兵納ぱぶねう中住フ鹿新乳テスノワ口風テケスソ乳テスノワ口風テケスソ高世なてぶべ認広新乳テスノワ口風ソ乳テ表邸支高世なてぶべ認広心びでド</p>
        <h6 style="font-size: 14px; line-height: 1.1; font-weight: 400; padding: 10px 0 5px; margin: 20px 0 10px;">Q&A</h6>
        <ul class="email-qa" style="list-style: none; padding-left: 10px;">
        {% for item in questions %}
              {% if item.question %}
                  <li style="margin: 0 0 10px;">
                    <span style="width: 8px; height: 8px; padding: 2px 4px; margin-right: 15px; background-color: #6f6e6e; color: #fff; border-radius: 50%;">{{ forloop.counter }}</span>
                    <span>{{item.question.content}}</span>
                    {% if item.question.multi is False %}
                       <span style="margin-left: 10px;"><a href="#" style="color: #009cc6; text-decoration: none;">{{item.answer}}</a></span>
                    {% else %}
                    <span>
                      <ul style="list-style: none; padding-left: 35px; margin: 8px 0 0;">
                      {% for answer in item.answer %}
                          <li><a href="#" style="color: #009cc6; text-decoration: none;">・ {{answer}}</a></li>
                      {% endfor %}
                      </ul>
                    </span>
                    {% endif %}
                  </li>
              {% endif %}
          {% endfor %}
          </ul>
        <div class="email-button" style="text-align: center; padding-bottom: 30px;">
          <a class="button" style="min-width: 250px; margin-top: 15px; background-image: linear-gradient(45deg, rgba(0, 161, 221, 0.73), #009ace); height: 44px; line-height: 44px; display: inline-block; padding: 0 15px; color: #fff; border-radius: 23px; text-decoration: none;">
            応募を認証
          </a>
        </div>
        <div class="email-footer" style="padding: 30px 0 25px;">
          <div class="table" role="presentation" border="0" cellpadding="0" cellspacing="0">
              Copyright© SOREMO Co.,Ltd. All Right Reserved.
          </div>
        </div>
      </div>
    </main>
  </body>
</html>
