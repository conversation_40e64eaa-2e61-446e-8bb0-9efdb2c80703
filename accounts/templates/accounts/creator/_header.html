{% load util %}
{% with  creator_profile.get_block_header as header_block %}
  <div class="header-fullscreen-container {% if can_edit %}editable{% endif %}{% if header_block.type_header == '2' %} style2{% endif %}"
       data-key-visual-pc="{{ header_block.get_key_visual_pc }}"
       data-key-visual-sp="{{ header_block.get_key_visual_sp }}"
       data-style="{{ header_block.type_header }}"
       data-content-style="{{ header_block.type_header }}"
       data-key-visual-pc-name="{{ header_block.get_key_visual_pc_name }}"
       data-key-visual-sp-name="{{ header_block.get_key_visual_sp_name }}"
       data-banner-name="{{ header_block.get_banner_name }}"
       data-key-visual-pc-type="{{ header_block|get_type_file_header:'key_visual_pc' }}"
       data-key-visual-sp-type="{{ header_block|get_type_file_header:'key_visual_sp' }}"
       data-banner-type="{{ header_block|get_type_file_header:'banner' }}">
    <video autoplay playsinline muted loop id="bgVideoPC" preload="metadata">
      <source src="">
    </video>
    <video autoplay playsinline muted loop id="bgVideoSP" preload="metadata">
      <source src="">
    </video>

    <div class="container header-fullscreen-container-content rel">
      <video autoplay playsinline muted loop id="bgVideoBanner" preload="metadata">
        <source src="">
      </video>
      <div class="logo-container container">
        <img class="scroll-to-top" src="{{ header_block.get_logo }}" style="height: 40px; width: auto;">
      </div>
      {% with show_sign_in=user|check_show_sign_in:creator_profile check_redirect_contact=user|check_redirect_contact:user_creator %}
      <div class="header-profile-artist container"> 
        {% with header_block.get_logo as logo_url %}
        <div {% if logo_url %} class="header-profile-pc-logo scroll-to-top"
             {% else %} class="header-profile-pc-logo nologo" {% endif %}
             data-logo="{{ header_block.get_logo }}"
             data-logo-name="{{ header_block.get_logo_name }}">
        </div>
        {% endwith %}
        <div class="header-profile-pc">
          <div class="header-profile-pc-right">
            {% with creator_profile|get_button_in_top_menu as item_block %}
              {% if item_block %}
                <div class="header-profile-pc-right-link">
                  <a class="sheader-link sheader-link-block"
                     href="#profile">{{ item_block.item_block.profile_block.section_name_jp }}</a>
                </div>
              {% endif %}
            {% endwith %}

            {% if user|check_show_contact:user_creator %}
              <div class="header-profile-pc-right-link btn-open-modal-contact-artist {% if user_creator.user_creator.first.show_profile == 'public' and user_creator.user_creator.first.is_direct %}artist-public-direct-on{% endif %}"
                   check-redirect-contact='{{ check_redirect_contact }}'>
                CONTACT
              </div>
            {% endif %}

            {% if not user.is_authenticated %}
              {% if show_sign_in or user|check_redirect_contact_boolean:user_creator %}
                <div class="header-profile-pc-right-link">
                  <a class="sheader-link" href="{% url 'accounts:accounts_login' %}">
                    SIGN IN
                  </a>
                </div>
              {% endif %}
            {% endif %}
          </div>
        </div>

        <div class="header-profile-sp">
          <div class="header-profile-sp-button">
          </div>
          <div class="header-profile-sp-dropdown">
            <div class="header-profile-sp-button-close">
            </div>

            {% with creator_profile|get_button_in_top_menu as item_block %}
              {% if item_block %}
                <div class="header-profile-sp-link">
                  <a class="sheader-link sheader-link-block"
                     href="#profile">{{ item_block.item_block.profile_block.section_name_jp }}</a>
                </div>
              {% endif %}
            {% endwith %}

            {% if not user.is_authenticated or user.role == 'master_client' %}
              <div class="header-profile-sp-link btn-open-modal-contact-artist {% if user_creator.user_creator.first.show_profile == 'public' and user_creator.user_creator.first.is_direct %}artist-public-direct-on{% endif %}"
              check-redirect-contact='{{ check_redirect_contact }}'>
                CONTACT
              </div>
            {% endif %}
            
            {% if show_sign_in or user|check_redirect_contact_boolean:user_creator %}
              <div class="header-profile-sp-link">
                <a class="sheader-link" href="{% url 'accounts:accounts_login' %}">
                  SIGN IN
                </a>
              </div>
            {% endif %}

            <div class="header-profile-sp-link change-language-button" data-language='jp'>
              <span class="label--8" data-select='jp'>日本</span>
              <span class="label--8" data-select='en'>En</span>
            </div>
          </div>
        </div>
      </div>
      {% endwith %}
      {% with catchphrase_jp_1=header_block.catchphrase_jp_1 catchphrase_jp_2=header_block.catchphrase_jp_2 catchphrase_jp_3=header_block.catchphrase_jp_3 catchphrase_en_1=header_block.catchphrase_en_1 catchphrase_en_2=header_block.catchphrase_en_2 catchphrase_en_3=header_block.catchphrase_en_3 %}
        <div class="heading--40 profile-artist-catch-phrase"
             data-catchphrase-1="{{ catchphrase_jp_1 }}"
             data-content-catchphrase-1="{{ catchphrase_jp_1 }}"
             data-catchphrase-1-en="{{ catchphrase_en_1 }}"
             data-content-catchphrase-1-en="{{ catchphrase_en_1 }}"
             data-catchphrase-2="{{ catchphrase_jp_2 }}"
             data-content-catchphrase-2="{{ catchphrase_jp_2 }}"
             data-catchphrase-2-en="{{ catchphrase_en_2 }}"
             data-content-catchphrase-2-en="{{ catchphrase_en_2 }}"
             data-catchphrase-3="{{ catchphrase_jp_3 }}"
             data-content-catchphrase-3="{{ catchphrase_jp_3 }}"
             data-catchphrase-3-en="{{ catchphrase_en_3 }}"
             data-content-catchphrase-3-en="{{ catchphrase_en_3 }}"
             data-banner="{{ header_block.get_banner }}"
             data-banner-name="{{ header_block.get_banner_name }}">
          <span>{{ catchphrase_jp_1 }}</span><span>{{ catchphrase_jp_2 }}</span><span>{{ catchphrase_jp_3 }}</span>
        </div>
      {% endwith %}

      <div class="profile-artist-info">
        {% with artist_name_jp=header_block.artist_name_jp artist_name_en=header_block.artist_name_en %}
          <div class="profile-artist-info-name {% if not header_block.display_tag_line_1 %}hide{% endif %}"
               data-tagline-1-toggle="{{ header_block.display_tag_line_1 }}"
               data-content-tagline-1-toggle="{{ header_block.display_tag_line_1 }}"
               data-tagline-1="{{ artist_name_jp }}"
               data-content-tagline-1="{{ artist_name_jp }}"
               data-tagline-1-en="{{ artist_name_en }}"
               data-content-tagline-1-en="{{ artist_name_en }}"
               data-tagline-1-initial="{{ creator_profile.creator.user.get_display_name }}"
               data-tagline-1-initial-en="{{ creator_profile.creator.user.get_stage_name_en }}">{{ artist_name_jp }}
          </div>
        {% endwith %}
        {% with title_jp=header_block.title_jp title_en=header_block.title_en %}
          <div class="profile-artist-info-title heading--16 {% if not header_block.display_tag_line_2 %}hide{% endif %}"
               data-tagline-2-toggle="{{ header_block.display_tag_line_2 }}"
               data-content-tagline-2-toggle="{{ header_block.display_tag_line_2 }}"
               data-tagline-2="{{ title_jp }}"
               data-content-tagline-2="{{ title_jp }}"
               data-tagline-2-en="{{ title_en }}"
               data-content-tagline-2-en="{{ title_en }}"
               data-tagline-2-initial="{{ creator_profile.creator.user.position }}"
               data-tagline-2-initial-en="{{ creator_profile.creator.user.type }}">{{ title_jp }}
          </div>
        {% endwith %}
      </div>
    </div>
{#    {% if is_checker and public_profile and public_profile.get_block_header != header_block %}#}
{#      <div class="new-button">NEW</div>#}
{#      <div class="menu-checking side-rounded-menu hide">#}
{#        <div class="edit-btn menu-btn"></div>#}
{#        <div class="approve-btn menu-btn"></div>#}
{#      </div>#}
{#    {% endif %}#}
  </div>
{% endwith %}
