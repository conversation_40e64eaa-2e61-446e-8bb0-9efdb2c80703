{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 compress %}
{% load widget_tweaks %}
{% load static %}
{% block extrahead %}
<style type="text/css">
    .border-width-button{
        border-width: inherit;
    }
    .editor-datetime {
      position: relative;
    }
</style>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
{% endblock %}
{% block content %}

<form id="creator_social_media" method="post" action="{% url 'accounts:accounts_creator_social_media' creator.pk %}">
    {% csrf_token %}
    <main class="account">
        <div class="container">
            <div class="account__container">
            <div class="profile__page-title">プロフィール</div>
            <div class="account__profile-social">
                <div class="account__sub-title">LINKS</div>
                <div class="account__form-group">
                <div class="form-group">
                    <label for="official-site">OFFICIAL SITE</label>
                    {{ form.official_site|add_class:"form-control"|append_attr:"placeholder:https://www.soremo.jp"|append_attr:"pattern:https?://.+" }}
                </div>
                <div class="form-group">
                    <label for="twitter">Twitter</label>
                    {{ form.twitter_link|add_class:"form-control"|append_attr:"placeholder:@SOREMONTER"|append_attr:"pattern:https?://.+" }}
                </div>
                <div class="form-group">
                    <label for="facebook">Facebook</label>
                    {{ form.facebook_link|add_class:"form-control"|append_attr:"placeholder:https://www.facebook.com/soremo.jp"|append_attr:"pattern:https?://.+" }}
                </div>
                <div class="form-group">
                    <label for="instagram">Instagram</label>
                    {{ form.instagram_link|add_class:"form-control"|append_attr:"placeholder:@soremonster"|append_attr:"pattern:https?://.+" }}
                </div>
                </div>
            </div>
            <div class="account__action">
                <div class="account__form-group">
                    {% buttons %}
                    <input type="submit" class="button button--gradient button--gradient-primary button--round border-width-button" role="button" value="変更を申請"/>
                    {% endbuttons %}
                </div>
            </div>
            </div>
        </div>
    </main>
</form>
    {% compress js inline %}
<script src="{% static 'js/creator.js' %}"></script>
<script>
    function hasHtml5Validation () {
          return typeof document.createElement('input').checkValidity === 'function';
        }
    if (hasHtml5Validation()) {
        $('#creator_social_media').submit(function (e) {
            $(this).find('input#id_twitter_link').trigger('change');
            $(this).find('input#id_instagram_link').trigger('change');
            if (!this.checkValidity()) {
                e.preventDefault();
                $(this).addClass('invalid');
                alert("URLのフォーマットが正しくありません。");
            } else {
                $(this).removeClass('invalid');
            }
        });
    }
    let social_form_el = $('form#creator_social_media');
    social_form_el.on('change', 'input#id_twitter_link, input#id_instagram_link', function (e) {
      let twitter_origin = "https://www.twitter.com";
      let instagram_origin = "https://www.instagram.com";

      let social_origin = instagram_origin;
      if ($(this).is('#id_twitter_link')) {
        social_origin = twitter_origin;
      }
      let raw_input = $(this).val().trim();
      if (!raw_input) {
        return
      }
      let social_link = new URL(raw_input, social_origin);
      if (social_link.origin !== social_origin) {
        social_link = new URL(social_link.pathname, social_origin);
      }
      $(this).val(social_link.toString());
    });
</script>
    {% endcompress %}
{% endblock %}
