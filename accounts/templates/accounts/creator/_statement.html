{% load util %}
{% with  creator_profile.get_block_statement as statement_block %}
  {% with creator_profile.creator.user as user_creator %}
    <div class="statement container {% if can_edit %}editable{% endif %}">
      <div class="statement-container">
        <div class="statement-main {% if not statement_block.is_show_avatar %}no-avatar{% endif %}">
          <div class="statement-avatar">
            <img src="{{ user_creator|get_avatar:'medium' }}" alt="">
          </div>

          <div class="statement-quote" data-quote="{{ statement_block.theme_jp }}"
               data-content-quote="{{ statement_block.theme_jp }}"
               data-quote-en="{{ statement_block.theme_en }}"
               data-content-quote-en="{{ statement_block.theme_en }}"></div>
        </div>
        <div class="statement-info"
             data-toggle-avatar="{{ statement_block.is_show_avatar }}"
             data-content-toggle-avatar="{{ statement_block.is_show_avatar }}"
             data-toggle-name="{{ statement_block.is_show_name }}"
             data-content-toggle-name="{{ statement_block.is_show_name }}"
             data-toggle-title="{{ statement_block.is_show_title }}"
             data-content-toggle-title="{{ statement_block.is_show_title }}">
          <div class="statement-info-name heading--16 {% if not statement_block.is_show_name %}hide{% endif %}"
               data-artist-name="{{ user_creator.get_display_name }}"
               data-artist-name-en="{{ user_creator.get_stage_name_en }}"></div>
          <div class="statement-info-title caption--11 {% if not statement_block.is_show_title %}hide{% endif %}"
               data-title="{{ user_creator.position }}"
               data-title-en="{{ user_creator.type }}"></div>
        </div>

        <div class="edit-profile-statement-btn edit-button"></div>
      </div>
    </div>
  {% endwith %}
{% endwith %}
