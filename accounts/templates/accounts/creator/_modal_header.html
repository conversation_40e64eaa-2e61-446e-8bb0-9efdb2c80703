{% load static %}

<div class="profile-input-container flex_column align-left">
    <div class="form-group col-sm-12 col-xs-12 nice-scroll" style="padding: 0; margin-bottom: -8px; overflow-x: scroll;">
        <div class="account__form-multi card-container" style="margin-left: 4px;">
            <input type="radio" name="header_style_radio" checked
                   value="1" index="1" id="header_style_radio_option1"/>

            <label class="input-radio-card" for="header_style_radio_option1">
                <div class="input-radio-card-img"
                    style="background-image: url({% static 'images/bg_header_fullscreen.png' %})"></div>
                <div class="input-radio-card-content">
                    <div class="input-radio-card-content-title heading--16">フルスクリーン</div>
                    <div class="input-radio-card-content-description caption--11">キービジュアルを全幅で表示。キャッチフレーズ、タグラインはオンオフできます。</div>
                </div>
            </label>

            <input type="radio" name="header_style_radio"
                   value="2" index="2" id="header_style_radio_option2"/>

            <label class="input-radio-card" for="header_style_radio_option2">
                <div class="input-radio-card-img"
                    style="background-image: url({% static 'images/bg_header_banner.png' %})"></div>
                <div class="input-radio-card-content">
                    <div class="input-radio-card-content-title heading--16">バナー</div>
                    <div class="input-radio-card-content-description caption--11">縦横比固定のバナーを表示。アーティスト名、タイトルはオンオフできます。</div>
                </div>
            </label>
        </div>
    </div>

    <hr>

    <div class="form-group col-sm-8 col-xs-12" style="padding: 0; margin-bottom: 16px;">
        <span class="heading--16" style="line-height: 200%; margin-bottom: 8px;">ロゴ<span class="account__jp-astarisk-op"
            style="margin-left: 8px; line-height: 200%; margin-bottom: 8px; font-size: 8px;">[任意]</span></span>
            <div class="caption--11 hint-text">左上のロゴ画像を設定できます。（透過PNG推奨）</div>
        <div class="mattach mattach-form">
            <div class="mcomment-attached">
                <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
                    <div class="mattach-previews mattach-previews-form collection">
                        <div class="mattach-template mattach-template-form collection-item item-template">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                                class="icon icon--sicon-close"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mattach-template current-file">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                        class="icon icon--sicon-close"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dropzone-area" id="profile-header-fullscreen-logo-dz">
                <div class="dz-button">
                    <i class="icon icon--sicon-add-cirlce"></i>
                    <p>ファイルを選択</p>
                </div>
            </div>
        </div>
    </div>

    <hr>

    <div class="form-group col-sm-8 col-xs-12" style="padding: 0; margin-bottom: 16px;">
        <span class="heading--16" style="line-height: 200%; margin-bottom: 8px;">キービジュアル<span class="account__jp-astarisk-op"
            style="margin-left: 8px; line-height: 200%; margin-bottom: 8px; font-size: 8px;">[任意]</span></span>
            <div class="caption--11 hint-text">フルスクリーンの画像か動画をアップロードしましょう。</div>
        <div class="mattach mattach-form">
            <div class="mcomment-attached">
                <div class="mattach-preview-container mattach-preview-container-form-keyvisual-pc">
                    <div class="mattach-previews mattach-previews-form collection">
                        <div class="mattach-template mattach-template-form collection-item item-template">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                                class="icon icon--sicon-close"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mattach-template current-file">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                        class="icon icon--sicon-close"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dropzone-area" id="profile-header-fullscreen-kvpc-dz">
                <div class="dz-button">
                    <i class="icon icon--sicon-add-cirlce"></i>
                    <p>ファイルを選択</p>
                </div>
            </div>
        </div>
        <div class="preview-header-keyvisual-pc hide"></div>
    </div>

    <div class="form-group col-sm-8 col-xs-12" style="padding: 0; margin-bottom: 16px;">
        <span class="heading--16" style="line-height: 200%; margin-bottom: 8px;">キービジュアル<span class="account__jp-astarisk-op"
            style="margin-left: 8px; line-height: 200%; margin-bottom: 8px; font-size: 8px;">[任意]</span></span>
            <div class="caption--11 hint-text">スマートフォン向けの縦撮りデータを追加して、切り替えもできます。</div>
        <div class="mattach mattach-form">
            <div class="mcomment-attached">
                <div class="mattach-preview-container mattach-preview-container-form-keyvisual-sp">
                    <div class="mattach-previews mattach-previews-form collection">
                        <div class="mattach-template mattach-template-form collection-item item-template">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                                class="icon icon--sicon-close"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mattach-template current-file">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                        class="icon icon--sicon-close"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dropzone-area" id="profile-header-fullscreen-kvsp-dz">
                <div class="dz-button">
                    <i class="icon icon--sicon-add-cirlce"></i>
                    <p>ファイルを選択</p>
                </div>
            </div>
        </div>
        <div class="preview-header-keyvisual-sp hide"></div>
    </div>

    <hr>

    <div class="form-group col-sm-8 col-xs-12" style="padding: 0; margin-bottom: 16px;">
        <span class="heading--16" style="line-height: 200%; margin-bottom: 8px;">バナー<span class="account__jp-astarisk-op"
            style="margin-left: 8px; line-height: 200%; margin-bottom: 8px; font-size: 8px;">[任意]</span></span>
            <div class="caption--11 hint-text">2.35:1（シネスコープ）の画像をアップロードしましょう。PDF書き出しでも利用します。</div>
        <div class="mattach mattach-form">
            <div class="mcomment-attached">
                <div class="mattach-preview-container mattach-preview-container-form-banner">
                    <div class="mattach-previews mattach-previews-form collection">
                        <div class="mattach-template mattach-template-form collection-item item-template">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                                class="icon icon--sicon-close"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mattach-template current-file">
                            <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                                        <i class="icon icon--sicon-close"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dropzone-area" id="profile-header-fullscreen-banner-dz">
                <div class="dz-button">
                    <i class="icon icon--sicon-add-cirlce"></i>
                    <p>ファイルを選択</p>
                </div>
            </div>
        </div>
        <div class="preview-header-banner hide"></div>
    </div>

    <hr>

    <div class="form-group col-sm-12" style="padding: 0; margin-bottom: 16px;">
      <span class="heading--16" style="line-height: 200%; margin-bottom: 8px;">キャッチフレーズ<span class="account__jp-astarisk-op"
                                              style="margin-left: 8px; line-height: 200%; margin-bottom: 8px; font-size: 8px;">[任意]</span></span>
      <div class="caption--11 hint-text">PCでは１行。SPでは3行で表示されます。</div>
      <div class="col-sm-12 mg-top-xs" style="margin-top: 12px; padding: 0">
        <div class="profile-title-input col-sm-4 col-xs-12" style="padding: 0 0 0 8px; margin: 0 8px 0 -8px">
          <input type="text" name="catch-phrase-jp-1" placeholder="音で" class="form-control" id="id_catch-phrase-jp-1"
                maxlength="15">
        </div>
        <div class="profile-title-input col-sm-4 col-xs-12" style="padding: 0 8px 0 0; margin: 0 -8px 0 0">
          <input type="text" name="catch-phrase-jp-2" placeholder="“ワクワク”を" class="form-control"
                id="id_catch-phrase-jp-2" maxlength="15">
        </div>
        <div class="profile-title-input col-sm-4 col-xs-12" style="padding: 0 8px 0 0; margin: 0 -8px 0 8px">
            <input type="text" name="catch-phrase-jp-3" placeholder="デザイン" class="form-control"
                  id="id_catch-phrase-jp-3" maxlength="15">
          </div>
      </div>

      <div class="col-sm-12 mg-top-xs" style="margin-top: 12px; padding: 0">
        <div class="profile-title-input col-sm-4 col-xs-12" style="padding: 0 0 0 8px; margin: 0 8px 0 -8px">
          <input type="text" name="catch-phrase-en-1" placeholder="Design" class="form-control" id="id_catch-phrase-en-1"
                maxlength="15">
        </div>
        <div class="profile-title-input col-sm-4 col-xs-12" style="padding: 0 8px 0 0; margin: 0 -8px 0 0">
          <input type="text" name="catch-phrase-en-2" placeholder="with" class="form-control"
                id="id_catch-phrase-en-2" maxlength="15">
        </div>
        <div class="profile-title-input col-sm-4 col-xs-12" style="padding: 0 8px 0 0; margin: 0 -8px 0 8px">
            <input type="text" name="catch-phrase-en-3" placeholder="Sound" class="form-control"
                  id="id_catch-phrase-en-3" maxlength="15">
          </div>
      </div>
    </div>

    <hr>
    <div class="form-group col-sm-12 tagline-component" style="padding: 0; margin-bottom: 16px;">
        <div class="custom-switch bodytext--13">
            <label class="form-check-label">
              <div class="form-check-group">
                <input class="form-check-input switch-checkbox" type="checkbox" name="switch-tagline1"
                      id="switch-tagline1">
                <span class="switch-slider"></span>
              </div>
              <span class="switch-label bodytext--13">タグライン１を表示</span>
            </label>
        </div>
        <div class="col-sm-12 mg-top-xs" style="margin-top: 12px; padding: 0">
          <div class="profile-title-input col-sm-5 col-xs-12" style="padding: 0 0 0 8px; margin: 0 8px 0 -8px">
            <input type="text" name="tagline1_jp" placeholder="アーティスト名" class="form-control" id="id_tagline1_jp"
                  maxlength="20">
          </div>
          <div class="profile-title-input col-sm-5 col-xs-12" style="padding: 0 8px 0 0; margin: 0 -8px 0 8px">
            <input type="text" name="tagline1_en" placeholder="Artist Name" class="form-control"
                  id="id_tagline1_en" maxlength="20">
          </div>
        </div>
    </div>

    <div class="form-group col-sm-12 tagline-component" style="padding: 0; margin-bottom: 16px;">
        <div class="custom-switch bodytext--13">
            <label class="form-check-label">
              <div class="form-check-group">
                <input class="form-check-input switch-checkbox" type="checkbox" name="switch-tagline2"
                      id="switch-tagline2">
                <span class="switch-slider"></span>
              </div>
              <span class="switch-label bodytext--13">タグライン２を表示</span>
            </label>
        </div>
        <div class="col-sm-12 mg-top-xs" style="margin-top: 12px; padding: 0">
          <div class="profile-title-input col-sm-5 col-xs-12" style="padding: 0 0 0 8px; margin: 0 8px 0 -8px">
            <input type="text" name="tagline2_jp" placeholder="タイトル" class="form-control" id="id_tagline2_jp"
                  maxlength="40">
          </div>
          <div class="profile-title-input col-sm-5 col-xs-12" style="padding: 0 8px 0 0; margin: 0 -8px 0 8px">
            <input type="text" name="tagline2_en" placeholder="Title" class="form-control"
                  id="id_tagline2_en" maxlength="40">
          </div>
        </div>
    </div>
</div>
