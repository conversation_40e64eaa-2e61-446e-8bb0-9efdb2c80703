{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load compress %}
{% block content %}
    <style type="text/css">
        .border-width-button {
            border-width: inherit;
        }

        .editor-datetime {
            position: relative;
        }

        #get_zip_code {
            cursor: pointer;
        }

        .datepicker .day {
            cursor: pointer;
        }

        .datepicker .day.active:after {
            content: unset
        }

        .datepicker .prev:before {
            left: 7px
        }
        .forgot_pass {
            margin: 10px 0 0;
        }

        .loading-icon {
            background-image: url({% static 'images/loading-circle.svg' %});
            background-size: 40px;
            background-position: center;
            background-repeat: no-repeat;
            margin-bottom: 25px;
            min-height: 30px;
            width: 30px;
        }

        .pt-0 {
          padding-top: 2px;
        }
    </style>
    <main class="account">
        <div class="container">
            <div class="account__container">
                <form method="post" id="creator_info_form"
                      action="{% url 'accounts:accounts_creator_info' object.pk %}">
                    {% csrf_token %}
                    <div class="account__info">
                        <div class="account__title">アカウント情報</div>
                        {{ form.post_number.errors }}
                        <div class="account__form-group">
                            <div class="form-group">
                                <label for="email">メールアドレス*</label>
                                {{ form.email|add_class:"form-control" }}
                            </div>
                            <div class="form-group">
                                <label for="password">パスワード</label>
                                <div>
                                    <a class="button button--background button--background-primary button--round forgot_pass"
                                       href="{% url 'accounts:pwd_reset_confirm' uidb64=uidb64 token=token %}"
                                       role="button">変更する</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="account__personal-info">
                        <div class="account__sub-title">個人情報</div>
                        <div class="account__form-group">
                            <div class="account__column-2">
                                <div class="form-group">
                                    <label for="last_name">姓*</label>
                                    {{ form.last_name|add_class:"form-control" }}
                                </div>
                                <div class="form-group">
                                    <label for="first_name">名*</label>
                                    {{ form.first_name|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="account__notice">※本名（プロフィールページには掲載はされません）</div>
                            <div class="form-group">
                                <label for="birthday">生年月日 *</label>
                                {{ form.dob|add_class:"form-control"|append_attr:"data-format=YYYY/MM/DD data-value=1988/09/20" }}
                            </div>

                            <div class="account__column-2">
                                <div class="form-group">
                                    <label for="post_number">郵便番号*</label>
                                    {{ form.post_number|add_class:"form-control" }}
                                </div>
                                <a class="button button--background button--background-primary button--round border-width-button"
                                   id="get_zip_code" href="javascript:void(0)">郵便番号検索</a>
                                <div class="loading-icon button hide"></div>
                            </div>
                            <div class="account__column-2">
                                <div class="form-group">
                                    <label for="province">都道府県*</label>
                                    {{ form.province|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="city">市区町村*</label>
                                {{ form.city|add_class:"form-control" }}
                            </div>
                            <div class="form-group">
                                <label for="mansion">町名・番地・建物名</label>
                                {{ form.mansion|add_class:"form-control" }}
                            </div>
                            <div class="form-group">
                                <label for="phone">電話番号*</label>
                                {{ form.phone|add_class:"form-control" }}
                            </div>
                        </div>
                    </div>
                    <div class="account__transfer-info">
                        <div class="account__sub-title">入金口座</div>
                        <div class="account__form-group">
                            <div class="form-group">
                                <label for="bank">銀行名*</label>
                                {{ form.bank|add_class:"form-control" }}
                            </div>
                            <div class="account__column-2">
                                <div class="form-group">
                                    <label for="bank_branch">支店*</label>
                                    {{ form.bank_branch|add_class:"form-control" }}
                                </div>

                                <div class="form-group">
                                    <label for="bank_branch_number">支店番号*</label>
                                    {{ form.bank_branch_number|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="account__column-2">
                                <div class="form-group">
                                    <label for="account_number">口座種類*</label>
                                    {{ form.account_type|add_class:"form-control pt-0" }}
                                </div>

                                <div class="form-group">
                                    <label for="account_number">口座番号*</label>
                                    {{ form.account_number|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="account__sub-group">
                                <div class="account__input-title">自動振込</div>
                                {% for choice in form.checkout_setting %}
                                    <label class="input-radio">
                                        <input type="radio" name="{{ choice.data.name }}"
                                               {% if choice.data.selected %}checked{% endif %}
                                               id="{{ choice.data.attrs.id }}"
                                               required="{{ choice.data.attrs.required }}"
                                               index="{{ choice.data.index }}"
                                               value="{{ choice.data.value }}"/>{{ choice.data.label }}
                                        <div class="check-mark"></div>
                                    </label>
                                {% endfor %}
                                <div class="checkbox input-checkbox">
                                    {{ form.next_checkout }}
                                    <label for="auto-withdraw">30,000円未満は、次回にまとめる。</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="account__action">
                        <div class="account__form-group">
                            {% buttons %}
                                <input type="submit"
                                       class="button button--gradient button--gradient-primary button--round border-width-button"
                                       role="button" value="OK"/>
                            {% endbuttons %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    {% compress js inline %}
    <script src="{% static 'js/combodate.js' %}"></script>
    <script src="{% static 'js/common_variable.js' %}"></script>
    <script src="{% static 'js/creator_info.js' %}"></script>
    {% endcompress %}
{% endblock content %}
