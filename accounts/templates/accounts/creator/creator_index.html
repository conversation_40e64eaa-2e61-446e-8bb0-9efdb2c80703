{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load user_agents %}
{% load static %}
{% load util %}
{% load i18n %}
{% load compress %}
{% block extrahead %}
  <script>
    $('html').attr('prefix', 'og: http://ogp.me/ns# fb: http://ogp.me/ns/ fb# article: http://ogp.me/ns/article#')
  </script>
  <meta property="og:url" content="https://soremo.jp/?v={% now 'U' %}" />
  <meta property="og:type" content="article" />
  {% if share_link_album %}
    <meta property="og:title" content="{{ share_link_album|get_title_sale_content:user }}" />
    {% if share_link_album|get_description_sale_content %}<meta property="og:description" content="{{share_link_album|get_description_sale_content }}"/>{% endif %}
    <meta property="og:image" content="{{ share_link_album|get_thumbnail_sale_content_url:user }}" />
    <meta property="og:site_name" content="{{ share_link_album|get_title_sale_content:user }}" />
  {% else %}
    <meta property="og:title" content="{{ user_creator.get_display_name }}{% if user_creator.position %} | {{ user_creator.position }}{% endif %}" />
    <meta property="og:description" content="{{ creator_profile.theme_quote }}" />
    <meta property="og:image" content="{{ user_creator|get_avatar:'medium' }}" />
    <meta property="og:site_name" content="{{ title_page }}" />
  {% endif %}


  <meta property="og:image:width" content="400" />
  <meta property="og:image:height" content="230" />

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/css/splide.min.css">
    {% compress css %}
    <link href="{% static 'css/jquery.datetimepicker.min.css' %}" rel="stylesheet">
  <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
  <link href="{% static 'css/modal_manager.css' %}" rel="stylesheet">
  <link href="{% static 'css/uploading-button.css' %}" rel="stylesheet">
  <link href="{% static 'css/modal_fullscreen.css' %}" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link href="{% static 'css/creator_style.css' %}" rel="stylesheet">
  <link href="{% static 'css/order_step.css' %}" rel="stylesheet">
  <link href="{% static 'css/topic_style.css' %}" rel="stylesheet">
  <link href="{% static 'css/creator_profile.css' %}" rel="stylesheet">
  <link href="{% static 'css/components/modal.css' %}" rel="stylesheet">
  <link href="{% static 'css/components/utils.css' %}" rel="stylesheet">
  <link href="{% static 'css/components/header.css' %}" rel="stylesheet">
  <link href="{% static 'css/components/card.css' %}" rel="stylesheet">
  <link href="{% static 'css/profile_statement.css' %}" rel="stylesheet">
  <link href="{% static 'css/profile_footer.css' %}" rel="stylesheet">
  <link href="{% static 'css/audio-navi.css' %}" rel="stylesheet">
  <link href="{% static 'css/components/list/item.css' %}" rel="stylesheet">
    {% endcompress %}
{% endblock %}

{% block content %}
  <style>
  .hash-tag {
    color: #009ace;
  }

  [contentEditable=true]:empty:not(:focus):before {
    content: attr(data-text);
    color: darkgrey;
  }
</style>


  <div id='loading_animation'>
    <svg class='loader-example' viewBox='0 0 100 100'>
      <defs>
          <filter id='goo'>
              <feGaussianBlur in='SourceGraphic' stdDeviation='8' result='blur' />
              <feColorMatrix in='blur' mode='matrix' values='1 0 0 0 0
                                                            0 1 0 0 0
                                                            0 0 1 0 0
                                                            0 0 0 25 -8' result='goo' />
              <feBlend in='SourceGraphic' in2='goo' />
          </filter>
      </defs>
      <g filter='url(#goo)' fill='#f0f0f0' stroke='#fcfcfc'>
          <g transform='translate(50, 50)'>
              <g class='circle -a'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='25' cy='50' r='9' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -b'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='25' r='8'  />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -c'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='75' cy='50' r='7' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -d'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='75' r='6' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -e'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='25' cy='50' r='5' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -f'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='25' r='4' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -g'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='75' cy='50' r='3' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -h'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='75' r='2' />
                  </g>
              </g>
          </g>
      </g>
    </svg>
  </div>
  <main class="profile header-fullscreen {% if not request|is_pc %}mobile{% endif %}" data-profile-id="{{ creator_profile.pk }}"
        data-modified="{{ creator_profile|get_modified_time }}" data-role="{{ user.role }}"
        data-user="{{ creator_profile.creator.user.pk }}" data-current-user="{{ user.pk }}"
        {% if creator_profile.creator.slug %}data-slug="{{ creator_profile.creator.slug }}"{% endif %} data-artist-name="{{ user_creator.get_display_name }}">
    {% include 'accounts/creator/_header.html' with public_profile=public_profile creator_profile=creator_profile %}
    {% include 'accounts/creator/_statement.html' with public_profile=public_profile creator_profile=creator_profile %}

    <!-- TODO: show topic profile -->
    {% include "creator/_topic_list.html" with user=user topics=topics gallery=False topic_ids=topic_ids total_page=total_page artist_id=creator_profile.creator.user.pk %}

    <div style="background: #FFFFFF;">
      <div class="container profile__content">

        <div class="container sample-audio-container {% if can_edit %}can-sort{% endif %}">
          {% for sale_content in sale_contents %}
            {% include 'accounts/creator/_sale_content.html' %}
          {% endfor %}
          {% if can_edit %}
            <div class="sample-audio-item upload-audio">
              <div class="sample-audio-thumbnail upload-new-audio">
              </div>
            </div>
          {% endif %}
        </div>

      {% include 'accounts/creator/_text_profile.html' %}

      </div>
      {% if not user.is_authenticated or user.role == 'master_client' %}
        <div class='btn-contact-with-artist hide'>
          <div class='container flex-row'>
            {% if user_creator.user_creator.first.show_profile == 'public' and user_creator.user_creator.first.is_direct %}
              <div class="btn btn--primary btn-right btn-round btn-open-modal-contact-artist artist-public-direct-on">オファーを送る</div>
            {% else %}
              <div class="btn btn--primary btn-right btn-round btn-open-modal-contact-artist">お問い合わせ</div>
            {% endif %}
          </div>
        </div>
      {% else %}
        <div class='submit-profile-btn-container hide'>
          <div class='container flex-row'>
            <div class="form-hint">変更を申請する必要があります。</div>
            <div class="btn btn--primary btn-right btn-round btn-submit-profile hide">OK</div>
          </div>
        </div>
      {% endif %}


    </div>
  </main>

  <div class="audio-navi">
    <div class="audio-navi-titlebar">
      <div class="audio-navi-titlebar-info">
        <div class="audio-navi-titlebar-info-title"></div>
        <div class="audio-navi-titlebar-info-name"></div>
      </div>
      <div class="audio-navi-titlebar-copylink">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 20H5V7C5 6.45 4.55 6 4 6C3.45 6 3 6.45 3 7V20C3 21.1 3.9 22 5 22H15C15.55 22 16 21.55 16 21C16 20.45 15.55 20 15 20ZM20 16V4C20 2.9 19.1 2 18 2H9C7.9 2 7 2.9 7 4V16C7 17.1 7.9 18 9 18H18C19.1 18 20 17.1 20 16ZM18 16H9V4H18V16Z" fill="#A7A8A9"/>
        </svg>
      </div>
      <div class="audio-navi-titlebar-bookmark"><span class="icon icon--sicon-bookmark-o fa-bookmark"></span></div>
    </div>

    <div class="audio-navi-nowplayingbar">
      <div class="audio-navi-playpause">
        <svg class="audio-navi-play" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g>
          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 15.5V8.5C10 8.09 10.47 7.85 10.8 8.1L15.47 11.6C15.74 11.8 15.74 12.2 15.47 12.4L10.8 15.9C10.47 16.15 10 15.91 10 15.5Z" fill="#A7A8A9"/>
          </g>
        </svg>

        <svg class="audio-navi-pause hide" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g>
          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16C9.45 16 9 15.55 9 15V9C9 8.45 9.45 8 10 8C10.55 8 11 8.45 11 9V15C11 15.55 10.55 16 10 16ZM14 16C13.45 16 13 15.55 13 15V9C13 8.45 13.45 8 14 8C14.55 8 15 8.45 15 9V15C15 15.55 14.55 16 14 16Z" fill="#A7A8A9"/>
          </g>
        </svg>
      </div>
      <div class="audio-navi-wave"></div>
      <div class="audio-navi-time">00:00</div>
    </div>
  </div>

  {% include "accounts/creator/_footer.html" %}

  <div class="upload-button-wrapper fullscreen">
    <p>アップロード中</p>
    <div class="fill">
      <div class="process"></div>
    </div>
    <div class="fa fa-check"></div>
  </div>

  <div class="modal fade" id="modalCrop" style="z-index: 1060">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title">画像を登録</h4>
        </div>
        <div class="modal-body" style="padding: 0;">
          <img src="" id="image" style="max-width: 100%;" alt="">
        </div>
        <div class="modal-footer">
          <div class="btn-group pull-left" role="group">
            <button type="button" class="btn btn-default js-zoom-in">
              <span class="glyphicon glyphicon-zoom-in"></span>
            </button>
            <button type="button" class="btn btn-default js-zoom-out">
              <span class="glyphicon glyphicon-zoom-out"></span>
            </button>
          </div>
          <button type="button" class="btn btn-primary js-crop-and-upload">OK</button>
        </div>
      </div>
    </div>
  </div>

  {% include 'messenger/_modal_open_file.html' %}
  {% include 'messenger/_modal_confirm_step.html' with add_by_sale=True hide_step_3=True page='gallery' %}
  {% include 'components/_modal.html' with position="center" body_html="accounts/creator/_modal_header.html" footer="True" modal_id="modal_profile_header" modal_zindex="1041" btn_primary="btn-submit-profile-header" btn_tertiary="btn-close-profile-header" %}
  {% include 'components/_modal.html' with position="center" body_html="accounts/creator/_modal_statement.html" footer="True" modal_id="modal_profile_statement" modal_zindex="1041" btn_primary="btn-submit-profile-statement" btn_tertiary="btn-close-profile-statement" %}
  {% include 'components/_modal.html' with position="center" body_html="accounts/creator/_modal_footer.html" footer="True" modal_id="modal_profile_footer" modal_zindex="1041" btn_primary="btn-submit-profile-footer" btn_tertiary="btn-close-profile-footer" %}

  <div class="modal popup-container fade" id="modal-delete-topic" role="dialog" style="z-index: 9998">
    <div class="modal-dialog popup-dialog">
      <div class="modal-content popup-content">
        <div class="popup-body">
          <div class="delete-work-title">
            {% trans "Do you really want to delete this?" %}
          </div>
        </div>
        <div class="popup-footer" style="text-align: right; padding-top: 24px; border-top: 1px solid #f0f0f0;">
          <button type="button" class="btn btn--primary" data-dismiss="modal"
                  aria-label="Close">{% trans "cancel" %}</button>
          <button type="button" class="btn btn--tertiary btn-popup-send">{% trans "yes" %}</button>
        </div>
      </div>
    </div>
  </div>

  <script>
  let user_role = '{{ user.role }}';
  window.CSRF_TOKEN = "{{ csrf_token }}";
  let csrf = '{% csrf_token %}';
  let user_pk = '{{ user.pk }}'
  </script>
    {% compress js inline %}
  <script src="{% static 'js/sortable.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/js/splide.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/splide-extension-auto-scroll.js' %}"></script>
  <script src="{% static 'js/jquery.datetimepicker.full.min.js' %}"></script>
  <script src="{% static 'js/main.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
  <script src="{% static 'js/cropper.min.js' %}"></script>
  <script src="{% static 'js/main_cropping.js' %}"></script>
  <script src="{% static 'js/utils.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    <script src="https://www.youtube.com/iframe_api"></script>
    {% compress js inline %}
  <script src="{% static 'js/profile_statement.js' %}"></script>
  <script src="{% static 'js/profile_footer.js' %}"></script>
  <script src="{% static 'js/creator_profile.js' %}"></script>
  <script src="{% static 'js/modal_contact.js' %}"></script>
  <script src="{% static 'js/modal_contact_artist.js' %}"></script>
  <script src="{% static 'js/album_preview.js' %}"></script>
  <script src="{% static 'js/components/header.js' %}"></script>
    {% endcompress %}
  <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
  <script src="{% static 'js/upload_file.js' %}"></script>
  <script src="{% static 'js/save_order_contact.js' %}"></script>
  <script src="{% static 'js/audio-navi.js' %}"></script>
  <script src="{% static 'js/topic_detail.js' %}"></script>
  <script src="{% static 'js/creator_profile_new.js' %}"></script>
    {% endcompress %}
{% endblock content %}
