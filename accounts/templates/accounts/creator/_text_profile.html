{% load util %}
{% with creator_profile.get_block_profile as profile_text %}
  <div class="profile__profile" id="profile">
    <div class="profile__profile-title heading-24 {% if profile_text.section_name_jp == "" and profile_textsection_name_en == "" %}hide{% endif %}">{{ creator_profile.get_block_profile.section_name_jp }}</div>
    <div class="profile__profile-quote rel{% if can_edit %} editable{% endif %}"
         data-title="{{ profile_text.section_name_jp }}"
         data-title-en="{{ profile_text.section_name_en }}"
         data-title-text="{{ profile_text.content_jp }}"
         data-title-text-en="{{ profile_text.content_en }}"
         data-toggle-header="{{ profile_text.is_link_menu }}"
         data-content-title="{{ profile_text.section_name_jp }}"
         data-content-title-en="{{ profile_text.section_name_en }}"
         data-content-title-text="{{ profile_text.content_jp }}"
         data-content-title-text-en="{{ profile_text.content_en }}"
         data-content-toggle-header="{{ profile_text.is_link_menu }}">
      <span>{{ profile_text.content_jp }}</span>

      <div class="edit-profile-profile-btn edit-button"></div>
    </div>
  </div>
{% endwith %}
