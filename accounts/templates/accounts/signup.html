{% extends "base_nofooter_refactor.html" %}
{% load widget_tweaks %}
{% load bootstrap3 %}
{% load static compress %}
{% block extrahead %}
{% load i18n %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.css" rel="stylesheet"/>
    {% compress css %}
    <link rel="stylesheet" href="{% static 'css/login.css' %}"/>
    <style>
        .banner__img-pc {
            transition: background .8s ease-in-out;
        }

        .img1 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201801.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img2 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201808.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img3 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201811.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img4 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201901.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img5 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201904.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img6 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201908.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img7 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        @media (max-width: 576px) {
            .banner__img-pc {
                display: none;
            }

            .img1 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201801.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img2 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201808.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img3 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201811.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img4 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201901.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img5 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201904.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img6 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201908.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img7 {
                background-color: transparent;
                background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }
        }
    </style>
    {% endcompress %}
{% endblock %}
{% block content %}
    <style>
        body {
            overflow-y: hidden !important;
        }
    </style>
    <div class="login">
        <div class="banner">
            <div class="bxslider">
                <div class="banner__img visible-xs visible-sm img0">
                </div>
                <div class="banner__img-pc img0"></div>
            </div>
        </div>
        <div class="auth">
            <div class="auth__content">
                <div class="auth__main">
                    <div class="auth__form">
                        <form method="post" action="{% url 'accounts:accounts_signup' %}{% if next_url %}?next_url={{ next_url }}{% endif %}" style="display:flex; flex-direction: column; gap:12px">
                            {% csrf_token %}
                            <div class="form-group mg-25">
                                {{ form.email|attr:"placeholder:登録するメールアドレス" }}
                                {% if form.errors_mail %}
                                    <div class="error-messager valid_mail">
                                        {{ form.errors_mail }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                {{ form.password1|attr:"placeholder:パスワード" }}
                                {% if form.errors_password1 %}
                                    <div class="error-messager valid_mail">
                                        {{ form.errors_password1 }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group">
                                {{ form.password2|attr:"placeholder:パスワード（確認）" }}
                                {% if form.errors_password2 %}
                                    <div class="error-messager valid_mail">
                                        {{ form.errors_password2 }}
                                    </div>
                                {% endif %}
                            </div>
                             <p class="form-group auth__form-note">利用開始をもって<a href="{% url 'app:termsofservice' %}" target="_blank">利用規約</a>と<a href="{% url 'app:privacypolicy' %}" target="_blank">プライバシーポリシー</a>に同意したものとみなします
                             </p>
                            <div class="form-group text-center mg-20" style="padding: 0;">
                                {% buttons %}
                                    <input type="submit" class="btn button btn-signup auth__form-button" value="サインアップ"/>
                                {% endbuttons %}
                            </div>
                            <!-- <div class="form-group join-form__social-title text-center">OR</div> -->
                            <!-- <div class="login-social form-group"
                                 style="display: flex;justify-content: center;flex-wrap: wrap;">


                                <div class="join-form__social-list">
                                    <a class="join-form__social-network google"
                                       href="{% url 'social:begin' 'google-oauth2' %}?next={{ social_next }}"></a>
                                    <a class="join-form__social-network facebook"
                                       href="{% url 'social:begin' 'facebook' %}?next={{ social_next }}"></a>
                                </div>
                            </div> -->

                        </form>
                        <hr>
                            <div class="auth__form-login" style="text-align:center;">すでにアカウントをお持ちの方は<a href="{% url 'accounts:accounts_login' %}{% if next_url %}?next_url={{ next_url }}{% endif %}">こちら</a></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{% static 'js/random.js' %}"></script>
    <script>
        //Array of images which you want to show: Use path you want.
        let images = ['img1', 'img2', 'img3', 'img4', 'img5', 'img6', 'img7'];
        removeNavbar()
        randomImageBackground(images);

        function validateSignUp() {
            email = $('input[name="email"]').val().trim();
            newPass = $('input[name="password1"]').val().trim();
            passConfirm = $('input[name="password2"]').val().trim();
            let checkNewPass = newPassFnc(newPass, $('input[name="password1"]'));
            let checkConfirmNewPass = confirmPassFnc(passConfirm, newPass, $('input[name="password2"]'));
            let checkEmail = emailFnc(email, $('input[name="email"]'));
            button_disable_toggle =  checkNewPass && checkConfirmNewPass && checkEmail;
            $('.btn-signup').toggleClass('btn--disabled', !button_disable_toggle);
        }

        $(document).ready(function() {
            rezizeAuth();
            $(window).on('resize', () => {
                rezizeAuth()
            })

            validateSignUp();

            $(document).on('input', 'input[name="password2"], input[name="password1"], input[name="email"]', () => {
                validateSignUp()
            })
        })
    </script>
     <script src="{% url 'javascript-catalog' %}"></script>
{% endblock %}
