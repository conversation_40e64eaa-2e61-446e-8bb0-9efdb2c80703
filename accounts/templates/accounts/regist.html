{% extends "base.html" %}
{% load bootstrap3 %}


{% block extrahead %}
  <script type="text/javascript">
      let user_role = '{{ user.role }}';

      function showOrHideListIp() {
          if ($('#id_is_check_ip').is(":checked")) {
              $("#id_list_ip").prop('disabled', false);
          } else {
              $("#id_list_ip").prop('disabled', true);
          }
      }

      //check list ip
      let checkListIp = function (this_dom) {
          if ($('#id_is_check_ip').is(':checked') && !$('#id_list_ip').disabled && $('#id_list_ip').val() == '') {
              alert('IPアドレスを入力してください。');
              return false;
          } else {
              $(this_dom).prop('disabled', true);
              $(this_dom).parents('form').submit();
          }
      };

      $(document).ready(function () {
          if (user_role === "curator") {
              $('#id_products').parents('.form-group').addClass('hide');
              $('#id_products').prop('required', false);
          }
          showOrHideListIp();
          $("#id_is_check_ip").click(showOrHideListIp);

          if ($('#id_role').val() != 'master_client') {
              $('#id_products').prop('required', false);
              $('#id_products').closest('.form-group').hide()
          }
          if (user_role !== "curator") {
              $(document).on('change', '#id_role', function () {
                  if ($('#id_role').val() != 'master_client') {
                      $('#id_products').closest('.form-group').hide();
                  } else {
                      $('#id_products').closest('.form-group').show();
                  }
              })
          }
      });
  </script>

{% endblock %}

{% block content %}
    <div class="container">
        <div class="col-xs-6">
            <form method="post" action="{% url 'accounts:accounts_regist' %}">
                {% csrf_token %}
                {% bootstrap_form form %}
                {% buttons %}
                    <input type="submit" value="登録" class="btn btn-primary" onclick="return checkListIp(this);" />
                {% endbuttons %}
            </form>
        </div>
    </div>
{% endblock content %}
