{% load util %}
<style>
  @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-footer {
        font-size: 12px;
      }

      img.fullwidth,
      img.fullwidthOnMobile {
        max-width: 100% !important;
      }
    }
</style>
<body style="background: #f5f5f4; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
  <div class="email-template" style="max-width: 600px; margin: 20px auto; height: 100%; position: relative; background-color: white; padding: 10px;">
    <div class="hr-space" style="clear: both; height:2px; background-color: #999; margin: 25px 0 10px;"></div>
    <div class="email-template__block">
      <div class="email-template-left" style="float: left">
        <img src="{{host}}/static/images/soremo-favi2_01.png" height="45px" width="45px" style="border-radius: 50%">
      </div>
      <div class="email-template-right" style="float: right">
        <div class="user" style="text-align: right">
          <span style="font-size: 12px; margin-right: 5px; color: #000; position: relative;
      top: 3px;">{% if user.fullname %}{{ user.fullname }}{% else %}{{ user.last_name }} {{ user.first_name }}{% endif %} 様</span>
          <img src="{{ user|get_user_url:'small' }}" height="24px" width="24px" style="float: right; border-radius: 50%">
        </div>
        <p style="margin: 5px 0; color: #999; font-size: 12px;">新しいサインインがありました。</p>
      </div>
    </div>
    <div class="clearfix" style="clear: both"></div>
    <div class="email-template__block" style="padding-top: 10px; padding-bottom: 20px">
      <p style="color: #666; margin: 0; font-size: 14px;">お客様のアカウントへの新しいサインインが検出されました。</p>
    </div>
    <div class="email-template__content" style="border: 1px solid #0099cc; border-radius: 20px; padding: 20px 40px; font-size: 13px; margin-top: 5px">
      <div class="email-template__content-item">
        <p style="margin: 5px 0">
          <span style="color: #999">デバイス:</span>
          <span style="color: #666"> {{device}}</span>
        </p>
        <p style="margin: 5px 0">
          <span style="color: #999">地域:</span>
          <span style="color: #666"> {{province}}</span>
        </p>
        <p style="margin: 5px 0 5px 30px; color: #999">
          ※位置情報は正確でない場合があります。</p>
        <p style="margin: 5px 0">
          <span style="color: #999">時間:</span>
          <span style="color: #666"> {{last_login}}</span>
        </p>
      </div>
    </div>
    <div class="clearfix" style="clear: both"> </div>
    <p style="color: #666; margin-top: 20px; font-size: 13px;">※サインインした覚えがない場合は、アカウント保護のため、直ちにパスワードを変更することをおすすめします。</p>
    <p style="color: #666; margin-top: 20px; font-size: 13px;">今回のお知らせに関する詳細は、<a href="mailto:<EMAIL>">こちら</a>からお問い合わせください。</p>
    {% include 'emails/_footer_email.html' %}
  </div>
</body>

