{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util compress %}


{% block extrahead %}
    {% compress css %}
        <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
        <link href="{% static 'css/account_setting.css' %}" rel="stylesheet">
    {% endcompress %}
{% endblock%}

{% block content %}
<div class="user-info">
    <div class="container">
        <div class="user-info__main account__info">
            <div class="user-info__heading">
                <h3 class="heading--wrap">利用設定</h3>
            </div>

            <div class="user-info__content">
                <form method="post" action="{% url 'accounts:accounts_setting' user.pk %}" enctype="multipart/form-data" class="user-info__form row">
                    {% csrf_token %}
                    <div class="account__form-group form-group col-sm-12">
                        <h3 class="account__form-heading account__form-title">お知らせ</h3>
                        <p class="account__field-hint">新しいシーンやコメントが届いたときにのメール通知を設定できます
                        </p>
                        <div class="col-sm-12 form-group" style="margin-bottom: 40px;">
                            <label class="col-sm-12" for="">
                                <div class="account__sub-group">
                                {% for choice in form.setting_mail %}

                                    {% if choice.data.value == "on" %}
                                    <div class="account__form-multi account__form-flex">
                                        <label class="input-radio">
                                            <input type="radio" name="{{ choice.data.name }}" {%if choice.data.selected%}checked{%endif%}
                                                value={{choice.data.value}} index={{choice.data.index}} required={{choice.data.attrs.required}}
                                                id={{choice.data.attrs.id}}/>{{ choice.data.label }}
                                            <div class="check-mark"></div>
                                        </label>
                                        <div class="notification-time">
                                            <select class="input-time" name={{form.noti_hours.name}} value="{{user.noti_hours}}">
                                                {% for i in 24|make_list %}
                                                    {% with forloop.counter|option_time as option_time %}
                                                    <option value="{{ option_time }}" {% if option_time == user.noti_hours %}selected="selected"{% endif %}>
                                                        {{ option_time }}
                                                    </option>
                                                    {% endwith %}
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="account__form-multi">
                                        <label class="input-radio">
                                            <input type="radio" name="{{ choice.data.name }}" {%if choice.data.selected%}checked{%endif%}
                                                value={{choice.data.value}} index={{choice.data.index}} required={{choice.data.attrs.required}}
                                                id={{choice.data.attrs.id}}/>{{ choice.data.label }}
                                            <div class="check-mark"></div>
                                        </label>
                                            {% if choice.data.value == "now" %}
                                            <p class="account__field-text">※短時間での連投を防ぐため、15分ごとにまとめてお知らせします。</p>
                                            {% endif %}
                                    </div>
                                    {% endif %}
                                {% endfor %}
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="user-info__submit col-sm-12 acc_action">
                        {% buttons %}
                        <input type="submit" value="OK" id="btn__ok" class="btn btn--primary" />
                        {% endbuttons %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
{% block extra_script %}

<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
<script>
    $(document).ready(function() {
        $('[id^=id_notification]').on('change', function () {
            //uncheck all radio buttons
            $('[id^=id_notification]').attr('checked', null);
            //check current button
            $(this).attr('checked', 'checked');
        });
    });
</script>
{% endblock %}
