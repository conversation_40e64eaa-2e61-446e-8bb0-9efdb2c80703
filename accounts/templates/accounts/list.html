{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load static %}
{% load i18n %}
{% load compress %}

{% block extrahead %}
    {% compress css %}
        <link rel="stylesheet" type="text/css" href="{% static 'css/theme.default.min.css' %}"/>
        <link rel="stylesheet" href="{% static 'css/main_new.css' %}" />
        <style>
            .project-item__filter {
                padding: 10px;
            }
            .project-item__filter-item {
                margin: 0 10px;
                transition: .4s;
            }

            .button--add_user {
                display: flex;
                align-items: center;
                border-radius: 20px;
                padding: 0;
                background-color: #009ace;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                cursor: pointer;
            }

            .button--add_user a {
                padding: 4px 24px;
                color: #fff;
            }

            .button--add_user:hover {
                background-color: #0076a5;
            }

            .project-item__filter-item.active,
            .project-item__filter-item:hover {
                background-color: #009ace;
                color: #fff;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            }
        </style>
    {% endcompress %}
{% endblock %}

{% block content %}
    <main class="p10">
        <div class="project-item__filter">
            {% if user.role == 'master_admin' %}
                <div class="project-item__filter-item active" data-show="master_admin">{% trans "Administrator" %}</div>

                <div class="project-item__filter-item" data-show="master_client">{% trans "Owner" %}</div>

                <div class="project-item__filter-item" data-show="curator">{% trans "Curator" %}</div>

            {% else %}
                 <div class="project-item__filter-item active" data-show="admin">{% trans "Artist" %}</div>
            {% endif %}
            <div class="button--add_user"><a href="{% url 'accounts:accounts_regist' %}" >{% trans "Add account" %}</a></div>

        </div>
        <div class="table-responsive">
            <table class="table" id="tablesorter">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>{% trans "User name" %}</th>
                        <th>{% trans "Full name" %}</th>
                        <th>{% trans "email address" %}</th>
                        <th>{% trans "Registered Date" %}</th>
                        <th></th>
                        <th></th>
                    </tr>
                </thead>
            {% if user.role == 'master_admin' %}
                <tbody class="master_admin_list">
                {% for obj in master_admin %}
                    {% include 'accounts/_member_item.html' with obj=obj %}
                {% endfor %}
                </tbody>

                <tbody class="master_client_list hide">
                {% for obj in master_client %}
                    {% include 'accounts/_member_item.html' with obj=obj %}
                {% endfor %}
                </tbody>

                <tbody class="curator_list hide">
                {% for obj in curator %}
                    {% include 'accounts/_member_item.html' with obj=obj %}
                {% endfor %}
                </tbody>
            {% else %}
                <tbody class="admin_list">
                  {% for obj in admin %}
                    {% include 'accounts/_member_item.html' with obj=obj %}
                  {% endfor %}
                </tbody>
            {% endif %}

            </table>
        </div>
    </main>
{% endblock content %}
{% block extra_script %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    {% compress js inline %}
    <script src="{% static 'js/jquery.tablesorter.min.js' %}"></script>
    <script src="{% static 'js/jquery.tablesorter.widgets.min.js' %}"></script>
    <script src="{% static 'js/table_sorter.js' %}"></script>
    <script>
        $(document).ready(function () {
            $('.btn.delete-user').on('click', function () {
                let button = $(this);
                bootbox.confirm({
                message: "本当に削除しますか？",
                buttons: {
                    cancel: {
                        label: 'Cancel',
                        className: 'btn btn-info btn--primary btn-cancel-message'
                    },
                    confirm: {
                        label: 'OK',
                        className: 'btn btn-danger btn--tertiary btn-delete-message'
                    },
                },
                callback : function(result){
                    if(result) {
                        button.parent().find('.delete-user-btn').trigger('click');
                    }
                }});
            });

            $('.project-item__filter-item').on('click', function() {
                if($(this).not('.active')) {
                    $('.project-item__filter-item').removeClass('active');
                    $('tbody').addClass('hide');
                    $(this).addClass('active');
                    $('tbody.' + $(this).attr('data-show') + '_list').removeClass('hide');
                }
            })
        });
    </script>
    {% endcompress %}
{% endblock %}
