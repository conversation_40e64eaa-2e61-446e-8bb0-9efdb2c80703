{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Offer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
  <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <style type="text/css">
    @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-sub-title {
        font-size: 14px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 12px;
      }

      .email-info {
        font-size: 12px;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .email-info-detail {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }

      img.fullwidth,
      img.fullwidthOnMobile {
        max-width: 100% !important;
      }
    }

    .owner-project {
      filter: invert(28%) sepia(98%) saturate(1535%) hue-rotate(174deg) brightness(88%) contrast(102%);
    }
  </style>
</head>

<body style="margin: 0; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo"
                                    height="45px" width="45px" style="border-radius: 50%"></td>
        <td style="text-align: right">
          <table class="user-info" style="width:100%; table-layout: fixed">
            <tbody>
            <tr>
              <td class="user-name">
               {{ recipient.get_display_name }} 様</td>
              <td style="width: 40px; padding-left: 10px; background-color: white; border-radius: 50%;">
                <img src="{{ recipient_avt }}" alt="User Avatar"
                     border="0"
                     style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>

    <p class="email-text" style="margin: 0 0 8px">
      お世話になっております。</p>

    {# content #}
    {% if type == 'edit' %}
      {% if recipient.role == 'admin' %}
        <p class="email-text" style="margin: 0 0 8px">下記のとおり、プロフィール提案が届いております。</p>
        <p class="email-text" style="margin: 0 0 8px">
          更新箇所のNEWをクリックすると、「更新」「承認」をお選びいただけます。下記よりご意向をお知らせ頂けますと幸いです。
        </p>
        <p class="email-text" style="margin: 0 0 8px">プロフィールは、相互承認された内容のみオーナー様へ開示されます。</p>
      {% else %}
        <p class="email-text" style="margin: 0 0 8px">下記のとおり、プロフィール提案が到着しました。</p>
        <p class="email-text" style="margin: 0 0 8px">
          内容を確認の上、承認もしくは、アーティストとの調整を進めてください。
        </p>
      {% endif %}
    {% elif type == 'approve' %}
      <p class="email-text" style="margin: 0 0 8px">下記のとおり、プロフィールが承認されました。</p>
      <p class="email-text" style="margin: 0 0 8px">
      内容の変更は、PROFILEでいつでも更新を申請いただけます。お気軽にご意向をお知らせください。
      </p>
    {% endif %}

    {# button #}
    {% include 'emails/_button_go_to_web.html' with url=url %}

    {# footer #}
    {% include 'emails/_footer_email.html' %}
  </div>
</main>
</body>
</html>
