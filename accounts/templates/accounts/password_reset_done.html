{% extends "base_nofooter_refactor.html" %}
{% load widget_tweaks %}
{% load bootstrap3 %}
{% load static %}
{% block extrahead %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="{% static 'css/login.css' %}"/>
    <style>
        .banner__img-pc {
            transition: background .8s ease-in-out;
        }

        .img1 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201801.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img2 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201808.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img3 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201811.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img4 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201901.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img5 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201904.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img6 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201908.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img7 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .auth__form {
            width: 100%;
            background-color: rgba(255, 255, 255, 0.618) !important;
            border-radius: 12px;
            padding: 32px 12px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(13px);    
        }
        
        .auth__form-title {            
            margin: 0px !important;
            padding-bottom: 24px;

            font-family: 'A+mfCv-AXISラウンド 50 R StdN';
            font-size: 24px;
            line-height: 150%;
            color: #000000;
        }

        .form-group .input-box {
            border: 1px solid #F0F0F0 !important;
            background-color:  rgba(255, 255, 255, 0.618) !important;
            color: #000000;
            padding: 12px 12px !important;
            border-radius: 4px !important;
            box-shadow: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .auth__form-button {
            width: 100% !important;
            background-color: #009ace !important;
            color: #ffffff !important;
            padding: 12px 12px !important;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border-radius: 4px !important;
            font-family: 'A+mfCv-AXISラウンド 50 R StdN' !important;
            font-size: 13px !important;
            letter-spacing: 2.5px;
            line-height: 150%;
        }




        @media (max-width: 576px) {
            .banner__img-pc {
                display: none;
            }

            .img1 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201801.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img2 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201808.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img3 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201811.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img4 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201901.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img5 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201904.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img6 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201908.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img7 {
                background-color: transparent;
                background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }            
        }
    </style>
{% endblock %}
{% block content %}
    <style>
        body {
            overflow-y: hidden !important;
        }
    </style>
    <div class="login">
        <div class="banner">
            <div class="bxslider">
                <div class="banner__img visible-xs visible-sm img0">
                </div>
                <div class="banner__img-pc img0"></div>
            </div>
        </div>
        <div class="auth">
            <div class="auth__content">
                <div class="auth__main">
                    <div class="auth__form">
                        <div class="send-mail-complete" style="font-size:13px; line-height:200%; text-align: center;">
                            <p style="margin-bottom:0;">パスワードをリセットするためのメールを送信しました。<br>メールに記載されているリンクから、新しいパスワードを設定してください。
                            </p>
                            <input type="button" id="ok" value="OK" style="font-size:13px; margin-top: 24px; max-width: 192px; text-align: center;"
                                       class="auth__form-button btn-go-back"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{% static 'js/random.js' %}"></script>
    <script>
        //Array of images which you want to show: Use path you want.
        let images = ['img1', 'img2', 'img3', 'img4', 'img5', 'img6', 'img7'];
        removeNavbar()
        randomImageBackground(images);

        $(document).on('click', '.btn-go-back', () => {
                window.location = window.location.href.split('/accounts/')[0] + '/accounts/login';
            })

        $(document).ready(function () {
            rezizeAuth();
            $(window).on('resize', () => {
                rezizeAuth()
            })
        });
    </script>
{% endblock %}
