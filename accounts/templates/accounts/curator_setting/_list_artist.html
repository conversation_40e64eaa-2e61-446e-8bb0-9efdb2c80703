{% load static %}
{% load i18n %}
{% load util %}
{% load bootstrap3 %}

{% for artist in artists %}
  {% with artist.user_creator.first as creator %}
    <tr class="tr-artist-account-info" data-user-id="{{ artist.pk }}">
      <td>
        <div class="account-info">
          <a href="

                  {% if creator.first.slug %}{% url 'app:creator_info' slug=creator.slug %}{% else %}{% url 'accounts:accounts_creator' artist.pk %}{% endif %}">
            <div class="account-info__left__avatar">
              <img src="{{ artist|get_avatar:'medium' }}" alt="" class="avatar__image-avatar">
              {% with creator|get_task_in_progress as task_progress %}
                <div class="avatar__bandage {% if task_progress < 1 %}hide{% endif %}">{{ task_progress }}</div>
              {% endwith %}

            </div>
          </a>
          <a class="account-info__left" href="{% url 'accounts:creator_info' artist.pk %}">
            <div class="account-info__left__content">
              <div class="account-info__left__content__top">{{ artist.get_display_name }}</div>
              <div class="account-info__left__content__bottom">
                <div class="account-info__left__content__bottom__title">
                  {% if artist.position %}{{ artist.position }}{% endif %}</div>
                <div class="account-info__left__content__bottom__organization-name">
                  {% if artist.enterprise %}{{ artist.enterprise }}{% endif %}</div>
              </div>
            </div>
          </a>

          <div class="account-info__right">
            <div class="account-info__right__manage">
              {% if creator.creator_file_status != '0' %}
                <span class="account-info__right__manage__identification
                        {% if creator.creator_file_status == '1' %}idetity-uploaded{% endif %}">本人確認</span>
              {% endif %}
              <span class="account-info__right__manage__NDA {% if not creator.curator_file %}NDA-not-uploaded{% endif %}"
                    {% if creator.curator_file %}data-curator-file-name="{{ creator.curator_file_name }}"
                    data-curator-file-link="{{ creator.curator_file.url }}"{% endif %}>NDA</span>
            </div>
            <div class="account-info__right__tradeoff_slider">
              <div class="account__tradeoff">
                <a href="{% url 'accounts:accounts_creator_setting' creator.pk %}">
                  <div class="account__trade-slider">
                    {% for i in '12345' %}
                      <div class="account__trade-item {% if i == creator.get_trading_display|stringformat:"i" %}active{% endif %}"
                           data-option="{{ i }}"></div>
                    {% endfor %}
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </td>
      <td>{{ artist.user_creator.first.last_published_version.modified|get_short_datetime }}</td>
      <td>{{ artist|count_offer_done }}</td>
      <td><a href="
                    {% url 'payments:payment_current_artist' %}?user_id={{ artist.pk }}"
             style="color: #000000">{{ artist.balance_reward|display_currency }}円（税込）</a></td>
      <td>
        <div class="materials-used">
          <div class="materials-used__content"><span>{{ artist.evaluation_point|display_currency }}</span>マイル</div>
        </div>
      </td>
    </tr>
  {% endwith %}
{% endfor %}



