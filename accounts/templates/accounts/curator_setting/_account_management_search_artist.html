{% load static %}
{% load i18n %}

<div class="account-management__search-artist">
  <div class="psearch-content psearch-form">
    <div class="sform-row psearch-keyword">
      <div class="sform-group sform-group--required">
        <div class="sform-group__input-group sform-group__append-before">
          <input class="sform-control sform-control--input sform-control--full"
                 id="account-management__search-artist-input" type="search"
                 placeholder="アーティスト名で検索" required="required"/>
          <i class="icon icon--sicon-close search-delete"></i>
          <label class="sform-group__append" for="account-management__search-artist-input">
            <i class="icon icon--sicon-search"></i>
          </label>
        </div>
      </div>
    </div>

    <div class="psearch-filter">

      <ul class="nav tabs-skill">
        {% for group in skills %}
          <li class="nav-item {% if forloop.counter0 == 0 %}active{% endif %}">
            <a class="nav-link" data-target="#tab_{{ group.1.1.2 }}" data-toggle="tab">{{ group.0 }}</a>
            <div class="notification notification--blue notification--round skill-selected hide"></div>
          </li>
        {% endfor %}
      </ul>

      <div class="tab-content">

        {% for group in skills %}
          <div class="tab-pane {% if forloop.counter0 == 0 %}active{% endif %}" id="tab_{{ group.1.1.2 }}">
            <div class="skills-list-selected">
              {% for skill in group.1 %}
                <span class="skills-item" data-id="{{ skill.0 }}">{{ skill.1 }}</span>
                <input type="checkbox" name="skills" value="" id="" hidden>
              {% endfor %}
            </div>
          </div>
        {% endfor %}

      </div>
    </div>

  </div>
</div>
