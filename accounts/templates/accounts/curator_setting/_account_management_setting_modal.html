{% load static %}
{% load util %}
{% load i18n %}

<!-- Modal invite artist -->
<div class="modal popup-container fade" id="modal-invite-artist" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content" style="padding: 32px !important;">
      <div class="modal-header">
        <div class="modal-header__invite-artist__content">
          <div class="invite-artist__content__title">
            {% trans "Invite an artist" %}
          </div>
          <div class="invite-artist__content__title-hint">
            {% trans "Enter the information of the artist you want to invite." %}
          </div>
        </div>
      </div>
      <div class="popup-body">
        <form id='invite-artist-form' action='' class="form-group">
          <label class="label_field" for="id_email">
            <span class="account__field-label" style="font-weight: 400;">{% trans "email address" %}<span class="account__jp-astarisk">[必須]</span></span>
            <input class="form-control" placeholder="<EMAIL>" type='email' name='email' value=''
                   maxlength="50" id="id_email"/>
          </label>

          <label class="label_field1" for="input-container">
            <span class="account__field-label" style="font-weight: 400;">{% trans "Artist name" %}<span
                    class="account__jp-astarisk-op">[任意]</span></span>
            <div class="artist-management__input-container" id="input-container">
              <div class="artist-management__input-container__input-1">
                <input class="form-control" placeholder="ジョン・レノン" type='text' name='stage_name' value='' maxlength="60"/>
              </div>
              <div class="artist-management__input-container__input-2">
                <input class="form-control" placeholder="John Lennon" type='text' name='stage_name_en' value=''
                       maxlength="60"/>
              </div>
            </div>
          </label>

          <label class="label_field1" for="input-container">
            <span class="account__field-label" style="font-weight: 400;">{% trans "Title account" %}<span
                    class="account__jp-astarisk-op">[任意]</span></span>
            <div class="artist-management__input-container" id="input-container">
              <div class="artist-management__input-container__input-1">
                <input class="form-control" placeholder="コンポーザー" type='text' name='position' value='' maxlength="255"/>
              </div>
              <div class="artist-management__input-container__input-2">
                <input class="form-control" placeholder="Composer" type='text' name='type' value='' maxlength="60"/>
              </div>
            </div>
          </label>

          <label class="label_field1 label-enterprise" for="enterprise">
            <span class="account__field-label" style="font-weight: 400;">{% trans "Organization name" %}<span
                    class="account__jp-astarisk-op">[任意]</span></span>
            <input class="form-control" placeholder="@soremo" type='text' name='enterprise' value='' maxlength="255"/>
          </label>
        </form>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-close"
                data-dismiss="modal">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-invite-artist"
                id="submit--form">{% trans "Send invitation email" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal invite artist -->

<!-- Modal upload NDA -->
<div class="modal popup-container fade" id="modal-upload-NDA" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content" style="padding: 32px !important;">
      <div class="modal-header">
        <div class="modal-header__invite-artist__content">
          <div class="invite-artist__content__title">
            {% trans "Upload NDA" %}
          </div>
          <div class="invite-artist__content__title-hint">
            {% trans "Upload and share your contract with the artist you've signed." %}
          </div>
        </div>
      </div>
      <div class="popup-body">
        <div class="drag-drop-container">
          <div class="mattach-info mattach-info-file mattach-info-file-information hide" data-dz-thumbnail="">
            <div class="mcommment-file">
              <div class="mcommment-file__name mcommment-file__name-form" data-dz-name="">
                <i class="icon icon--sicon-clip"></i>
                <p class="file-name"></p>
              </div>
              <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                <i class="icon icon--sicon-close"></i>
              </div>
            </div>
          </div>

          <div class="account_upload-file mattach mattach-form">
            <div class="mcomment-attached">
              <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
                <div class="mattach-previews mattach-previews-form collection">
                  <div class="mattach-template mattach-template-form collection-item item-template">
                    <div class="mattach-info" data-dz-thumbnail="">
                      <div class="mcommment-file">
                        <i class="icon icon--sicon-clip"></i>
                        <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                        <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                        <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                          <i class="icon icon--sicon-close"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div id="dropzoneUpLoadNDA" class="fallback dropzone">
            </div>
          </div>
        </div>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-close"
                data-dismiss="modal">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-upload-NDA" id="submit--form">OK</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal upload NDA -->

<!-- Modal approval of identity -->
<div class="modal popup-container fade" id="modal-approval-of-indentity" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content" style="padding: 32px !important;">
      <div class="modal-header">
          <button type="button" class="popup-close-approval-of-indentity" data-dismiss="modal" aria-label="Close">
              <i class="icon icon--sicon-close"></i>
          </button>
        <div class="modal-header__invite-artist__content">
          <div class="invite-artist__content__title">
            {% trans "Approval of identity verification documents" %}
          </div>
          <div class="invite-artist__content__title-hint">
            {% trans "Check and approve the content, or email the artist why and what to do about the disapproval." %}
          </div>
        </div>
      </div>
      <div class="popup-body">
        <div class="indentity-container mscrollbar">
        </div>

        <div class="indentity-content-view-container mscrollbar">

        </div>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-send btn-reject-of-indentity">{% trans "Not approve" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-approval-of-indentity"
                id="submit--form">{% trans "recognize" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal approval of identity -->

<!-- Modal approval of identity -->
<div class="modal popup-container fade" id="modal-edit-materials-used" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content" style="padding: 32px !important;">
      <div class="popup-body">
        <form id='materials-used-edit' action='' class="form-group">
{#          <input class="form-control" placeholder="利用料" type='text' name='materials-used-input' value=''#}
{#                 maxlength="255"/>#}
                    <input class="form-control" placeholder="評価" type='text' name='materials-used-input' value=''
                 maxlength="19"/>
        </form>
      </div>
      <div class="popup-footer" style="text-align: right; margin-top: 0px;">
        <button type="button" class="btn btn--tertiary btn-popup-close"
                data-dismiss="modal">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-edit-materials-used" id="submit--form">OK
        </button>
      </div>
    </div>
  </div>
</div>
<!-- End modal approval of identity -->
