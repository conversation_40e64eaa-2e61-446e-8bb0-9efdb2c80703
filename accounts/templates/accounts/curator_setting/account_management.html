{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load static %}
{% load i18n %}
{% load compress %}

{% block extrahead %}
    {% compress css %}
     <link rel="stylesheet" type="text/css" href="{% static 'css/theme.default.min.css' %}"/>
     <link rel="stylesheet" type="text/css" href="{% static 'css/account_management.css' %}"/>
     <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
     <link rel="stylesheet" href="{% static 'css/main_new.css' %}" />
    {% endcompress %}
{% endblock %}

{% block content %}
    <main class="p10 owner-top" style="background: #FCFCFC;">
        <div class="container">

            {% include 'accounts/curator_setting/_account_management_setting_modal.html' %}

            <div class="account-management">
                <div class="account-management__add-artist">
                    <div class="account-management__add-artist__content">
                        <div class="add-artist__content__icon">
                            <i class="icon icon--sicon-plus"></i>
                        </div>
                        <div class="add-artist_content__text">{% trans "Invite an artist" %}</div>
                    </div>
                </div>

              <div class="account_management__list-invited {% if not artist_invitings.exists %}hide{% endif %}">
                <div class="list-invited__head">
                  <div class="list-invited__head__content">{% trans "In reception" %}</div>
                </div>
                <div class="list-invited__list mscrollbar">
                  {% for artist_inviting in  artist_invitings %}
                     {% include 'accounts/curator_setting/_account_management_list_invited.html' with artist_inviting=artist_inviting%}
                  {% endfor %}
                </div>
              </div>

                <div class="account_management__search-and-results">
                  {% include 'accounts/curator_setting/_account_management_search_artist.html' %}

                  <div class="account-management__list-acount list-all-artist" data-total-page="{{ total_page }}">
                     {% include 'accounts/curator_setting/_account_management_account_info.html' with artists=artists all_artist=all_artist list_count=list_count %}
                  </div>

                  <div class="account-management__list-acount list-search-artist"></div>
                </div>
            </div>
        </div>
    </main>
{% endblock content %}
{% block extra_script %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    {% compress js inline %}
    <script src="{% static 'js/jquery.tablesorter.min.js' %}"></script>
    <script src="{% static 'js/jquery.tablesorter.widgets.min.js' %}"></script>
    {% endcompress %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
    {% compress js inline %}
    <script src="{% static 'js/table_sorter.js' %}"></script>
    <script src="{% static 'js/account_management.js' %}"></script>
    <script src="{% static 'js/common_variable.js' %}"></script>
    <script src="{% static 'js/validate_contract.js' %}"></script>
    {% endcompress %}
    <script src="{% url 'javascript-catalog' %}"></script>
{% endblock %}
