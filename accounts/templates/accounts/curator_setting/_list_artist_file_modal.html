{% load static %}
{% load i18n %}
{% load util %}

{% with artist.user_creator.first as creator %}
  {% for file in creator.idetity_files.all %}
    {% with file.get_type_file_artist as type_file %}
      <div class="indentity-component-container" data-type="{{ type_file }}"
           data-source="{{ file.file|get_link_to_preview_file:type_file }}">
        <div class="indentity-component-name">
          <i class="icon icon--sicon-clip"></i>
          <div class="indentity-component-name__content">{{ file.real_name }}</div>
        </div>
      </div>
    {% endwith %}
  {% endfor %}
{% endwith %}
