{% load util %}
<style type="text/css">
  @font-face {
    font-family: AxisRound;
    font-weight: 700;
    src: url('../fonts/AxisRound100StdN-B.otf');
  }

  @font-face {
    font-family: AxisRound;
    font-weight: 400;
    src: url('../fonts/AxisRound100StdN-R.otf');
  }

  body {
    margin: 0;
    padding: 0;
  }
  span {
    font-size: 12px;
  }

		table,
		td,
		tr {
			vertical-align: top;
			border-collapse: collapse;
		}

		* {
			line-height: inherit;
		}

		a[x-apple-data-detectors=true] {
			color: inherit !important;
			text-decoration: none !important;
		}

		.ie-browser table {
			table-layout: fixed;
		}

		[owa] .img-container div,
		[owa] .img-container button {
			display: block !important;
		}

		[owa] .fullwidth button {
			width: 100% !important;
		}

		[owa] .block-grid .col {
			display: table-cell;
			float: none !important;
			vertical-align: top;
		}

		.ie-browser .block-grid,
		.ie-browser .num12,
		[owa] .num12,
		[owa] .block-grid {
			width: 600px !important;
		}

		.ie-browser .mixed-two-up .num4,
		[owa] .mixed-two-up .num4 {
			width: 200px !important;
		}

		.ie-browser .mixed-two-up .num8,
		[owa] .mixed-two-up .num8 {
			width: 400px !important;
		}

		.ie-browser .block-grid.two-up .col,
		[owa] .block-grid.two-up .col {
			width: 300px !important;
		}

		.ie-browser .block-grid.three-up .col,
		[owa] .block-grid.three-up .col {
			width: 300px !important;
		}

		.ie-browser .block-grid.four-up .col [owa] .block-grid.four-up .col {
			width: 150px !important;
		}

		.ie-browser .block-grid.five-up .col [owa] .block-grid.five-up .col {
			width: 120px !important;
		}

		.ie-browser .block-grid.six-up .col,
		[owa] .block-grid.six-up .col {
			width: 100px !important;
		}

		.ie-browser .block-grid.seven-up .col,
		[owa] .block-grid.seven-up .col {
			width: 85px !important;
		}

		.ie-browser .block-grid.eight-up .col,
		[owa] .block-grid.eight-up .col {
			width: 75px !important;
		}

		.ie-browser .block-grid.nine-up .col,
		[owa] .block-grid.nine-up .col {
			width: 66px !important;
		}

		.ie-browser .block-grid.ten-up .col,
		[owa] .block-grid.ten-up .col {
			width: 60px !important;
		}

		.ie-browser .block-grid.eleven-up .col,
		[owa] .block-grid.eleven-up .col {
			width: 54px !important;
		}

		.ie-browser .block-grid.twelve-up .col,
		[owa] .block-grid.twelve-up .col {
			width: 50px !important;
		}

  .button--goto-web {
    font-weight: 300;
    line-height: 21px;
    min-width: 100px;
    padding: 12px 39px;
    border-radius: 4px;
    border: none;
    color: #fff;
    background-color: #009ace;
    cursor: pointer
  }
  .button--goto-web:hover {
    background-color: #007096;
  }
	</style>
<style id="media-query" type="text/css">
		@media only screen and (min-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }

			.block-grid .col {
				vertical-align: top;
			}

			.block-grid .col.num12 {
				width: 600px !important;
			}

			.block-grid.mixed-two-up .col.num3 {
				width: 150px !important;
			}

			.block-grid.mixed-two-up .col.num4 {
				width: 200px !important;
			}

			.block-grid.mixed-two-up .col.num8 {
				width: 400px !important;
			}

			.block-grid.mixed-two-up .col.num9 {
				width: 450px !important;
			}

			.block-grid.two-up .col {
				width: 300px !important;
			}

			.block-grid.three-up .col {
				width: 200px !important;
			}

			.block-grid.four-up .col {
				width: 150px !important;
			}

			.block-grid.five-up .col {
				width: 120px !important;
			}

			.block-grid.six-up .col {
				width: 100px !important;
			}

			.block-grid.seven-up .col {
				width: 85px !important;
			}

			.block-grid.eight-up .col {
				width: 75px !important;
			}

			.block-grid.nine-up .col {
				width: 66px !important;
			}

			.block-grid.ten-up .col {
				width: 60px !important;
			}

			.block-grid.eleven-up .col {
				width: 54px !important;
			}

			.block-grid.twelve-up .col {
				width: 50px !important;
			}
		}

		@media (max-width: 405px) {

		}

		@media (max-width: 555px) {
			.date-time {
				font-size: 9px !important;
			}

			.number {
				padding: 1px 2px 0 !important;
				min-width: 10px !important;
			}

			.icon {
				font-size: 6px !important;
			}

			.new {
				padding: 1px 0px 0 !important;
				width: 25px !important;
				margin-left: 2px !important;
				text-align: center;
			}

			.title {
				font-size: 10px !important;
				line-height: 12px !important;
			}
		}

		@media (max-width: 620px) {


			.block-grid {
				width: 100% !important;
			}

			.col>div {
				margin: 0 auto;
			}

			img.fullwidth,
			img.fullwidthOnMobile {
				max-width: 100% !important;
			}

			.no-stack .col {
				min-width: 0 !important;
				display: table-cell !important;
			}

			.no-stack.two-up .col {
				width: 50% !important;
			}

			.no-stack .col.num4 {
				width: 33% !important;
			}

			.no-stack .col.num8 {
				width: 66% !important;
			}

			.no-stack .col.num4 {
				width: 33% !important;
			}

			.no-stack .col.num3 {
				width: 25% !important;
			}

			.no-stack .col.num6 {
				width: 50% !important;
			}

			.no-stack .col.num9 {
				width: 75% !important;
			}

			.video-block {
				max-width: none !important;
			}

			.mobile_hide {
				min-height: 0px;
				max-height: 0px;
				max-width: 0px;
				display: none;
				overflow: hidden;
				font-size: 0px;
			}

			.desktop_hide {
				display: block !important;
				max-height: none !important;
			}
		}
	</style>
<body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #FFFFFF;">
<style id="media-query-bodytag" type="text/css">
@media (max-width: 620px) {
  .col > div {
    margin: 0 auto;
  }
  img.fullwidth {
    max-width: 100%!important;
    height: auto!important;
  }
  img.fullwidthOnMobile {
    max-width: 100%!important;
    height: auto!important;
  }
  .no-stack .col {
    min-width: 0!important;
    display: table-cell!important;
  }
  .no-stack.two-up .col {
    width: 50%!important;
  }
  .no-stack.mixed-two-up .col.num4 {
    width: 33%!important;
  }
  .no-stack.mixed-two-up .col.num8 {
    width: 66%!important;
  }
  .no-stack.three-up .col.num4 {
    width: 33%!important
  }
  .no-stack.four-up .col.num3 {
    width: 25%!important
  }
}
</style>
<table bgcolor="#FFFFFF" cellpadding="0" cellspacing="0" class="nl-container" role="presentation" style="table-layout: fixed; vertical-align: top; min-width: 320px; Margin: 0 auto; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #FFFFFF; width: 100%;" valign="top" width="100%">
	<tbody style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
		<tr style="vertical-align: top;" valign="top">
			<td style="word-break: break-word; vertical-align: top; border-collapse: collapse;" valign="top">
				<div style="background-color:transparent;">
					<div class="block-grid two-up" style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
						<div class="email-template__block" style="overflow: hidden;">
							<div class="email-template-left" style="float: left"><img src="{{host}}/static/images/soremo-favi2_01.png" height="45px" width="45px" style="border-radius: 50%"></div>
							<div class="email-template-right" style="float: right">
                <div class="user" style="display: flex; justify-content: flex-end; align-items: center"><span
                        style="font-size: 12px; margin-right: 5px; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';">{{ user.get_display_name }} 様</span>
                  <img src="{{ user|get_user_url:'small' }}" height="24px" width="24px"
                       style="float: right; border-radius: 50%">
                </div>
								<p style="margin: 5px 0; color: #999; font-size: 12px; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';" title="{{subject}}">{{ subject|truncatechars:20 }}</p>
							</div>
						</div>
					</div>
				</div>

      {% for product in products %}
        {% if product and product.image %}
          <div style="background-color:transparent;">
            <div class="block-grid"
                 style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
              <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                <div class="col num12"
                     style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                  <div style="width:100% !important;">
                    <div style="margin-bottom: 5px;border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                      <div align="center" class="img-container center autowidth fullwidth"
                           style="padding-right: 0px;padding-left: 0px;">
                        <a href="{{ host }}/top/project/{{ product.pk }}" style="display: block; overflow: hidden;">
                          <img align="center" alt="Image" border="0" class="center autowidth fullwidth"
                               src="{{ product.image.url }}"
                               style="outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; clear: both; border: 0; height: auto; float: none; width: 100%; max-width: 600px; display: block;"
                               title="Image" width="600"/>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {% endif %}
        {% for product_scene in product.scene_list.all %}
          {% if product_scene.title_product_scene.exists %}
            {#              {% if product_scene|check_product_scene_has_scene %}#}
            <div style="background-color:transparent;">
              <div class="block-grid"
                   style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                  <div class="col num12"
                       style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                    <div style="width:100% !important;">
                      <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px;">
                        <div style="color:#555555;font-family:Arial, 'Helvetica Neue', Helvetica, sans-serif;line-height:120%;padding-top:5px;padding-right:5px;">
                          <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                            <p style="font-size: 14px; line-height: 16px; margin: 0;"><span
                                    style="color: #000; margin: 0; text-transform: uppercase; font-weight: 600;"><span
                                    style="line-height: 20px; font-size: 16px;">{{ product_scene.name }}</span></span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="background-color:transparent;">
              <div class="block-grid"
                   style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                  <div class="col num12"
                       style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                    <div style="width:100% !important;">
                      <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:2px; padding-bottom:5px;">
                        <table border="0" cellpadding="0" cellspacing="0" class="divider" role="presentation"
                               style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                               valign="top" width="100%">
                          <tbody>
                          <tr style="vertical-align: top;" valign="top">
                            <td class="divider_inner"
                                style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; border-collapse: collapse;"
                                valign="top">
                              <table align="center" border="0" cellpadding="0" cellspacing="0"
                                     class="divider_content" height="0" role="presentation"
                                     style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 1px solid #BBBBBB; height: 0px;"
                                     valign="top" width="100%">
                                <tbody>
                                <tr style="vertical-align: top;" valign="top">
                                  <td height="0"
                                      style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                      valign="top"><span></span></td>
                                </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="background-color:transparent;">
              <div class="block-grid three-up"
                   style="Margin: 0 auto; padding-top: 20px; min-width: 320px; max-width: 610px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                  {% for scene_title in product_scene.title_product_scene.all %}
                    {% if scene_title.scene_title.exists %}
                      {% with scene_title.scene_title.first as scene %}
                        <div style="width: 60%; float: left; min-height: 200px; overflow: hidden; margin-left: 20%;
                                      border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent;
                                      border-right:0px solid transparent; padding-bottom:20px; padding-right: 0px; padding-left: 0px;">
                          <div align="right" class="img-container center autowidth fullwidth"
                               style="padding-right: 5px;padding-left: 5px;">
                            <a href="{{ host }}/top/project/{{ product.pk }}/scene/{{ scene|get_first_version_id }}">
                              <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo','Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';
                                      font-size: 12px; line-height: 14px; color: #555555; padding-bottom: 48%;
                                      background-image: url('{{ scene|get_last_version_thumbnail:host }}');
                                      background-size: cover; background-position: center center;">
                              </div>
                            </a>
                          </div>
                          <div style="clear: both; color:#555555; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
                            <div style="padding: 0 5px; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                              <p style="margin: 0;"><span class="title"
                                                          style="color: #666666; font-size: 15px;line-height: 18px;">{{ scene.title.title }}</span>
                              </p>
                            </div>
                          </div>
                          <div style="color:#555555; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%; padding: 10px 5px 5px; overflow: hidden;">
                            <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                              <p class="date" style="font-size: 14px; line-height: 13px; margin: 0;"><span
                                      class="date-time"
                                      style="color: #0099cc; font-size: 11px;">{{ scene.title.modified_str }}</span>
                              </p>
                            </div>
                          </div>

                          {% with scene|get_last_comment_scene:time_ago as comments %}
                            {% with user|get_last_comment_new_scene:comments as last_comment %}
                              {% if last_comment %}
                                <div style="background-color:transparent; margin-top: 10px">
                                  <div class="block-grid"
                                       style="Margin: 0 auto; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                                    <div class="email-box"
                                         style="border: 1px solid #eee; border-radius: 10px; padding: 10px;">
                                      <table class="user-info"
                                             style="width:100%; table-layout: fixed; border-spacing: 0;">
                                        <tbody>
                                        <tr>
                                          <td style="width: 40px; padding-left: 10px; vertical-align: top; background-color: white; border-radius: 50%;">
                                            <img class="" src="{{ last_comment.user|get_user_url:'medium' }}"
                                                 alt="User Avatar" border="0"
                                                 style="width: 40px;height: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
                                            <div class="email-time"
                                                 style="color: #ababab; font-size: 10px; text-align: center">{{ last_comment.created|get_weekday }}
                                            </div>
                                          </td>
                                          <td style="padding-left: 10%">
                                            <div class="email-message"
                                                 style="white-space: pre-line; font-size: 12px;">{{ last_comment.comment | linebreaks }}</div>

                                            {% if last_comment.files.exists %}
                                              <div class="email-link"
                                                   style="margin-top: 5px;align-content: center;">
                                                {% for file in last_comment.files.all %}
                                                  {% if not file.folder %}
                                                    <div style="display: flex; margin-bottom: 5px;">
                                                      <img src="{{ host }}/static/images/download-gray.png" alt="Soremo"
                                                           style="height: 20px; width: 20px; padding-right: 5px">
                                                      <a class="email-link" href="{{ file.file.url }}" target="_blank"
                                                         style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;">
                                                        <span style="margin: 10px;">{{ file.real_name }}</span>
                                                      </a>
                                                    </div>
                                                  {% endif %}
                                                {% endfor %}

                                                {% for folder in last_comment.folders.all %}
                                                  {% if not folder.parent %}
                                                    <div style="display: flex; margin-bottom: 5px;">
                                                      <img src="{{ host }}/static/images/icon_folder.png" alt="Soremo"
                                                           style="height: 18px; width: 18px; padding-right: 5px">
                                                      <a class="email-link" href="#"
                                                            style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px;">
                                                      <span class="email-link" style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px; font-size: 12px">{{ folder.name }}</span>
                                                    </a>
                                                    </div>
                                                  {% endif %}
                                                {% endfor %}
                                              </div>
                                            {% endif %}

                                          </td>
                                        </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                              {% endif %}
                            {% endwith %}
                          {% endwith %}

                        </div>
                      {% endwith %}
                    {% else %}
                      {% with scene_title.last_version as scene %}
                        <div style="width: 60%; float: left; min-height: 200px; overflow: hidden; margin-left: 20%;
                                      border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent;
                                      border-right:0px solid transparent; padding-bottom:20px; padding-right: 0px; padding-left: 0px;">
                          <div align="right" class="img-container center autowidth fullwidth"
                               style="padding-right: 5px;padding-left: 5px;">
                            <a href="{{ host }}/top/project/{{ product.pk }}/scene/{{ scene|get_first_version_id }}">
                              <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo','Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';
                                      font-size: 12px; line-height: 14px; color: #555555; padding-bottom: 48%;
                                      background-image: url('{{ scene|get_last_version_thumbnail:host }}');
                                      background-size: cover; background-position: center center;">
                              </div>
                            </a>
                          </div>
                          <div style="clear: both; color:#555555; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
                            <div style="padding: 0 5px; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                              <p style="margin: 0;"><span class="title"
                                                          style="color: #666666; font-size: 15px;line-height: 18px;">{{ scene.title.title }}</span>
                              </p>
                            </div>
                          </div>
                          <div style="color:#555555; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%; padding: 10px 5px 5px; overflow: hidden;">
                            <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                              <p class="date" style="font-size: 14px; line-height: 13px; margin: 0;"><span
                                      class="date-time"
                                      style="color: #0099cc; font-size: 11px;">{{ scene.title.modified_str }}</span>
                              </p>
                            </div>
                          </div>
                          {#                              {% if scene.scene_comment_scene.exists %}#}
                          {% with scene_title|get_last_comment:time_ago as comments %}
                            {% with user|get_last_comment_new_scene:comments as last_comment %}
                              <div style="background-color:transparent; margin-top: 10px">
                                <div class="block-grid"
                                     style="Margin: 0 auto; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                                  <div class="email-box"
                                       style="border: 1px solid #eee; border-radius: 10px; padding: 10px;">
                                    <table class="user-info"
                                           style="width:100%; table-layout: fixed; border-spacing: 0;">
                                      <tbody>
                                      <tr>
                                        <td style="width: 40px; padding-left: 10px; vertical-align: top; background-color: white; border-radius: 50%;">
                                          <img class="" src="{{ last_comment.user|get_user_url:'medium' }}"
                                               alt="User Avatar" border="0"
                                               style="width: 40px; height: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
                                          <div class="email-time"
                                               style="color: #ababab; font-size: 10px; text-align: center">{{ last_comment.created|get_weekday }}
                                          </div>
                                        </td>
                                        <td style="padding-left: 10%">
                                          <div class="email-message"
                                               style="white-space: pre-line; font-size: 12px;">{{ last_comment.comment | linebreaks }}</div>

                                          {% if last_comment.files.exists %}
                                            <div class="email-link"
                                                 style="margin-top: 5px;align-content: center;">
                                              {% for file in  last_comment.files.all %}
                                                {% if not file.folder %}
                                                  <div style="display: flex; margin-bottom: 5px;">
                                                    <img src="{{ host }}/static/images/download-gray.png" alt="Soremo"
                                                         style="height: 20px; width: 20px; padding-right: 5px">
                                                    <a class="email-link" href="{{ file.file.url }}" target="_blank"
                                                       style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;">
                                                      <span style="margin: 10px;">{{ file.real_name }}</span>
                                                    </a>
                                                  </div>
                                                {% endif %}
                                              {% endfor %}

                                              {% for folder in last_comment.folders.all %}
                                                {% if not folder.parent %}
                                                  <div style="display: flex; margin-bottom: 5px;">
                                                    <img src="{{ host }}/static/images/icon_folder.png" alt="Soremo"
                                                         style="height: 18px; width: 18px; padding-right: 5px">
                                                    <a class="email-link" href="#"
                                                          style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px;">
                                                      <span class="email-link" style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px; font-size: 12px">{{ folder.name }}</span>
                                                    </a>
                                                  </div>
                                                {% endif %}
                                              {% endfor %}
                                            </div>
                                          {% endif %}

                                        </td>
                                      </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            {% endwith %}
                          {% endwith %}
                          {#                              {% endif %}#}
                        </div>
                      {% endwith %}
                    {% endif %}
                  {% endfor %}
                </div>
              </div>
            </div>
            {#              {% endif %}#}
          {% endif %}
        {% endfor %}
        <div class="email-button" style="text-align: center; margin: 40px 0;">
          <a class="button button--goto-web" href="{{ host }}/top/project/{{ product.pk }}" target="_blank"
             style="font-size: 16px; text-decoration: none;font-weight: 300;line-height: 21px;min-width: 100px;
                    padding: 12px 39px;border-radius: 4px;border: none;color: #fff;background-color: #009ace;cursor: pointer">
            WEBで確認
          </a>
        </div>
      {% endfor %}
      <div style="background-color:transparent">
        <div style="background-color:#f0f0f0; margin: 0 auto; min-width: 320px; max-width: 600px">
          <div class="block-grid"
               style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
            <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
              <div class="col num12"
                   style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                <div style="width:100% !important;">
                  <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                    <div style="color:#555555;font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
                      <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                        <p style="font-size: 14px; line-height: 14px; text-align: center; margin-bottom: 10px;"><span
                                style="color: #999999; font-size: 12px;">通知を希望されない方は、<a
                                href="{{ host }}/accounts/setting/{{ user.id }}">こちら</a>から通知をOFFにしてください。</span><br/>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="background-color:transparent">
        <div style="padding:30px 0 25px;margin:0 auto; min-width: 320px; max-width: 600px">
          {% include 'emails/_footer_email.html' %}
        </div>
      </div>
    </td>
  </tr>
  </tbody>
</table>
</body>
