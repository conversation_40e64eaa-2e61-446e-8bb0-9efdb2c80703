{% extends "base_nofooter_refactor.html" %}
{% load widget_tweaks %}
{% load bootstrap3 %}
{% load static %}
{% block extrahead %}
{% load i18n %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
    <link rel="stylesheet" href="{% static 'css/login.css' %}"/>
    <style>
        .banner__img-pc {
            transition: background .8s ease-in-out;
        }

        .img1 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201801.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img2 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201808.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img3 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201811.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img4 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201901.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img5 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201904.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img6 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201908.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img7 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .btn-forgot-pass {
            display: flex;
            justify-content: space-between;
        }

        .btn-go-back {
            background: #FFFFFF !important;
            color: #53565A !important;
            box-shadow: none;
            margin-right: 12px;
            width: 100%;
        }

        .btn-go-back:hover {
            color: #009ACE !important;
            background-color: #FFFFFF !important;
        }

        .btn-go-back:focus {
            color: #53565A;
            background-color: #FFFFFF !important;
        }

        .btn-submit {
            margin-left: 12px;
            width: 100%;
        }

        .error-mail-syntax {
            display: none !important;
        }

        .show-syntax-error {
            display: flex !important;
        }

        .input-forgot-password {
            min-width: 250px;
        }

        .auth__form {
            width: 100%;
            background-color: rgba(255, 255, 255, 0.618) !important;
            border-radius: 12px;
            padding: 32px 12px 12px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(13px);    
        }
        
        .auth__form-title {            
            margin: 0px !important;
            padding-bottom: 24px;

            font-family: 'A+mfCv-AXISラウンド 50 R StdN';
            font-size: 24px;
            line-height: 150%;
            color: #000000;
        }

        .form-group .input-box {
            border: 1px solid #F0F0F0 !important;
            background-color:  rgba(255, 255, 255, 0.618) !important;
            color: #000000;
            padding: 12px 12px !important;
            border-radius: 4px !important;
            box-shadow: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .auth__form-button {
            width: 100% !important;
            background-color: #009ace !important;
            color: #ffffff !important;
            padding: 16px 12px !important;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border-radius: 4px !important;
            font-family: 'A+mfCv-AXISラウンド 50 M StdN' !important;
            font-size: 18px !important;
            letter-spacing: 2.5px;
        }
        
        @media (max-width: 576px) {
            .banner__img-pc {
                display: none;
            }

            .img1 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201801.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img2 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201808.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img3 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201811.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img4 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201901.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img5 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201904.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img6 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201908.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img7 {
                background-color: transparent;
                background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }
        }
    </style>
{% endblock %}
{% block content %}
    <style>
        body {
            overflow-y: hidden !important;
        }
    </style>
    <div class="login">
        <div class="banner">
            <div class="bxslider">
                <div class="banner__img visible-xs visible-sm img0">
                </div>
                <div class="banner__img-pc img0"></div>
            </div>
        </div>
        <div class="auth">
            <div class="auth__content">
                <div class="auth__main">
                    <div class="auth__form">
                        <h3 class="auth__form-title title-form">{% trans "Forgot pass confirm title" %}</h3>
                        <div class="bodytext-11">ご登録のメールアドレスを入力すると、パスワード再設定のご案内が届きます。</div>
                        <form method="post" action="{% url 'accounts:pwd_reset' %}">
                            {% csrf_token %}
                            <div class="form-group" style="margin:0;">
                                <input type="text" name="email" maxlength="254" class="input-label input-box input-forgot-password"
                                       placeholder="メールアドレス" id="id_email">
                                {% if form.errors %}
                                    <div class="error-messager valid_mail">
                                        {{ form.errors.email }}
                                    </div>
                                {% endif %}
                                <div class="error-messager valid_mail error-mail-syntax">
                                    メールアドレスが正しくありません
                                </div>
                                {% if form.errors_mail %}
                                    {% for error in form.errors_mail %}
                                        <div class="error-messager">
                                            {{ error|escape }}
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            {% buttons %}
                            <div class="btn-forgot-pass">
                                <input type="button" id="back" value="{% trans 'cancel' %}"
                                       class="btn btn-go-back btn--tertiary"/>
                                <input type="submit" id="save" value="送信"
                                       class="btn btn-submit btn--primary" style="margin: 0px;"/>
                            </div>
                            {% endbuttons %}
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{% static 'js/random.js' %}"></script>
    <script>
        //Array of images which you want to show: Use path you want.
        let images = ['img1', 'img2', 'img3', 'img4', 'img5', 'img6', 'img7'];

        function goBack() {
            window.history.go(go_back*(-1));
        }

        function isEmail(email) {
            var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
            return regex.test(email);
        }
        removeNavbar()
        randomImageBackground(images);

        $(document).ready(function() {
            rezizeAuth();
            $(window).on('resize', () => {
                rezizeAuth()
            })

            $(document).on('click', '.btn-go-back', () => {
                window.location = window.location.href.split('/accounts/')[0] + '/accounts/login';
            })

            function validateEmail() {
                if($('.input-forgot-password').val()!=='') {
                    if(!isEmail($('.input-forgot-password').val())) {
                        $('.error-mail-syntax').addClass('show-syntax-error')
                        $('.btn-submit').addClass('disabled');
                    } else {
                        $('.error-mail-syntax').removeClass('show-syntax-error')
                        $('.btn-submit').removeClass('disabled');
                    }
                } else {
                    $('.btn-submit').addClass('disabled');
                }
            }

            validateEmail();

            $(document).on('input', '.input-forgot-password', () => {
                validateEmail();
            })


        })
    </script>
    <script src="{% url 'javascript-catalog' %}"></script>
{% endblock %}
