{% extends "base_nofooter_refactor.html" %}
{% load widget_tweaks %}
{% load bootstrap3 %}
{% load static %}
{% block extrahead %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="{% static 'css/login.css' %}"/>
    <style>
        .banner__img-pc {
            transition: background .8s ease-in-out;
        }

        .img1 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201801.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img2 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201808.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img3 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201811.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img4 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201901.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img5 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201904.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img6 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201908.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img7 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        @media (max-width: 576px) {
            .banner__img-pc {
                display: none;
            }

            .img1 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201801.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img2 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201808.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img3 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201811.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img4 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201901.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img5 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201904.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img6 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201908.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img7 {
                background-color: transparent;
                background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }
        }
    </style>
{% endblock %}
{% block content %}
    <style>
        body {
            overflow-y: hidden !important;
        }
    </style>
    <div class="login">
        <div class="banner">
            <div class="bxslider">
                <div class="banner__img visible-xs visible-sm img0">
                </div>
                <div class="banner__img-pc img0"></div>
            </div>
        </div>
        <div class="auth">
            <div class="auth__content">
                <div class="auth__main">
                    <div class="auth__form">
                        <div class="send-mail-complete">
                            <p style="text-align: center">
                                登録メールアドレスに、メールをお送りしました。<br>
                                確認の上、認証を行ってください。<br>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{% static 'js/random.js' %}"></script>
    <script>
        //Array of images which you want to show: Use path you want.
        let images = ['img1', 'img2', 'img3', 'img4', 'img5', 'img6', 'img7'];
        removeNavbar()
        randomImageBackground(images);
        
        $(document).ready(function () {
            rezizeAuth();
            $(window).on('resize', () => {
                rezizeAuth()
            })
        })
    </script>
{% endblock %}
