
{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util compress %}

{% block extrahead %}
    {% compress js inline %}
    <!-- <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script> -->
    <!-- <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
    <script src="{% static 'js/cropper.min.js' %}"></script> -->
    {% endcompress %}
    {% compress css %}
    <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
    <style>
        .has-success .form-control {
            border-color: #ccc !important;
        }

        .has-success .control-label {
            color: #333 !important;
        }

        .has-success .help-block {
            color: #737373 !important;
        }

        .user-info__notifi ul {
            display: inline-flex;
            list-style: none;
            padding-left: 0px;
        }

        .user-info__images img {
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
        }

        .user-info {
            margin-top: 50px;
        }

        .h3-title {
            font-size: 1.1em;
            color: #1a1a1a;
        }

        .user-info__upload label {
            color: white;
        }

        .user-info__form .form-control {
            border-radius: 0 !important;
        }

        .user-info__form label {
            margin-left: 0;
        }

        .user-info__upload {
            margin-top: 10px;
        }

        .modal-backdrop {
            z-index: -1 !important;
        }
    </style>
    {% endcompress %}
{% endblock %}

{% block content %}
    <div class="user-info">
        <div class="container">
            <div class="user-info__main account__info">
                <form class="user-info__form row" method="post"
                      action="{% url 'accounts:accounts_update' request.user.pk %}" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="account__title">アカウント情報</div>
                    <div class="form-group text-center col-sm-12">
                        <div class="user-info__images">
                            <img src="{{ user|get_avatar:'medium' }}" alt="" width="300" height="300">
                        </div>

                        <div class="modal" id="modalCrop">
                            <div class="modal-dialog" style="transform: translate(0,10%);">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                        <h4 class="modal-title">画像を登録</h4>
                                    </div>
                                    <div class="modal-body">
                                        <img src="" id="image" style="max-width: 100%;">
                                    </div>
                                    <div class="modal-footer">
                                        <div class="btn-group pull-left" role="group">
                                            <button type="button" class="btn btn-default js-zoom-in">
                                                <span class="glyphicon glyphicon-zoom-in"></span>
                                            </button>
                                            <button type="button" class="btn btn-default js-zoom-out">
                                                <span class="glyphicon glyphicon-zoom-out"></span>
                                            </button>
                                        </div>
                                        <button type="button" class="btn btn-primary js-crop-and-upload">登録する</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h3 class="user-info__title"></h3>
                        <div class="user-info__upload">
                            <label class="user-info__upload-title" for="id_avatar">プロフィール画像を選択</label>
                            <input type="file" name="avatar" class="user-info__upload-file" id="id_avatar">
                        </div>
                    </div>

                    <div class="account__form-group">
                        <div class="form-group col-sm-12 user-info__email">
                            <label for="email-info">メールアドレス</label>
                            {{ form.email|add_class:"form-control" }}
                        </div>

                         <div class="form-group col-sm-12">
                            <label for="email-info">パスワード</label>
                            <div>
                                <a class="button button--background button--background-primary button--round forgot_pass"
                                   href="{% url 'accounts:pwd_reset_confirm' uidb64=uidb64 token=token %}"
                                   role="button">変更する</a>
                            </div>
                        </div>

                        <div class="h3-title" style="color: transparent;">
                            _
                        </div>
                        <div class="account__sub-title">個人情報</div>
                        <div class="form-group col-sm-6 col-xs-6">
                            <label for="last-name">姓*</label>
                            {{ form.last_name|add_class:"form-control"|attr:"placeholder:姓" }}
                        </div>
                        <div class="form-group col-sm-6 col-xs-6">
                            <label for="first-name">名*</label>
                            {{ form.first_name|add_class:"form-control"|attr:"placeholder:名" }}
                        </div>

                        <div class="form-group col-sm-12 user-info__enterprise">
                            <label for="enterprise">会社名</label>
                            {{ form.enterprise|add_class:"form-control" }}
                        </div>
                        <div class="form-group col-sm-12 user-info__position">
                            <label for="position">役職</label>
                            {{ form.position|add_class:"form-control" }}
                        </div>
                        {{ form.x }}
                        {{ form.y }}
                        {{ form.width }}
                        {{ form.height }}

                        <div class="user-info__submit col-sm-12 text-right account__action">
                            {% buttons %}
                                <input type="submit" value="OK" class="button button--gradient button--gradient-primary button--round border-width-button"/>
                            {% endbuttons %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
{% endblock content %}

{% block extra_script %}
{% compress js inline %}
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
    <script src="{% static 'js/cropper.min.js' %}"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $('#id_avatar').attr({accept: 'image/*'});

            $('#id_avatar').on('change', function () {
                var image_dom = $('.user-info__images img');
                if (this.files && this.files[0] && this.files[0].name.match(/\.(jpg|jpeg|png|gif|JPG|PNG|JPEG|GIF)$/)) {
                    let reader = new FileReader();
                    reader.onload = function (e) {
                        $('#image').attr('src', e.target.result);
                        $('#modalCrop').modal('show');
                    };
                    reader.readAsDataURL(this.files[0]);
                } else if (this.files.length == 0) {
                    return false;
                } else {
                    alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
                    $(this).val('').clone(true);
                }
            })
            $("#modalCrop").modal({
                show: false,
                backdrop: 'static'
            });

            var $image = $('#image');
            var cropBoxData;
            var canvasData;
            $('#modalCrop').on('shown.bs.modal', function () {
                $image.cropper({
                    viewMode: 1,
                    aspectRatio: 1 / 1,
                    minCropBoxWidth: 200,
                    minCropBoxHeight: 200,
                    minContainerHeight: 300,
                    ready: function () {
                        $image.cropper('setCanvasData', canvasData);
                        $image.cropper('setCropBoxData', cropBoxData);
                    }
                });
                $(document).keypress(function (e) {
                    e.preventDefault()
                    var code = e.which; // recommended to use e.which, it's normalized across browsers
                    if (code == 13) {
                        $('.js-crop-and-upload').click();
                        return false;
                    }
                });
            }).on('hidden.bs.modal', function () {
                cropBoxData = $image.cropper('getCropBoxData');
                canvasData = $image.cropper('getCanvasData');
                if ($('.user-info__images img')[0].src.match('/default-avt.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-client.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-admin.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-master-admin.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-creator.png')) {
                    $('#id_avatar').val('').clone(true);
                }
                $image.cropper('destroy');
            });

            // Enable zoom in button
            $('.js-zoom-in').click(function () {
                $image.cropper('zoom', 0.1);
            });

            // Enable zoom out button
            $('.js-zoom-out').click(function () {
                $image.cropper('zoom', -0.1);
            });

            $('.js-crop-and-upload').click(function () {
                var cropData = $image.cropper("getData", {fillColor: '#fff'});
                var croppedImageDataURL = $image.cropper('getCroppedCanvas', {fillColor: '#fff'}).toDataURL("image/png");
                var image_dom = $('.user-info__images img');
                image_dom.attr('src', croppedImageDataURL);

                $('#id_x').val(cropData['x']);
                $('#id_y').val(cropData['y']);
                $('#id_height').val(cropData['height']);
                $('#id_width').val(cropData['width']);
                $image[0].height = cropData['height'];
                $image[0].width = cropData['width'];
                console.log($image[0].height, $image[0].width, $image[0].x, $image[0].y);
                $('#modalCrop').modal('hide');
            });

        });

        let checkActive = function (obj) {
            let $obj = $(obj);
            $.ajax({
                url: '/product_user/' + $obj.attr('value') + '/?active=' + $obj.prop('checked'),
                type: 'GET',
            })
                .done(function () {
                    console.log("success");
                })
                .fail(function () {
                    $obj.prop('checked', false)
                });

        }
    </script>
    {% endcompress %}
{% endblock %}
