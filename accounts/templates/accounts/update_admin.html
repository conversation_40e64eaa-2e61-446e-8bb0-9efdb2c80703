{% extends "base.html" %}
{% load bootstrap3 %}


{% block extrahead %}
    <style>
    </style>
    <script type="text/javascript">
        function showOrHideListIp() {
            if ($('#id_is_check_ip').is(":checked")) {
                $("#id_list_ip").prop('disabled', false);
            } else {
                $("#id_list_ip").prop('disabled', true);
            }
        }

        let checkListIp = function(){
            if($('#id_is_check_ip').is(':checked') && !$('#id_list_ip').disabled && $('#id_list_ip').val() == ''){
                alert('IPアドレスを入力してください。');
                return false;
            } else {
                $(this_dom).prop('disabled', true);
                $(this_dom).parents('form').submit();
            }
        }

        $(document).ready(function () {
            showOrHideListIp();
            $("#id_is_check_ip").click(showOrHideListIp);
            let userRole = $('form').attr('data-user-role');
            $('select[name=role] option[value=' + userRole + ']').attr('selected', true);
            if ($('#id_role').val() != 'master_client') {
                $('#id_products').prop('required', false);
                $('#id_products').closest('.form-group').hide()
            }
            $(document).on('change', '#id_role', function () {
                if ($('#id_role').val() != 'master_client') {
                    $('#id_products').closest('.form-group').hide();
                } else {
                    $('#id_products').closest('.form-group').show();
                }
            })
        });
    </script>
{% endblock %}

{% block content %}
    <div class="m10 row">
        <div class="col-xs-6">
            <form method="post" action="{% url 'accounts:accounts_update_admin' user_id %}" data-user-role="{{ user_updated.role }}">
                {% csrf_token %}
                {% bootstrap_form form %}
                {% buttons %}
                    <input type="submit" value="更新" class="btn btn-primary" onclick="return checkListIp();">
                {% endbuttons %}
            </form>
        </div>
    </div>
{% endblock content %}
