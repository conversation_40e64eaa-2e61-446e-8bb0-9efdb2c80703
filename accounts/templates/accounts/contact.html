{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util compress %}


{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" />
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
    <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
    <link href="{% static 'css/contact.css' %}" rel="stylesheet">
    {% endcompress %}
    {% compress js inline %}
    <script src="{% static 'js/cropper.min.js' %}"></script>
    {% endcompress %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
{% endblock %}

{% block content %}
  <div class="contact-info">
    <div class="container">
      <div class="contact-info__main">
        <div class="contact-info__heading">
          <h3>CONTACT</h3>
        </div>

        <div class="contact-info__content">
          <form class="contact-info__form row" id="contact__form" method="post"
                action="" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="account__form-group form-group col-sm-12">
                <div class="col-sm-12 form-group">
                    <label class="col-sm-12" for="">
                        <span class="account__field-label">種類</span>
                        <div class="account__sub-group">
                            <div class="account__form-multi">
                                <label class="input-radio">
                                    <input type="radio" name="show_contact" checked="" value="public" index="0" required="True" id="id_contact_0/">お仕事のご相談
                                    <div class="check-mark"></div>
                                </label>
                            </div>
                            <div class="account__form-multi">
                                <label class="input-radio">
                                    <input type="radio" name="show_contact" value="private" index="1" required="True" id="id_contact_1/">ご応募
                                    <div class="check-mark"></div>
                                </label>
                            </div>
                            <div class="account__form-multi">
                                <label class="input-radio">
                                    <input type="radio" name="show_contact" value="private" index="1" required="True" id="id_contact_2/">その他
                                    <div class="check-mark"></div>
                                </label>
                            </div>
                        </div>
                    </label>
                </div>
            </div>

            <div class="account__form-group form-group col-sm-12">
                <h3 class="account__form-heading">ご連絡先</h3>
                <div class="col-sm-12 form-group">
                    <label class="col-sm-4" for="id_fullname">
                    <span class="account__field-label">氏名<span class="account__jp-astarisk">[必須]</span></span>
                    <input type="text" name="fullname" value="" placeholder="善里 信哉"class="form-control account__input-text" id="id_fullname">
                    </label>
                </div>

                <div class="col-sm-12 form-group">
                    <label class="col-sm-4" for="id_email">
                    <span class="account__field-label">メールアドレス<span class="account__jp-astarisk">[必須]</span></span>
                    <input type="text" name="email" value="" placeholder="<EMAIL>"class="form-control account__input-text" id="id_email">
                    </label>
                </div>

                <div class="col-sm-12 form-group">
                    <label class="col-sm-4" for="id_email_confirm">
                    <span class="account__field-label">メールアドレス（確認）<span class="account__jp-astarisk">[必須]</span></span>
                    <input type="text" name="email_confirm" value="" placeholder="<EMAIL>"class="form-control account__input-text" id="id_email_confirm">
                    </label>
                </div>

                <div class="col-sm-12 form-group">
                    <label class="col-sm-4" for="id_job_type">
                    <span class="account__field-label">ポジション<span class="account__jp-astarisk-op">[任意]</span></span>
                    <input type="text" name="job_type" value="" placeholder="ディレクター"class="form-control account__input-text" id="id_job_type">
                    </label>
                </div>

                <div class="col-sm-12 form-group">
                    <label class="col-sm-4" for="id_company_url">
                    <span class="account__field-label">会社名<span class="account__jp-astarisk-op">[任意]</span></span>
                    <input type="text" name="company_url" value="" placeholder="株式会社ソレモ"class="form-control account__input-text" id="id_company_url">
                    </label>
                </div>

                <div class="col-sm-12 form-group">
                    <label class="col-sm-4" for="id_website">
                    <span class="account__field-label">WEBサイト<span class="account__jp-astarisk-op">[任意]</span></span>
                    <input type="text" name="website" value="" placeholder="https://soremo.jp"class="form-control account__input-text" id="id_website">
                    </label>
                </div>

                <div class="col-sm-12 form-group">
                    <label class="col-sm-3" for="id_phone">
                    <span class="account__field-label">電話番号（日中連絡がとれる番号）<span class="account__jp-astarisk-op">[任意]</span></span>
                    <input type="text" name="phone" value="" placeholder="03-6457-1780"class="form-control account__input-text" id="id_phone">
                    </label>
                </div>

                <div class="col-sm-12 form-group">
                    <label class="col-sm-12" for="">
                        <span class="account__field-label">ご希望の連絡方法</span>
                        <div class="account__sub-group">
                            <div class="account__form-multi">
                                <label class="input-radio">
                                    <input type="radio" name="show_profile" checked="" value="public" index="0" required="True" id="id_new_project/">メール
                                    <div class="check-mark"></div>
                                </label>
                            </div>
                            <div class="account__form-multi">
                                <label class="input-radio">
                                    <input type="radio" name="show_profile" value="private" index="1" required="True" id="id_recuitement/">電話
                                    <div class="check-mark"></div>
                                </label>
                            </div>
                        </div>
                    </label>
                </div>
            </div>

            <div class="account__form-group form-group col-sm-12">
                <h3 class="account__form-heading">お問い合わせ内容</h3>
                <div class="col-sm-12 form-group">
                    <label class="col-sm-12" for="id_message">
                        <span class="account__field-label">メッセージ</span>
                        <div class="account__textarea">
                            <textarea name="message" cols="40" rows="3" class="form-textarea" id="id_message"
                            placeholder="新規プロジェクトのサウンド開発をご相談したいです。プロジェクト詳細は、仕様書をお送りしましたので、ご確認ください。折り返しをお待ち しております。"></textarea>
                        </div>
                    </label>
                </div>
            </div>

            <div class="account__form-group form-group col-sm-12">
                <h3 class="account__form-heading"></h3>
                <div class="col-sm-12 form-group">
                    <label class="col-sm-12" for="">
                        <span class="account__field-label">資料<span class="account__jp-astarisk-op">[任意]</span></span>
                        <p class="account__field-label">ファイルを追加</p>
                        <p class="account__field-text">※フォルダごとドラッグ＆ドロップでお送りいただけます。</p>
                        <div class="account__upload-file">
                            <div class="account__file">
                                <i class="icon icon--sicon-clip"></i>
                                <a href="" target="_blank">
                                  <div class="account__file-name">***************.pdf</div>
                                </a>
                                <i class="icon icon--sicon-close"></i>
                            </div>
                        </div>
                        <div class="account_upload-file dz-clickable">
                            <div id="myId" class="fallback dropzone">
                                <input type="checkbox" name="user_file-clear" id="user_file-clear_id" style="display: none;" />
                                <label for="user_file-clear_id" style="display: none;">クリア</label>
                                <input type="file" name="user_file" class="account_file" id="id_user_file" accept="application/pdf" />
                                <div class="dz-default dz-message">
                                    <button class="dz-button" type="button">
                                        <i class="icon icon--sicon-add-cirlce"></i>
                                        <p>ファイルを選択</p>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="account__field-text-link">※SOREMOでは、個人情報の保護に関する法令を遵守するとともに、
                            <a href="" class="account__field-text-blue">プライバシーポリシー</a>に基づき、お客様の個人情報を取り扱います。</div>
                    </label>
                </div>
            </div>

            <div class="contact-info__submit col-sm-12 account__action">
              {% buttons %}
                <input type="button" value="OK" id="btn__ok"  data-toggle="modal" data-target="#modalConfirm"
                       class="button button--gradient button--gradient-primary button--round border-width-button"/>
              {% endbuttons %}
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal confirm -->
  <div class="modal popup-container popup-confirm fade" id="modalConfirm" role="dialog" style="z-index: 9999">
    <div class="modal-dialog popup-dialog">
        <div class="modal-content popup-content">
            <div class="popup-header">
                <button type="button" class="popup-close" data-dismiss="modal" aria-label="Close">
                    <i class="icon icon--sicon-close"></i>
                  </button>
                <h4 class="popup-title">以下の内容で送信します。ご確認ください。</h4>
            </div>
            <div class="popup-body">
                <div class="popup-body__wrap">
                    <div class="popup-body__list">
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">種類:</div>
                            <div class="popup-body__text text-content">お仕事のご相談</div>
                        </div>
                    </div>

                </div>
                <div class="popup-body__wrap">
                    <div class="popup-body__heading">ご連絡先</div>
                    <div class="popup-body__list">
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">氏名:</div>
                            <div class="popup-body__text text-content">お仕事のご相談</div>
                        </div>
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">メールアドレス:</div>
                            <div class="popup-body__text text-content"><EMAIL></div>
                        </div>
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">メールアドレス（確認）:</div>
                            <div class="popup-body__text text-content"><EMAIL></div>
                        </div>
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">役職・ポジション:</div>
                            <div class="popup-body__text text-content">ディレクター</div>
                        </div>
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">会社名:</div>
                            <div class="popup-body__text text-content">株式会社ソレモ</div>
                        </div>
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">WEBサイト:</div>
                            <div class="popup-body__text text-content">https://soremo.jp</div>
                        </div>
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">電話番号（日中連絡がとれる番号）:</div>
                            <div class="popup-body__text text-content">03-6457-1780</div>
                        </div>
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">ご希望の連絡方法:</div>
                            <div class="popup-body__text text-content">メール</div>
                        </div>
                    </div>
                </div>

                <div class="popup-body__wrap">
                    <div class="popup-body__heading">お問い合わせ内容</div>
                    <div class="popup-body__list">
                        <div class="popup-body__item">
                            <div class="popup-body__text text-title">メッセージ :</div>
                            <div class="popup-body__text text-content">新規プロジェクトのサウンド開発をご相談したいです。プロジェクト詳細は、仕様書をお送りしましたので、ご確認ください。折り返しをお待ちしております。は、仕様書をお送りしましたので、ご確認ください。折</div>
                        </div>
                    </div>

                </div>

                <div class="popup-body__wrap">
                    <div class="popup-body__text text-title" style="margin-top: 24px;">資料</div>
                    <div class="popup-body__list">
                        <div class="popup-body__item">
                            <div class="account__upload-file">
                                <div class="account__file">
                                    <i class="icon icon--sicon-clip"></i>
                                    <a href="" target="_blank">
                                      <div class="account__file-name">***************.pdf</div>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="popup-body__item">
                            <div class="account__field-text-link" style="padding: 0; border: 0;">※SOREMOでは、個人情報の保護に関する法令を遵守するとともに、
                                <a href="" class="account__field-text-blue">プライバシーポリシー</a>に基づき、お客様の個人情報を取り扱います。</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="popup-footer">
                <button type="button" class="btn btn-popup-close" data-dismiss="modal">戻る</button>
                <button type="button" class="btn btn-popup-send" data-toggle="modal" data-target="#modalOk">送信</button>
            </div>
        </div>
    </div>
</div>
  <!-- End modal confirm -->


<!-- Modal delete -->
<div class="modal popup-container" id="modalOk" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog popup-dialog" role="document">
        <div class="modal-content popup-content">
            <div class="popup-header"></div>
            <div class="popup-body">
                <h5 class="popup-title">送信完了</h5>
                <p class="popup-text">お問い合わせありがとうございました。
                    確認メールをお送りしましたので、ご確認ください。</p>
            </div>
            <div class="popup-footer">
                <button type="button" class="btn btn-popup-send">OK</button>
            </div>
        </div>
    </div>
</div>
<!-- End modal delete -->

{% endblock content %}
{% block extra_script %}
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/contact.js' %}"></script>
    {% endcompress %}
{% endblock %}
