{% load util %}
<style type="text/css">
		body {
			margin: 0;
			padding: 0;
		}

		table,
		td,
		tr {
			vertical-align: top;
			border-collapse: collapse;
		}

		* {
			line-height: inherit;
		}

		a[x-apple-data-detectors=true] {
			color: inherit !important;
			text-decoration: none !important;
		}

		.ie-browser table {
			table-layout: fixed;
		}

		[owa] .img-container div,
		[owa] .img-container button {
			display: block !important;
		}

		[owa] .fullwidth button {
			width: 100% !important;
		}

		[owa] .block-grid .col {
			display: table-cell;
			float: none !important;
			vertical-align: top;
		}

		.ie-browser .block-grid,
		.ie-browser .num12,
		[owa] .num12,
		[owa] .block-grid {
			width: 600px !important;
		}

		.ie-browser .mixed-two-up .num4,
		[owa] .mixed-two-up .num4 {
			width: 200px !important;
		}

		.ie-browser .mixed-two-up .num8,
		[owa] .mixed-two-up .num8 {
			width: 400px !important;
		}

		.ie-browser .block-grid.two-up .col,
		[owa] .block-grid.two-up .col {
			width: 300px !important;
		}

		.ie-browser .block-grid.three-up .col,
		[owa] .block-grid.three-up .col {
			width: 300px !important;
		}

		.ie-browser .block-grid.four-up .col [owa] .block-grid.four-up .col {
			width: 150px !important;
		}

		.ie-browser .block-grid.five-up .col [owa] .block-grid.five-up .col {
			width: 120px !important;
		}

		.ie-browser .block-grid.six-up .col,
		[owa] .block-grid.six-up .col {
			width: 100px !important;
		}

		.ie-browser .block-grid.seven-up .col,
		[owa] .block-grid.seven-up .col {
			width: 85px !important;
		}

		.ie-browser .block-grid.eight-up .col,
		[owa] .block-grid.eight-up .col {
			width: 75px !important;
		}

		.ie-browser .block-grid.nine-up .col,
		[owa] .block-grid.nine-up .col {
			width: 66px !important;
		}

		.ie-browser .block-grid.ten-up .col,
		[owa] .block-grid.ten-up .col {
			width: 60px !important;
		}

		.ie-browser .block-grid.eleven-up .col,
		[owa] .block-grid.eleven-up .col {
			width: 54px !important;
		}

		.ie-browser .block-grid.twelve-up .col,
		[owa] .block-grid.twelve-up .col {
			width: 50px !important;
		}
	</style>
<style id="media-query" type="text/css">
		@media only screen and (min-width: 620px) {
			.block-grid {
				width: 610px !important;
			}

			.block-grid .col {
				vertical-align: top;
			}

			.block-grid .col.num12 {
				width: 600px !important;
			}

			.block-grid.mixed-two-up .col.num3 {
				width: 150px !important;
			}

			.block-grid.mixed-two-up .col.num4 {
				width: 200px !important;
			}

			.block-grid.mixed-two-up .col.num8 {
				width: 400px !important;
			}

			.block-grid.mixed-two-up .col.num9 {
				width: 450px !important;
			}

			.block-grid.two-up .col {
				width: 300px !important;
			}

			.block-grid.three-up .col {
				width: 200px !important;
			}

			.block-grid.four-up .col {
				width: 150px !important;
			}

			.block-grid.five-up .col {
				width: 120px !important;
			}

			.block-grid.six-up .col {
				width: 100px !important;
			}

			.block-grid.seven-up .col {
				width: 85px !important;
			}

			.block-grid.eight-up .col {
				width: 75px !important;
			}

			.block-grid.nine-up .col {
				width: 66px !important;
			}

			.block-grid.ten-up .col {
				width: 60px !important;
			}

			.block-grid.eleven-up .col {
				width: 54px !important;
			}

			.block-grid.twelve-up .col {
				width: 50px !important;
			}
		}

		@media (max-width: 405px) {

		}

		@media (max-width: 555px) {
			.date-time {
				font-size: 9px !important;
			}

			.number {
				padding: 1px 2px 0 !important;
				min-width: 10px !important;
			}

			.icon {
				font-size: 6px !important;
			}

			.new {
				padding: 1px 0px 0 !important;
				width: 25px !important;
				margin-left: 2px !important;
				text-align: center;
			}

			.title {
				font-size: 10px !important;
				line-height: 12px !important;
			}
		}

		@media (max-width: 620px) {

			.block-grid,
			.col {
				min-width: 320px !important;
				max-width: 100% !important;
				display: block !important;
			}

			.block-grid {
				width: 100% !important;
			}

			.col {
				width: 100% !important;
			}

			.col>div {
				margin: 0 auto;
			}

			img.fullwidth,
			img.fullwidthOnMobile {
				max-width: 100% !important;
			}

			.no-stack .col {
				min-width: 0 !important;
				display: table-cell !important;
			}

			.no-stack.two-up .col {
				width: 50% !important;
			}

			.no-stack .col.num4 {
				width: 33% !important;
			}

			.no-stack .col.num8 {
				width: 66% !important;
			}

			.no-stack .col.num4 {
				width: 33% !important;
			}

			.no-stack .col.num3 {
				width: 25% !important;
			}

			.no-stack .col.num6 {
				width: 50% !important;
			}

			.no-stack .col.num9 {
				width: 75% !important;
			}

			.video-block {
				max-width: none !important;
			}

			.mobile_hide {
				min-height: 0px;
				max-height: 0px;
				max-width: 0px;
				display: none;
				overflow: hidden;
				font-size: 0px;
			}

			.desktop_hide {
				display: block !important;
				max-height: none !important;
			}
		}
	</style>
</head>
<body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #FFFFFF;">
<style id="media-query-bodytag" type="text/css">
@media (max-width: 620px) {
  .block-grid {
    min-width: 320px!important;
    max-width: 100%!important;
    width: 100%!important;
    display: block!important;
  }
  .col {
    min-width: 320px!important;
    max-width: 100%!important;
    width: 100%!important;
    display: block!important;
  }
  .col > div {
    margin: 0 auto;
  }
  img.fullwidth {
    max-width: 100%!important;
    height: auto!important;
  }
  img.fullwidthOnMobile {
    max-width: 100%!important;
    height: auto!important;
  }
  .no-stack .col {
    min-width: 0!important;
    display: table-cell!important;
  }
  .no-stack.two-up .col {
    width: 50%!important;
  }
  .no-stack.mixed-two-up .col.num4 {
    width: 33%!important;
  }
  .no-stack.mixed-two-up .col.num8 {
    width: 66%!important;
  }
  .no-stack.three-up .col.num4 {
    width: 33%!important
  }
  .no-stack.four-up .col.num3 {
    width: 25%!important
  }
}
</style>
<table bgcolor="#FFFFFF" cellpadding="0" cellspacing="0" class="nl-container" role="presentation" style="table-layout: fixed; vertical-align: top; min-width: 320px; Margin: 0 auto; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #FFFFFF; width: 100%;" valign="top" width="100%">
	<tbody>
		<tr style="vertical-align: top;" valign="top">
			<td style="word-break: break-word; vertical-align: top; border-collapse: collapse;" valign="top">
				<div style="background-color:transparent;">
					<div class="block-grid two-up" style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
						<div class="email-template__block" style="overflow: hidden;">
							<div class="email-template-left" style="float: left"><img src="{{host}}/static/images/soremo-favi2_01.png" width="120px" style="border-radius: 50%"></div>
							<div class="email-template-right" style="float: right">
								<div class="user" style="display: flex; justify-content: flex-end; align-items: center"><span style="color: #000; font-size: 12px; margin-right: 5px; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';">{{ user.get_display_name }} 様</span>{% if user.avatar %}
                                    <img src="{{ user|get_avatar:'small' }}" height="24px" width="24px" style="float: right; border-radius: 50%">
                                    {% else %}
                                    <img src="{{host}}/static/images/nav_login.png" height="24px" width="24px" style="float: right; border-radius: 50%">
                                    {% endif %}
                                </div>
								{% with products|get_title_mail_quarter:user_exclude_list as title%}<p style="margin: 5px 0; color: #999; font-size: 12px; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';" title="{{title}}">{{ title|truncatechars:20 }}</p>{% endwith %}
							</div>
						</div>
					</div>
				</div>
				<div style="background-color:transparent;">
					<div class="block-grid" style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
						<div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
							<div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
								<div style="width:100% !important;">
									<div style="margin-top: 7px; border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
										<div align="center" class="button-container" style="padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
											<a href="{{ host }}/" style="-webkit-text-size-adjust: none; text-decoration: none; display: block; color: #ffffff; background-color: #0099cc; border-radius: 20px; -webkit-border-radius: 20px; -moz-border-radius: 20px; width: 90%; width: calc(90% - 2px); border-top: 1px solid #0099cc; border-right: 1px solid #0099cc; border-bottom: 1px solid #0099cc; border-left: 1px solid #0099cc; padding-top: 0px; padding-bottom: 0px; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;" target="_blank"><span style="padding-left:20px;padding-right:20px;font-size:14px;display:inline-block;">
												<span style="font-size: 16px; line-height: 28px;"><span style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 14px; line-height: 28px;font-weight: 600;">WEBで見る</span></span>
											</span></a>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
        </div>
        {% for product in products %}
          {% with product|check_product_quarter:user_exclude_list as product %}
            {% if product and product.image %}
              <div style="background-color:transparent;">
                <div class="block-grid"
                     style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                  <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                    <div class="col num12"
                         style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                      <div style="width:100% !important;">
                        <div style="margin-bottom: 5px;border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                          <div align="center" class="img-container center autowidth fullwidth"
                               style="padding-right: 0px;padding-left: 0px;">
                            <a href="{{ host }}/" style="display: block; overflow: hidden;">
                              <img align="center" alt="Image" border="0" class="center autowidth fullwidth"
                                   src="{{ product.image.url }}"
                                   style="outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; clear: both; border: 0; height: auto; float: none; width: 100%; max-width: 600px; display: block;"
                                   title="Image" width="600"/>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
          {% endwith %}
          {% for product_scene in product|product_scene_title:scene_act %}
            <div style="background-color:transparent;">
              <div class="block-grid"
                   style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                  <div class="col num12"
                       style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                    <div style="width:100% !important;">
                      <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px;">
                        <div style="color:#555555;font-family:Arial, 'Helvetica Neue', Helvetica, sans-serif;line-height:120%;padding-top:5px;padding-right:5px;">
                          <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                            <p style="font-size: 14px; line-height: 16px; margin: 0;"><span
                                    style="color: #000; margin: 0; text-transform: uppercase; font-weight: 600;"><span
                                    style="line-height: 20px; font-size: 16px;">{{ product_scene.name }}</span></span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="background-color:transparent;">
              <div class="block-grid"
                   style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                  <div class="col num12"
                       style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                    <div style="width:100% !important;">
                      <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:2px; padding-bottom:5px;">
                        <table border="0" cellpadding="0" cellspacing="0" class="divider" role="presentation"
                               style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                               valign="top" width="100%">
                          <tbody>
                          <tr style="vertical-align: top;" valign="top">
                            <td class="divider_inner"
                                style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; border-collapse: collapse;"
                                valign="top">
                              <table align="center" border="0" cellpadding="0" cellspacing="0" class="divider_content"
                                     height="0" role="presentation"
                                     style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 1px solid #BBBBBB; height: 0px;"
                                     valign="top" width="100%">
                                <tbody>
                                <tr style="vertical-align: top;" valign="top">
                                  <td height="0"
                                      style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                      valign="top"><span></span></td>
                                </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {% with product_scene|filter_scense_act:scene_act as scenes %}
              <div style="background-color:transparent;">
                <div class="block-grid three-up"
                     style="Margin: 0 auto; padding-top: 20px; min-width: 320px; max-width: 610px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                  <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                    {% for item in scenes|filter_user_quarter:user_exclude_list %}
                      <div style="width: 33.3333333%; float: left; min-height: 200px; overflow: hidden; border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-bottom:20px; padding-right: 0px; padding-left: 0px;">
                        <div align="right" class="img-container center autowidth fullwidth"
                             style="padding-right: 5px;padding-left: 5px;">
                          <a href="{{ host }}">
                            <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555; padding-bottom: 48%; background-image: url('
                                    {% if item.thumbnail %}{{ item.thumbnail.url }}{% else %} {{ host }}/static/images/img-email.jpg{% endif %}'); background-size: cover; background-position: center center;">
                              <div class="icon"
                                   style="color: #fff; font-size: 8px; padding-top: 5px; padding-right: 5px;">
                                {% if item.check_new %}
                                  <span class="new"
                                        style="margin-left: 5px; width: 20px; border-radius: 20px; height: 15px; line-height: 15px; display: inline-block; background-color: #e6002d; padding: 1px 5px 0; text-transform: uppercase;">
											        new</span>
                                {% else %}
                                  {% if item.count_comment > 0 %}
                                    <span class="number"
                                          style="min-width: 5px; border-radius: 20px; background-color: #e6002d; padding: 1px 5px 0; text-align: center; display: inline-block; line-height: 15px; height: 15px;">
                                                            {{ item.count_comment }}</span>
                                  {% endif %}
                                {% endif %}
                              </div>
                            </div>
                          </a>
                        </div>

                        <div style="color:#555555; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%; padding: 10px 5px 5px; overflow: hidden;">
                          <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                            <p class="date" style="font-size: 14px; line-height: 13px; margin: 0;"><span
                                    class="date-time"
                                    style="color: #0099cc; font-size: 11px;">{{ item.modified }}</span></p>
                          </div>
                        </div>
                        <div style="clear: both; color:#555555; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
                          <div style="padding: 0 5px; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                            <p style="margin: 0;"><span class="title"
                                                        style="color: #666666; font-size: 15px; line-height: 18px;">{{ item.title }}</span>
                            </p>
                          </div>
                        </div>
                      </div>
                    {% endfor %}
                  </div>
                </div>
              </div>
            {% endwith %}
          {% endfor %}
        {% endfor %}
        <div style="background-color:#fafafa;">
          <div class="block-grid"
               style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
            <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
              <div class="col num12"
                   style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                <div style="width:100% !important;">
                  <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:60px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                    <div style="color:#555555;font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
                      <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                        <p style="font-size: 14px; line-height: 14px; text-align: center; margin-bottom: 10px;"><span
                                style="color: #999999; font-size: 12px;">通知を希望されない方は、<a
                                href="{{ host }}/accounts/setting/{{ user.id }}">こちら</a>から通知をOFFにしてください。</span><br/></p>
                      </div>
                    </div>
                    <div style="color:#555555;font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif';line-height:120%;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
                      <div style="font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 12px; line-height: 14px; color: #555555;">
                        <p style="font-size: 14px; line-height: 14px; text-align: center; margin: 0;"><span
                                style="color: #999999; font-size: 12px;">Copyright© SOREMO Co.,Ltd. All Right Reserved.</span><br/><br/>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
			</td>
		</tr>
	</tbody>
</table>
</body>
