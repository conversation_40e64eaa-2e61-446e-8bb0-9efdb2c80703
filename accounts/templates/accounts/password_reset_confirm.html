{% extends "base_nofooter_refactor.html" %}
{% load widget_tweaks %}
{% load bootstrap3 %}
{% load static %}
{% block extrahead %}
{% load i18n %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bxslider/4.2.15/jquery.bxslider.min.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
    <link rel="stylesheet" href="{% static 'css/login.css' %}"/>
    <style>
        .banner__img-pc {
            transition: background .8s ease-in-out;
        }

        .img1 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201801.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img2 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201808.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img3 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201811.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img4 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201901.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img5 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201904.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img6 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201908.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        .img7 {
            background-color: transparent;
            background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        @media (max-width: 576px) {
            .banner__img-pc {
                display: none;
            }

            .img1 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201801.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img2 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201808.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img3 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201811.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img4 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201901.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img5 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201904.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img6 {
                background-color: transparent;
                background-image: url({% static 'images/txt_sp_mvimage201908.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }

            .img7 {
                background-color: transparent;
                background-image: url({% static 'images/txt_pc_mvimage201911.jpg' %});
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
            }
        }
    </style>

{% endblock %}
{% block content %}
    <style>
        body {
            overflow-y: hidden !important;
        }
    </style>
    <div class="login">
        <div class="banner">
            <div class="bxslider">
                <div class="banner__img visible-xs visible-sm img0">
                </div>
                <div class="banner__img-pc img0"></div>
            </div>
        </div>
        <div class="auth">
            <div class="auth__content">
                <div class="auth__main">
                    <div class="auth__form">
                        <div class="confirm">
                            <h4 class="auth__form-title">{% trans "Forgot pass confirm title" %}</h4>
                            <form method="post" action="{% if next_url %}?next_url={{next_url}}{% endif %}">
                                {% csrf_token %}
                                <div class="account__field-hint">8文字以上（数字のみは不可）でパスワードを設定してください。</div>
                                <div class="form-group">
                                    {% trans "Placeholder new password" as Placeholder_new_password %}
                                    {% render_field form.new_password1 type="password" class+="input-box" required="required" placeholder=Placeholder_new_password %}
                                  {{ form.new_password1.errors }}
                                </div>

                                <div class="form-group">
                                    {% trans "Placeholder confirm password" as Placeholder_confirm_password %}
                                    {% render_field form.new_password2 type="password" class+="input-box" required="required" placeholder=Placeholder_confirm_password %}
                                    {{ form.new_password2.errors }}
                                </div>
                                <p class="form-group auth__form-note">{% trans "With the start of use" %} <a href="{% url 'app:termsofservice' %}" target="_blank">{% trans "terms of service" %} </a>と
                                    <a href="{% url 'app:privacypolicy' %}" target="_blank">{% trans "privacy policy" %} </a>{% trans "It is considered that you have agreed to" %}
                                </p>
                                <div class="form-group" style="display: flex; justify-content: center; padding: 0; width: 100%;">
                                    {% buttons %}
                                        <input type="submit" id="save" value="サインアップ" style="width: 100% !important;font-size: 18px!important;"
                                               class="btn btn--primary button button--submit btn-confirm-password"/>
                                    {% endbuttons %}
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{% static 'js/random.js' %}"></script>
    <script src="{% static 'js/password_reset_confirm.js' %}"></script>
    <script src="{% url 'javascript-catalog' %}"></script>
    <script>
        removeNavbar()
        randomImageBackground(images);
        $(document).ready(function () {
            rezizeAuth();
            $(window).on('resize', () => {
                rezizeAuth()
            })
        });
    </script>
{% endblock %}

