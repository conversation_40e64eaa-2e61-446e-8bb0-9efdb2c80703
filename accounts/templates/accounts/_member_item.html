{% load bootstrap3 %}
{% load static %}
{% load i18n %}

<tr>
    <td>{{ forloop.counter }}</td>
    <td>{{ obj.username }}</td>
    <td>{{ obj.fullname }}</td>
    <td>{{ obj.email }}</td>
    <td>{{ obj.created_str }}</td>
    <td>
        {% if obj.role == 'admin' %}
          <a href="{% url 'accounts:creator_info' obj.pk %}">{% trans "Change" %}</a>
        {% elif obj.role == 'master_client' %}
          <a href="{% url 'accounts:accounts_update_info' obj.pk %}">{% trans "Change" %}</a>
        {% else %}
           <a href="{% url 'accounts:accounts_update_admin' obj.pk %}">{% trans "Change" %}</a>
        {% endif %}
    </td>
    <td>
        <div class="btn btn-xs btn-default delete-user">{% trans "delete" %}</div>
        {% with obj.get_delete_form as form %}
            <form method="post" action="{% url 'accounts:accounts_delete' obj.pk %}">
                {% csrf_token %}
                {% bootstrap_form form %}
                {% buttons %}
                    <input type="submit" value="{% trans "delete" %}" class="btn btn-xs btn-default delete-user-btn hide"/>
                {% endbuttons %}
            </form>
        {% endwith %}
    </td>
</tr>
