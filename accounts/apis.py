import logging

from django.contrib.auth import login
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework.renderers import J<PERSON><PERSON>enderer  # Import JSONRenderer
from rest_framework.permissions import AllowAny, IsAuthenticated

from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from django.db.models import Q
from .models import AuthUser
from .serializers import LoginSerializer, AuthUserSerializer, MeAuthUserSerializer  # Ensure this serializer is set up to work with AuthUser
from rest_framework.authtoken.models import Token
from django.conf import settings
from django.shortcuts import get_object_or_404

@method_decorator(csrf_exempt, name='dispatch')
class LoginApiView(APIView):
	renderer_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON>]  # Specify J<PERSON><PERSON>ender<PERSON> for JSON responses
	permission_classes = [AllowAny]  # ログインAPIは認証不要
	authentication_classes = []  # ログインAPIは認証クラス不要

	def post(self, request, *args, **kwargs):
		serializer = LoginSerializer(data=request.data)
		# return Response({'error': 'Invalid credentials.'}, status=status.HTTP_200_OK)
		if serializer.is_valid():
			username_or_email = serializer.validated_data['username']
			password = serializer.validated_data['password']
			remember_me = serializer.validated_data.get('remember_me', False)
			ip = request.META['REMOTE_ADDR']  # Get the IP address of the request

			# Query to filter by active users with the given username or email
			filter = Q(is_active=True) & (Q(username__iexact=username_or_email) | Q(email__iexact=username_or_email))
			try:
				user = AuthUser.objects.get(filter)

				# Check password
				if user.check_password(password):
					# Log the user in
					login(request, user, backend='django.contrib.auth.backends.ModelBackend')

					# Generate or get the token
					token, created = Token.objects.get_or_create(user=user)

					# Set the token in cookies
					response = Response({
						'success': True,
						'token': token.key
					}, status=status.HTTP_200_OK)

					if not remember_me:
						request.session.set_expiry(86400)  # Set session to expire on browser close

					return response

			except AuthUser.DoesNotExist:
				return Response({'error': 'Invalid credentials.'}, status=status.HTTP_401_UNAUTHORIZED)
			except Exception as e:
				print(e)  # Log the error for debugging
				return Response({'error': 'An error occurred. Please try again.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

		return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AuthUserApiView(APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]  # 認証が不要であればそのまま
    permission_classes = [AllowAny]  # 必要に応じて変更
    http_method_names = ['get', 'patch']  # 許可するHTTPメソッドを明示

    def get(self, request, id=None):
        # IDが指定されている場合
        if id is not None:
            user = get_object_or_404(AuthUser, id=id)  # IDが一致するユーザーを取得
            serializer = AuthUserSerializer(user)
            return Response({'status': 200, 'result': serializer.data}, status=status.HTTP_200_OK)
        
        # IDが指定されていない場合（全ユーザー取得）
        users = AuthUser.objects.all()
        serializer = AuthUserSerializer(users, many=True)
        return Response({'status': 200, 'result': serializer.data}, status=status.HTTP_200_OK)

    def patch(self, request, id=None):
        # IDが指定されていない場合はエラーを返す
        if id is None:
            return Response({'status': 400, 'message': 'User ID is required for PATCH requests.'}, status=status.HTTP_400_BAD_REQUEST)

        # IDが指定されている場合、データを更新
        user = get_object_or_404(AuthUser, id=id)  # IDが一致するユーザーを取得
        serializer = AuthUserSerializer(user, data=request.data, partial=True)  # 部分更新のためpartial=True
        if serializer.is_valid():
            serializer.save()
            return Response({'status': 200, 'result': serializer.data}, status=status.HTTP_200_OK)
        return Response({'status': 400, 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

	# def post(self, request):
	# 	# Create a new AuthUser
	# 	serializer = AuthUserSerializer(data=request.data)
	# 	if serializer.is_valid():
	# 		serializer.save()
	# 		return Response({'status': 201, 'result': serializer.data})
	# 	return Response({'status': 400, 'error': serializer.errors})

	# def put(self, request, pk=None):
	# 	# Update an existing AuthUser
	# 	try:
	# 		user = AuthUser.objects.get(pk=pk)
	# 	except AuthUser.DoesNotExist:
	# 		return Response({'status': 404, 'error': 'AuthUser not found'})

	# 	serializer = AuthUserSerializer(user, data=request.data)
	# 	if serializer.is_valid():
	# 		serializer.save()
	# 		return Response({'status': 200, 'result': serializer.data})
	# 	return Response({'status': 400, 'error': serializer.errors})

	# def delete(self, request, pk=None):
	# 	# Delete an AuthUser
	# 	try:
	# 		user = AuthUser.objects.get(pk=pk)
	# 		user.delete()
	# 		return Response({'status': 204, 'message': 'AuthUser deleted'})
	# 	except AuthUser.DoesNotExist:
	# 		return Response({'status': 404, 'error': 'AuthUser not found'})

class MeApiView(APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]
    renderer_classes = [JSONRenderer]

    def get(self, request):
        """
        認証ユーザーの情報を取得するAPIエンドポイント
        """
        user = request.user
        
        # シリアライザーを使用してユーザー情報を取得
        serializer = MeAuthUserSerializer(user)
        
        return Response({
            'status': 'success',
            'data': serializer.data
        }, status=status.HTTP_200_OK)

    def patch(self, request):
        """
        認証ユーザーの情報を更新するAPIエンドポイント
        """
        user = request.user
        
        # 部分更新のためpartial=Trueを指定
        serializer = MeAuthUserSerializer(user, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            return Response({
                'status': 'success',
                'data': serializer.data,
                'message': 'User information updated successfully'
            }, status=status.HTTP_200_OK)
        
        return Response({
            'status': 'error',
            'errors': serializer.errors,
            'message': 'Failed to update user information'
        }, status=status.HTTP_400_BAD_REQUEST)