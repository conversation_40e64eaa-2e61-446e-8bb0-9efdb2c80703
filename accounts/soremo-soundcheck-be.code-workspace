{"folders": [{"path": ".."}], "settings": {"python.defaultInterpreter": "/usr/bin/python3", "python.pythonPath": "/usr/bin/python3", "python.analysis.extraPaths": ["."], "python.analysis.autoSearchPaths": true, "python.analysis.autoImportCompletions": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.analysis.typeCheckingMode": "basic", "python.envFile": "${workspaceFolder}/.env", "python.terminal.activateEnvironment": true, "terminal.integrated.env.linux": {"DJANGO_SETTINGS_MODULE": "voice.settings", "PYTHONPATH": "${workspaceFolder}"}}}