# -*- coding: utf-8 -*-
from decimal import Decimal
from django.utils.translation import gettext as _

from django.db.models import Sum, F, When, Case, Q
from django.http import JsonResponse
from django.shortcuts import redirect
from django.views.generic import TemplateView
from django.template.loader import render_to_string

from accounts.models import AuthUser
from app.models import OfferCreator, Product
from app.services import get_sub_offers
from payments.models import PaymentRequest
from payments.services import get_payment_products, list_offer_request, create_payment_request_service, get_payment_requests, \
    master_admin_get_payment_products, master_admin_change_payment_status, master_admin_change_status_type, count_offer_by_payment_status
from django.urls import reverse

DEFAULT_PAGE = 1


class PaymentArtist(TemplateView):
    template_name = 'payment_artist.html'

    def get(self, *args, **kwargs):
        if self.request.user.role not in [AuthUser.CREATOR, AuthUser.CURATOR]:
            return redirect(reverse("app:top_page") + "?force=true")
        if self.request.user.role == AuthUser.CURATOR:
            user_id = self.request.GET.get('user_id', None)
            if not user_id:
                return redirect('app:index')
            if user_id:
                target_user = AuthUser.objects.filter(pk=user_id, role=AuthUser.CREATOR).first()
                if not target_user:
                    return redirect('app:index')
        return super().get(*args, **kwargs)

    def get_context_data(self, **kwargs):
        # load payment info projects
        user = self.request.user
        if self.request.user.role == AuthUser.CURATOR:
            user_id = self.request.GET.get('user_id', None)
            if user_id:
                target_user = AuthUser.objects.filter(pk=user_id)
                if target_user.exists():
                    user = target_user.first()

        projects = get_payment_products(user)
        context = {'projects': projects, 'current_user': user}

        # load balance info & data create payment request
        offer_creator_ids = list_offer_request(user)
        context.update(offer_creator_ids=offer_creator_ids)

        # load payment request
        payment_request_paginator = get_payment_requests(user)
        payment_requests = payment_request_paginator.page(DEFAULT_PAGE) if payment_request_paginator.num_pages >= DEFAULT_PAGE else []
        context.update(payment_requests=payment_requests, payment_request_total_page=payment_request_paginator.num_pages, current_user_role=self.request.user.role, targeted_user_id=user.pk)
        return context


def payment_info_projects(request):
    if request.user.role not in [AuthUser.CREATOR, AuthUser.CURATOR]:
        return redirect(reverse("app:top_page") + "?force=true")
    user = request.user
    if request.user.role == AuthUser.CURATOR:
       user_id = request.GET.get('user_id', None)
       if user_id:
           target_user = AuthUser.objects.filter(pk=user_id)
           if target_user.exists():
               user = target_user.first()

    with_paid = request.GET.get('with_paid') == '1'
    offset = request.GET.get('offset')
    offset = int(offset) if offset.isdigit() else 0
    projects = get_payment_products(user, with_paid, offset)
    html = render_to_string('_payment_info_project_item.html', {'projects': projects, 'current_user': user})

    return JsonResponse({'html': html}, status=200)


def create_payment_request(request):
    if request.method != 'POST':
        return redirect('payments:payment_current_artist')

    offer_creator_ids = request.POST.get('offer_creator_ids')
    if not offer_creator_ids:
        JsonResponse({'error': 'offer_creator_ids field is required.'}, status=400)

    if not request.user.role == AuthUser.CREATOR:
        JsonResponse({'error': 'Permission Denied'}, status=400)

    return create_payment_request_service(offer_creator_ids, request.user)


def load_payment_requests(request):
    if request.user.role not in [AuthUser.CREATOR, AuthUser.CURATOR]:
        return redirect(reverse("app:top_page") + "?force=true")
    user = request.user
    if request.user.role == AuthUser.CURATOR:
       user_id = request.GET.get('user_id', None)
       if user_id:
           target_user = AuthUser.objects.filter(pk=user_id)
           if target_user.exists():
               user = target_user.first()

    page = request.GET.get('page')
    page = int(page) if page.isdigit() else DEFAULT_PAGE
    paginator = get_payment_requests(user)
    payment_requests = paginator.page(page) if paginator.num_pages >= page else []
    html = render_to_string('_payment_request_item.html', {'payment_requests': payment_requests})
    return JsonResponse({'html': html, 'payment_request_total_page': paginator.num_pages}, status=200)


class PaymentAdmin(TemplateView):
    template_name = 'payment_admin.html'

    def get(self, *args, **kwargs):
        if self.request.user.role != AuthUser.MASTERADMIN:
            return redirect(reverse("app:top_page") + "?force=true")
        return super().get(*args, **kwargs)

    def get_context_data(self, **kwargs):
        # load payment request
        payment_request_paginator = get_payment_requests()
        payment_requests = payment_request_paginator.page(DEFAULT_PAGE) if payment_request_paginator.num_pages >= DEFAULT_PAGE else []
        context = {'payment_requests': payment_requests, 'payment_request_total_page': payment_request_paginator.num_pages}

        # load projects with offer
        projects = master_admin_get_payment_products(self.request.user)
        context.update(projects=projects, current_user=self.request.user)
        return context


def master_admin_load_payment_requests(request):
    if request.user.role != AuthUser.MASTERADMIN:
        return redirect(reverse("app:top_page") + "?force=true")

    page = request.GET.get('page')
    page = int(page) if page.isdigit() else DEFAULT_PAGE
    paginator = get_payment_requests()
    payment_requests = paginator.page(page) if paginator.num_pages >= page else []
    html = render_to_string('_master_admin_payment_request_item.html', {'payment_requests': payment_requests})
    return JsonResponse({'html': html, 'payment_request_total_page': paginator.num_pages}, status=200)


def master_admin_payment_info_projects(request):
    if request.user.role != AuthUser.MASTERADMIN:
        return redirect(reverse("app:top_page") + "?force=true")

    with_paid = request.GET.get('with_paid') == '1'
    offset = request.GET.get('offset')
    offset = int(offset) if offset.isdigit() else 0
    projects = master_admin_get_payment_products(request.user, with_paid, offset)
    html = render_to_string('_master_admin_payment_info_project_item.html', {'projects': projects, 'current_user': request.user})
    return JsonResponse({'html': html, 'current_project_count': projects.count()}, status=200)


def update_status_payment_request(request):
    if request.user.role != AuthUser.MASTERADMIN:
        return JsonResponse({'error': 'Forbidden'}, status=403)

    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allow'}, status=405)

    payment_request_id = request.POST.get('payment_request_id')
    payment_request = PaymentRequest.objects.filter(pk=payment_request_id).first()
    if not payment_request:
        return JsonResponse({'error': 'NotFound'}, status=404)

    master_admin_change_status_type(payment_request)
    return JsonResponse({'msg': 'Success'}, status=200)


def update_payment_status(request):
    if request.user.role != AuthUser.MASTERADMIN:
        return JsonResponse({'error': 'Forbidden'}, status=403)

    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allow'}, status=405)

    offer_creator_id = request.POST.get('offer_creator_id')
    project_id = request.POST.get('project_id')
    target_payment_status = bool(int(request.POST.get('target_payment_status')))

    offer_creator = OfferCreator.original_objects.filter(pk=offer_creator_id, project__is_active=True).first()
    id_project_update_html = request.POST.get('id_project')
    data_payment = request.POST.get('data_payment')
    with_paid = bool(int(request.POST.get('with_paid')))
    project_update = Product.objects.filter(pk=id_project_update_html).first()
    project = Product.objects.filter(pk=project_id).first()
    if not offer_creator and not project or offer_creator and \
            offer_creator.status != OfferCreator.STATUS_COMPLETED or project and \
            OfferCreator.original_objects.filter(project=project, status__in=OfferCreator.STATUS_IN_PROGRESS).exists():
        return JsonResponse({'error': 'NotFound or missing offer_creator_id and project_id'}, status=404)

    if offer_creator:
        current_payment_status = offer_creator.payment_status
        if current_payment_status == target_payment_status or offer_creator.status != OfferCreator.STATUS_COMPLETED:
            return JsonResponse({'error': 'NotFound or missing offer_creator_id and project_id'}, status=404)
        if offer_creator.creator_payment_request or offer_creator.admin_payment_request:
            return JsonResponse(
                {'error': _('The task has already applied for a transfer and cannot be updated at this time.')},
                status=404)

        if OfferCreator.original_objects.filter(
                Q(status=OfferCreator.STATUS_COMPLETED) & Q(admin=offer_creator.creator) & Q(
                    project=offer_creator.project) & (
                        Q(creator_payment_request__isnull=False) | Q(
                    admin_payment_request__isnull=False) | Q(payment_status=True) | Q(
                    legaxy_payment=True))).exclude(
            creator__role=AuthUser.MASTERADMIN).count() != OfferCreator.original_objects.filter(
            Q(admin=offer_creator.creator) & Q(project=offer_creator.project) & Q(
                status__in=OfferCreator.STATUS_OFFER_ACTIVE) & ~Q(
                creator__role=AuthUser.MASTERADMIN)).count():
            return JsonResponse({'error': 'NotFound or missing offer_creator_id and project_id'}, status=404)

    elif project:
        if OfferCreator.original_objects.filter(project=project,
                                                status__in=OfferCreator.STATUS_IN_PROGRESS).exists():
            return JsonResponse({'error': 'NotFound or missing offer_creator_id and project_id'}, status=404)
        if OfferCreator.original_objects.filter(Q(project=project) & (
                                                Q(admin_payment_request__isnull=False) |
                                                Q(creator_payment_request__isnull=False))).exists():
            return JsonResponse(
                {'error': _('The task has already applied for a transfer and cannot be updated at this time.')},
                status=404)

    master_admin_change_payment_status(target_payment_status, offer_creator, project)
    project_to_update = master_admin_get_payment_products(request.user, with_paid, 0, project_update)
    html = render_to_string('_temp_master_admin_payment_info_project_item.html',
                            {'project': project_to_update.first(), 'current_user': request.user,
                             'data_loop': data_payment})
    return JsonResponse({'msg': 'Success', 'html': html}, status=200)


def check_can_change_payment_status(project, current_user):
    remain_offers = get_sub_offers(project, current_user)
    budget_offer = remain_offers.aggregate(total_reward=Sum(
        Case(When(admin=current_user, then=-1 * F('reward')),
             default='reward'))).get('total_reward', 0) if remain_offers else 0
    can_check = True
    if budget_offer < 0:
        can_check = False
    return can_check
