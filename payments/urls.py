# -*- coding: utf-8 -*-
from django.contrib.auth.decorators import login_required
from django.urls import re_path

from payments import views

urlpatterns = [
    re_path(r'^artists/info$', login_required(views.PaymentArtist.as_view()), name='payment_current_artist'),
    re_path(r'^artists/project_info', login_required(views.payment_info_projects), name='payment_info_projects'),
    re_path(r'^artists/payment_requests$', login_required(views.create_payment_request), name='create_payment_request'),
    re_path(r'^artists/payment_requests/load_more$', login_required(views.load_payment_requests), name='load_payment_requests'),
    re_path(r'^master_admins/info$', login_required(views.PaymentAdmin.as_view()), name='payment_current_master_admin'),
    re_path(r'^master_admins/payment_requests/load_more$', login_required(views.master_admin_load_payment_requests),
        name='master_admin_load_payment_requests'),
    re_path(r'^master_admins/project_info', login_required(views.master_admin_payment_info_projects), name='master_admin_payment_info_projects'),
    re_path(r'^master_admins/update_status_payment_request', login_required(views.update_status_payment_request),
        name='master_admin_update_status_payment_request'),
    re_path(r'^master_admins/update_payment_status', login_required(views.update_payment_status), name='master_admin_update_payment_status'),
]
