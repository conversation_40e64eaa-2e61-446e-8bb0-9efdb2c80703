# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-27 15:48+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: payments/models.py:17
msgid "requested"
msgstr "登録済"

#: payments/models.py:18
msgid "processing"
msgstr "（処理中）"

#: payments/models.py:19
msgid "paid"
msgstr "（支払済）"

#: payments/models.py:20
msgid "rejected"
msgstr "（不承認）"

#: payments/models.py:21
msgid "canceled"
msgstr "（不承認）"

#: payments/services.py:67
msgid "Balance not enough"
msgstr "残高が足りません。"

#: payments/services.py:84
msgid "Data has not been updated"
msgstr "データは更新されていませんでした。"

#: payments/templates/_balance_info.html:5
msgid "残高"
msgstr "残高"

#: payments/templates/_balance_info.html:5
msgid "（検収済み売上金含む）"
msgstr "（検収済み売上金含む）"

#: payments/templates/_balance_info.html:10
#: payments/templates/_balance_info.html:21
msgid "Yen tax included"
msgstr "円（税込)"

#: payments/templates/_balance_info.html:16
msgid "振込申請"
msgstr "振込申請"

#: payments/templates/_balance_info.html:20
msgid "進行中"
msgstr "進行中"

#: payments/templates/_balance_info.html:20
msgid "（検収待ちの売掛金と買掛金）"
msgstr "（検収待ちの売掛金と買掛金）"

#: payments/templates/_master_admin_payment_info_projects.html:5
#: payments/templates/_payment_info_projects.html:5
msgid "取引履歴"
msgstr "取引履歴"

#: payments/templates/_master_admin_payment_info_projects.html:12
#: payments/templates/_payment_info_projects.html:12
msgid "支払い済みも表示"
msgstr "支払い済みも表示"

#: payments/templates/_master_admin_payment_request_item.html:14
#: payments/templates/_payment_info_project_item.html:49
#: payments/templates/_payment_info_project_item.html:71
#: payments/templates/_payment_info_project_item.html:100
#: payments/templates/_payment_request_item.html:14
#: payments/templates/_temp_master_admin_payment_info_project_item.html:107
#: payments/templates/_temp_master_admin_payment_info_project_item.html:167
#: payments/templates/_temp_master_admin_payment_info_project_item.html:224
#: payments/templates/_temp_master_admin_payment_info_project_item.html:281
msgid "円"
msgstr "円"

#: payments/templates/_master_admin_payment_request_item.html:21
msgid "支払い完了"
msgstr "支払い完了"

#: payments/templates/_master_admin_payment_request_item.html:21
msgid "取り消す"
msgstr "取り消す"

#: payments/templates/_master_admin_payment_request_item.html:23
#: payments/templates/_payment_request_item.html:18
msgid "PDFを見る"
msgstr "PDFを見る"

#: payments/templates/_master_admin_payment_requests.html:9
#: payments/templates/_payment_requests.html:8
msgid "申請日"
msgstr "申請日"

#: payments/templates/_master_admin_payment_requests.html:10
#: payments/templates/_payment_requests.html:9
msgid "支払日"
msgstr "支払日"

#: payments/templates/_master_admin_payment_requests.html:11
#: payments/templates/_payment_requests.html:10
#, fuzzy
#| msgid "金額（税込）"
msgid "金額（税込)"
msgstr "金額（税込）"

#: payments/templates/_master_admin_payment_requests.html:12
#: payments/templates/_payment_info_project_item.html:13
msgid "相手先"
msgstr "相手先"

#: payments/templates/_modal_payments.html:8
msgid "振込申請を受け付けました。"
msgstr "振込申請を受け付けました。"

#: payments/templates/_modal_payments.html:9
msgid "本日付の支払明細書のPDFもダウンロードできます。"
msgstr "本日付の支払明細書のPDFもダウンロードできます。"

#: payments/templates/_modal_payments.html:12
msgid "OK"
msgstr "OK"

#: payments/templates/_modal_payments.html:24
msgid "お振込に必要な入金口座が登録されていないようです。"
msgstr "お振込に必要な入金口座が登録されていないようです。"

#: payments/templates/_modal_payments.html:25
msgid "アカウント情報から登録しましょう。"
msgstr "アカウント情報から登録しましょう。"

#: payments/templates/_modal_payments.html:28
msgid "キャンセル"
msgstr "キャンセル"

#: payments/templates/_modal_payments.html:29
msgid "アカウント情報を見る"
msgstr "アカウント情報を見る"

#: payments/templates/_payment_info_project_item.html:14
msgid "金額（税込）"
msgstr "金額（税込）"

#: payments/templates/_payment_info_project_item.html:15
msgid "取引成立日"
msgstr "取引成立日"

#: payments/templates/_payment_info_project_item.html:16
msgid "検収日"
msgstr "検収日"

#: payments/templates/_payment_info_project_item.html:17
msgid "シーン"
msgstr "シーン"

#: payments/templates/_payment_info_project_item.html:18
msgid "ロール"
msgstr "ロール"

#: payments/templates/payment_admin.html:38
#: payments/templates/payment_artist.html:31
msgid "支払明細書"
msgstr "支払明細書"

#: payments/templates/payment_artist.html:21
msgid "ウォレット"
msgstr "ウォレット"

#: payments/templates/payment_artist.html:22
msgid "残高を入金口座へ振込申請できます。"
msgstr "残高を入金口座へ振込申請できます。"

#: payments/templates/payment_artist.html:23
msgid "取引履歴も確認できます。"
msgstr "取引履歴も確認できます。"

#: payments/views.py:203 payments/views.py:226
msgid ""
"The task has already applied for a transfer and cannot be updated at this "
"time."
msgstr "タスクがすでに振込申請していますので、現時点に更新できません。"

#~ msgid "No Projects"
#~ msgstr "データーなし"

#~ msgid "振込金額"
#~ msgstr "振込金額"

#~ msgid "No Payment Requests"
#~ msgstr "リクエストなし"

#~ msgid "増やす"
#~ msgstr "増やす"

#~ msgid "Something went wrong!"
#~ msgstr "エラーが発生しました"
