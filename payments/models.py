import uuid

from django.db import models
from django.utils.translation import gettext as _

from app.models import ModelBase


class PaymentRequest(ModelBase):
    REQUESTED_STATUS = 1
    PROCESSING_STATUS = 2
    PAID_STATUS = 3
    REJECTED_STATUS = 4
    CANCELED_STATUS = 5

    STATUS_TYPES = (
        (REQUESTED_STATUS, _('requested')),
        (PROCESSING_STATUS, _('processing')),
        (PAID_STATUS, _('paid')),
        (REJECTED_STATUS, _('rejected')),
        (CANCELED_STATUS, _('canceled')),
    )

    settlement_date = models.DateTimeField(null=True, blank=True, default=None)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    status_type = models.IntegerField(choices=STATUS_TYPES)
    creator = models.ForeignKey('accounts.AuthUser', related_name='payment_requests', null=True, on_delete=models.SET_NULL)
    payment_content = models.FileField(upload_to='file', blank=True, null=True)
    payment_code = models.CharField(max_length=8, null=True, unique=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self.payment_code:
            self.payment_code = uuid.uuid4().hex.upper()[:8]
            while PaymentRequest.objects.filter(payment_code=self.payment_code).exists():
                self.payment_code = uuid.uuid4().hex.upper()[:8]
