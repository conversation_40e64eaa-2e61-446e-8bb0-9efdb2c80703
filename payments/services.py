# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 25/11/2021
import io
import os

import datetime
import urllib
import requests
from django.conf import settings
from django.db import transaction
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.template.loader import get_template
from django.utils.translation import gettext as _
from django.template.loader import render_to_string
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Prefetch, Sum, Case, When, F, Q, Subquery

from accounts.models import AuthUser, ProductUser
from app.services import get_sub_offers
from voice.logger import logger
from accounts.models import AuthUser
from app.models import FormContractAndPlan, OfferCreator, OfferProduct, Product
from payments.models import PaymentRequest
from voice.utils import get_image_file_as_base64_data, pdfkit_generate_pdf
from app.templatetags.util import format_date_jp, format_short_date, display_currency

PER_PAGE = 3
PAYMENT_REQUEST_PER_PAGE = 5

def get_payment_products(current_user, with_paid=False, offset=0):
    offer_creators = OfferCreator.original_objects.filter(
        (Q(status__in=OfferCreator.STATUS_OFFER_ACTIVE) & Q(admin=current_user)) | (
                Q(status__in=['2', '3', '4']) & Q(creator=current_user)))
    if not with_paid:
        offer_creators = offer_creators.exclude(
            (Q(payment_status=True) & Q(creator_payment_request_id__isnull=True) & Q(
                admin_payment_request_id__isnull=True)) | (Q(payment_status=True) & Q(creator=current_user)) | \
            (Q(admin=current_user) & Q(admin_payment_request_id__isnull=False)) | \
            (Q(creator=current_user) & Q(creator_payment_request_id__isnull=False)))
    offer_creators = Subquery(offer_creators.values_list('id', flat=True))

    products = query_project_by_offer_creators(offer_creators, current_user, 'offer').distinct()
    products = total_user_reward_in_project(products, current_user)
    return count_offer_by_payment_status(products[offset:offset + PER_PAGE])


def list_offer_request(user):
    offer_creator_ids = OfferCreator.original_objects.filter(
        (Q(admin_id=user.id) & Q(admin_payment_request_id__isnull=True)) |
        (Q(creator_id=user.id) & Q(creator_payment_request_id__isnull=True) & Q(payment_status=False)),
        Q(status=OfferCreator.STATUS_COMPLETED),
        Q(legaxy_payment=False), Q(project__is_active=True)).exclude(
        (Q(payment_status=True) & Q(creator_payment_request_id__isnull=True) & Q(
            admin_payment_request_id__isnull=True)) | (Q(payment_status=True) & Q(creator=user))).values_list('id',
                                                                                                              flat=True)

    return ','.join([str(offer_id) for offer_id in offer_creator_ids])


def create_payment_request_service(offer_creator_ids, user):
    with transaction.atomic():
        AuthUser.objects.select_for_update().filter(pk=user.id)
        user = AuthUser.objects.select_for_update().filter(pk=user.id).first()
        if user.balance_available <= 0 or user.balance_expected < 0:
            return JsonResponse({'error': _('Balance not enough')}, status=400)

        if not(user.bank and user.bank_branch and user.bank_branch_number and user.account_type and user.account_number and user.account_name):
            return JsonResponse({'error': 'Lack of Bank info.'}, status=400)

        offer_creator_ids = [int(offer_id) for offer_id in offer_creator_ids.split(',')]
        offer_creators = OfferCreator.original_objects.filter(Q(id__in=offer_creator_ids), Q(project__is_active=True),
                                                              ((Q(admin_id=user.id) & Q(
                                                                  admin_payment_request_id__isnull=True)) |
                                                               (Q(creator_id=user.id) & Q(
                                                                   creator_payment_request_id__isnull=True) & Q(
                                                                   payment_status=False))),
                                                              Q(status=OfferCreator.STATUS_COMPLETED),
                                                              Q(legaxy_payment=False)).exclude(
            (Q(payment_status=True) & Q(creator_payment_request_id__isnull=True) & Q(
                admin_payment_request_id__isnull=True)) | (Q(payment_status=True) & Q(creator=user)))
        if len(offer_creators) != len(offer_creator_ids):
            return JsonResponse({'error': _('Data has not been updated')}, status=404)

        logger.info(f'User {user.id} creating request payments, amount: {user.balance_available}')
        payment_request = PaymentRequest(amount=user.balance_available, status_type=PaymentRequest.PROCESSING_STATUS,
                                         creator=user,
                                         created=datetime.datetime.now())
        products = query_project_by_offer_creators(offer_creator_ids, user, 'offer').distinct()
        file_name, file_name_unique = generate_payment_content_pdf('pdf/payment_pdf.html', payment_request, products)

        payment_request.save()

        for offer_creator in offer_creators:
            reward = 1 * offer_creator.reward
            if offer_creator.creator == user and not offer_creator.creator_payment_request:
                offer_creator.creator_payment_request = payment_request
                product_creator = ProductUser.objects.filter(product=offer_creator.project,
                                                             user=offer_creator.creator).first()
                if product_creator:
                    ProductUser.update_available_budget(product_creator, -1 * reward)
            elif offer_creator.admin == user and not offer_creator.admin_payment_request:
                offer_creator.admin_payment_request = payment_request
            offer_creator.save()

        AuthUser.change_balance(user.pk, [('balance_available', -1 * user.balance_available)])

    with open(f'/tmp/{file_name_unique}', 'rb') as f:
        output = io.BytesIO(f.read())
        payment_request.payment_content = InMemoryUploadedFile(output, 'FileField', file_name, 'application/pdf', output.getbuffer().nbytes, None)
        payment_request.save()
    os.remove(f'/tmp/{file_name_unique}')
    logger.info(f'User {user.id} created request payments, amount: {user.balance_available}')

    user.refresh_from_db()
    balance_info_html = render_to_string('_balance_info.html', {'current_user': user})
    return JsonResponse({'msg': 'Create success.', 'balance_info_html': balance_info_html}, status=200)


def get_payment_requests(user=None):
    if not user:
        return Paginator(PaymentRequest.objects.order_by('-id'), PAYMENT_REQUEST_PER_PAGE)
    return Paginator(PaymentRequest.objects.filter(creator=user).order_by('-id'), PAYMENT_REQUEST_PER_PAGE)


def generate_payment_content_pdf(template_src, payment_request, products):
    current_user = payment_request.creator
    payment_request.payment_date = payment_request.created + datetime.timedelta(days=7)
    context_dict = {
        'payment_request': payment_request,
        'payment_info': products,
        'current_user': payment_request.creator,
        # 'image_base64': get_image_file_as_base64_data(os.path.join(settings.BASE_DIR, 'app/static/images/logo_export.png')),
        # 'axis_l_path': os.path.join(settings.BASE_DIR, 'app/static/fonts/AxisL.otf'),
    }
   
    file_name = f'支払明細書_{payment_request.created.strftime("%Y%m%d")}.pdf'
    file_name_unique = f'支払明細書_{payment_request.created.strftime("%Y%m%d")}_{payment_request.payment_code}.pdf'

    api_url = settings.VIVLIOSTYLE_SERVER + "/generate-pdf"

    payload = {
        "config": f"module.exports = {{title: 'Payment', entry: ['index.html']}};",
        "html_input": {
            "template_info": {
                "name": "payment"
            },
            "data_info": {
                "payment": {
                    "current_user": {
                        "id": current_user.id,
                        "fullname": current_user.fullname
                    },
                    "payment_request": {
                        "payment_date": format_date_jp(context_dict["payment_request"].payment_date),
                        "created": format_short_date(context_dict["payment_request"].created),
                        "amount": display_currency(context_dict["payment_request"].amount)
                    },
                    "bank_details": {
                        "bank": current_user.bank,
                        "bank_branch": current_user.bank_branch,
                        "bank_branch_number": current_user.bank_branch_number,
                        "account_type": current_user.get_account_type_display(),
                        "account_number": current_user.account_number,
                        "account_name": current_user.account_name
                    },
                    "payment_info": [
                        {
                            "project_name": project.get_name_by_code_name(),
                            "product_offers": [
                                {
                                    "admin": {
                                        "id": offer.admin.id,
                                        "display_name": (offer.admin.get_display_name() or "")
                                    },
                                    "creator": {
                                        "display_name": (offer.creator.get_display_name() or "")
                                    },
                                    "accept_time": format_short_date(offer.accept_time),
                                    "check_time": format_short_date(offer.check_time),
                                    "custom_contract_dsp": offer.custom_contract_dsp(),
                                    "scenes": offer.scenes,
                                    "reward": display_currency(offer.reward)
                                } for offer in project.product_offers.all()
                            ]
                        } for project in context_dict['payment_info']
                    ]
                }
            }
        }
    }

    # Make the POST request to the Vivliostyle API
    response = requests.post(api_url, json=payload)
    if response.status_code == 200:
        with open(f'/tmp/{file_name_unique}', 'wb') as file:
            file.write(response.content)

    return file_name, file_name_unique


def query_project_by_offer_creators(offer_creator_ids, current_user, offer_sort='-check_time', project=None):
    if offer_sort != '-check_time':
        offer_owner = list(
            OfferCreator.original_objects.filter(pk__in=offer_creator_ids, type='2', creator=current_user).values_list(
                'pk', flat=True))
        offer_soremo = list(
            OfferCreator.original_objects.filter(pk__in=offer_creator_ids, type='2', admin=current_user).values_list(
                'pk', flat=True))
        offer_creators = OfferCreator.objects.filter(pk__in=offer_creator_ids)
        offer_creator = list(
            offer_creators.filter(creator=current_user).order_by('-check_time').values_list('pk', flat=True))
        offer_admin = list(
            offer_creators.filter(admin=current_user).order_by('-check_time').values_list('pk', flat=True))
        offer_ids = offer_owner + offer_soremo + offer_creator + offer_admin
        offer_sort = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(offer_ids)])
    if current_user.role == AuthUser.MASTERADMIN:
        if project:
            products = Product.objects.filter(pk=project.pk).prefetch_related(
                Prefetch('product_offers',
                         queryset=OfferCreator.original_objects.prefetch_related('creator', 'admin').filter(
                             id__in=offer_creator_ids).order_by('-type', offer_sort))).prefetch_related(
                Prefetch('offer_product',
                         queryset=OfferProduct.objects.all().prefetch_related(Prefetch('form_contract_and_plans',
                                                                                       queryset=FormContractAndPlan.objects.all().order_by(
                                                                                           '-created', '-id')))))
        else:
            products = Product.objects.prefetch_related(
                Prefetch('product_offers',
                         queryset=OfferCreator.original_objects.prefetch_related('creator', 'admin').filter(
                             id__in=offer_creator_ids).order_by('-type', offer_sort))).prefetch_related(
                Prefetch('offer_product',
                         queryset=OfferProduct.objects.all().prefetch_related(Prefetch('form_contract_and_plans',
                                                                                       queryset=FormContractAndPlan.objects.all().order_by(
                                                                                           '-created', '-id')))))
        return products.all()
    else:
        products = current_user.products
        products = products.prefetch_related(
            Prefetch('product_offers',
                     queryset=OfferCreator.original_objects.prefetch_related('creator', 'admin').filter(
                         id__in=offer_creator_ids).order_by(offer_sort)))

    products = products.filter(product_offers__isnull=False, product_offers__id__in=offer_creator_ids)

    return products


def master_admin_get_payment_products(current_user, with_paid=False, offset=0, project=None):
    offer_creators = OfferCreator.original_objects.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE)
    if not with_paid:
        offer_creators = offer_creators.exclude(payment_status=True)
    offer_creators = Subquery(offer_creators.values_list('id', flat=True))

    products = query_project_by_offer_creators(offer_creators, current_user, '-checked_time', project).distinct()
    return count_offer_by_payment_status(products[offset:offset + PER_PAGE])


def master_admin_change_payment_status(target_payment_status, offer_creator, project):
    if offer_creator:
        offer_creator.payment_status = target_payment_status
        offer_creator.save()
        update_balance_for_user(offer_creator, target_payment_status)
    elif project:
        offer_creators = OfferCreator.original_objects.filter(project=project, status__in=OfferCreator.STATUS_OFFER_ACTIVE).all()
        OfferCreator.original_objects.filter(project=project, status__in=OfferCreator.STATUS_OFFER_ACTIVE).update(
            payment_status=target_payment_status)
        for offer_creator in offer_creators:
            update_balance_for_user(offer_creator, target_payment_status)


def update_balance_for_user(offer_creator, target_payment_status):
    product_creator = ProductUser.objects.filter(product=offer_creator.project,
                                                 user=offer_creator.creator).first()
    product_admin = ProductUser.objects.filter(product=offer_creator.project,
                                                 user=offer_creator.admin).first()
    # da thanh toan -> chua thanh toan
    if not target_payment_status:
        if not offer_creator.creator_payment_request and offer_creator.status == OfferCreator.STATUS_COMPLETED:
            AuthUser.change_balance(offer_creator.creator.pk, [('balance_available', offer_creator.reward)])
            if product_creator:
                ProductUser.update_available_budget(product_creator, offer_creator.reward)
        if not offer_creator.admin_payment_request and offer_creator.status == OfferCreator.STATUS_COMPLETED:
            AuthUser.change_balance(offer_creator.admin.pk, [('balance_available', -1*offer_creator.reward)])
            if product_admin:
                ProductUser.update_available_budget(product_admin, -1*offer_creator.reward)

    # chua thanh toan -> da thanh toan
    else:
        if not offer_creator.creator_payment_request and offer_creator.status == OfferCreator.STATUS_COMPLETED:
            AuthUser.change_balance(offer_creator.creator.pk, [('balance_available', -1 * offer_creator.reward)])
            if product_creator:
                ProductUser.update_available_budget(product_creator, -1 * offer_creator.reward)
        if not offer_creator.admin_payment_request and offer_creator.status == OfferCreator.STATUS_COMPLETED:
            AuthUser.change_balance(offer_creator.admin.pk, [('balance_available', offer_creator.reward)])
            if product_admin:
                ProductUser.update_available_budget(product_admin, offer_creator.reward)


def master_admin_change_status_type(payment_request):
    with transaction.atomic():
        if payment_request.status_type == PaymentRequest.PAID_STATUS and payment_request.settlement_date:
            payment_request.status_type = PaymentRequest.PROCESSING_STATUS
            payment_request.settlement_date = None
            OfferCreator.original_objects.filter(creator_payment_request=payment_request,
                                                 status__in=OfferCreator.STATUS_OFFER_ACTIVE).update(
                payment_status=False)
        else:
            payment_request.status_type = PaymentRequest.PAID_STATUS
            payment_request.settlement_date = datetime.datetime.now()
            OfferCreator.original_objects.filter(creator_payment_request=payment_request,
                                                 status__in=OfferCreator.STATUS_OFFER_ACTIVE).update(
                payment_status=True)
        payment_request.save()


def count_offer_by_payment_status(projects):
    for project in projects:
        offers = OfferCreator.original_objects.filter(project=project)
        count_task_progress = offers.filter(status__in=OfferCreator.STATUS_IN_PROGRESS).count()
        project.counter_done = offers.filter(payment_status=True).count()
        project.counter_not_done = offers.filter(payment_status=False).count()
        project.counter_task_progress = count_task_progress
    return projects

def total_user_reward_in_project(projects, user):
    return projects.annotate(total_reward=Sum(Case(When(Q(product_offers__admin_id=user.id) &
                ~Q(product_offers__creator_id=user.id), then=-1 * F('product_offers__reward')),
                                                       default='product_offers__reward'))).order_by('-modified')

def get_total_budget_and_used_budget(project, user, with_paid=False):
    total_budget = 0
    used_budget = 0
    if user.role == AuthUser.CREATOR:
        offer_creators = OfferCreator.original_objects.filter(
            Q(project=project) & ((Q(status__in=OfferCreator.STATUS_OFFER_ACTIVE) & Q(admin=user)) | (
                    Q(status__in=['2', '3', '4']) & Q(creator=user))))
        if not with_paid:
            offer_creators = offer_creators.exclude(
                (Q(payment_status=True) & Q(creator_payment_request_id__isnull=True) & Q(
                    admin_payment_request_id__isnull=True)) | (Q(payment_status=True) & Q(creator=user)) | \
                (Q(admin=user) & Q(admin_payment_request_id__isnull=False)) | \
                (Q(creator=user) & Q(creator_payment_request_id__isnull=False)))


        total_budget = offer_creators.filter(creator=user).aggregate(total_sum=Sum('reward'))['total_sum'] or 0
        used_budget = offer_creators.filter(Q(admin=user) & ~Q(creator=user)).order_by('-reward').aggregate(total_sum=Sum('reward'))['total_sum'] or 0

    elif user.role == AuthUser.MASTERADMIN:
        total_budget = project.total_budget
        used_budget = project.product_offers.filter(Q(admin__role=AuthUser.MASTERADMIN)).aggregate(total_sum=Sum('reward'))['total_sum'] or 0

    else:
        total_budget = project.total_budget
        used_budget = project.total_budget
    return total_budget, used_budget