/* Payment - Artist*/
:root {
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
}

.payment {
    margin-top: 65px;
    background-color: var(--white-color);
}

.payment__heading h3 {
    margin: 32px 0 4px;
}

.payment__heading p {
    margin: 0;
    word-break: break-word;
    white-space: pre-line;
}

.payment__content-wrap {
    margin: 32px 0;
    padding: 24px 12px;
    border: 1px solid var(--soremo-border);
    border-radius: 8px;
    overflow-y: auto;
    max-height: 600px;
}

#payment-requests-content {
    max-height: 300px;
}

.payment__content-heading .heading--18 {
    margin-bottom: 8px;
}

.payment__content-body {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    margin: 4px 0 32px;
}

.payment__content-body-left {
    display: flex;
    align-items: baseline;
}

.payment__content-body-left .bodytext--13 {
    margin-left: 8px;
}

.payment__content-body-right .btn {
    padding: 12px 43px !important;
}

.payment__content-footer .bodytext--13 {
    margin-left: 4px;
}

.payment__content-list {

}

.payment__content-item {
    padding: 16px 0;
}

.payment__content-item .heading--16 {
    padding: 16px 0;
}

.payment-switch {
    margin-bottom: 16px;
}

.table-report td:last-child {
    text-align: right;
}

.table-report td:last-child button {
    padding: 8px 37px !important;
}

.flex-btn {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
}

.table-report td button:nth-child(2) {
    margin-left: 8px;
}

.table thead tr th, .table tr td {
    padding-left: 0 !important;
}

.table thead tr th:last-child, .table tr td:last-child {
    padding-right: 0 !important;
}

.payment .table thead tr th {
    padding: 8px 8px 16px;
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    color: var(--grey1-color);

    border-bottom: 1px solid var(--soremo-border);
}

.payment .table thead tr th:not(:last-child) {
    min-width: 120px;
}

.payment .table tr td {
    padding: 8px;
    padding-bottom: 0;
    border: none;
    vertical-align: middle;
}

.payment .table tr td .bodytext--13 {
    word-break: break-word;
    white-space: pre-line;
}

.payment .table tr td .caption--11 {
    display: inline-block;
    padding: 4px 16px;
    margin: 4px;
    color: var(--black1-color);
    border: 1px solid var(--black2-color);
    border-radius: 4px;
}

.table.table-history thead tr th:nth-child(2) {
    text-align: center;
}

.table.table-history tr td:nth-child(2) {
    text-align: right;
    padding-right: 30px;
}

.payment .table tfoot tr td {
    border-top: 1px solid var(--soremo-border);
}

.table tfoot tr td:last-child {
    text-align: right;
    margin-top: -1px;
}

.payment__content-list .table tr td:last-child .icon,
.payment__content-item-heading .icon {
    /* color: var(--soremo-placeholder); */
    font-size: 16px;
    margin: 0 2px;
    cursor: default;
    line-height: 20px;
}

.payment-scene, .payment-scene_wrap {
    display: flex;
    align-items: center;
}

.payment-scene__user {
    display: flex;
    align-items: center;
}

.payment-scene .icon {
    color: var(--grey1-color);
    font-size: 12px;
    margin: 0 2px;
}

.payment-scene__name {
    margin-left: 9px;
}

.payment-scene__name .bodytext--13 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
}

table {
    width: 100%;
}

@media (max-width: 992px) {
    /* .payment__content-wrap {
        padding: 24px;
    } */

    .payment__content-body-right .btn {
        padding: 12px 36px !important;
    }

    .payment-scene__name .bodytext--13 {
        max-width: 64px;
        white-space: nowrap !important;
    }
}

@media (max-width: 739px) {
    .payment__content-body-left .heading--40 {
        font-size: 24px;
        line-height: 36px;
    }

    .payment__content-body-left {
        width: 100%;
        justify-content: right;
    }

    .payment__content-body-right {
        display: flex;
        justify-content: right;
        margin: 4px;
        width: 100%;
    }

    .table-report td:last-child button {
        padding: 8px 16px !important;
    }
}
/* End Payment - Artist*/

/* Payment - Admin*/
.payment-scene .icon--next {
    margin: 0 8px;
}

.payment__content-list .table tr td:last-child .icon.checked,
.payment__content-item-heading .icon.checked {
    color: var(--blue-color);
    font-size: 16px;
    margin: 0 2px;
    cursor: pointer;
    line-height: 20px;
}

.payment__content-item-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.icon--center {
    float: right;
    min-height: 40px;
    display: flex;
    align-items: center;
}

.payment-admin--all .icon--center .icon--sicon-mark-done:not(.checked) {
    display: none;
}

.payment-admin--all .payment__content-list .table tr td:last-child .icon,
.payment-admin--all .payment__content-item-heading .icon {
    cursor: pointer;
}

@media (max-width: 739px) {
    .payment-scene_wrap {
        min-width: 100px;
    }
}

/* Modal payment */
#modalRequestCreated .popup-dialog, #modalEditBank .popup-dialog {
    width: 408px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    height: 100%;
    border-radius: 12px;
}

#modalRequestCreated .popup-body p, #modalEditBank .popup-body p {
    margin: 0;
    word-break: break-word;
    white-space: pre-line;
    text-align: left !important;
}

#modalRequestCreated .popup-dialog .popup-content, #modalEditBank .popup-dialog .popup-content {
    width: 100%;
}

.modal--pdf .modal-dialog {
    min-width: 360px;
    width: 90%;
    background-color: #fff;
    border-radius: 12px;
    margin: 10px auto;
}

.modal--pdf iframe {
    min-height: 90vh;
    width: 100%;
}

.disable-click {
    pointer-events:none;
}

@media (max-width: 739px) {
    #modalRequestCreated .popup-dialog {
        width: auto;
    }
}

.opacity-0 {
    opacity: 0;
}

#modal-preview-pdf .modal-dialog, #modal-preview-pdf .modal-content, #modal-preview-pdf .document-popup__content {
    max-height: 75vh;
    max-width: 50vw;
    width: 50vw;
}

#modal-preview-pdf {
    background-color: unset !important;
}

#modal-preview-pdf .modal-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#modal-preview-pdf .modal-content {
    height: 100%;
    margin: 0px !important;
    border-radius: 12px;
    position: relative;
}

#modal-preview-pdf .document-popup__content {
    padding: 56px 8px 62px 8px;
}

#modal-preview-pdf iframe {
    max-width: 100%;
    min-height: calc(75vh - 62px - 56px) !important;
    border-radius: 8px;
}

#modal-preview-pdf .smodal-close {
    position: absolute;
    left: 8px;
    top: 24px;
    color: #A7A8A9;
    width: 24px;
    height: 24px;
}

#modal-preview-pdf .smodal-close:hover svg path {
    fill: #009ace;
}

#modal-preview-pdf .modal-content {
    filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.05));
    box-sizing: border-box;
    border: 1px solid #F0F0F0;
}

.smodal[id="modal-preview-pdf"] {
    background-color: transparent !important;
}

.payment-scene__user img {
    max-width: unset;
}

@media (max-width: 992px) {
    #modal-preview-pdf .modal-dialog, #modal-preview-pdf .modal-content, #modal-preview-pdf .document-popup__content {
        max-height: 75vh;
        max-width: calc(100vw - 32px) !important;
        min-width: unset;
        width: 100%;
    }

    #modal-preview-pdf {
        margin: 0;
        top: unset;
    }

    #modal-preview-pdf .smodal-close {
        left: 8px;
    }
}

.payment__content-table tr.offer-item .icon.icon--sicon-pencil,
.payment__content-table tr.offer-item .icon.icon--sicon-trash {
    opacity: 0;
}

.payment__content-table tr.offer-item:hover .icon.icon--sicon-pencil,
.payment__content-table tr.offer-item:hover .icon.icon--sicon-trash {
    opacity: 1;
}

.account_upload-file {
    margin-right: 0;
}

#modal-edit-offer .form-textarea textarea {
    width: 100%;
    word-break: break-word;
    white-space: pre-line;
}

/* End Payment - Admin*/
