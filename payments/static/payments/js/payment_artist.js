var call_ajax = false;

var payment_request_current_page = 1;
var payment_request_call_ajax = false;
var payment_request_total_page = parseInt($('#payment-requests-content #payment_request_total_page').val());

function handleScroll() {
    $('.payment--all').on('scroll', function() {
        if($(this).scrollTop() + $(this).get(0).clientHeight >= $(this)[0].scrollHeight - 100) {
            if (!call_ajax) {
                getPaymentProjects(true);
            }
        }
    })
}

function handlePaymentRequestScroll() {
    $('#payment-requests-content').on('scroll', function() {
        if($(this).scrollTop() + $(this).get(0).clientHeight >= $(this)[0].scrollHeight - 100) {
            if (payment_request_current_page < payment_request_total_page && !payment_request_call_ajax) {
                getPaymentRequests(true);
            }
        }
    })
}


function getPaymentProjects(isLoadMore=false) {
    var with_paid = '0';
    if ($('#filter-payment').is(':checked')) {
        with_paid = '1';
    }

    let offset = isLoadMore ? $('.payment__content-item').length : 0;
    call_ajax = true;
    $.ajax({
        type: 'GET',
        data: {
            'offset': offset,
            'with_paid': with_paid,
            'user_id': targeted_user_id
        },
        url: '/payments/artists/project_info',
        beforeSend: function() {
            $('.payment__content-list .load-more-loading').removeClass('opacity-0');
        },
        success: function (data) {
            if (isLoadMore) {
                $(data.html).insertBefore($('.payment__content-list .load-more-loading').last());
            } else {
                $('.payment__content-list').empty();
                $('.payment__content-list').append(data.html);
                $('.payment__content-list').append(`<div class="load-more-loading opacity-0"></div>`);
                notEnoughDataForScroll();
            }
        },
        error: function () {
            console.log('error')
        },
        complete: function () {
            call_ajax = false;
            $('.payment__content-list .load-more-loading').addClass('opacity-0');
        }
    });
}

function reloadPaymentRequests() {
    $("#payment-requests-content").scrollTop(0);
    payment_request_current_page = 0;
    getPaymentRequests();
}


function getPaymentRequests(isLoadMore=false) {
    payment_request_current_page++;
    payment_request_call_ajax = true;
    let current_page_tostr = payment_request_current_page.toString();
    $.ajax({
        type: 'GET',
        data: {
            'page': payment_request_current_page,
            'user_id': targeted_user_id
        },
        url: '/payments/artists/payment_requests/load_more',
        beforeSend: function() {
            $('#payment-requests-content .table-report .load-more-loading').removeClass('opacity-0');
        },
        success: function (data) {
            payment_request_total_page = data.payment_request_total_page;
            if (isLoadMore) {
                $(data.html).insertBefore($('#payment-requests-content .table-report .load-more-loading').last());
            } else {
                $('#payment-requests-content .table-report tbody').empty();
                $('#payment-requests-content .table-report tbody').append(data.html);
                $('#payment-requests-content .table-report tbody').append(`<tr class="load-more-loading opacity-0"></tr>`);
            }
        },
        error: function () {
            payment_request_current_page--;
        },
        complete: function () {
            payment_request_call_ajax = false;
            $('#payment-requests-content .table-report .load-more-loading').addClass('opacity-0');
        }
    });
}

function switchPayment() {
    $(document).on('change', '#filter-payment', function () {
        getPaymentProjects();
    });
}

function handleCreatePaymentRequest() {
    $(document).on('click', '#balance-info #create-payment-request-btn', function() {
        $(this).addClass('disable');
        let offer_creator_ids = $('#offer_creator_ids').val();
        let csrfmiddlewaretoken = $('input[name="csrfmiddlewaretoken"]').val();

        $.ajax({
            type: 'POST',
            url: '/payments/artists/payment_requests',
            data: {
                'csrfmiddlewaretoken': csrfmiddlewaretoken,
                'offer_creator_ids': offer_creator_ids,
                'user_id': targeted_user_id
            },
            datatype: "json",
            success: function (data) {
                $('#balance-info').empty();
                $('#balance-info').append(data.balance_info_html);
                $('#modalRequestCreated').modal('show');
                reloadPaymentRequests();
            },
            statusCode: {
                400: function (data) {
                    if (data.responseJSON.error === 'Lack of Bank info.') {
                        $("#modalEditBank").modal('show');
                    } else {
                        setTimeout(() => { location.reload(); }, 2000);
                    }
                },
                404: function (data) {
                    location.reload();
                },
                500: function (data) {
                    location.reload();
                }
            },
            error: function (data) {
                if (data.responseJSON.error === 'Lack of Bank info.') {
                    return
                }
                else if (data.responseJSON) {
                    toastr.error(data.responseJSON.error);
                } else {
                    toastr.error(gettext('Something went wrong!'));
                }
            }
        });
    })
}

function handleChoseEditBankInfo() {
    $('.btn-editbank').on('click', function () {
        window.location.href = '/accounts/creator_account/' + $('#current_user_id').val() + '/?next=' + window.location.pathname + '#tab_4'
    });
}


function handleChoseCancelEditBankInfo() {
    $('.btn-editbank-cancel').on('click', function () {
        $("#modalEditBank").modal('hide');
    });
}

function handleConfirmRequestCreated() {
    $(document).on('click', '.btn-request-created', function () {
        $('#modalRequestCreated').modal('hide');
    });
}

function previewPDF() {
    $(document).on('click', '#payment-requests .btn-preview-pdf', function () {
        $('body').addClass('modal-no-overlay');
        let name = $(this).attr('data-name');
        let link = $(this).attr('data-link');
        $('#modal-preview-pdf').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(link) + '#zoom=page-width');
        $('#modal-preview-pdf').find('.btn-preview-pdf').html(name);
        $('#modal-preview-pdf').modal('show');
    });

    $(document).on('hidden.bs.modal', '#modal-preview-pdf', function (e) {
        $('body').removeClass('modal-no-overlay');
    })
}

function notEnoughDataForScroll() {
    if (invisibleScroll()) {
        getPaymentProjects(true);
    }
}

function invisibleScroll(){
    return $('.payment--all').get(0).clientHeight == $('.payment--all')[0].scrollHeight;
}

$(document).ready(function () {
    switchPayment();
    handleScroll();
    handleCreatePaymentRequest();
    handlePaymentRequestScroll();
    handleChoseCancelEditBankInfo();
    handleConfirmRequestCreated();
    handleChoseEditBankInfo();
    previewPDF();
    notEnoughDataForScroll()
});
