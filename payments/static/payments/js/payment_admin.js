var call_ajax = false;

var payment_request_current_page = 1;
var payment_request_call_ajax = false;
var payment_request_total_page = parseInt($('#payment-requests-content #payment_request_total_page').val());;
var payment_project_current_project_count = 1;
var is_editing = false;

function handleScroll() {
    $('.payment--all').on('scroll', function() {
        if($(this).scrollTop() + $(this).get(0).clientHeight >= $(this)[0].scrollHeight - 100) {
            if (!call_ajax) {
                getPaymentProjects(true);
            }
        }
    })
}

function handlePaymentRequestScroll() {
    $('#payment-requests-content').on('scroll', function() {
        if($(this).scrollTop() + $(this).get(0).clientHeight >= $(this)[0].scrollHeight - 100) {
            if (payment_request_current_page < payment_request_total_page && !payment_request_call_ajax) {
                getPaymentRequests(true);
            }
        }
    })
}


function getPaymentProjects(isLoadMore=false) {
    var with_paid = '0';
    if ($('#filter-payment').is(':checked')) {
        with_paid = '1';
    }

    call_ajax = true;
    $.ajax({
        type: 'GET',
        data: {
            'offset': payment_project_current_project_count,
            'with_paid': with_paid
        },
        url: '/payments/master_admins/project_info',
        beforeSend: function() {
            $('.payment__content-list .load-more-loading').removeClass('opacity-0');
        },
        success: function (data) {
            payment_project_current_project_count += parseInt(data.current_project_count);
            if (isLoadMore) {
                $(data.html).insertBefore($('.payment__content-list .load-more-loading').last());
            } else {
                $('.payment__content-list').empty();
                $('.payment__content-list').append(data.html);
                $('.payment__content-list').append(`<div class="load-more-loading opacity-0"></div>`);
            }
            notEnoughDataForScroll()
        },
        error: function () {
            console.log('error')
        },
        complete: function () {
            call_ajax = false;
            $('.payment__content-list .load-more-loading').addClass('opacity-0');
        }
    });
}

function reloadPaymentRequests() {
    payment_request_current_page = 0;
    getPaymentRequests();
}


function getPaymentRequests(isLoadMore=false) {
    payment_request_current_page++;
    payment_request_call_ajax = true;
    $.ajax({
        type: 'GET',
        data: {
            'page': payment_request_current_page
        },
        url: '/payments/master_admins/payment_requests/load_more',
        beforeSend: function() {
            $('#payment-requests-content .table-report .load-more-loading').removeClass('opacity-0');
        },
        success: function (data) {
            payment_request_total_page = data.payment_request_total_page;
            if (isLoadMore) {
                $(data.html).insertBefore($('#payment-requests-content .table-report .load-more-loading').last());
            } else {
                $('#payment-requests-content .table-report tbody').empty();
                $('#payment-requests-content .table-report tbody').append(data.html);
                $('#payment-requests-content .table-report tbody').append(`<tr class="load-more-loading opacity-0"></tr>`);
            }
        },
        error: function () {
            payment_request_current_page--;
        },
        complete: function () {
            payment_request_call_ajax = false;
            $('#payment-requests-content .table-report .load-more-loading').addClass('opacity-0');
        }
    });
}

function switchPayment() {
    $(document).on('change', '#filter-payment', function () {
        payment_project_current_project_count = 0;
        getPaymentProjects();
    });
}

function previewPDF() {
    $(document).on('click', '#payment-requests .btn-preview-pdf', function () {
        $('body').addClass('modal-no-overlay');
        let name = $(this).attr('data-name');
        let link = $(this).attr('data-link');
        $('#modal-preview-pdf').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(link) + '#zoom=page-width');
        $('#modal-preview-pdf').find('.btn-preview-pdf').html(name);
        $('#modal-preview-pdf').modal('show');
    });

    $(document).on('hidden.bs.modal', '#modal-preview-pdf', function (e) {
        $('body').removeClass('modal-no-overlay');
    })
}

function handlePaymentIcon() {
    // Check row
    $(document).on('click', '.payment--all .icon--center .icon--sicon-mark-done', function () {
        let hasChecked = $(this).hasClass('checked');
        updatePaymentStatus($(this), hasChecked, 'offer_creator_id');
    });

    // Check all
    $(document).on('click', '.payment--all .checked--all .icon--sicon-mark-done', function () {
        let hasChecked = $(this).hasClass('checked');
        updatePaymentStatus($(this), hasChecked, 'project_id');
    });

    $(document).on('mouseenter mouseleave', '.payment--all .payment__content-item tr', function(e) {
        if(e.type === 'mouseenter'){
            $(this).find('.icon--sicon-mark-done:not(.checked)').css('display', 'block');
        }
        else {
            $(this).find('.icon--sicon-mark-done:not(.checked)').css('display', 'none');
        }
    });
}

function isEmptyProjects() {
    console.log($('.payment__content-list').find('.payment__content-item:not(.hide)').length)
}

function handleChangeStatusPaymentRequest() {
    $(document).on('click', '#payment-requests .btn-payment-switch-status', function() {
        $(this).addClass('disable');
        var $btn = $(this);
        let payment_request_id = $(this).data('value');
        let csrfmiddlewaretoken = $('input[name="csrfmiddlewaretoken"]').val();
        let current_text = $(this).text();

        $.ajax({
            type: 'POST',
            data: {
                'csrfmiddlewaretoken': csrfmiddlewaretoken,
                'payment_request_id': payment_request_id
            },
            url: '/payments/master_admins/update_status_payment_request',
            success: function (data) {
                if (current_text === gettext('支払い完了')) {
                    $btn.text(gettext('取り消す'));
                    $btn.removeClass('btn--primary').addClass('btn--tertiary');
                    $btn.parent().parent().find('td:nth-child(2) span').text(getCurrentDateStr())
                } else {
                    $btn.text(gettext('支払い完了'));
                    $btn.removeClass('btn--tertiary').addClass('btn--primary');
                    $btn.parent().parent().find('td:nth-child(2) span').text(gettext('（処理中）'))
                }
            },
            error: function () {
                toastr.error(gettext('Something went wrong!'));
            },
            complete: function () {
                $btn.removeClass('disable');
            }
        });
    })
}

function getCurrentDateStr() {
    let d = new Date();
    let strDate = d.getFullYear().toString().substr(-2) + "/" + (d.getMonth()+1) + "/" + d.getDate();
    return strDate
}

function invisibleScroll(){
    return $('.payment--all').get(0).clientHeight == $('.payment--all')[0].scrollHeight;
}


function notEnoughDataForScroll(){
    if (invisibleScroll()) {
        getPaymentProjects(true);
    }
}

function updatePaymentStatus($element, hasChecked, param_name) {
    $element.addClass('disable-click');
    let id = $element.data('value');
    let target_payment_status = hasChecked ? 0 : 1;
    let csrfmiddlewaretoken = $('input[name="csrfmiddlewaretoken"]').val();
    let idProject = $element.parents('.payment__content-item').attr('data')
    let data_payment = $element.parents('.payment__content-item').attr('data-payment')
    let checkSwitch = $('#filter-payment').is(':checked') ? 1 : 0;
    var data = {
        'csrfmiddlewaretoken': csrfmiddlewaretoken,
        'target_payment_status': parseInt(target_payment_status),
        'id_project': idProject,
        'with_paid': checkSwitch,
        'data_payment': data_payment,
    };
    data[param_name] = id;
    $.ajax({
        type: 'POST',
        data: data,
        url: '/payments/master_admins/update_payment_status',
        success: function (data) {
            let checkedSwitch = $('#filter-payment').is(':checked');
            if (param_name === 'offer_creator_id') {
                if (checkedSwitch) {
                    if (hasChecked) {
                        $element.parents('tr').removeClass('checked--row');
                        $element.removeClass('checked');
                    } else {
                        $element.parents('tr').attr('class', 'checked--row');
                        $element.addClass('checked');
                    }
                } else {
                    if ($element.hasClass('checked')) {
                        $element.parents('tr').addClass('hide');
                    } else {
                        $element.parents('tr').addClass('hide');
                        $element.parents('tr').removeClass('checked--row');
                        $element.removeClass('checked');
                        if ($element.parents('table').find('tbody tr:not(.hide)').length < 1) {
                            $element.parents('.payment__content-item').addClass('hide');
                            isEmptyProjects();
                        }
                    }
                }

                if ($element.parents('table').find('tbody tr').length == $element.parents('table').find('tbody tr.checked--row').length) {
                    $element.parents('.payment__content-item').find('.checked--all .icon').addClass('checked').removeClass('hide');
                } else if ($element.parents('table').find('tbody tr').length == $element.parents('table').find('tbody tr:not(.checked--row)').length) {
                    $element.parents('.payment__content-item').find('.checked--all .icon').removeClass('checked').removeClass('hide');
                } else {
                    $element.parents('.payment__content-item').find('.checked--all .icon').removeClass('checked').addClass('hide');
                }
            } else {
                if (checkedSwitch) {
                    if (hasChecked) {
                        $element.removeClass('checked');
                        $element.parents('.payment__content-item').find('tr').removeAttr('class', 'checked--row');
                        $element.parents('.payment__content-item').find('.icon--center .icon').removeClass('checked');
                        $(this).parents('.payment__content-item').find('.icon--center .icon').css('display', 'none');
                    } else {
                        $element.addClass('checked');
                        $element.parents('.payment__content-item').find('tr').attr('class', 'checked--row');
                        $element.parents('.payment__content-item').find('.icon--center .icon').addClass('checked');
                        $element.parents('.payment__content-item').find('.icon--center .icon').css('display', 'block');
                    }
                } else {
                    $element.parents('.payment__content-item').addClass('hide');
                }
                isEmptyProjects();
            }
            let dom = $element.parents('.payment__content-item')
            $(data.html).insertAfter(dom)
            dom.remove()
            if (!checkedSwitch && target_payment_status === 1) {
                notEnoughDataForScroll();
            }
            console.log('Success');
        },
        error: function (xhr, status, error) {
            if (xhr.responseJSON) {
                toastr.error(xhr.responseJSON.error)
            }
        },
        complete: function () {
            $element.removeClass('disable-click');
        }
    });
}
// var csrf =  $('input[name="csrfmiddlewaretoken"]').val();
var csrf;
function masterAdminDeleleOfferProject() {
    $(document).on('click', '.icon.icon--sicon-trash', function () {
        let parentDom = $(this).parents('.offer-item');
        let offerId = '';
        let projectId = '';
        let modalDelete = $('#delete-offer');
        if (parentDom.length) {
            offerId = parentDom.attr('data-offer');
        } else {
            parentDom = $(this).parents('.payment__content-item');
            projectId = parentDom.attr('data');
        }
        modalDelete.attr('data-offer-id', offerId).attr('data-project-id', projectId)
    });

    $(document).on('click', '#delete-offer .btn-popup-delete', function () {
        let modalDelete = $('#delete-offer');
        let offer_id = modalDelete.attr('data-offer-id');
        let project_id = modalDelete.attr('data-project-id');
        let buttonDom = $(this);
        let csrfmiddlewaretoken = $('input[name="csrfmiddlewaretoken"]').val();
        if (project_id && !buttonDom.hasClass('disable')) {
            buttonDom.addClass('disable');
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/master_admin/delete_project_view",
                data: {
                    'project_id': project_id,
                    'csrfmiddlewaretoken': csrfmiddlewaretoken
                },
                success: function (response) {
                    $('.payment__content-item[data=' + project_id + ']').addClass('hide');
                    notEnoughDataForScroll();
                },
                complete: function () {
                    buttonDom.removeClass('disable');
                    modalDelete.modal('hide')
                },
                error: function () {
                    toastr.error(gettext('Something went wrong!'));
                }
            })
        } else if (offer_id && !buttonDom.hasClass('disable')) {
            buttonDom.addClass('disable');
            let data_payment = $('.offer-item[data-offer='+offer_id+']').parents('.payment__content-item').attr('data-payment');
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/master_admin/delete_offer_creator_view",
                data: {
                    'offer_id': offer_id,
                    'csrfmiddlewaretoken': csrfmiddlewaretoken,
                    'with_paid': $('#filter-payment').is(':checked') ? '1' : '0',
                    'data_payment': data_payment
                },
                success: function (response) {
                    let projectDom = $('.payment__content-item[data=' + response.project_id + ']');
                    $(response.html).insertAfter(projectDom);
                    if ($('.payment__content-item[data=' + response.project_id + ']').length == '2') {
                        projectDom.remove();
                    } else {
                        projectDom.addClass('hide')
                    }
                    notEnoughDataForScroll()
                },
                complete: function () {
                    buttonDom.removeClass('disable');
                    $('.btn-popup-close').click()
                },
                error: function () {
                    toastr.error(gettext('Something went wrong!'));
                }
            })
        }
    });

    $(document).on('click', '.icon.icon--sicon-pencil', function () {
        $('.modal-create-edit-offer').remove();
        let parentDom = $(this).parents('.offer-item');
        let offerId = '';
        if (parentDom.length) {
            offerId = parentDom.attr('data-offer');
            if (offerId) {
                $.ajax({
                    type: "GET",
                    datatype: "json",
                    url: "/master_admin/get_information_offer_creator_view",
                    data: {
                        'offer_id': offerId,
                    },
                    success: function (response) {
                        console.log('ok');
                        $('body').append(response.form_html);
                        $('#modal-edit-offer').modal('show');
                        actionForm($('#modal-edit-offer'));
                        intitActionForm();
                        dragDropSearch();
                        // $('#deadline-date').val(response.range_deadline);
                        $('.select-deadline_time').datetimepicker({ format: 'HH:mm', ignoreReadonly: true});
                        $('.select-deadline_time').val(response.time_deadline);

                        response.allow_subcontracting ? $('.allow_subcontracting').addClass('checked'): $('.allow_subcontracting').removeClass('checked');
                        response.allow_subcontracting ? $('#allow_subcontracting').attr('checked', true) : $('#allow_subcontracting').attr('checked', false);
                        if(response.selected_job_type && response.selected_job_type !== null){
                            const listJobType = response.selected_job_type?.split(',');
                            $('.modal-create-edit-offer').find('.job-title-container .tab-content-offer .skills-item-offer').each(function() {
                                if(listJobType.includes($(this).attr('data-id'))) {
                                    const indexTab = $(this).closest('.tab-pane-offer').attr('data-index');
                                    const tabId = $(this).closest('.tab-pane-offer').attr('id');
                                    countSkillsOffer(tabId, $(`.tabs-skill-offer .offer-form[data-index="${indexTab}"]`).find('.selected').length);
                                    $(this).trigger('click');
                                }
                            })
                        }
                        $('#input-scenes').val(response.scenes);
                        $('#input-quantity').val(response.quantity);
                        $('#id_data_format').val(response.data_format);
                        $('#id_data_format').closest('.input-select-container').find('ul .selected').removeClass('selected');
                        $('#id_data_format').attr('data-type', 'input');
                        $('#id_data_format').closest('.input-select-container').find('ul .list-input-select').each(function(){
                            if($(this).attr('data-value') === response.data_format) {
                                $(this).addClass('selected');
                                $('#id_data_format').attr('data-type', 'select');
                            }
                        });
                        $('#input-message').val(response.message);
                        $('#id_total_amount').html(parseFloat(response.reward).toLocaleString(undefined));
                        $('#modal-edit-offer').attr('data-offer', offerId);
                        let reward = response.reward;

                        let startDate = response.range_deadline ? moment(new Date(response.range_deadline?.split(' - ')[0])) : moment(new Date(response.start_time));
                        let endDate = response.range_deadline ? moment(new Date(response.range_deadline?.split(' - ')[1])) : moment(new Date(response.deadline));

                        $('#deadline-date').datepicker({
                            format: 'yyyy/m/d',
                            locale: 'ja',
                            forceParse: false,
                        });

                        const deadlineDate = moment(new Date(response.deadline.split('T')[0])).format('YYYY/M/D');
                        $('#deadline-date').val(deadlineDate);
                        $('#deadline-date').datepicker('setDate', deadlineDate);
                        // $('.select-deadline_time').val('10:00');
                        $('#id_period_offer').daterangepicker({
                            startDate: startDate,
                            endDate: endDate,
                        });
                        let valid_date = response.valid_date ? new Date(response.valid_date?.split('T')[0]) :  new Date(endDate.format('YYYY/MM/DD'))
                        $('#id_valid_offer').datepicker('remove');
                        $('#id_valid_offer').datepicker({
                            format: 'yyyy/mm/dd',
                            locale: 'ja',
                            forceParse: false,
                        })
                        $('#id_valid_offer').val(moment(valid_date).format('YYYY/MM/DD'));
                        $('#id_valid_offer').datepicker('setDate', moment(valid_date).format('YYYY/MM/DD'));
                        const valTime = response.valid_date ? response.valid_date.split('T')[1].split(':')[0] + ":"+ response.valid_date.split('T')[1].split(':')[1] :
                        response.deadline.split('T')[1].split(':')[0] + ":"+ response.deadline.split('T')[1].split(':')[1]
                        $('#valid_time_offer').val(valTime);
                        $('#input-remarks').val(response.note);

                        let total_amount = parseFloat(reward);
                        let tmp_reward = Math.round(total_amount / 1.1);
                        let tax_amount = Math.round(tmp_reward * 0.1);
                        $('#budget').val(tmp_reward.toLocaleString(undefined));

                        $('#id_tax_amount').html((tax_amount).toLocaleString(undefined));

                        $('.file_offer').remove();
                        let file_name = response.file_name;
                        let file_html = '<div class="file_offer">' + file_name + '</div>';
                        $(file_html).insertAfter($('#modal-edit-offer').find('.mupload-label'));
                        if (file_name) {
                            $('.mattach-info-file .file-name').text(file_name);
                            $('.mattach-info-file').removeClass('hide');
                        }

                        if (response.can_edit_reward) {
                            $('.modal-create-edit-offer #budget').attr('readonly', false)
                        } else {
                            $('.modal-create-edit-offer #budget').attr('readonly', true)
                        }

                        addDataList($('#offer_scenes'), response.list_scenes);
                        $('#id_contract').val(response.contract);
                        addDataList($('#offer_format'), response.list_data_format);
                        addDataList($('#offer_quantity'), response.list_quantity);
                        $(document).find('#id_pickup_offer').SumoSelect({
                            search: false,
                            forceCustomRendering: true,
                        });

                        $(document).find('#id_delivery_place_offer').SumoSelect({
                            search: false,
                            forceCustomRendering: true,
                        });
                        let valMethod = response.pick_up_method
                        $(document).find('#id_pickup_offer').attr('value', valMethod);
                        if(!!valMethod && valMethod!=='None' && valMethod!=="undefined") {
                            $('#id_pickup_offer').val(valMethod);
                            $('#id_pickup_offer').closest('.input-select-container').find('ul .selected').removeClass('selected');
                            $('#id_pickup_offer').attr('data-type', 'input');
                            $('#id_pickup_offer').closest('.input-select-container').find('ul .list-input-select').each(function(){
                                if($(this).attr('data-value') === valMethod) {
                                    $(this).addClass('selected');
                                    $('#id_pickup_offer').attr('data-type', 'select');
                                }
                            });
                        }

                        let valDelivery = response.delivery_place
                            $(document).find('#id_delivery_place_offer').attr('value', valDelivery);
                            if(!!valDelivery && valDelivery!=='None' && valDelivery!=="undefined") {
                                $('#id_delivery_place_offer').val(valDelivery);
                                $('#id_delivery_place_offer').closest('.input-select-container').find('ul .selected').removeClass('selected');
                                $('#id_delivery_place_offer').attr('data-type', 'input');
                                $('#id_delivery_place_offer').closest('.input-select-container').find('ul .list-input-select').each(function(){
                                    if($(this).attr('data-value') === valDelivery) {
                                        $(this).addClass('selected');
                                        $('#id_delivery_place_offer').attr('data-type', 'select');
                                    }
                                });
                        }
                        is_editing = true;
                        intitActionForm();
                    },
                    complete: function () {

                    },
                    error: function () {
                        toastr.error(gettext('Something went wrong!'));
                    }
                })
            }
        }
    });

    $(document).on('click', '.edit-offer-submit:not(.disabled)', function () {
        $('.errorlist').remove();
        $('.error-border').removeClass('error-border');
        let buttom_dom = $(this);
        let data_form = new FormData();
        let deadline = $('#deadline-date').val();
        let time_deadline = $('.select-deadline_time').val();
        let contract = $('#id_contract').val();
        // let type_contract = $('#id_type_contract').val();
        let scenes = $('#input-scenes').val();
        // let quantity = $('#input-quantity').val();
        let data_format = $('#id_data_format').val();
        let message = $('#input-message').val();
        let reward = $('#id_total_amount').html();
        let offer_id = $('#modal-edit-offer').attr('data-offer');
        let delivery_place = $('#id_delivery_place_offer').val();
        let period = $('#id_period_offer').val();
        let allow_subcontracting = $('.allow_subcontracting').hasClass('checked') ? 1 : 0;
        let selected_job_type = []
        $('.skills-item-offer.selected').each(function() {
            selected_job_type.push($(this).attr('data-id'));
        })
        let note = $('#input-remarks').val();
        let pick_up_method = $('#id_pickup_offer').val();
        let valid_date = $('#id_valid_offer').val();
        let valid_time = $('#valid_time_offer').val();
        valid_date = valid_date + ' ' + valid_time;

        let is_blank = checkValidateBlank(['#budget', '#id_contract']);
        if (is_blank) {
            return false
        }

        buttom_dom.addClass('disabled');
        data_form.append('range_deadline', period);
        data_form.append('contract', contract);
        // data_form.append('type_contract', type_contract);
        data_form.append('scenes', scenes);
        // data_form.append('quantity', quantity);
        data_form.append('data_format', data_format);
        data_form.append('message', message);
        data_form.append('reward', reward);
        data_form.append('time_deadline', time_deadline);
        data_form.append('offer_id', offer_id);
        data_form.append('key_file', key_file);
        data_form.append('real_name', real_name);
        data_form.append('is_delete_file', is_delete_file);
        data_form.append('with_paid', $('#filter-payment').is(':checked') ? '1' : '0');
        let data_payment = $('.offer-item[data-offer='+offer_id+']').parents('.payment__content-item').attr('data-payment');
        data_form.append('data_payment', data_payment);
        data_form.append('deadline', deadline);
        data_form.append('valid_date', valid_date);
        data_form.append('note', note);
        data_form.append('pick_up_method', pick_up_method);
        data_form.append('delivery_place', delivery_place);
        data_form.append('allow_subcontracting', allow_subcontracting);
        data_form.append('selected_job_type', selected_job_type);

        $.ajax({
            type: "POST",
            contentType: false,
            processData: false,
            cache: false,
            url: "/master_admin/update_info_offer_creator_view",
            data: data_form,
            success: function (data) {
                $('#modal-edit-offer').modal('hide');
                let projectDom = $('.payment__content-item[data=' + data.project_id + ']');
                $(data.html).insertAfter(projectDom);
                projectDom.remove()
                notEnoughDataForScroll
            },
            error: function (data) {
                toastr.error(gettext('Something went wrong!'));
                buttom_dom.removeClass('disabled');
            },
            complete: function () {
                buttom_dom.removeClass('disabled');
            }
        });
    });
}

Date.prototype.addDays = function (days) {
    let date = new Date(this.valueOf());
    date.setDate(date.getDate() + days);
    return date;
};
let startDate = new Date();
let endDate = new Date().addDays(5);

function updateCountProject() {
    payment_project_current_project_count = parseInt($('.payment__content').attr('data-project-count'))
}

$(document).ready(function () {
    updateCountProject();
    handlePaymentRequestScroll();
    previewPDF();
    switchPayment();
    handleScroll();
    handlePaymentIcon();
    handleChangeStatusPaymentRequest();
    notEnoughDataForScroll();
    masterAdminDeleleOfferProject();
    activeTabOffer();
    intitActionForm();
});
