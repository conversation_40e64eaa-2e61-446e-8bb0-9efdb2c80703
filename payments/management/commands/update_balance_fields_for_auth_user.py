# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 17/12/2021
import datetime

from django.db.models import Prefetch, F
from django.core.management.base import BaseCommand

from app.models import OfferCreator, Product
from accounts.models import AuthUser


# Update DB từng user theo project
# balance_available = (offer chưa thanh toán AND done)(offer đên AND chưa request - offer đi) - (offer đã thanh toán)(offer đến - offer đi)(nếu hiệu âm)
# balance_expected = (offer chưa thanh toán AND chưa done)(offer đến - offer đi)
from payments.models import PaymentRequest


def update_balance_fields_for_auth_user():
    # update status for offer payment
    OfferCreator.objects.filter(payment_status=True, status__in=OfferCreator.STATUS_IN_PROGRESS,
                                accept_time__isnull=True).update(accept_time=F('modified'))
    OfferCreator.objects.filter(payment_status=True, status__in=OfferCreator.STATUS_IN_PROGRESS).update(
        status=OfferCreator.STATUS_COMPLETED, check_time=F('modified'))

    # update payment status
    payment_requests = PaymentRequest.objects.filter()
    for payment_request in payment_requests:
        if payment_request.status_type == PaymentRequest.PAID_STATUS:
            payment_request.recipient_offers.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE).update(
                payment_status=True)
        else:
            payment_request.recipient_offers.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE).update(
                payment_status=False)

    # update
    creator_prefetch = Prefetch('offer_to_creator',
                                queryset=OfferCreator.objects.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE))
    admin_prefetch = Prefetch('offer_by',
                              queryset=OfferCreator.objects.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE))
    users = AuthUser.objects.filter(role=AuthUser.CREATOR).prefetch_related(creator_prefetch, admin_prefetch)
    for user in users:
        user.balance_available = 0
        user.balance_expected = 0
        for project in user.products.all():
            budget_payment = 0
            offer_admins = user.offer_by.filter(project=project)
            offer_creators = user.offer_to_creator.filter(project=project)
            for offer_admin in offer_admins:
                if not offer_admin.payment_status:
                    if offer_admin.status == OfferCreator.STATUS_COMPLETED:
                        if not offer_admin.admin_payment_request:
                            user.balance_available -= offer_admin.reward
                        else:
                            budget_payment -= offer_admin.reward
                    else:
                        user.balance_expected -= offer_admin.reward
                else:
                    budget_payment -= offer_admin.reward

            for offer_creator in offer_creators:
                if not offer_creator.payment_status:
                    if offer_creator.status == OfferCreator.STATUS_COMPLETED:
                        if not offer_creator.creator_payment_request:
                            user.balance_available += offer_creator.reward
                        else:
                            budget_payment += offer_creator.reward
                    elif offer_creator.status != '1':
                        user.balance_expected += offer_creator.reward
                else:
                    budget_payment += offer_creator.reward
            if budget_payment < 0:
                user.balance_available += budget_payment

            # update for master producer

            # offer = project.offer_product.first()
            # p_producer = project.productuser_set.filter(is_super_producer=True, user=user).first()
            # if p_producer:
            #     if offer.condition in ['4', '5', '6']:
            #         user.balance_available += p_producer.current_budget
            #     else:
            #         user.balance_expected += p_producer.current_budget

        user.save()


class Command(BaseCommand):
    help = 'Update payment status for offer creators'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start update payment status for offer creators__________')
        update_balance_fields_for_auth_user()
        self.stdout.write(self.style.SUCCESS('__________done!__________'))
