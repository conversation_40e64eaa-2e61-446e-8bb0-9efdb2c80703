# -*- coding: utf-8 -*-
import datetime

from django.db.models import Prefetch, F, Q
from django.core.management.base import BaseCommand

from app.models import OfferCreator, Product, FormContractAndPlan
from accounts.models import AuthUser, ProductUser


def update_payment_for_master_producer():
    project_users = ProductUser.objects.filter(is_super_producer=True)

    for project_user in project_users:
        user = project_user.user
        project = project_user.product
        offer_project = project.offer_product.last()

        if offer_project and offer_project.condition in ['3', '4', '5', '6', '9']:
            if OfferCreator.original_objects.filter(type='2', project=project).exists():
                # offer_1 = OfferCreator.original_objects.get(type='2', creator=user, project=project)
                # offer_2 = OfferCreator.original_objects.get(type='2', admin=user, project=project)
                continue
            else:
                owner = project.get_owner()
                master_admin = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).first()
                reward_soremo = round(project.total_budget * project_user.user.user_creator.first().usage_fee / 100, 0)
                accept_time = project.created

                form_contract = FormContractAndPlan.objects.filter(product_message_files__offer=offer_project).order_by(
                    '-created', '-id').first()
                if form_contract:
                    accept_time = form_contract.created
                offer_1 = OfferCreator.original_objects.create(type='2', admin=owner, creator=user,
                                                               reward=project.total_budget,
                                                               status='2', project=project, accept_time=accept_time,
                                                               scenes=project.name)
                name_offer_2 = f'利用料{project_user.usage_fee}%'
                offer_2 = OfferCreator.original_objects.create(type='2', admin=user, creator=master_admin,
                                                               reward=reward_soremo,
                                                               status='2', project=project, accept_time=accept_time,
                                                               scenes=name_offer_2)
                if offer_project.condition in ['4', '5', '6']:
                    OfferCreator.original_objects.filter(
                        Q(type='2') & Q(project=project) & (Q(admin=user) | Q(creator=user))).update(status='4',
                                                                                                     check_time=project.modified)
                    AuthUser.change_balance(user.pk,
                                            [('balance_available', offer_1.reward - offer_2.reward)])
                else:
                    AuthUser.change_balance(user.pk, [('balance_expected', project.total_budget - reward_soremo)])


class Command(BaseCommand):
    help = 'Update payment request for offer creators'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start update payment__________')
        update_payment_for_master_producer()
        self.stdout.write(self.style.SUCCESS('__________done!__________'))
