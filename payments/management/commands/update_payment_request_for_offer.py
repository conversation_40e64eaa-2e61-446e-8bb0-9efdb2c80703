# -*- coding: utf-8 -*-
import datetime

from django.db.models import Prefetch, F
from django.core.management.base import BaseCommand

from app.models import OfferCreator, Product
from accounts.models import AuthUser


def update_payment_request_for_offer():
    # update
    creator_prefetch = Prefetch('offer_to_creator',
                                queryset=OfferCreator.objects.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE))
    admin_prefetch = Prefetch('offer_by',
                              queryset=OfferCreator.objects.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE))
    users = AuthUser.objects.filter(role='admin').prefetch_related(creator_prefetch, admin_prefetch)

    for user in users:
        for project in user.products.all():
            budget_payment = 0
            offer_admins = user.offer_by.filter(project=project)
            offer_creators = user.offer_to_creator.filter(project=project)
            for offer_admin in offer_admins:
                if not offer_admin.payment_status:
                    if offer_admin.status == OfferCreator.STATUS_COMPLETED:
                        if offer_admin.creator_payment_request:
                            budget_payment -= offer_admin.reward
                else:
                    budget_payment -= offer_admin.reward

            for offer_creator in offer_creators:
                if not offer_creator.payment_status:
                    if offer_creator.status == OfferCreator.STATUS_COMPLETED:
                        if offer_creator.creator_payment_request:
                            budget_payment += offer_creator.reward
                else:
                    budget_payment += offer_creator.reward
            if budget_payment >= 0:
                offer_payment = offer_admins.filter(payment_status=True)
                if offer_payment.exists():
                    offer_payment.update(legaxy_payment=True)


class Command(BaseCommand):
    help = 'Update payment request for offer creators'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start update payment request for offer creators__________')
        update_payment_request_for_offer()
        self.stdout.write(self.style.SUCCESS('__________done!__________'))
