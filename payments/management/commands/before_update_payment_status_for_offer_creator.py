# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 16/12/2021
import datetime

from django.core.management.base import BaseCommand

from app.models import OfferCreator


def update_payment_status_for_offer_creator(date):
    return OfferCreator.objects.filter(check_time__lt=date).update(payment_status=True)


class Command(BaseCommand):
    help = 'Update payment status for offer creators'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def add_arguments(self, parser):
        parser.add_argument('date_filter', type=str, help='Last date paid, format YYY/MM/DD')

    def handle(self, *args, **options):
        self.stdout.write('__________start update payment status for offer creators__________')
        date_filter = options['date_filter']
        try:
            date_filter = datetime.datetime.strptime(date_filter, '%Y/%m/%d') + datetime.timedelta(days=1)
        except ValueError:
            self.stdout.write(self.style.ERROR('Format date wrong, format date expected: YYYY/MM/DD'))
            return
        num_records = update_payment_status_for_offer_creator(date_filter)
        self.stdout.write(f'=============> Updated {num_records} records.')
        self.stdout.write(self.style.SUCCESS('__________done!__________'))
