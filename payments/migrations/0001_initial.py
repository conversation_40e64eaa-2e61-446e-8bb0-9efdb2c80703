# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-12-02 15:31
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentRequest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('settlement_date', models.DateTimeField(blank=True, default=None, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('status_type', models.IntegerField(choices=[(1, 'requested'), (2, 'processing'), (3, 'paid'), (4, 'rejected'), (5, 'canceled')])),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
