{% load i18n %}
{% load util %}

{% for payment_request in payment_requests %}
<tr>
    <td>
        <div class="bodytext--13">{{ payment_request.created|format_short_date }}</div>
    </td>
    <td>
        <span class="bodytext--13">{% if payment_request.settlement_date %}{{ payment_request.settlement_date|format_short_date }}{% else %}{{ payment_request.get_status_type_display }}{% endif %}</span>
    </td>
    <td>
        <span class="bodytext--13">{{ payment_request.amount|display_currency }}</span>
        <span class="bodytext--13"> {% trans "円" %}</span>
    </td>
    <td>
        <span class="bodytext--13">{{ payment_request.amount|calculate_after_tax_payment|display_currency }}</span>
        <span class="bodytext--13"> {% trans "円" %}</span>
    </td>

    <td>
      <span class="bodytext--13">{{ payment_request.creator.get_display_name }}</span>
    </td>
    <td class="flex-btn">
        <button class="btn {% if payment_request.status_type == 2 %}btn--primary{% else %}btn--tertiary{% endif %} btn-payment-switch-status"
                data-value="{{ payment_request.pk }}">{% if payment_request.status_type == 2 %}{% trans "支払い完了" %}{% else %}{% trans "取り消す" %}{% endif %}</button>
        <button class="btn btn--secondary btn-preview-pdf" data-toggle="modal" data-target="#modal-preview-pdf"
                data-name="{{ payment_request.payment_content.name }}"  data-link="{{ payment_request.payment_content.url }}">{% trans "PDFを見る" %}</button>
    </td>
</tr>
{% endfor %}
