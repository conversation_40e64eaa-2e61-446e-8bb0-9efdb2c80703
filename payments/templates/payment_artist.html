{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% block title %}<title>WALLET</title>{% endblock %}
{% load i18n compress %}

{% block extrahead %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}" />
    <link href="{% static 'payments/css/payment_artist.css' %}" rel="stylesheet">
    {% endcompress %}
{% endblock%}

{% block content %}
<div class="payment">
    <div class="container">
        <input type="hidden" id="offer_creator_ids" value="{{ offer_creator_ids }}">
        <input type="hidden" id="current_user_id" value="{{ current_user.id }}">
        <div class="payment__wrap">
            {% csrf_token %}
            <div class="payment__heading">
                <h3 class="heading--40">ウォレット</h3>
                <p class="caption--11">振込申請すると、残高が銀行口座に移動します。取引履歴はいつでも確認できます。</p>
            </div>
            <div class="payment__content">
                <div class="payment__content-wrap" id="balance-info">
                    {% include '_balance_info.html' %}
                </div>
                <div class="payment__content-wrap" id="payment-requests-content">
                    <div class="payment__content-heading">
                        <div class="heading--18">{% trans "支払明細書" %}</div>
                    </div>
                    <div class="payment__content-table mscrollbar">
                        {% include '_payment_requests.html' %}
                    </div>
                </div>
                {% include '_payment_info_projects.html' %}
            </div>
        </div>
    </div>
</div>

{% include '_modal_payments.html' %}

{% include '_model_preview_payments.html' %}

{% endblock %}

{% block extra_script %}
<script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}"; var targeted_user_id='{{ targeted_user_id }}'</script>
<script src="{% url 'javascript-catalog' %}"></script>
    {% compress js inline %}
<script type="text/javascript" src="{% static 'payments/js/payment_artist.js' %}"></script>
    {% endcompress %}
{% endblock %}
