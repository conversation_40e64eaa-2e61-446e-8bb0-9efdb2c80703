{% load i18n %}
{% load util %}

{% for payment_request in payment_requests %}
<tr>
    <td>
        <div class="bodytext--13">{{ payment_request.created|format_short_date }}</div>
    </td>
    <td>
        <span class="bodytext--13">{% if payment_request.settlement_date %}{{ payment_request.settlement_date|format_short_date }}{% else %}{{ payment_request.get_status_type_display }}{% endif %}</span>
    </td>
    <td>
        <span class="bodytext--13">{{ payment_request.amount|display_currency }}</span>
        <span class="bodytext--13"> {% trans "円" %}</span>
    </td>
    <td>
        <button class="btn btn--secondary btn-preview-pdf" data-toggle="modal" data-target="#modal-preview-pdf"
                data-name="{{ payment_request.payment_content.name }}"  data-link="{{ payment_request.payment_content.url }}">{% trans "PDFを見る" %}</button>
    </td>
</tr>
{% endfor %}
