{% load util %}
{% load i18n %}
{% load static %}

{% if project|check_show_project_payment_admin %}
    <div class="payment__content-item" data-payment="{% if forloop.counter %}{{forloop.counter}}{% else %}{{data_loop}}{% endif %}" data="{{project.pk}}">
        <div class="payment__content-item-heading">
            <div class="heading--16">{{ project.get_name_by_code_name }}</div>
          {% if project.counter_task_progress < 1 %}
            {% if project.counter_done == 0 %}
              <div class="checked--all">
                <i class="icon icon--sicon-mark-done" data-value="{{ project.pk }}"></i>
                {% if project.check_can_delete_project %}
                  <a class="button-delete_offer" data-toggle="modal" data-target="#delete-offer">
                    <i class="icon icon--sicon-trash"></i>
                  </a>
                {% endif %}
              </div>
            {% elif project.counter_not_done == 0 %}
              <div class="checked--all">
                <i class="icon icon--sicon-mark-done checked" data-value="{{ project.pk }}"></i>
                {% if project.check_can_delete_project %}
                  <a class="button-delete_offer" data-toggle="modal" data-target="#delete-offer">
                    <i class="icon icon--sicon-trash"></i>
                  </a>
                {% endif %}
              </div>
            {% else %}
              <div class="checked--all">
                <i class="icon icon--sicon-mark-done hide" data-value="{{ project.pk }}"></i>
                {% if project.check_can_delete_project %}
                  <a class="button-delete_offer" data-toggle="modal" data-target="#delete-offer">
                    <i class="icon icon--sicon-trash"></i>
                  </a>
                {% endif %}
              </div>
            {% endif %}
          {% else %}
            <div class="checked--all">
              <i class="icon icon--sicon-mark-done hide" data-value="{{ project.pk }}"></i>
              {% if project.check_can_delete_project %}
                <a class="button-delete_offer" data-toggle="modal" data-target="#delete-offer">
                  <i class="icon icon--sicon-trash"></i>
                </a>
              {% endif %}
            </div>
          {% endif %}
        </div>

        <div class="payment__content-table mscrollbar">
            <table class="table table-history">
                <thead>
                    <tr>
                        <th width="30%">相手先</th>
                        <th width="12%">金額（税込）</th>
                        <th width="10%">取引成立日</th>
                        <th width="10%">検収日</th>
                        <th width="15%">シーン</th>
                        <th>ロール</th>
                        <th width="20px"></th>
                    </tr>
                </thead>
                <tbody>
                    {% if project|check_master_producer_is_master_admin == 'is_admin' and project|get_owner_payment %}
                      <tr>
                        <td>
                            <div class="payment-scene">
                                <div class="payment-scene_wrap">
                                    {% with project|get_owner_payment as owner %}
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{{ project|get_owner_payment|get_avatar:'small' }}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">{{  owner.get_display_name|default_if_none:"" }}</div>
                                    </div>
                                    {% endwith %}
                                </div>
                                <i class="icon icon--sicon-drop-next icon--next"></i>
                                <div class="payment-scene_wrap">
                                    {% with project|get_master_producer_payment as master_producer %}
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{{ master_producer|get_avatar:'small' }}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">{{  master_producer.get_display_name|default_if_none:"" }}</div>
                                    </div>
                                    {% endwith %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="bodytext--13">{{project|get_sum_profit:'offer'}}</span>
                            <span class="bodytext--13"> {% trans "円" %}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ project|get_accept_date|format_short_date }}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ project|get_done_date|format_short_date }}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ project|get_scene_payment:'name' }}</span>
                        </td>
                        <td>
                          <span class="{% if offer.type == '1' %}caption--11{% endif %}"></span>
                        </td>
                        <td class="icon--center">
                        </td>
                      </tr>
                    {% elif project|check_master_producer_is_master_admin == 'is_producer' and  project|get_owner_payment %}
                      <tr>
                        <td>
                            <div class="payment-scene">
                                <div class="payment-scene_wrap">
                                    {% with project|get_owner_payment as owner %}
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{{ owner|get_avatar:'small' }}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">{{ owner.get_display_name }}</div>
                                    </div>
                                    {% endwith %}
                                </div>
                                <i class="icon icon--sicon-drop-next icon--next"></i>
                                <div class="payment-scene_wrap">
                                    {% with project|get_master_producer_payment as master_producer %}
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{{ master_producer|get_avatar:'small' }}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">{{  master_producer.get_display_name|default_if_none:"" }}</div>
                                    </div>
                                    {% endwith %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="bodytext--13">{{project|get_sum_profit:'offer'}}</span>
                            <span class="bodytext--13"> {% trans "円" %}</span>
                        </td>
                        <td>
                            <span class="bodytext--13"></span>
                        </td>
                        <td>
                            <span class="bodytext--13"></span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ project|get_scene_payment:'name' }}</span>
                        </td>
                        <td>
                          <span class="{% if offer.type == '1' %}caption--11{% endif %}"></span>
                        </td>
                        <td class="icon--center">
                        </td>
                      </tr>
                      <tr>
                        <td>
                            <div class="payment-scene">
                                <div class="payment-scene_wrap">
                                    {% with project|get_master_producer_payment as master_producer %}
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{{ master_producer|get_avatar:'small' }}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">{{  master_producer.get_display_name|default_if_none:"" }}</div>
                                    </div>
                                    {% endwith %}
                                </div>
                                <i class="icon icon--sicon-drop-next icon--next"></i>
                                <div class="payment-scene_wrap">
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{% static 'images/soremo-favi2_01.png' %}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">SOREMO</div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="bodytext--13">{{project|calculate_usage_fee|display_currency}}</span>
                            <span class="bodytext--13"> {% trans "円" %}</span>
                        </td>
                        <td>
                            <span class="bodytext--13"></span>
                        </td>
                        <td>
                            <span class="bodytext--13"></span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ project|get_scene_payment:'' }}</span>
                        </td>
                        <td>
                          <span class=""></span>
                        </td>
                        <td class="icon--center">
                        </td>
                      </tr>
                    {% endif %}
                    {% for offer in project.product_offers.all %}
                    <tr class="{% if offer.payment_status %}checked--row{% endif %} offer-item" data-offer="{{ offer.pk }}">
                        <td>
                            <div class="payment-scene">
                                <div class="payment-scene_wrap">
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{{ offer.admin|get_avatar:'small' }}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">{{  offer.admin.get_display_name|default_if_none:"" }}</div>
                                    </div>
                                </div>
                                <i class="icon icon--sicon-drop-next icon--next"></i>
                                <div class="payment-scene_wrap">
                                    <div class="payment-scene__user">
                                        <div class="avatar avatar--image avatar--24 avatar--round stooltip"
                                            data-toggle="tooltip" data-placement="bottom"
                                            data-html="true" style="background-color: white;"
                                            title="">
                                            <img class="avatar-image"
                                                src="{% if offer.creator.role == 'master_admin' %}{% static 'images/soremo-favi2_01.png' %}{% else %}{{ offer.creator|get_avatar:'small' }}{% endif %}"
                                                style="padding-bottom: 0;" />
                                        </div>
                                    </div>
                                    <div class="payment-scene__name">
                                        <div class="bodytext--13">{% if offer.creator.role == 'master_admin' %}SOREMO{% else %}{{  offer.creator.get_display_name|default_if_none:"" }}{% endif %}</div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ offer.reward|display_currency }}</span>
                            <span class="bodytext--13"> {% trans "円" %}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ offer.accept_time|format_short_date }}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ offer.check_time|format_short_date }}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ offer.scenes }}</span>
                        </td>
                        <td>
                            <span class="{% if offer.type == '1' %}caption--11{% endif %}">{{ offer|master_admin_get_offer_role }}</span>
                        </td>
                        <td class="icon--center" style="width: 60px;">
                          {% if offer.status == '4'%}
                            {% if offer.payment_status %}
                              <i class="icon icon--sicon-mark-done checked" data-value="{{ offer.pk }}"></i>
                            {% else %}
                              <i class="icon icon--sicon-mark-done" data-value="{{ offer.pk }}"
                                 style="display: none;"></i>
                            {% endif %}
                          {% endif %}
                            {% if offer.check_can_delete_offer %}
                              <a class="button-edit_offer" data-toggle="modal" data-target="#modal-edit-offer">
                                <i class="icon icon--sicon-pencil"></i>
                              </a>
                              <a class="button-delete_offer" data-toggle="modal" data-target="#delete-offer">
                                <i class="icon icon--sicon-trash"></i>
                              </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                  <tr>
                      <td></td>
                      <td>
                          <span class="bodytext--13">{{project|get_sum_profit:''}}</span>
                          <span class="bodytext--13"> 円</span>
                      </td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td class="icon--center">

                      </td>
                  </tr>
                </tfoot>
            </table>
        </div>
    </div>
{% endif %}
