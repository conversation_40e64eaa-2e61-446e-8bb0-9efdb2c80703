{% load util %}
{% load i18n %}
{% load static %}

{% if projects %}
    {% for project in projects %}
    <div class="payment__content-item" data-payment="{{ forloop.counter }}">
        <div class="heading--16">{{ project.get_name_by_code_name }}</div>
        <div class="payment__content-table mscrollbar">
            <table class="table table-history">
                <thead>
                    <tr>
                        <th width="20%">{% trans "相手先" %}</th>
                        <th width="12%">{% trans "金額（税込）" %}</th>
                        <th width="10%">{% trans "取引成立日" %}</th>
                        <th width="10%">{% trans "検収日" %}</th>
                        <th width="15%">{% trans "シーン" %}</th>
                        <th>{% trans "ロール" %}</th>
                        <th width="20px"></th>
                    </tr>
                </thead>
                <tbody>
                    {% for offer in project.product_offers.all %}
                    <tr>
                        {% if offer.admin == current_user and offer.creator != current_user %}
                        <td>
                            <div class="payment-scene">
                                <div class="payment-scene__user">
                                    <i class="icon icon--sicon-drop-next"></i>
                                    <div class="avatar avatar--image avatar--24 avatar--round stooltip" data-toggle="tooltip" data-placement="bottom" data-html="true" style="background-color: white;" title="">
                                        <img
                                            class="avatar-image"
                                            src="{% if offer.creator.role == 'master_admin' %}{% static 'images/soremo-favi2_01.png' %}{% else %}{{ offer.creator|get_avatar:'small' }}{% endif %}"
                                            style="padding-bottom: 0;"
                                        />
                                    </div>
                                </div>
                                <div class="payment-scene__name">
                                    <div class="bodytext--13">{% if offer.creator.role == 'master_admin' %}SOREMO{% else %}{{  offer.creator.get_display_name|default_if_none:"" }}{% endif %}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                        {% if offer.reward %}
                            <span class="bodytext--13">-{{ offer.reward|display_currency }}</span>
                        {% else %}
                            <span class="bodytext--13">{{ offer.reward|display_currency }}</span>
                        {% endif %}
                            <span class="bodytext--13"> {% trans "円" %}</span>
                        </td>
                        {% else %}
                        <td>
                            <div class="payment-scene">
                                <div class="payment-scene__user">
                                    <div class="avatar avatar--image avatar--24 avatar--round stooltip" data-toggle="tooltip" data-placement="bottom" data-html="true" style="background-color: white;" title="">
                                        <img
                                            class="avatar-image"
                                            src="{{ offer.admin|get_avatar:'small' }}"
                                            style="padding-bottom: 0;"
                                        />
                                    </div>
                                </div>
                                <div class="payment-scene__name">
                                    <div class="bodytext--13">{{ offer.admin.get_display_name|default_if_none:"" }}</div>
                                </div>
                                <i class="icon icon--sicon-drop-next"></i>
                            </div>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ offer.reward|display_currency }}</span>
                            <span class="bodytext--13"> {% trans "円" %}</span>
                        </td>
                        {% endif %}
                        <td>
                            <span class="bodytext--13">{{ offer.accept_time|format_short_date }}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ offer.check_time|format_short_date }}</span>
                        </td>
                        <td>
                            <span class="bodytext--13">{{ offer.scenes }}</span>
                        </td>
                        <td> {% if offer.custom_contract_dsp %}
                            <span class="caption--11">{{ offer.custom_contract_dsp }}</span>
                        {% endif %}
                        </td>
                        <td class="icon--center">
                        {% if offer.payment_status and project.counter_not_done and offer|is_show_icon_payment_done_task:current_user%}
                            <i class="icon icon--sicon-mark-done"></i>
                        {% endif %}
                        </td>
                    </tr>
                    {% endfor %}

                </tbody>
                <tfoot>
                    <tr>
                        <td></td>
                        <td>
                            <span class="bodytext--13">{{ project.total_reward|display_currency }}</span>
                            <span class="bodytext--13"> {% trans "円" %}</span>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="icon--center">
                        {% if project.counter_not_done == 0 %}
                          <i class="icon icon--sicon-mark-done"></i>
                        {% endif %}
                        </td>
                    </tr>
                  </tfoot>
            </table>
        </div>
    </div>
    {% endfor %}
{% endif %}
