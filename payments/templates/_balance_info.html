{% load util %}
{% load i18n %}

<div class="payment__content-heading">
    <div class="heading--18">{% trans "残高" %} <span class="caption--11">{% trans "（検収済み売上金含む）" %}</span></div>
</div>
<div class="payment__content-body">
    <div class="payment__content-body-left">
        <div class="heading--40">{{ current_user.balance_available|display_currency }}</div>
        <div class="bodytext--13">{% trans "Yen tax included" %}</div>
    </div>
    <div class="payment__content-body-right">
        {# <button class="btn btn--tertiary">{% trans "増やす" %}</button> #}
        <button
            class="btn btn--primary {% if current_user.balance_available <= 0 or current_user.balance_expected < 0 or current_user_role != 'admin' %}disable{% endif %}"
            id="create-payment-request-btn">{% trans "振込申請" %}</button>
    </div>
</div>
<div class="payment__content-footer">
    <div class="heading--13">{% trans "進行中" %}<span class="caption--11">{% trans "（検収待ちの売掛金と買掛金）" %}</span></div>
    <div class="heading--16">{{ current_user.balance_expected|display_currency }}<span class="bodytext--13">{% trans "Yen tax included" %}</span></div>
</div>
