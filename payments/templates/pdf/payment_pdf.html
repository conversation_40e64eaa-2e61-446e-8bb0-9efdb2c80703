{% load util %}

<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>支払明細書</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Payment pdf */
        @font-face {
            font-family: 'Axis L';
            src: url('{{ axis_l_path }}') format('opentype');
            font-weight: 100;
            font-style: normal;
        }

        .container {
            max-width: 970px;
            padding: 0px 100px 100px;
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        table {
            width: 100%;
        }

        .table.table-header {
            margin: 30px 0 60px;
        }

        .table-header tr td, .table-quotation tr td {
            vertical-align: top;
            word-break: break-word;
        }

        .table-header tr td:nth-child(1) {
            padding-left: 0;
        }

        .table-header tr td:nth-child(2) {
            padding-right: 0;
        }

        .table-project tbody tr:last-child td {
            padding: 12px;
        }

        .table-quotation tr:first-child td {
            padding: 20px 0;
        }

        .table-quotation tr td {
            padding: 0;
        }

        .table-header tr td:nth-child(2), .table-quotation tr td:nth-child(2) {
            text-align: right;
        }

        .table-quotation tr div {
            height: 17px;
        }

        .table-quotation tr:first-child td {
            padding-bottom: 30px;
        }

        .export-wrap__header {
            margin-bottom: 50px;
        }

        .export-wrap__header-logo img {
            width: 80px;
            height: auto;
        }

        .export-wrap__content .heading--18 {
            margin-bottom: 8px;
        }

        .export-wrap__content .bodytext--13 {
            margin-bottom: 16px;
        }

        .table-project {
            border-collapse: collapse;
            width: 100%;
        }

        .table-project thead,
        .table-project tfoot {
            border-top: 0.5mm solid #53565A !important;
            border-bottom: 0.5mm solid #53565A !important;
        }

        table th,
        table td {
            color: #000000;
            line-height: 17px;
            padding: 8px;
            word-break: break-word;
            vertical-align: top;
        }

        .table-project thead tr th {
            border: none;
            min-width: 70px;
            text-align: left;
        }

        table tr th, table tr td {
            padding-top: 3px;
            padding-bottom: 3px;
        }

        .table-project tbody tr:not(:last-child) td,
        .table-project tfoot tr:not(:last-child) td {
            border-bottom: 0.5mm solid #D3D3D3;
        }

        .table-project td:nth-child(2) {
            text-align: right;
        }

        .table-project td, .table-project th {
            padding-left: 0px;
        }

        .letter-spacing-300 {
            letter-spacing: 4.8px;
        }

        .letter-spacing-250 {
            letter-spacing: 4px;
        }

        .letter-spacing-230 {
            letter-spacing: 3.68px;
        }

        .bodytext--13 {
            line-height: 20px;
            color: #000000 !important;
            font-weight: 300;
        }

        .heading--18 {
            line-height: 27px;
            color: #000000;
            font-weight: 400;
        }

        .heading--20 {
            line-height: 30px;
            color: #000000;
            font-weight: 400;
            word-break: break-word;
            white-space: pre-line;
        }

        .text-13q-r {
            font-size: 4.25mm;
            font-family: 'Axis L';
            font-weight: 600;
        }

        .text-16q-r {
            font-size: 5mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-9q-l {
            font-size: 3.25mm;
            font-family: 'Axis L';
        }

        .text-9q-r {
            font-size: 3.25mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-r {
            font-size: 2.75mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-l {
            font-size: 2.75mm;
            font-family: 'Axis L';
        }

        .moffer {
            padding-left: 8px !important;
        }

        .mt-4 {
            margin-top: 4px;
        }

        .mb-4 {
            margin-bottom: 4px;
        }
        /* End Payment pdf */
    </style>
</head>
<body>
    <div>
      <main>
        <div class="container">
            <div class="export-wrap">
                <div class="export-wrap__header">
                    <table class="table table-header">
                        <tbody>
                            <tr>
                                <td width="80%">
                                    <div class="heading--18 text-13q-r letter-spacing-250">
                                        {{ current_user.fullname }} 様
                                    </div>
                                </td>
                                <td width="20%">
                                    <div class="export-wrap__header-logo">
                                        <img src="data:;base64,{{ image_base64 }}" alt="SOREMO" width="24mm" height="13.75mm">
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table class="table table-quotation" style="font-family: 'Axis L';">
                        <tbody>
                            <tr>
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <div class="heading--18 text-16q-r letter-spacing-300">支払明細書</div>
                                    </div>
                                </td>
                                <td width="50%" class="text-7q-l">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13">発行日： </span>
                                            <span class="bodytext--13">{{ payment_request.created|format_short_date }}</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13">下記の通り、御支払い申し上げます。</span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13">株式会社ソレモ</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13"></span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13">登録番号：T5010601036833</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13">支払期日：</span>
                                        <span class="bodytext--13">{{ payment_request.payment_date|format_date_jp }}</span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13">〒 135-0064 東京都江東区青海 2-7-4</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13"></span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13">theSOHO1226</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-7q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13"></span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail mt-4">
                                            <span class="bodytext--13">tel: 03-6457-1780</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="export-wrap__content">
                {% for project in payment_info %}
                    <div class="export-wrap__content-wrap" style="page-break-inside: avoid">
                        <div class="heading--20 text-13q-r mb-4">{{ project.get_name_by_code_name }}</div>
                        <table class="table table-project">
                            <thead>
                                <tr class="text-9q-r">
                                    <th width="18%">相手先</th>
                                    <th width="14%">金額（税込）</th>
                                    <th width="11%">取引成立日</th>
                                    <th width="11%">検収日</th>
                                    <th width="22%">シーン</th>
                                    <th width="14%">ロール</th>
                                </tr>
                            </thead>
                            <tbody class="text-9q-l">
                            {% for offer in project.product_offers.all %}
                                <tr>
                                    {% if current_user != offer.admin %}
                                    <td class="text-9q-l">{{ offer.admin.get_display_name|default_if_none:"" }}</td>
                                    <td class="text-9q-l">¥{{ offer.reward|display_currency }}</td>
                                    {% else %}
                                    <td class="text-9q-l">　{{ offer.creator.get_display_name|default_if_none:"" }}</td>
                                        {% if offer.reward %}
                                        <td class="text-9q-l">- ¥{{ offer.reward|display_currency }}</td>
                                        {% else %}
                                        <td class="text-9q-l">¥{{ offer.reward|display_currency }}</td>
                                        {% endif %}
                                    {% endif %}
                                    <td class="text-9q-l">{{ offer.accept_time|format_short_date }}</td>
                                    <td class="text-9q-l">{{ offer.check_time|format_short_date }}</td>
                                    <td class="text-7q-l">{{ offer.scenes }}</td>
                                    <td class="text-7q-l">{{ offer.custom_contract_dsp }}</td>
                                </tr>
                                {% if forloop.last %}
                                <tr>
                                    <td colspan="6"></td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                            </tbody>
                            {% if forloop.last %}
                            <tfoot class="text-9q-l">
                                <tr>
                                    <td>合計</td>
                                    <td>¥{{ payment_request.amount|display_currency }}</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </tfoot>
                            {% endif %}
                        </table>
                    </div>
                {% endfor %}
                </div>
            </div>
        </div>
      </main>
    </div>
</body>
</html>
