<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        @font-face {
            font-family: 'Axis L';
            src: url('/code/app/static/fonts/AxisL.otf') format('opentype');
        }

        table {
            width: 100%;
        }

        .bodytext--13 {
            line-height: 20px;
            color: #000000 !important;
            font-weight: 300;
        }

        .export-wrap__footer .bodytext--13 {
            text-align: right;
        }

        .text-9q-l {
            font-size: 3.25mm;
            font-family: 'Axis L';
        }

        .footer-line {
            border-top: 0.4mm solid #53565A;
            margin-top: 0px;
            margin-bottom: 0px;
            border-bottom-width: 0px;
            border-left-width: 0px;
            border-right-width: 0px;
        }

        .container {
            padding-left: 100px;
            padding-right: 100px;
            max-width: 970px;
            margin-right: auto;
            margin-left: auto;
        }

        .table-footer tr td {
            vertical-align: top;
            word-break: break-word;
        }

        .table-footer tr td:nth-child(1) {
            padding-left: 0;
        }

        .table-footer tr td:nth-child(1) .bodytext--13 {
            text-align: left;
        }

        .table-footer tr td:nth-child(2) {
            padding-right: 0;
        }

        .table-footer tr td:nth-child(2) .bodytext--13 {
            text-align: right;
        }
    </style>
    <script charset="utf-8">
        function replaceText(selector, text, newText, flags) {
            var matcher = new RegExp(text, flags);
            var elems = document.querySelectorAll(selector);

            for (i = 0; i < elems.length; i++)
                elems[i].innerHTML = elems[i].innerHTML.replace(matcher, newText);
        }

        function replaceParams() {
            var url = window.location.href.replace(/#$/, ""); // Remove last # if exist
            var params = (url.split("?")[1] || "").split("&");
            for (var i = 0; i < params.length; i++) {
                var param = params[i].split("=");
                var key = param[0];
                var value = param[1] || '';
                var regex = new RegExp('{' + key + '}', 'g');
                replaceText('div .bodytext--13', regex, decodeURIComponent(decodeURIComponent(value)));
            }
        }
  </script>
</head>

<body onload="replaceParams()">
    <div class="export-wrap__footer text-9q-l container">
        <hr class="footer-line">

        <div class="export-wrap__footer">
            <table class="table table-footer">
                <tbody>
                    <tr>
                        <td width="80%">
                            <div class="bodytext--13">振込先 : {bank}　{bank_branch}（支店番号 {bank_branch_number}）　{account_type} {account_number}　{account_name}</div>
                        </td>
                        <td width="20%">
                            <div class="bodytext--13">https://soremo.jp</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>

</html>
