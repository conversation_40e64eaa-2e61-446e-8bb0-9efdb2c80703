# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 21/12/2021
import os
import base64

import pdfkit
from pyvirtualdisplay import Display


def get_image_file_as_base64_data(filepath):
    with open(filepath, 'rb') as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def pdfkit_generate_pdf(html, file_name, options):
    display = Display()
    display.start()
    pdfkit.from_string(html, file_name, options=options)
    os.kill(display.pid, 9)
