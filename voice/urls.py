"""voice URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/1.11/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  re_path(r'^$', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  re_path(r'^$', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.conf.urls import url, include
    2. Add a URL to urlpatterns:  re_path(r'^blog/', include('blog.urls'))
"""
from django.urls import re_path, include
from django.contrib import admin
from django.conf import settings
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.views.i18n import JavaScriptCatalog

from . import views

urlpatterns = [
    re_path(r'^admin/', admin.site.urls),
    re_path(r'^ping', views.ping),
    re_path(r'', include(("app.urls", 'app'), namespace='app')),
    re_path(r'accounts/', include(("accounts.urls", 'accounts'), namespace='accounts')),
    re_path('oauth/', include(("social_django.urls", 'social'), namespace='social')),
    re_path(r'landingPage/', include(("landingPage.urls", 'landingPage'), namespace='landingPage')),
    re_path(r'aoiumito/', include(("aoiumito.urls", 'aoiumito'), namespace='aoiumito')),
    re_path(r'payments/', include(("payments.urls", 'payments'), namespace='payments')),
    re_path(r'mileages/', include(("mileages.urls", 'mileages'), namespace='mileages')),
    re_path(r'common/', include(("common.urls", 'common'), namespace='common'))
]
urlpatterns += staticfiles_urlpatterns()
js_info_dict = {
    'domain': 'djangojs',
    'packages': ['app', 'accounts', 'landingPage', 'aoiumito', 'payments', 'mileages'],
}
urlpatterns += [re_path(r'^jsi18n/$', JavaScriptCatalog.as_view(packages=js_info_dict['packages']), name='javascript-catalog'), ]

if settings.DEBUG:
    import debug_toolbar
    urlpatterns += [re_path(r'^__debug__/', include(debug_toolbar.urls))]
