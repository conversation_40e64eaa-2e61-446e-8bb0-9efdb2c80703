"""
Django's settings for voice project.

Generated by 'django-admin startproject' using Django 1.11.3.

For more information on this file, see
https://docs.djangoproject.com/en/1.11/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.11/ref/settings/
"""

import os

import environ

# http://django-environ.readthedocs.io/en/latest/
# https://github.com/joke2k/django-environ
env = environ.Env()
try:
    env.read_env('.env')  # .envファイルの値を呼び出す
except IOError:
    pass

DEBUG = eval(env('DEBUG', default='False'))

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# LOCALE_PATHS = (
#     os.path.join(BASE_DIR, 'locale'),  # underneath project root folder
# )

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.11/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'r_)1c2n!vm&idpi1&&=2r40gzvpbw(5@)7n=xj!*tv1ud-a+f)'

# SECURITY WARNING: don't run with debug turned on in production!

DJANGO_READ_ENV_FILE = DEBUG

ALLOWED_HOSTS = ['*']

# Application definition
INSTALLED_APPS = [
    'corsheaders',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'widget_tweaks',
    'bootstrap3',
    # 'debug_toolbar',
    'storages',
    'django_extensions',
    'app',  # アプリ本体
    'django_user_agents',
    'accounts',
    'channels',
    'social_django',
    'landingPage',
    'aoiumito',
    'payments',
    'mileages',
    'common',
    'compressor',
    'rest_framework',
    'rest_framework.authtoken',  # Django REST Framework Token認証用
    'django_dump_die',  # Development debugging tool
    # 認証
]

if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 'debug_toolbar.middleware.DebugToolbarMiddleware',
    'django_user_agents.middleware.UserAgentMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'social_django.middleware.SocialAuthExceptionMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django_dump_die.middleware.DumpAndDieMiddleware',  # Development debugging middleware
]

if DEBUG:
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware',]

ROOT_URLCONF = 'voice.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')]
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'social_django.context_processors.backends',
                'social_django.context_processors.login_redirect'
            ],
            'builtins': [
                'bootstrap3.templatetags.bootstrap3',
            ],
        },
    },
]

BOOTSTRAP3 = {
    'formset_renderers': {
        'default': 'bootstrap3.renderers.FormsetRenderer',
        'tabular': 'app.django-bootstrap3-tabular.ConcordiaFormsetRenderer',
    },
    'field_renderers': {
        'default': 'bootstrap3.renderers.FieldRenderer',
        'inline': 'bootstrap3.renderers.InlineFieldRenderer',
        'tabular': 'app.django-bootstrap3-tabular.ConcordiaFieldRenderer',
    },
}

WSGI_APPLICATION = 'voice.wsgi.application'

# Database
# https://docs.djangoproject.com/en/1.11/ref/settings/#databases

DATABASES = {}
DB_NAME = env('DB_NAME')
DB_USER_NAME = env('DB_USER_NAME')
DB_PASSWORD = env('DB_PASSWORD')
DB_HOST = env('DB_HOST')
DB_PORT = env('DB_PORT', default=3306)
if DB_NAME and DB_USER_NAME and DB_PASSWORD and DB_HOST:
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': DB_NAME,
        'USER': DB_USER_NAME,
        'PASSWORD': DB_PASSWORD,
        'HOST': DB_HOST,
        'PORT': DB_PORT,
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'; SET NAMES 'utf8mb4' COLLATE 'utf8mb4_unicode_ci'",
            'charset': env('DB_CHARSET', default='utf8mb4'),
        },
    }
else:
    # ルート直下の.env に設定を記載してください。
    # mysqlなら
    # DATABASE_URL=mysql://{ユーザ名}:{パスワード}@=127.0.0.1:3306/{DB名}
    # 詳しくは下記参照
    # http://django-environ.readthedocs.io/en/latest/
    DATABASES['default'] = env.db()
    DATABASES['default']['OPTIONS'] = {
        'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        'charset': 'utf8mb4',
    }
# mysql -h voice.cwxldbtrlhdg.ap-northeast-1.rds.amazonaws.com -P 3306 -u voicetokyo -p

# Password validation
# https://docs.djangoproject.com/en/1.11/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/1.11/topics/i18n/

LANGUAGE_CODE = 'ja'

LANGUAGES = [
    ('ja', 'Japanese'),
]

TIME_ZONE = 'Asia/Tokyo'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.11/howto/static-files/
# STATIC_URL = '/static/'

# django-storagesの設定
# http://django-storages.readthedocs.io/
# https://github.com/jschneier/django-storages
# DEFAULT_FILE_STORAGE = 'voice.storages.backends.s3boto.S3BotoStorage'
STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3.S3Storage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}
AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME')
# AWS MediaConvert settings
AWS_CONVERTED_MEDIA_BUCKET_NAME = env('AWS_CONVERTED_MEDIA_BUCKET_NAME', default='soremo-converted-media')
AWS_S3_HOST = env('AWS_S3_HOST')
# STATICFILES_STORAGE = 'storages.backends.s3boto.S3BotoStorage'
# STATIC_URL = 'http://' + AWS_S3_HOST + '/' + AWS_STORAGE_BUCKET_NAME + '/'
# STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
# if DEBUG is False:
#     STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, 'static/')
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
]

COMPRESS_ENABLED = eval(env('COMPRESS_ENABLED', default='True'))

ADMIN_MEDIA_PREFIX = STATIC_URL + 'admin/'
AWS_S3_FILE_OVERWRITE = False

if DEBUG:
    AWS_S3_USE_SSL = False

    DEBUG_TOOLBAR_PANELS = [
        'debug_toolbar.panels.versions.VersionsPanel',
        'debug_toolbar.panels.timer.TimerPanel',
        'debug_toolbar.panels.settings.SettingsPanel',
        'debug_toolbar.panels.headers.HeadersPanel',
        'debug_toolbar.panels.request.RequestPanel',
        'debug_toolbar.panels.sql.SQLPanel',
        'debug_toolbar.panels.staticfiles.StaticFilesPanel',
        'debug_toolbar.panels.templates.TemplatesPanel',
        'debug_toolbar.panels.cache.CachePanel',
        'debug_toolbar.panels.signals.SignalsPanel',
        'debug_toolbar.panels.logging.LoggingPanel',
        'debug_toolbar.panels.redirects.RedirectsPanel',
    ]

    DEBUG_TOOLBAR_CONFIG = {
        'SHOW_TEMPLATE_CONTEXT': True,
    }

INTERNAL_IPS = ('127.0.0.1',)

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://" + env('REDIS_HOST') + ":6379/voice",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}
if DEBUG:
    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s',
                'datefmt': '%d/%m/%Y %H:%M:%S',
            },
        },
        'handlers': {
            'file': {
                'level': 'INFO',
                'class': 'logging.FileHandler',
                'filename': os.path.join(BASE_DIR, 'logs/infos.log'),
                'formatter': 'verbose'
            },
            'console': {
                'level': 'DEBUG',
                'class': 'logging.StreamHandler',
                'formatter': 'verbose',
            },
        },
        'root': {
            'handlers': ['file'],
            'level': 'INFO',
        },
        'loggers': {
            'django': {
                'handlers': ['file'],
                'level': 'INFO',
                'propagate': True,
            },
            'django.db.backends': {
                'handlers': ['console'],
                'level': 'DEBUG',
            },
        },
    }
else:
    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
            },
        },
        'handlers': {
            'file': {
                'level': 'INFO',
                'class': 'logging.FileHandler',
                'filename': os.path.join(BASE_DIR, 'logs/infos.log'),
                'formatter': 'verbose'
            },
        },
        'root': {
            'handlers': ['file'],
            'level': 'INFO',
        },
        'loggers': {
            'django': {
                'handlers': ['file'],
                'level': 'INFO',
                'propagate': True,
            },
        },
    }

SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"
USER_AGENTS_CACHE = 'default'
# ログイン
SITE_ID = 1
LOGIN_URL = '/accounts/login'
LOGIN_REDIRECT_URL = '/'
LOGIN_ERROR_URL = '/accounts/login'
AUTH_USER_MODEL = 'accounts.AuthUser'
AUTHENTICATION_BACKENDS = (
    # 'social.backends.twitter.TwitterOAuth',
    'social_core.backends.facebook.FacebookOAuth2',
    'social_core.backends.google.GoogleOAuth2',
    'django.contrib.auth.backends.ModelBackend',
)

# メール
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_USE_TLS = env('EMAIL_USE_TLS', default=True)
EMAIL_HOST = env('EMAIL_HOST')
EMAIL_HOST_USER = env('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD')
EMAIL_PORT = env('EMAIL_PORT', default=587)
PASSWORD_RESET_TIMEOUT_DAYS = 1
# SEND_GRID_API_KEY='*********************************************************************'

REDIS_HOST = env('REDIS_HOST')
REDIS_PORT = env('REDIS_PORT')
BROKER_URL = 'redis://' + REDIS_HOST + ':' + REDIS_PORT + '/0'
BROKER_TRANSPORT_OPTIONS = {'visibility_timeout': 3600}
CELERY_BROKER_URL = 'redis://' + REDIS_HOST + ':' + REDIS_PORT + '/0'
CELERY_RESULT_BACKEND = 'redis://' + REDIS_HOST + ':' + REDIS_PORT + '/0'
AWS_QUERYSTRING_EXPIRE = *********  # 5years
HOST = env('HOST', default='https://soremo.jp')
AWS_QUERYSTRING_AUTH = True
ASGI_APPLICATION = "voice.routing.application"
X_FRAME_OPTIONS = "SAMEORIGIN"
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [(REDIS_HOST, 6379)],
        },
    },
}

SCENE_PER_PAGE = env('SCENE_PER_PAGE', default=8)
SCENE_PER_UPDATE_PAGE = env('SCENE_PER_UPDATE_PAGE', default=4)
PROJECT_PER_PAGE = env('PROJECT_PER_PAGE', default=10)
PRODUCT_SCENE_LOADED = env('PRODUCT_SCENE_LOADED', default=2)

# AcrCloud config
ACR_HOST = env('ACR_HOST', default='')
ACR_ACCESS_KEY = env('ACR_ACCESS_KEY', default='')
ACR_ACCESS_SECRET = env('ACR_ACCESS_SECRET', default='')

SOCIAL_AUTH_URL_NAMESPACE = 'social'
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = env('SOCIAL_AUTH_GOOGLE_OAUTH2_KEY', None)
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = env('SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET', None)

SOCIAL_AUTH_FACEBOOK_KEY = env('SOCIAL_AUTH_FACEBOOK_KEY', None)
SOCIAL_AUTH_FACEBOOK_SECRET = env('SOCIAL_AUTH_FACEBOOK_SECRET', None)
SOCIAL_AUTH_FACEBOOK_SCOPE = ['email']
SOCIAL_AUTH_FACEBOOK_PROFILE_EXTRA_PARAMS = {
    'fields': 'id, name, email, age_range',
}

SOCIAL_AUTH_REDIRECT_IS_HTTPS = True

SOCIAL_AUTH_PIPELINE = (
    'social_core.pipeline.social_auth.social_details',
    'social_core.pipeline.social_auth.social_uid',
    'social_core.pipeline.social_auth.social_user',
    'social_core.pipeline.user.get_username',
    'social_core.pipeline.social_auth.associate_by_email',
    'social_core.pipeline.user.create_user',
    'social_core.pipeline.social_auth.associate_user',
    'social_core.pipeline.social_auth.associate_by_email',
    'social_core.pipeline.social_auth.associate_user',
    'social_core.pipeline.social_auth.load_extra_data',
    'social_core.pipeline.user.user_details',
)

CUSTOMER_PK = env('CUSTOMER_PK', None)
CUSTOMER_SK = env('CUSTOMER_SK', None)
STRIPE_CURRENCY = 'jpy'
MAX_AMOUNT = 99999999
MIN_AMOUNT = 100

# update old/new creator is_direct value if edit using: update_is_direct_for_artist
ARTIST_MAIL_LANDINGPAGE = '<EMAIL>'
ACR_ACCESS_TOKEN = env('ACR_ACCESS_TOKEN', None)
ACR_FS_KEY = env('ACR_FS_KEY', None)

DATA_UPLOAD_MAX_MEMORY_SIZE = 2 * 1024 * 1024 * 1024
URL_FILE_S3 = 'https://%s.s3.amazonaws.com/%s'


CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # ReactアプリのURL
    "http://127.0.0.1:3000",
    "https://soremo.jp",
]

CSRF_TRUSTED_ORIGINS = [
    "http://localhost:3000",  # ReactアプリのURL
    "https://soremo.jp",
    "https://pre-prod.soremo.jp",
    "https://develop.soremo.jp",
    "https://aoiumito.jp"
]

# テスト用にCSRFを無効化（セキュリティリスクがあるため本番環境では非推奨）
# CSRF_COOKIE_SECURE = False

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_HEADERS = [
    "authorization",
    "content-type",
    "accept",
    "origin",
    "x-csrftoken",
    "x-requested-with",
]

CORS_ALLOW_METHODS = [
    'GET',
    'OPTIONS',
    'HEAD',
    'PATCH',
    'POST',
    'PUT',
    'DELETE',
]

VIVLIOSTYLE_SERVER = env('VIVLIOSTYLE_SERVER', default='http://vivliostyle.soremo.jp')

