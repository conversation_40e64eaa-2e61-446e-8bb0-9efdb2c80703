import os
from django.shortcuts import redirect
from datetime import datetime

def parse_now_string():
    return datetime.now().strftime('%Y/%m/%d %H:%M:%S')

def check_host_aoiumito(get_response):
    def middleware(request):
        current_host = request.get_host()
        if current_host != os.getenv('AOIUMITO_HOST'):
            return redirect('app:warning')
        else:
            return get_response(request)

    return middleware
