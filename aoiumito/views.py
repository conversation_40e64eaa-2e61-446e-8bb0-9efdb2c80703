# 01EG89H6SS2141VNGDDEHBMV4Q
import json
import logging

from django.shortcuts import render
from django.views.generic.base import TemplateView, View

from aoiumito.form import ContactForm
from aoiumito.task import send_mail_contact


class AoiumitoView(TemplateView):
    template_name = "index.html"


class EquipmentListView(TemplateView):
    template_name = "equipmentlist.html"


class ContactView(View):
    FUNC_STEP = {
        'entry': 'render_comfirm',
        'confirm': 'send_mail'
    }

    def post(self, request):
        step = request.POST.get('step', 'entry')
        form = ContactForm(request.POST)

        try:
            # When form not is_valid render entry contact
            if not form.is_valid():
                return render(request, 'contact.html', {'form': form})

            return getattr(self, self.FUNC_STEP.get(step))(request, form)
        except Exception as e:
            logging.error(e)
            return self.__render_error(request)

    def get(self, request):
        form = ContactForm()
        return render(request, 'contact.html', {'form': form, 'default_message': True})

    def render_comfirm(self, request, form):
        context = {'form': form}
        return render(request, 'confirm.html', context)

    def send_mail(self, request, form):
        try:
            params = json.dumps({
                'name': form.cleaned_data.get('name'),
                'phone': form.cleaned_data.get('phone'),
                'email': form.cleaned_data.get('email'),
                'comment': form.cleaned_data.get('comment'),
                'ip': self.request.META.get('REMOTE_ADDR'),
                'host': self.request.get_host(),
                'ua': self.request.META['HTTP_USER_AGENT'],
            })

            send_mail_contact.delay(params=params)

            return render(request, 'complete.html')
        except Exception as e:
            logging.error(e)
            return self.__render_error(request)

    def __render_error(self, request):
        return render(request, 'error.html')
