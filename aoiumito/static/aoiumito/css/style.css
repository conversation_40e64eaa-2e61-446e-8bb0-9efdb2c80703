/*! web v.0.0.0 2015-02-20 */
@charset "UTF-8";
/*! hirorock */
/*! normalize.css v3.0.2 | MIT License | git.io/normalize */
/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-family: sans-serif;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */ }

/**
 * Remove default margin.
 */
body {
  margin: 0; }

/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block; }

/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */
audio,
canvas,
progress,
video {
  display: inline-block;
  /* 1 */
  vertical-align: baseline;
  /* 2 */ }

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0; }

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/11, Safari, and Firefox < 22.
 */
[hidden],
template {
  display: none; }

/* Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background-color: transparent; }

/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */
a:active,
a:hover {
  outline: 0; }

/* Text-level semantics
   ========================================================================== */
/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted; }

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */
b,
strong {
  font-weight: bold; }

/**
 * Address styling not present in Safari and Chrome.
 */
dfn {
  font-style: italic; }

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0; }

/**
 * Address styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000; }

/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%; }

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

/* Embedded content
   ========================================================================== */
/**
 * Remove border when inside `a` element in IE 8/9/10.
 */
img {
  border: 0; }

/**
 * Correct overflow not hidden in IE 9/10/11.
 */
svg:not(:root) {
  overflow: hidden; }

/* Grouping content
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari.
 */
figure {
  margin: 1em 40px; }

/**
 * Address differences between Firefox and other browsers.
 */
hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0; }

/**
 * Contain overflow in all browsers.
 */
pre {
  overflow: auto; }

/**
 * Address odd `em`-unit font size rendering in all browsers.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em; }

/* Forms
   ========================================================================== */
/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */
/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  /* 1 */
  font: inherit;
  /* 2 */
  margin: 0;
  /* 3 */ }

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible; }

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none; }

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */ }

/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default; }

/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
input {
  line-height: normal; }

/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 */
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */ }

/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto; }

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  /* 2 */
  box-sizing: content-box; }

/**
 * Remove inner padding and search cancel button in Safari and Chrome on OS X.
 * Safari (but not Chrome) clips the cancel button when the search input has
 * padding (and `textfield` appearance).
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em; }

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  /* 2 */ }

/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */
textarea {
  overflow: auto; }

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold; }

/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0; }

td,
th {
  padding: 0; }

html {
  overflow-y: scroll;
  -webkit-touch-callout: none; }

img {
  -ms-interpolation-mode: bicubic; }

.clearfix,
.cf {
  zoom: 1; }
  .clearfix:before, .clearfix:after,
  .cf:before,
  .cf:after {
    content: "";
    display: table; }
  .clearfix:after,
  .cf:after {
    clear: both; }

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden; }

.centered {
  position: relative;
  overflow: hidden; }

.centered ul {
  position: relative;
  left: 50%;
  float: left;
  padding-left: 0; }

.centered ul li {
  position: relative;
  left: -50%;
  float: left;
  padding: 0;
  text-indent: 0;
  list-style: none; }

.none,
.hidden {
  display: none; }

.invisible {
  visibility: hidden; }

.list-square {
  list-style: inside square; }

.list-circle {
  list-style: inside circle; }

.list-disc {
  list-style: inside disc; }

.unlist {
  padding: 0;
  margin: 0;
  list-style: none; }

body {
  font-family: "游ゴシック", YuGothic, "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
  min-width: 1140px;
  /* background: url(../../aoiumito/images/); */
  line-height: 1.8em;
  position: relative; }

a {
  color: inherit;
  text-decoration: none; }
  a.btn {
    display: block;
    height: 50px;
    background: #036ea7;
    color: #FFF;
    line-height: 50px;
    overflow: hidden;
    text-align: center; }
    a.btn i {
      font-style: normal;
      margin-right: 1em;
      font-size: 12px; }
    a.btn:hover {
      background: #53a2c3; }

h1, h2, h3, h4, h5, h6 {
  margin: 0; }

ul {
  margin: 0; }

.nav-page {
  padding-top: 28px;
  padding-bottom: 2px;
  margin-top: 0px auto;
  position: absolute;
  z-index: 777;
  width: 100%; }
  .nav-page.fixed {
    position: fixed;
    top: 0 !important;
    background-color: #FFF; }
  .nav-page ul {
    padding: 0;
    list-style: none;
    width: 1000px;
    margin: 0 auto;
    zoom: 1; }
    .nav-page ul:before, .nav-page ul:after {
      content: "";
      display: table; }
    .nav-page ul:after {
      clear: both; }
    .nav-page ul li {
      float: left;
      height: 20px;
      text-indent: -7777px;
      background-image: url(../../aoiumito/images/menu/menu.png);
      background-repeat: no-repeat;
      margin-left: 81px; }
      .nav-page ul li.nav-top {
        display: none;
        width: 45px;
        background-position: 0 center; }
      .nav-page ul li.nav-concept {
        margin-left: 0;
        width: 108px;
        background-position: -147px center; }
      .nav-page ul li.nav-studio {
        width: 88px;
        background-position: -347px center; }
      .nav-page ul li.nav-access {
        width: 84px;
        background-position: -537px center; }
      .nav-page ul li.nav-contact {
        width: 111px;
        background-position: -709px center; }
      .nav-page ul li.nav-price {
        width: 66px;
        background: url(../../aoiumito/images/menu/menu_price.png) center 1px no-repeat; }
      .nav-page ul li.nav-equipment {
        width: 135px;
        left: 100px;
        background: url(../../aoiumito/images/menu/menu_equipment.png) center 1px no-repeat; }
      .nav-page ul li a {
        height: 100%;
        width: 100%;
        display: block;
        padding-bottom: 2px;
        position: relative; }
        .nav-page ul li a .under_l {
          height: 3px;
          width: 0%;
          background: #036ea7;
          position: absolute;
          bottom: -3px;
          display: block;
          -webkit-transition: width 0.18s linear;
          -ms-transition: width 0.18s linear;
          -moz-transition: width 0.18s linear;
          -o-transition: width 0.18s linear;
          transition: width 0.18s linear; }
        .nav-page ul li a.active > .under_l, .nav-page ul li a:hover > .under_l {
          width: 100%; }

.content {
  width: 1000px;
  height: 650px;
  top: 50%;
  position: absolute;
  margin-top: -325px;
  margin-left: -500px;
  left: 50%;
  color: #036ea7; }
  .content h3 {
    text-indent: -99999px;
    height: 40px; }

.page-section {
  display: block;
  width: 100%;
  min-height: 700px;
  min-width: 1140px;
  position: relative; }

#page-top {
  background: url(../../aoiumito/images/logo.png) center center no-repeat; }
  #page-top h1 {
    visibility: hidden; }
  #page-top .sns {
    background: url(../../aoiumito/images/ft.png) center center no-repeat;
    width: 90px;
    height: 42px;
    zoom: 1; }
    #page-top .sns:before, #page-top .sns:after {
      content: "";
      display: table; }
    #page-top .sns:after {
      clear: both; }
    #page-top .sns a {
      display: block;
      width: 100%;
      height: 100%;
      overflow: hidden;
      text-indent: -7777px; }
      #page-top .sns a.facebook {
        float: left;
        width: 45px; }
      #page-top .sns a.twitter {
        float: right;
        width: 43px; }

#page-concept .content {
  background: url(../../aoiumito/images/concept_text.png) center center no-repeat; }
  #page-concept .content h3 {
    background: url(../../aoiumito/images/concept.png) center left no-repeat; }

#page-studio {
  z-index: 1;
  position: relative; }
  #page-studio #pages {
    width: 100%;
    position: absolute;
    top: 0;
    height: 100%; }
    #page-studio #pages.fixed {
      position: fixed;
      top: 0; }
      #page-studio #pages.fixed .page-studio-child {
        position: absolute;
        top: 0; }
    #page-studio #pages .page-studio-child {
      overflow: inherit;
      position: absolute;
      min-height: 700px;
      height: 100%;
      width: 100%;
      min-width: 1140px; }
      #page-studio #pages .page-studio-child .content {
        width: 1000px; }
        #page-studio #pages .page-studio-child .content h3 {
          background: url(../../aoiumito/images/studio.png) center left no-repeat; }
        #page-studio #pages .page-studio-child .content h5 {
          text-indent: -9999px;
          height: 60px;
          width: 240px;
          position: absolute;
          top: 50px; }
        #page-studio #pages .page-studio-child .content .icons {
          position: absolute;
          bottom: 0;
          width: 100%;
          clear: both; }
          #page-studio #pages .page-studio-child .content .icons .icon-bottom {
            clear: both; }
        #page-studio #pages .page-studio-child .content ul.icon-ul.icon-top {
          margin-bottom: 10px; }
        #page-studio #pages .page-studio-child .content ul.icon-ul li {
          padding-top: 75px;
          width: 170px;
          text-align: center;
          font-size: 13px;
          background-position: top center;
          background-repeat: no-repeat; }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-1 {
            background-image: url(../../aoiumito/images/studio_icon01.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-2 {
            background-image: url(../../aoiumito/images/studio_icon02.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-3 {
            background-image: url(../../aoiumito/images/studio_icon03.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-4 {
            background-image: url(../../aoiumito/images/studio_icon04.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-5 {
            background-image: url(../../aoiumito/images/studio_icon05.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-6 {
            background-image: url(../../aoiumito/images/studio_icon06.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-7 {
            background-image: url(../../aoiumito/images/studio_icon07.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-8 {
            background-image: url(../../aoiumito/images/studio_icon08.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-9 {
            background-image: url(../../aoiumito/images/studio_icon09.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-10 {
            background-image: url(../../aoiumito/images/studio_icon10.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-11 {
            background-image: url(../../aoiumito/images/studio_icon11.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-12 {
            background-image: url(../../aoiumito/images/studio_icon12.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-13 {
            background-image: url(../../aoiumito/images/studio_icon13.min.png); }
          #page-studio #pages .page-studio-child .content ul.icon-ul li.icon-14 {
            background-image: url(../../aoiumito/images/studio_icon14.min.png); }
      #page-studio #pages .page-studio-child#page-studio-rec {
        background: #FFF url(../../aoiumito/images/bg-assets/s_studio01.png) center center no-repeat;
        z-index: 50; }
        #page-studio #pages .page-studio-child#page-studio-rec .content h5 {
          background: url(../../aoiumito/images/studio_text01.png) left 0 no-repeat; }
      #page-studio #pages .page-studio-child#page-studio-control {
        background: #FFF url(../../aoiumito/images/bg-assets/s_studio02.png) center center no-repeat;
        z-index: 40; }
        #page-studio #pages .page-studio-child#page-studio-control .content h5 {
          background: url(../../aoiumito/images/studio_text02.png) left 0 no-repeat; }
      #page-studio #pages .page-studio-child#page-studio-floor {
        background: #FFF url(../../aoiumito/images/bg-assets/s_studio03.png) center center no-repeat;
        z-index: 30; }
        #page-studio #pages .page-studio-child#page-studio-floor .content h5 {
          background: url(../../aoiumito/images/studio_text03.png) left 0 no-repeat; }
        #page-studio #pages .page-studio-child#page-studio-floor .content .icons-square {
          position: absolute;
          bottom: 0;
          left: 100px; }
          #page-studio #pages .page-studio-child#page-studio-floor .content .icons-square li {
            width: 90px;
            padding: 0px;
            height: 70px; }
            #page-studio #pages .page-studio-child#page-studio-floor .content .icons-square li.icon-s-1 {
              background-image: url(../../aoiumito/images/floor_icon1.png); }
            #page-studio #pages .page-studio-child#page-studio-floor .content .icons-square li.icon-s-2 {
              background-image: url(../../aoiumito/images/floor_icon2.png); }
            #page-studio #pages .page-studio-child#page-studio-floor .content .icons-square li.icon-s-3 {
              background-image: url(../../aoiumito/images/floor_icon3.png); }
            #page-studio #pages .page-studio-child#page-studio-floor .content .icons-square li.icon-s-4 {
              background-image: url(../../aoiumito/images/floor_icon4.png); }
            #page-studio #pages .page-studio-child#page-studio-floor .content .icons-square li.icon-s-5 {
              background-image: url(../../aoiumito/images/floor_icon5.png); }
        #page-studio #pages .page-studio-child#page-studio-floor .content .madori {
          height: 480px;
          width: 393px;
          position: absolute;
          right: 0;
          bottom: 0;
          background: url(../../aoiumito/images/floor_madori.png) center no-repeat; }
    #page-studio #pages .studio-navigation_list {
      position: absolute;
      right: 100px;
      z-index: 777;
      list-style: none;
      padding: 0;
      overflow: hidden; }
      #page-studio #pages .studio-navigation_list li {
        margin: 0;
        padding: 0;
        text-indent: -777px; }
        #page-studio #pages .studio-navigation_list li a {
          height: 20px;
          width: 20px;
          background: url(../../aoiumito/images/button_off.png) center no-repeat;
          display: block;
          margin-bottom: 15px; }
          #page-studio #pages .studio-navigation_list li a.active {
            background: url(../../aoiumito/images/button_on.png) center no-repeat; }
    #page-studio #pages .studio-navigation {
      position: relative; }

#cont-background {
  min-width: 1140px;
  margin-left: -570px;
  left: 50%;
  position: fixed;
  top: 0;
  z-index: 0; }
  #cont-background .js-scene {
    position: absolute;
    top: 0;
    background: #FFF; }

#page-price {
  background: #FFF url(../../aoiumito/images/bg-assets/s_studio04.png) center center no-repeat; }
  #page-price .content h3 {
    background: url(../../aoiumito/images/price.png) left center no-repeat; }
  #page-price .content .flame {
    position: absolute;
    bottom: 0;
    left: 0;
    background-image: url(../../aoiumito/images/price_flame.png);
    height: 357px;
    width: 518px; }
    #page-price .content .flame .strong {
      display: none; }
    #page-price .content .flame p {
      position: absolute;
      top: 135px;
      left: 80px; }

#page-equipment {
  background: url(../../aoiumito/images/bg-assets/s_equipment.png) center center no-repeat; }
  #page-equipment .content h3 {
    background: url(../../aoiumito/images/equipment.png) center left no-repeat; }
  #page-equipment .content .flame {
    position: absolute;
    margin-top: -80px;
    margin-left: -270px;
    left: 50%;
    top: 50%;
    background: url(../../aoiumito/images/equipment_text.png) center left no-repeat;
    width: 536px;
    height: 229px; }
    #page-equipment .content .flame .strong {
      display: none; }
    #page-equipment .content .flame .btn {
      position: absolute;
      top: 120px;
      width: 370px;
      left: 85px; }

#page-access {
  background: url(../../aoiumito/images/bg-assets/s_access.png) center 40% no-repeat; }
  #page-access .content h3 {
    background: url(../../aoiumito/images/access.jpg) center left no-repeat; }
  #page-access .content .address {
    position: absolute;
    bottom: 25px;
    left: 90px; }
    #page-access .content .address .btn-facebook {
      height: 30px;
      width: 70%;
      line-height: 30px;
      margin-bottom: 10px;
      margin-top: 10px;
      background-color: #009fd8; }
      #page-access .content .address .btn-facebook:hover {
        background-color: #6ec8e9; }
    #page-access .content .address .address-info .btn {
      margin-top: 10px; }
  #page-access .content .map {
    width: 484px;
    height: 512px;
    position: absolute;
    bottom: 0;
    right: 0;
    background: url(../../aoiumito/images/access_map.png) center left no-repeat; }

#page-contact {
  background: url(../../aoiumito/images/bg-assets/s_contact.png) center center no-repeat; }
  #page-contact .content h3 {
    background: url(../../aoiumito/images/contact.png) center left no-repeat; }
  #page-contact .content .flame {
    width: 532px;
    height: 295px;
    bottom: 40px;
    position: absolute;
    left: 261px;
    background: url(../../aoiumito/images/contact_image.png) center left no-repeat; }
    #page-contact .content .flame .flame-child {
      position: absolute;
      left: 60px;
      top: 75px; }
      #page-contact .content .flame .flame-child .btn {
        margin-top: 10px;
        width: 370px; }

.foot {
  position: relative;
  padding-top: 60px;
  padding-bottom: 50px; }
  .foot .logo {
    text-indent: -99999px;
    background: url(../../aoiumito/images/logo.png) center center no-repeat;
    height: 94px; }
  .foot .copy {
    background: url(../../aoiumito/images/copyright.png) center center no-repeat;
    text-indent: -7777px;
    overflow: hidden;
    margin-top: 60px;
    height: 11px; }

.box {
  right: 125px;
  bottom: 30px;
  position: fixed;
  z-index: 1000;
  display: none; }
  .box a {
    display: block;
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 21px;
    background-color: #036ea7; }
    .box a:hover {
      background-color: #53a2c3; }
  .box.fixed {
    position: absolute;
    bottom: 215px; }

body#contact {
  height: 1050px;
  color: #036ea7; }
  body#contact.contact-entry {
    background: url(../../aoiumito/images/contact_entry.jpg) center center no-repeat; }
  body#contact.contact-confirm {
    background: url(../../aoiumito/images/contact_confirm.jpg) center center no-repeat; }
  body#contact.contact-complete {
    background: url(../../aoiumito/images/contact_complete.jpg) center center no-repeat; }
    body#contact.contact-complete a.btn {
      position: absolute;
      top: 500px;
      width: 430px;
      background-color: #BBB; }
      body#contact.contact-complete a.btn:hover {
        background-color: #DDD; }
  body#contact #contact_content {
    height: 991px;
    width: 858px;
    position: absolute;
    top: 30px;
    left: 50%;
    margin-left: -429px; }
    body#contact #contact_content .contact_box {
      width: 430px;
      margin: 110px 0 0 210px; }
      body#contact #contact_content .contact_box p {
        margin-top: 1em;
        margin-bottom: 2em; }
        body#contact #contact_content .contact_box p a {
          text-decoration: underline; }
      body#contact #contact_content .contact_box .confirm_input {
        line-height: 42px;
        height: 42px;
        color: #000; }
      body#contact #contact_content .contact_box .confirm_textarea {
        height: 200px;
        overflow:auto;
        white-space: pre;}
      body#contact #contact_content .contact_box h3 {
        height: 22px;
        width: 239px;
        margin-bottom: 10px;
        text-indent: -7777px;
        overflow: hidden; }
      body#contact #contact_content .contact_box label {
        display: block;
        margin-bottom: 1em; }
      body#contact #contact_content .contact_box .red {
        color: #F55;
        font-size: .8em; }
      body#contact #contact_content .contact_box .error_message {
        float: right;
        color: #F55; }
      body#contact #contact_content .contact_box input {
        width: 100%;
        border: none;
        background: #EEE;
        display: block;
        margin: 0;
        padding: 5px;
        box-sizing: border-box; }
      body#contact #contact_content .contact_box textarea {
        width: 100%;
        height: 200px;
        border: none;
        background: #EEE;
        box-sizing: border-box; }
      body#contact #contact_content .contact_box a.btn i {
        font-style: normal;
        margin-right: 1em; }
    body#contact #contact_content .info {
      margin-top: .5em;
      display: block;
      font-size: .9em; }

/*# sourceMappingURL=style.css.map */
