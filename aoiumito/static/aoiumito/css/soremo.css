@charset "UTF-8";
/* CSS Document */

/* html {
font-family: trajan-sans-pro,sans-serif;
font-weight: 400;
font-style: normal;
} */

/**
 * Remove default margin.
 */
/* body {
  margin: 0;
} */

/* img {
    width:100%;
} */

/* h2 {
    font-size: 1.5rem;
    color:#333333;
    margin:0px auto 90px;
    
} */

/* h3 {
    font-size: 1.25rem;
    color:#333333;
    margin:60px auto 30px;
    border-top:1px solid #f0f0f0;
    
} */

/* h4 {
    text-decoration: none;
    font-size: 1.25rem;
    color:#009dc4;
    margin:30px 5px 15px 5px;
}

h5 {
    text-decoration: none;
    font-size: 1rem;
    color:#333333;
    margin:30px 0px 0px 30px;
}


p {
    font-size: 0.8125rem;
    color:#333333;
    margin:5px 5px 5px 15px;
}

ul {
    font-size: 1rem;
    color: #009dc4;
    margin:30px 0px 30px;
    list-style: none;
}

li {
    text-decoration: none;
    font-size: 0.8125rem;
    color:#333333;
    margin:15px 10px 15px 15px;
    list-style: none;
}

a {
    text-decoration: none;
    font-size: 0.8125rem;
    color:#555555;
}

.tag {
    color: #ffffff;
    background-color: #009dc4;
    font-size: 0.8125rem;
    padding:0 15px 0;
    margin:0 30px;    
}

.wrapper {
    width: 1000px;
    margin:15px auto;
    padding:0 5px;
    text-align: left;
}

.line {
    border-top: 1px solid #f0f0f0;
    
} */