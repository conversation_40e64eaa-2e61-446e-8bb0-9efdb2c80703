phoneRegex = /^\d{2,5}-\d{1,4}-\d{4}$/;
emailRegex = /^([a-z0-9_]|\-|\.|\+)+@(([a-z0-9_]|\-)+\.)+[a-z]{2,6}$/i;

messageError = {
    name : {
        require: gettext('error message required'),
        maxLength: gettext('error message max length'),
    },
    phone: {
        isPhone: gettext('error message phone')
    },
    email: {
        require: gettext('error message required'),
        isEmail: gettext('error message email'),
    },
    comment: {
        require: gettext('error message required'),
        maxLength: gettext('error message max length'),
    }
};

functionValidate = {
    require: function(elem) {
        let value = elem.val();

        if(!value || value && !value.trim()) {
            return false;
        }

        return true;
    },
    isPhone: function(elem) {
        let value = elem.val();
        if(value) {
            if(!value.match(phoneRegex)) {
                return false;
            }
        }

        return true;
    },
    isEmail: function(elem) {
        let value = elem.val();
        if(value) {
            if(!value.match(emailRegex)) {
                return false;
            }
        }

        return true;
    },
    maxLength: function(elem, extra) {
        let value = elem.val();
        if(value) {
            if(value.trim().length > extra.maxLength) {
                return false
            }
        }

        return true
    }
};

configForm = {
    'name': {label: gettext('contact name label'), rules: {require: true, maxLength: 100}},
    'phone': {label: gettext('contact phone label'), rules: {isPhone: true}},
    'email': {label: gettext('contact email label'), rules: {require:true, isEmail: true}},
    'comment': {label: gettext('contact comment label'), rules: {require: true, maxLength: 1000}}
};

function escapeHtml(unsafe) {
    return $('<div>').text(unsafe).html();
}

function validateForm() {
    let valid = true;

    for (const field in configForm) {
        if(configForm[field]) {
            removeError($(`#${field}`));

            for(const typeValidate in configForm[field]['rules']) {
                let configValidate = configForm[field]['rules'][typeValidate];
                if(configValidate) {
                    if(!validateItem(field, typeValidate, {[`${typeValidate}`]: configValidate}) && valid) {
                        valid = false;
                    }
                }
            }
        }
    }

    return valid;
}

function validateItem(idElem, type, extra) {
    let elem = $(`#${idElem}`);
    if(!functionValidate[type](elem, extra)) {
        handleShowErrorMessage(elem, type, extra);
        return false;
    }

    return true;
}

function handleShowErrorMessage(elem, type, extra={}) {
    let id = elem.attr('id');
    let title = $(`#${id}_title`);
    let idError = `${id}_error`;
    if(title.find('.error_message').length > 0) {
        return;
    }
    let format = messageError[id][type];
    let label = configForm[id]['label'];
    let errorMessage = interpolate(format, Object.assign(extra, {label: label}), true);
    title.append(`<div id="${idError}" class="error_message">${escapeHtml(errorMessage)}</div>`)
}

function removeError(elem) {
    let id = elem.attr('id');
    let title = $(`#${id}_title`);
    $(`#${id}_error`).remove();
}

$(document).ready(function() {
    $('a.js-close').click(function(){
        window.open('about:blank','_self').close();
        window.close();
    });
    // 問い合わせフォーム サブミット用
    $('a.js-submit').click(function(e){
        e.preventDefault();
        if(validateForm()) {
            $($(this).attr('data-form')).submit();
        }
    });
})

