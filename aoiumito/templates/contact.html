{% extends "base_aoiumito.html" %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load i18n %}

{% block extrameta %}
<!-- お問い合わせ -->
<meta name="robots" content="noindex,nofollow" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no">
<meta name="format-detection" content="telephone=no, address=no, email=no" />
{% endblock %}
{% block title %}
<title>DAW STUDIO アオイウミト | お問い合わせ</title>
{% endblock %}

{% block extrahead %}
<link rel="stylesheet" type="text/css" href="{% static 'aoiumito/css/style.css' %}" />
<!--[if lt IE 9]>
    <script src="//cdn.jsdelivr.net/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
<script src="{% url 'javascript-catalog' %}"></script>
<script src="//code.jquery.com/jquery-1.11.1.js"></script>
<script src="{% static 'aoiumito/js/contact.js' %}"></script>

{% endblock %}

{% block content %}

<body id="contact" class="contact-entry">
    <form class="js-contact_form" id="contact_form" name="contact_form" action="" method="post">
        {% csrf_token %}
        <div id="error_message" style="color:red;font-weight:bold;">
            <!--エラーが発生しています。-->
        </div>

        <article id="contact_content">
            <div class="contact_box">
                <h3>{% trans "form contact" %}</h3>
                <p>{% trans "form contact summary" %}</p>

                <label>
                    <div class="i-title clearfix" id="name_title">
                        {% trans "contact name label" %}
                        <span class="red">※</span>
                        {% if form.name.errors %}
                        <div id="name_error" class="error_message">{{ form.name.errors.0 }}</div>
                        {% else %}
                            {% if default_message %}
                            <div id="name_error" class="error_message">{% trans "error message name" %}</div>
                            {% endif %}
                        {% endif %}
                    </div>
                    <input type="text" name="name" id="name" size="30" value="{{ form.name.value|default_if_none:'' }}" />
                </label>

                <label>
                    <div class="i-title clearfix" id="phone_title">
                        {% trans "contact phone label" %}
                        {% if form.phone.errors %}
                        <div id="phone_error" class="error_message">{{ form.phone.errors.0 }}</div>
                        {% else %}
                            {% if default_message %}
                            <div id="phone_error" class="error_message">{% trans "error message phone" %}</div>
                            {% endif %}
                        {% endif %}
                    </div>
                    <input type="text" name="phone" id="phone" size="40" value="{{ form.phone.value|default_if_none:'' }}" />
                </label>

                <label>
                    <div class="i-title clearfix" id="email_title">
                        {% trans "contact email label" %}
                        <span class="red">※</span>
                        {% if form.email.errors %}
                        <div id="email_error" class="error_message">{{ form.email.errors.0 }}</div>
                        {% else %}
                            {% if default_message %}
                            <div id="email_error" class="error_message">{% trans "error message email" %}</div>
                            {% endif %}
                        {% endif %}
                    </div>
                    <input type="text" name="email" id="email" size="40" value="{{ form.email.value|default_if_none:'' }}" />
                </label>

                <label>
                    <div class="i-title clearfix" id="comment_title">
                        {% trans "contact comment label" %}
                        <span class="red">※</span>
                        {% if form.comment.errors %}
                        <div id="comment_error" class="error_message">{{ form.comment.errors.0 }}</div>
                        {% else %}
                            {% if default_message %}
                            <div id="comment_error" class="error_message">{% trans "error message comment" %}</div>
                            {% endif %}
                        {% endif %}
                    </div>
                    <textarea name="comment" id="comment" rows="10">{{ form.comment.value|default_if_none:'' }}</textarea>
                </label>
                <input type="hidden" name="step" value="entry">
                <div>
                    <a class="button btn js-submit" data-form=".js-contact_form" href="#/">{% trans "contact submit button" %}</a>
                </div>

                <span class="info"><span class="red">※</span>{% trans "contact info required" %}</span>
            </div>
        </article>

    </form>
</body>
{% endblock %}

{% block extrascript %}
<script src="{% url 'javascript-catalog' %}"></script>
<script src="//code.jquery.com/jquery-1.11.1.js"></script>
<script src="{% static 'aoiumito/js/contact.js' %}"></script>
{% endblock %}
