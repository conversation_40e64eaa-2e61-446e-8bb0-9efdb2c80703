{% extends "base_aoiumito.html" %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load i18n %}

{% block extrameta %}
<!-- お問い合わせ -->
<meta name="robots" content="noindex,nofollow" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no">
<meta name="format-detection" content="telephone=no, address=no, email=no" />
{% endblock %}
{% block title %}
<title>{% trans "title aoiumito" %}</title>
{% endblock %}

{% block extrahead %}
<link rel="stylesheet" type="text/css" href="{% static 'aoiumito/css/style.css' %}" />
<!--[if lt IE 9]>
    <script src="//cdn.jsdelivr.net/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
<script src="//code.jquery.com/jquery-1.11.1.js"></script>
<script>
    $(function () {
        $('a.js-close').click(function () {
            window.open('about:blank', '_self').close();
            window.close();
        });
        // 問い合わせフォーム サブミット用
        $('a.js-submit').click(function () {
            $($(this).attr('data-form')).submit();
        });
    });
</script>
{% endblock %}

{% block content %}

<body id="contact" class="contact-confirm">
    <form class="js-contact_form" name="contact_form" action="" method="post">
        {% csrf_token %}
        <div id="error_message" style="color:red;font-weight:bold;">
            <!--エラーが発生しています。-->
        </div>

        <article id="contact_content">
            <div class="contact_box">
                <h3>{% trans "confirm contact form" %}</h3>
                <p>{% trans "confirm contact form summary" %}</p>

                <label>
                    <div class="i-title">
                        {% trans "contact name label" %}
                        <span class="red">※</span>
                    </div>
                    <div id="name" class="confirm_input">{{ form.name.value }}</div>
                    <input type="hidden" name="name" value="{{ form.name.value }}">
                </label>

                <label>
                    <div class="i-title">
                        {% trans "contact phone label" %}
                    </div>
                    <div id="phone" class="confirm_input">{{ form.phone.value }}</div>
                    <input type="hidden" name="phone" value="{{ form.phone.value }}">
                </label>

                <label>
                    <div class="i-title">
                        {% trans "contact email label" %}
                        <span class="red">※</span>
                    </div>
                    <div id="email" class="confirm_input">{{ form.email.value }}</div>
                    <input type="hidden" name="email" value="{{ form.email.value }}">
                </label>

                <label>
                    <div class="i-title">
                        {% trans "contact comment label" %}
                        <span class="red">※</span>
                    </div>
                    <div id="comment" class="confirm_textarea">{{ form.comment.value }}</div>
                    <input type="hidden" name="comment" value="{{ form.comment.value }}">
                </label>
                <input type="hidden" name="step" value="confirm">
                <div>
                    <a class="button btn js-submit" data-form=".js-contact_form" href="#/">{% trans "submit confirm button" %}</a>
                </div>

                <span class="info"><a href="{% url 'aoiumito:contact' %}">{% trans "back to contact form" %}</a></span>
            </div>
        </article>

    </form>
</body>
{% endblock %}
