# 01EG89H6SS2141VNGDDEHBMV4Q
import os

from django.core.mail.backends.smtp import EmailBackend
from django.core.mail import EmailMultiAlternatives
from django.template import loader
from django.utils.html import strip_tags
from django.utils.translation import gettext as _

from aoiumito.util import parse_now_string
from accounts.models import AuthUser


class SendMailServices(object):
    def __init__(self, subject, template, from_email, recipient_list):
        self.html_message = loader.render_to_string(template, self.get_context_template()).strip()
        self.body = strip_tags(self.html_message)
        self.recipient_list = recipient_list
        self.subject = subject
        self.from_email = from_email
        self.backend = EmailBackend(
            host=os.getenv('AOIUMITO_EMAIL_HOST'),
            port=os.getenv('AOIUMITO_EMAIL_PORT'),
            password=os.getenv('AOIUMITO_EMAIL_PASSWORD'),
            username=os.getenv('AOIUMITO_EMAIL_USER'),
        )

    def perform(self):
        if len(list(filter(None, self.recipient_list))) == 0:
            return
        mails = []
        for email in self.recipient_list:
            mail = EmailMultiAlternatives(
                subject=self.subject,
                body=self.body,
                from_email=self.from_email,
                to=[email],
            )
            mail.attach_alternative(self.html_message, 'text/html')
            mails.append(mail)
        self.backend.send_messages(mails)

    def get_context_template(self):
        return {}


class SendMailContactBaseServices(SendMailServices):
    def __init__(self, params):
        self.params = params
        config = self.config_send_mail()

        SendMailServices.__init__(
            self,
            config.get('subject'),
            config.get('template'),
            config.get('from_email'),
            config.get('recipient_list')
        )

    def config_send_mail(self):
        pass


class SendMailContactServices(SendMailContactBaseServices):
    def __init__(self, params):
        SendMailContactBaseServices.__init__(self, params)

    def get_context_template(self):
        return {
            'name': self.params.get('name'),
            'phone': self.params.get('phone'),
            'email': self.params.get('email'),
            'comment': self.params.get('comment'),
            'ip': self.params.get('ip'),
            'host': self.params.get('host'),
            'ua': self.params.get('ua'),
            'date': parse_now_string()
        }

    def config_send_mail(self):
        email_master_admins = list(AuthUser.get_master_admins().values_list('email', flat=True))
        return {
            'subject': _('subject contact mail') % {'name': self.params.get('name')},
            'template': 'aoiumito/email/contact.html',
            'from_email': os.getenv('AOIUMITO_FROM_EMAIL'),
            'recipient_list': email_master_admins
        }


class SendMailReplyContactServices(SendMailContactBaseServices):
    def __init__(self, params):
        SendMailContactBaseServices.__init__(self, params)

    def get_context_template(self):
        return {
            'name': self.params.get('name'),
            'phone': self.params.get('phone'),
            'email': self.params.get('email'),
            'comment': self.params.get('comment'),
        }

    def config_send_mail(self):
        return {
            'subject': _('subject contact reply mail') % {'name': self.params.get('name')},
            'template': 'aoiumito/email/contact_reply.html',
            'from_email': os.getenv('AOIUMITO_FROM_EMAIL'),
            'recipient_list': [self.params.get('email')]
        }
