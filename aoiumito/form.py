from django import forms
from django.core.validators import MaxLengthValidator, RegexValidator
from django.utils.translation import gettext as _


class ContactForm(forms.Form):
    name = forms.CharField(
        required=True,
        validators=[
            MaxLengthValidator(
                limit_value=100,
                message=_('contact error message max length') % {'label': _('contact name label'), 'maxLength': 100}
            )],
        error_messages={'required': _('contact error message required') % {'label': _('contact name label')}}
    )
    phone = forms.CharField(
        required=False,
        validators=[
            RegexValidator(
                regex=r'^\d{2,5}-\d{1,4}-\d{4}$',
                message=_('contact error message phone') % {'label': _('contact phone label')}
            )
        ])
    email = forms.CharField(
        required=True,
        validators=[
            RegexValidator(
                regex=r'^([a-z0-9_]|\-|\.|\+)+@(([a-z0-9_]|\-)+\.)+[a-z]{2,6}$',
                message=_('contact error message email') % {'label': _('contact email label')}
            ),
        ],
        error_messages={'required': _('contact error message required') % {'label': _('contact email label')}}
    )
    comment = forms.CharField(
        required=True,
        validators=[
            MaxLengthValidator(
                limit_value=1000,
                message=_('contact error message max length') % {'label': _('contact form comment label'), 'maxLength': 1000}
            )
        ],
        error_messages={'required': _('contact error message required') % {'label': _('contact form comment label')}}
    )

    class Meta:
        fields = ('name', 'phone', 'email', 'comment')
