from decimal import Decimal, ROUND_HALF_UP

from django.http import HttpResponseBadRequest, JsonResponse
from django.shortcuts import render, redirect
from django.template.loader import render_to_string
from django.urls import reverse_lazy
from django.views.generic import TemplateView, CreateView

from accounts.models import AuthUser
from app.views import ManagerMixin
from mileages import forms
from mileages.models import MileageRank
from mileages.services import get_artist_in_next_and_previous_rank, get_artist_in_previous_rank


class RequiredSuperMixin(ManagerMixin):
    def __has_permission(self):
        user = self.get_user()
        if not user or not user.is_authenticated:
            return False

        if user and user.role in [AuthUser.MASTERADMIN]:
            return True
        return False

    def dispatch(self, request, *args, **kwargs):
        if self.__has_permission():
            return super(RequiredSuperMixin, self).dispatch(request, args, **kwargs)
        return redirect("app:warning")


class MileageRankSetting(RequiredSuperMixin, TemplateView):
    template_name = "mileage/index.html"
    model = MileageRank

    def get_context_data(self, **kwargs):
        user = self.request.user
        mileage_ranks = MileageRank.objects.all()
        artists_count = AuthUser.objects.filter(role=AuthUser.CREATOR, is_active=True).count()
        context = {
            'user': user,
            'mileage_ranks': mileage_ranks,
            'artists_count': artists_count
        }
        return context


def ajax_create_mileage(request):
    user = request.user
    context = {}
    html = ''
    mileage_id = request.POST.get('mileage_id')
    type = 'edit'
    new_point = float(request.POST.get('point'))
    if mileage_id:
        mileage = MileageRank.objects.filter(pk=mileage_id).first()
        if not mileage:
            return JsonResponse({'error': 'not exist'}, status=500)
        form = forms.MileageRankSettingForm(request.POST, instance=mileage)
        if mileage.is_default and new_point != mileage.point:
            return JsonResponse({'error': 'rank is default'}, status=500)
        last_modified = mileage.modified.timestamp()
        modified = request.POST.get('modified', 0)
        if last_modified > float(modified):
            return JsonResponse({'error': 'edited before'}, status=500)
        if MileageRank.objects.filter(point=new_point).exclude(pk=mileage.pk).exists():
            return JsonResponse({'error': 'cannot same point'}, status=500)
        if new_point != mileage.point:
            type = 'edit_point'

    else:
        if MileageRank.objects.filter().count() >= 8:
            return JsonResponse({'error': 'had 8 ranks'}, status=500)
        type = 'create'
        form = forms.MileageRankSettingForm(request.POST)
        if MileageRank.objects.filter(point=new_point).exists():
            return JsonResponse({'error': 'cannot same point'}, status=500)

    image = request.FILES.get('image')
    artists_count = AuthUser.objects.filter(role=AuthUser.CREATOR, is_active=True).count()

    if not form.is_valid():
        data = list(form.errors.items())
        return JsonResponse({'error': ''}, status=500)
    mileage = form.save(commit=False)
    mileage.user = user
    if image:
        # mileage.x = float(request.POST.get('x', 0))
        # mileage.y = float(request.POST.get('y', 0))
        # mileage.height = float(request.POST.get('height', 0))
        # mileage.width = float(request.POST.get('width', 0))
        mileage.image = image
    if type == 'create':
        context.update(get_artist_in_previous_rank(mileage, artists_count))
        html = render_to_string('mileage/_rank_item.html', {'mileage': mileage, 'artists_count': artists_count})
    elif type == 'edit_point':
        mileage_ranks = MileageRank.objects.all()
        html = render_to_string('mileage/_all_ranks.html',
                                {'mileage_ranks': mileage_ranks, 'artists_count': artists_count})
    elif type == 'edit':
        context.update({'mileage_id': mileage_id})
        html = render_to_string('mileage/_rank_item.html', {'mileage': mileage, 'artists_count': artists_count})
    ## save
    mileage.save()

    context.update({'html': html, 'type': type})
    return JsonResponse(context, status=200)


def delete_mileage(request):
    context = {}
    mileage_id = request.POST.get('mileage_id')
    mileage = MileageRank.objects.filter(pk=mileage_id).first()
    if request.user.role != AuthUser.MASTERADMIN:
        return JsonResponse({'error': ''}, status=500)
    if not mileage:
        return JsonResponse({'error': 'not exist'}, status=500)
    if MileageRank.objects.filter().count() < 2 or mileage and mileage.is_default:
        return JsonResponse({'error': 'rank is default'}, status=500)
    mileage.delete()
    artists_count = AuthUser.objects.filter(role=AuthUser.CREATOR, is_active=True).count()
    context.update(get_artist_in_previous_rank(mileage, artists_count))
    return JsonResponse(context, status=200)


def get_mileage_rank_for_artist(request):
    current_user = request.user
    if current_user.role != AuthUser.CREATOR:
        return JsonResponse({'error': 'User is not a creator'}, status=403)
    
    try:
        mileage_rank = current_user.get_mileage_for_artist()
        if not mileage_rank:
            # デフォルトランクが存在しない場合のエラーメッセージ
            return JsonResponse({
                'error': 'Mileage rank not found. Please ensure default rank exists.',
                'total_point': current_user.total_point,
                'balance_reward': current_user.balance_reward,
                'evaluation_point': current_user.evaluation_point
            }, status=404)
        
        next_rank = mileage_rank.get_next_rank()
        html = render_to_string('_modal_mileage_popup_body.html', {
            'mileage_rank': mileage_rank,
            'next_rank': next_rank,
            'user': current_user,
        })
        return JsonResponse({'html': html})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
