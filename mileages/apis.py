from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import MileageRank
from .serializers import MileageRankSerializer

class MyMileageRankView(APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def options(self, request, *args, **kwargs):
        """CORSプリフライトリクエストを許可"""
        response = Response()
        response['Access-Control-Allow-Origin'] = "http://localhost:3000"
        response['Access-Control-Allow-Methods'] = "GET, OPTIONS"
        response['Access-Control-Allow-Headers'] = "Authorization, Content-Type"
        return response

    def get(self, request):
        mileage_ranks = MileageRank.objects.filter(user=request.user)
        serializer = MileageRankSerializer(mileage_ranks, many=True)
        return Response(serializer.data)
