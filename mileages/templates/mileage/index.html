{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load static %}
{% load i18n compress %}

{% block extrahead %}
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/theme.default.min.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/account_management.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
  <link rel="stylesheet" href="{% static 'css/main_new.css' %}"/>
  <link rel="stylesheet" href="{% static 'css/components/image.css' %}"/>
  <link rel="stylesheet" href="{% static 'css/components/table.css' %}"/>
  <link rel="stylesheet" href="{% static 'css/components/utils.css' %}"/>
  <link rel="stylesheet" href="{% static 'css/components/modal.css' %}"/>
  <link rel="stylesheet" href="{% static 'css/components/input.css' %}"/>
  <link rel="stylesheet" href="{% static 'mileages/css/mileage_index.css' %}"/>
  {% endcompress %}
{% endblock %}

{% block content %}
  <main class="p10 owner-top" style="background: #FCFCFC; margin-bottom: 48px;">
    <div class="container">
      <div class="account-management">
        <div class="account-management__add-artist btn-add-mileage-stage">
          <div class="account-management__add-artist__content">
            <div class="add-artist__content__icon">
              <i class="icon icon--sicon-plus"></i>
            </div>
            <div class="add-artist_content__text">{% trans "Add new stage" %}</div>
          </div>
        </div>
      </div>
      <div class="list-all-mileage mg-top-lg container overflow-x-scroll nice-scroll">
        <table>
          <thead>
          <tr>
            <th></th>
            <th class="text-left">{% trans "Rank name jp" %}</th>
            <th class="text-left">{% trans "Rank name en" %}</th>
            <th class="text-right">{% trans "Borderline" %}</th>
            <th class="text-right">{% trans "Use material" %}</th>
            <th class="text-right">{% trans "Rank distribution ratio" %}</th>
          </tr>
          </thead>
          <tbody>
          {% include 'mileage/_all_ranks.html' with mileage_ranks=mileage_ranks artists_count=artists_count %}
          </tbody>
        </table>
      </div>
    </div>

    {% include 'mileage/_modal_add.html' %}
    {% include 'mileage/_modal_confirm_delete.html' %}

    <div class="modal fade modal-centered" id="modalCrop">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="padding: 16px;">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
            <h3 class="modal-title">{% trans "Register image" %}</h3>
          </div>
          <div class="modal-body">
            <img src="" id="image" style="max-width: 100%;"/>
          </div>
          <div class="modal-footer">
            <div class="btn-group pull-left" role="group">
              <button type="button" class="btn btn-default js-zoom-in">
                <span class="glyphicon glyphicon-zoom-in"></span>
              </button>
              <button type="button" class="btn btn-default js-zoom-out">
                <span class="glyphicon glyphicon-zoom-out"></span>
              </button>
            </div>
            <button type="button" class="btn btn-primary js-crop-and-upload">{% trans "To register" %}</button>
          </div>
        </div>
      </div>
    </div>
  </main>
{% endblock content %}
{% block extra_script %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/jquery.tablesorter.min.js' %}"></script>
  <script src="{% static 'js/jquery.tablesorter.widgets.min.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/table_sorter.js' %}"></script>
  <script src="{% static 'js/common_variable.js' %}"></script>
  <script src="{% static 'js/cropper.min.js' %}"></script>
  <script src="{% static 'mileages/js/mileage_index.js' %}"></script>
    {% endcompress %}
  <script src="{% url 'javascript-catalog' %}"></script>
{% endblock %}
