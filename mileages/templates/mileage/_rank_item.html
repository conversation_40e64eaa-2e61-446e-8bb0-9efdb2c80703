{% load static %}
{% load i18n %}
{% load util %}
{% load bootstrap3 %}


<tr class="stage-item {% if mileage.is_default %}rank-default{% endif %}" data-stage-id="{{ mileage.pk }}" data-modified="{{ mileage.modified.timestamp }}">
  {% comment %} img {% endcomment %}
  <td>
    {% if mileage.image %}
      <div class="card-image-container__sm pointer"
           style="background-image: url({{ mileage.image.url }})">
      </div>
    {% else %}
      <div class="card-image-container__sm pointer"
           style="background-image: url({% static 'images/img_default_stage_card.png' %})">
      </div>
    {% endif %}
  </td>
  {% comment %} name jp {% endcomment %}
  <td class="stage-item-rank-name-jp pointer ">
    <div class="text-left flex_column align-left"><span>{{ mileage.rank_name_jp }}</span></div>
  </td>
  {% comment %} name en {% endcomment %}
  <td class="stage-item-rank-name-en pointer ">
    <div class="text-left flex_column align-left"><span>{{ mileage.rank_name_en }}</span></div>
  </td>
  {% comment %} borderline round to int {% endcomment %}
  <td class="stage-item-borderline pointer">
    <div class="text-right flex_column align-right"><span>{{ mileage.point|display_currency }}{% trans "Miles" %}</span></div>
  </td>
  {% comment %} usage fee, round to 1 decimal places {% endcomment %}
  <td class="stage-item-usage-fee pointer">
    <div class="text-right flex_column align-right"><span>{{ mileage.get_usage_fee }}%</span>
    </div>
  </td>
  {% comment %} distribution {% endcomment %}
  <td class="stage-item-distribution stage-distribution-text">
    <div class="text-right flex_column align-right">
      {% include 'mileage/_ratio_rank_item.html' with mileage=mileage artists_count=artists_count %}
    </div>
    {% comment %} stage delete button, remove if default stage {% endcomment %}
    {% if not mileage.is_default %}
      <div class="icon-stage-delete btn-general-font"><i class="icon icon--sicon-trash"></i></div>
    {% endif %}
  </td>
</tr>

