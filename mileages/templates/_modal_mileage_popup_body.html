{% load static %}
{% load util compress %}
{% compress css %}
<link rel="stylesheet" type="text/css" href="{% static 'mileages/css/popup_modal.css' %}"/>
{% endcompress %}
{% if default %}
  <div class="mileage-popup-container loading">
    <div class="icon-loading"></div>
    <div class="card-container card-image-container__lg" style="background-image: url({% static 'images/img_default_stage_card.png' %})">
      <div class="card-image-info u-text-white">
        <div class="u-row-end u-items-end">
          <div class="heading-24" title=""></div>
          <div class="bodytext-11">マイル</div>
        </div>
          <div class="u-mt16 bodytext-11 u-text-right">システム利用料</div>
          <div class="u-row-end u-items-end u-gap2">
            <div class="heading u-line-height-100">{{ user.get_usage_fee_for_user_to_show }}</div>
            <div class="bodytext-11 u-line-height-100">%</div>
          </div>
      </div>
    </div>

    <div class="mileage-progress">
      <div class="mileage-progress-current" style="width: 80%"></div>
    </div>

    <div class="next_rank-text bodytext-11">次のステージ（0%）まであとマイル</div>
  </div>
{% else %}
  <div class="mileage-popup-container">
    <div class="card-container card-image-container__lg" style="{% if mileage_rank.image %}background-image: url({{ mileage_rank.image.url }}){% else %}background-image: url({% static 'images/img_default_stage_card.png' %}){% endif %};">
      <div class="card-image-info u-text-white">
        <div class="u-row-end u-items-end">
          <a href="{% url 'payments:payment_current_artist' %}"><div class="heading-24" title="{{ user.total_point|display_currency }}">{{ user.total_point|display_currency }}</div></a>
          <div class="bodytext-11">マイル</div>
        </div>
          <div class="u-mt16 bodytext-11 u-text-right">システム利用料</div>
          <div class="u-row-end u-items-end u-gap2">
            <div class="heading u-line-height-100">{{ user.get_usage_fee_for_user_to_show }}</div>
            <div class="bodytext-11 u-line-height-100">%</div>
          </div>
      </div>
    </div>

    <div class="mileage-progress">
      <div class="mileage-progress-current" style="width: {{ user|get_current_point_rate:next_rank }}%"></div>
    </div>

    {% if next_rank %}
      <div class="next_rank-text bodytext-11">次の{{ next_rank.rank_name_jp }}ステージ（{{ user|get_usage_fee_next_rank_for_user_to_show:next_rank }}%）まであと{{ next_rank.point|minus:user.total_point|display_currency }}マイル</div>
    {% else %}
      <div class="next_rank-text bodytext-11">おめでとう！最高ランクに達しました。</div>
    {% endif %}
  </div>
{% endif %}
