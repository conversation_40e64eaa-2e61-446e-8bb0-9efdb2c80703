import ast
import json

import dateutil.parser
import datetime
from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import EmptyResultSet
from django.core.validators import FileExtensionValidator
from django.db import transaction
import re

from . import models
from .models import *


class MileageRankSettingForm(forms.ModelForm):
    rank_name_jp = forms.CharField(max_length=256, required=False)
    rank_name_en = forms.CharField(max_length=256, required=False)
    point = forms.FloatField()
    usage_fee = forms.FloatField()

    class Meta:
        model = MileageRank
        fields = ('rank_name_jp', 'rank_name_en', 'point', 'usage_fee')

