var $myDropZone;

var cropped = false;

var cardCropper = {
    viewMode: 1,
    rotatable: false,
    aspectRatio: 216 / 136,
    minCropBoxWidth: 200,
    minCropBoxHeight: 200,
    minContainerHeight: 400,
};

$(document).ready(function() {
    checkButtonAddStage();
    $(document).on('click', '.btn-add-mileage-stage:not(.btn--disabled)', function() {
        prepareDataCreate();
        $('#modal-add-mileage-stage').find('#id_borderline').attr('disabled', false);
        $('#modal-add-mileage-stage').modal('show');
    })

    $(document).on('click', '.icon-stage-delete', function () {
        let mileage_id = $(this).parents('.stage-item').attr('data-stage-id');
        if (mileage_id) {
            $('#modal-confirm-delete-mileage-stage').attr('data-mileage-id', mileage_id);
            $('#modal-confirm-delete-mileage-stage').modal('show');
        }
    })

    $(document).on('click', '.list-all-mileage tbody tr td:not(:last-child)', function() {
        prepareDataEdit($(this).parents('tr.stage-item'));
        if ($(this).parents('.stage-item').hasClass('rank-default')) {
            $('#modal-add-mileage-stage').find('#id_borderline').attr('disabled', true);
        } else {
            $('#modal-add-mileage-stage').find('#id_borderline').attr('disabled', false);
        }
        $('#modal-add-mileage-stage').modal('show');
    })

    $(document).on('click', '.btn-confirm-delete-cancel', function() {
        $('#modal-confirm-delete-mileage-stage').modal('hide');
    })

    $(document).on('click', '.btn-confirm-delete', function() {
        let mileage_id = $('#modal-confirm-delete-mileage-stage').attr('data-mileage-id');
        if (!mileage_id) {
            return
        }

        $.ajax({
            type: "POST",
            data: {
                'mileage_id': mileage_id
            },
            url: '/mileages/delete_mileage',
            beforeSend: function () {
            },
            success: function (response) {
                $('.stage-item[data-stage-id=' + mileage_id + ']').remove();
                checkButtonAddStage();
                updateRatioPreviousRank(response)
            },
            error: function (res) {
                switch (res.responseJSON.error) {
                    case 'not exist':
                        toastr.error('このランクは存在しません。')
                        break;
                    case 'rank is default':
                        toastr.error('デフォルトランクのボーダーラインが変更できません。')
                        break;
                    case 'edited before':
                        toastr.error('このランクは先ほど、すでに更新されました。')
                        break;
                    case 'cannot same point':
                        toastr.error('このランクのボーダーラインは他のランクと重複しまいました。')
                        break;
                    case 'had 8 ranks':
                        toastr.error('8つのランクが存在していますので、追加できません。')
                        break;
                    default:
                        toastr.error('エラーが発生しました')
                        break;
                }
            },
            complete: function () {
                $('#modal-confirm-delete-mileage-stage').modal('hide');

            }
        });
    });

    $(document).on('click', '.btn-cancel-modal-add', function() {
        prepareToClose();
        $('#modal-add-mileage-stage').modal('hide');
    });

    //for saving new/edit stage info
    $(document).on('click', '.btn-save-modal-add:not(.btn--disabled)', function() {
        let dataForm = new FormData();
        let buttonDom = $(this);
        buttonDom.addClass('btn--disabled');
        let rank_name_jp = $('#id_rank_name').val().trim();
        let rank_name_en = $('#id_rank_name_en').val().trim();
        let point = parseFloat($('#id_borderline').val().trim().replaceAll(',', ''));
        let usage_fee = $('#id_usage_fee').val().trim();
        dataForm.append('rank_name_jp', rank_name_jp);
        dataForm.append('rank_name_en', rank_name_en);
        dataForm.append('point', point);
        dataForm.append('usage_fee', usage_fee);
        if ($myDropZone.files.length && $myDropZone.files[0]) {
            dataForm.append('image', $myDropZone.files[0]);
            dataForm.append('x', parseFloat($('#id_x').val()));
            dataForm.append('y', parseFloat($('#id_y').val()));
            dataForm.append('height', parseFloat($('#id_height').val()));
            dataForm.append('width', parseFloat($('#id_width').val()));
        }
        let mileage_id = $('#modal-add-mileage-stage').attr('stage-id');
        if (mileage_id) {
            let modified = $('#modal-add-mileage-stage').attr('modified');
            modified = modified ? modified : 0;
            dataForm.append('mileage_id', mileage_id);
            dataForm.append('modified', modified);
        }
        ajaxCreateMileageRank(dataForm, buttonDom)
    });

    $(document).on('change', 'input#id_borderline', function (e) {
        // var start = this.selectionStart,
        // end = this.selectionEnd,
        // length = this.value.length;

        let intValue = this.value.replace(/\D/g, '');
        if (intValue.length > this.maxLength - 4) {
            intValue = intValue.slice(0, this.maxLength - 4);
        }
        this.value = intValue.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        // this.setSelectionRange(start, end + this.value.length - length);
        checkSubmitButton();
    });


    //check 0 - 100
    $(document).on('input', 'input#id_usage_fee', function () {
        checkSubmitButton();
    });

    initDropzone();
});


function ajaxCreateMileageRank(dataForm, buttonDom) {
    $.ajax({
        type: "POST",
        data: dataForm,
        contentType: false,
        processData: false,
        url: '/mileages/create_mileage',
        beforeSend: function () {
        },
        success: function (response) {
            prepareToClose();
            if (response.type === 'create') {
                updateRatioPreviousRank(response);
            } else if (response.type === 'edit_point') {
                $('.list-all-mileage table tbody').empty();
                $('.list-all-mileage table tbody').append(response.html);

            } else if (response.type === 'edit') {
                let rankDom = $('.stage-item[data-stage-id=' + response.mileage_id + ']');
                $(response.html).insertBefore(rankDom);
                rankDom.remove();
            }
            checkButtonAddStage();
            $('#modal-add-mileage-stage').modal('hide');
        },
        error: function (res) {
            buttonDom.removeClass('btn--disabled');
            switch (res.responseJSON.error) {
                case 'not exist':
                    toastr.error('このランクは存在しません。')
                    break;
                case 'rank is default':
                    toastr.error('デフォルトランクのボーダーラインが変更できません。')
                    break;
                case 'edited before':
                    toastr.error('このランクは先ほど、すでに更新されました。')
                    break;
                case 'cannot same point':
                    toastr.error('このランクのボーダーラインは他のランクと重複しまいました。')
                    break;
                case 'had 8 ranks':
                    toastr.error('8つのランクが存在していますので、追加できません。')
                    break;
                default:
                    toastr.error('エラーが発生しました')
                    break;
            }
        },
    });
}


function updateRatioPreviousRank(response) {
    if (response.previous_rank_id) {
        let previousRankDom = $('.stage-item[data-stage-id=' + response.previous_rank_id + ']');
        if (previousRankDom.length) {
            if (response.html) {
                $(response.html).insertBefore(previousRankDom);
            }
            previousRankDom.find('.stage-item-distribution .text-right').empty();
            previousRankDom.find('.stage-item-distribution .text-right').append(response.html_previous_rank);
        }
    }
}

function checkUsageFee() {
    let usageFeeDom = $('#id_usage_fee');
    let fee = usageFeeDom.val().trim();
    const numberRegex = new RegExp("^[0-9]+(\\.[0-9][0-9]?)?$");
    if (!numberRegex.test(fee) && fee !== '' || fee > 100) {
        if (!$('.error-number').length) {
            $('<div class="errorlist error-number error-message">' +
                ' 利用料のフォーマットが正しくありません。' +
                '</div>').insertAfter(usageFeeDom.parents('.col-sm-6.flex_row'));
        }
        $('#modal-add-mileage-stage .btn-save-modal-add').addClass('btn--disabled');
        return false
    } else if (fee !== ''){
        $('.errorlist').remove();
        return true
    }
    return false
}

function prepareDataEdit(target) {
    $('.errorlist').remove();
    let stage_id = target.attr('data-stage-id');
    let bg = target.find('td .card-image-container__sm').get(0).style.backgroundImage;
    let rank_en = target.find('td.stage-item-rank-name-en span')[0].innerText
    let rank_jp = target.find('td.stage-item-rank-name-jp span')[0].innerText
    let border = target.find('td.stage-item-borderline span')[0].innerText.replaceAll(/[^0-9]/g, '')
    let usage_fee = target.find('td.stage-item-usage-fee span')[0].innerText.replaceAll(/[^.0-9]/g, '')
    $('#modal-add-mileage-stage').attr('stage-id', stage_id);
    $('#modal-add-mileage-stage').attr('modified', target.attr('data-modified'));
    $('#modal-add-mileage-stage #card-image-dz').css('background-image', bg);
    $('#id_rank_name').val(rank_jp);
    $('#id_rank_name_en').val(rank_en);
    $('#id_borderline').val(parseInt(border).toLocaleString('ja'));
    $('#id_usage_fee').val(parseFloat(usage_fee).toFixed(2).replace(/\.0+$/, ''));
    checkSubmitButton();
}

function prepareDataCreate() {
    $('#modal-add-mileage-stage').attr('stage-id', '');
    $('#modal-add-mileage-stage #card-image-dz').css('background-image', 'none');
    $('#id_rank_name').val('');
    $('#id_rank_name_en').val('');
    $('#id_borderline').val('');
    $('#id_usage_fee').val('');
    $('.errorlist').remove();
    checkSubmitButton();
}

function readDataUploadImage(file, crop, stage_id) {
    let $image = $('#image');
    let cropBoxData, canvasData;
    let fileReader = new FileReader();

    fileReader.onloadend = function (e) {
        $image.attr('src', e.target.result);
        $('#modalCrop').modal('show');
    };
    fileReader.readAsDataURL(file);

    $("#modalCrop").modal({
        show: false,
        backdrop: 'static'
    });

    $('#modalCrop').off().on('shown.bs.modal', function () {
        $image.cropper({
            viewMode: crop.viewMode,
            rotatable: crop.rotatable,
            aspectRatio: crop.aspectRatio,
            minCropBoxWidth: crop.minCropBoxWidth,
            minCropBoxHeight: crop.minCropBoxHeight,
            minContainerHeight: crop.minContainerHeight,
            ready: function () {
                $image.cropper('setCanvasData', canvasData);
                $image.cropper('setCropBoxData', cropBoxData);
            }
        });
        // $('.modal:not(#modalCropBanner)').hide();
    }).on('hidden.bs.modal', function () {
        cropBoxData = $image.cropper('getCropBoxData');
        canvasData = $image.cropper('getCanvasData');
        $image.cropper('destroy');
    });

    $('.js-crop-and-upload').unbind().bind("click", function () {
        cropped = true;
        // var cropData = $image.cropper("getData");
        var croppedImageDataURL = $image.cropper('getCroppedCanvas', {fillColor: '#fff'}).toDataURL("image/png");
        $('#card-image-dz').css('background-image', 'url(' + croppedImageDataURL + ')');
        $myDropZone.files[0] = dataURLtoFile(croppedImageDataURL,'card_bg.png');
        $('#modalCrop').modal('hide');
        // $('#id_x').val(cropData['x']);
        // $('#id_y').val(cropData['y']);
        // $('#id_width').val(cropData['width']);
        // $('#id_height').val(cropData['height']);
    });


    // Enable zoom in button
    $('.js-zoom-in').click(function () {
        $image.cropper('zoom', 0.1);
    });

    // Enable zoom out button
    $('.js-zoom-out').click(function () {
        $image.cropper('zoom', -0.1);
    });

    $('#modalCrop').on('hidden.bs.modal', function() {
        if(!cropped) {
            $myDropZone.removeAllFiles(true);
        }
    })
}

function initDropzone () {
    $myDropZone = new Dropzone('#card-image-dz', {
        url: '/',
        autoDiscover: false,
        acceptedFiles: '.jpg, .png',
        timeout: 900000,
        clickable: '#card-image-dz',
        autoProcessQueue: false,
        autoQueue: false,
    });

    $myDropZone.on('addedfile', function (file, e) {
        $('#card-image-dz').removeClass('dropzone-active');
        console.log(file.size)
        if(file.size > 1024*1024*5) {
            alert("5MB以下のファイルのみアップロードできます!");
            this.removeFile(file);
            return false;
        }
        let file_dom = $(file.previewElement);
        file_dom.find('.determinate').css('width', '0%');

        if (file && file.name.match(/\.(png|PNG|jpg|JPG)$/)) {
            readDataUploadImage(file, cardCropper, $('#modal-add-mileage-stage').attr('stage-id'));
        } else {
            alert('PNG, JPGのみアップロードできます。');
            $myDropZone.removeAllFiles(true);
        }
    });

    $myDropZone.on('dragstart', function () {
        $('#card-image-dz').addClass('dropzone-active');
    });

    $myDropZone.on('dragenter', function () {
        $('#card-image-dz').addClass('dropzone-active');
    });

    $myDropZone.on('dragover', function () {
        $('#card-image-dz').addClass('dropzone-active');
    });

    $myDropZone.on('dragleave', function () {
        $('#card-image-dz').removeClass('dropzone-active');
    });

    $myDropZone.on('removedfile', function (file) {
        $('#card-image-dz').removeClass('dropzone-active');
    });
}

function checkSubmitButton() {
    let borderline = $('#id_borderline').val().replaceAll(/[^0-9]/g, '');
    let fee = $('#id_usage_fee').val().trim();
    if (!borderline || !fee || !checkUsageFee()) {
        $('#modal-add-mileage-stage .btn-save-modal-add').addClass('btn--disabled');
        return false;
    }

    let stage_id =  $('#modal-add-mileage-stage').attr('stage-id');
    let borders = $('.stage-item-borderline');
    if(stage_id) {
        borders = $('.stage-item:not([data-stage-id='+ stage_id +']) .stage-item-borderline');
    }
    let borderLineFlag = true;

    borders.each(function(i,e) {
        if(parseInt($(e).find('span').html().replaceAll(/[^0-9]/g, '')) == borderline) {
            $('#modal-add-mileage-stage .btn-save-modal-add').addClass('btn--disabled');
            $('#modal-add-mileage-stage .error-message.hide').removeClass('hide');
            borderLineFlag = false;
            return false;
        }
    });
    if (!borderLineFlag) {
        return false
    }
    $('#modal-add-mileage-stage .error-message').addClass('hide');

    $('#modal-add-mileage-stage .btn-save-modal-add').removeClass('btn--disabled');
}


function prepareToClose() {
    $myDropZone.removeAllFiles(true);
    $('#modal-add-mileage-stage').attr('stage-id', '');
    $('#modal-add-mileage-stage').find('#id_borderline').attr('disabled', false);
    cropped = false;
}

function checkButtonAddStage() {
    $('.btn-add-mileage-stage').removeClass('btn--disabled');
    if($('.list-all-mileage tbody tr').length > 7) {
        $('.btn-add-mileage-stage').addClass('btn--disabled')
    }
}

function dataURLtoFile(dataUrl, filename) {
    var arr = dataUrl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]), 
        n = bstr.length, 
        u8arr = new Uint8Array(n);
        
    while(n--){
        u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new File([u8arr], filename, {type:mime});
}
