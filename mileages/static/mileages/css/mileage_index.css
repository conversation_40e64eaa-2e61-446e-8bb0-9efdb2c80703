.icon-stage-delete {
    opacity: 0;
    position: absolute;
    height: 100%;
    width: 24px;
    top: 50%;
    right: -24px;
    transform: translateY(-50%);
    font-size: 16px;
    color: #a7a8a9;
    cursor: pointer;
    transition: 0.1s;
    padding: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dropzone-active {
    background-image: url('/static/images/img_dragging_bg.png') !important;
}

.img_stage_card:hover {
    background-image: url('/static/images/img_hover_upload_bg.png') !important;
}

tbody tr {
    margin-right: 24px;
}

tr:hover .icon-stage-delete,
.icon-stage-delete:hover {
    opacity: 1;
}

.stage-distribution-text {
    color: #a7a8a9;
    position: relative;
}

.dz-preview {
    display: none;
}

.btn-add-mileage-stage.btn--disabled {
    background: #F0F0F0;
}

.btn-add-mileage-stage.btn--disabled .add-artist_content__text {
    color: #A7A8A9 !important;
}

.rank-default .stage-item-borderline span {
    color: #a7a8a9 !important;
}

.rank-default td.stage-item-borderline {
    pointer-events: none;
    cursor: default;
}

@media (max-width: 767px) {
    .col-xs-12:not(:first-child) {
        padding: 0 8px 0 0 !important;
        margin: 8px 0 0 !important;
    }
}

td.pointer:hover span {
    color: #009ace;
}
