#modal_mileage_popup .card-container .heading--32,
#modal_mileage_popup .card-container .heading--16,
#modal_mileage_popup .card-container .caption--11 {
    line-height: 200% !important;
    color: #fff !important;
}

#modal_mileage_popup .card-container .card-image-point-mile {
    margin-bottom: 12px;
}

#modal_mileage_popup .card-container .card-image-usage-fee-value-percent {
    margin: 0 0 2px 4px;
}

.card-image-info {
    padding: 68px 24px 16px;
    filter: drop-shadow(1px 3px 5px rgba(0, 0, 0, 0.25));
}

.card-image-point,
.card-image-usage-fee-value {
    display: flex;
    flex-direction: row;
    align-items: end;
}

#modal_mileage_popup .card-container {
    border-radius: 12px !important;
}

.mileage-popup-container {
    /* width: 327px; */
    display: flex;
    flex-direction: column;
    align-items: center;
}

.mileage-progress {
    height: 4px;
    width: 311px;
    margin: 16px auto 8px;
    background: #a7a8a9;
    border-radius: 2px;
}

.mileage-progress-current {
    background-color: #009ace;
    height: 4px;
    border-radius: 2px;
}

.next_rank-text {
    width: 311px;
}

#modal_mileage_popup.modal,
body.no-backdrop .modal {
    background-color: transparent !important;
    position: absolute;
    top: 50px !important;
    right: 0;
    left: auto;
    bottom: auto;
    overflow: hidden;
}

/* @media (max-width: 576px) {
    #modal_mileage_popup.modal,
    body.no-backdrop .modal {
        background-color: transparent !important;
        position: fixed;
        top: 64px !important;
        right: 50%;
        left: auto;
        bottom: auto;
        overflow: hidden;
        transform: translateX(50%);
    }
} */

.mileage-popup-container.loading .icon-loading {
    position: absolute;
    width: 100%;
    height: 250px;
    background: white;
    background-image: url('/static/images/icon-loading-b.svg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 40px;
    z-index: 2;
}


/* #modal_mileage_popup .modal-dialog {
    width: auto !important;
    margin-top: 0;
    transform: translateY(0%) !important;
} */

/* #modal_mileage_popup .modal-content {
    border-radius: 0;
} */

.sheader-info {
    position: relative;
}

.card-image-point-value {
    max-width: 236px;
    text-overflow: ellipsis;
    overflow: hidden;
}

body.no-backdrop .modal-backdrop {
    opacity: 0;
    z-index: 998 !important;
}

body.no-backdrop.modal-open {
    padding-right: 0 !important;
    overflow: auto;
}

body.no-backdrop .modal-content {
    box-shadow: none;
    border: 1px solid #f0f0f0 !important;
}
