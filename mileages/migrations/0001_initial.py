# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-04-22 13:17
from __future__ import unicode_literals

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MileageRank',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('mileage_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rank_name_jp', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('rank_name_en', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('point', models.FloatField(default=0, null=True)),
                ('usage_fee', models.FloatField(default=17.5)),
                ('image', models.<PERSON><PERSON>ield(blank=True, upload_to='images')),
                ('x', models.FloatField(default=0)),
                ('y', models.FloatField(default=0)),
                ('width', models.FloatField(default=0)),
                ('height', models.FloatField(default=0)),
                ('is_default', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-point'],
            },
        ),
    ]
