import io
import os

import datetime
import urllib

from django.conf import settings
from django.db import transaction
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.template.loader import get_template
from django.utils.translation import gettext as _
from django.template.loader import render_to_string
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Prefetch, Sum, Case, When, F, Q, Subquery

from accounts.models import AuthUser, ProductUser
from voice.logger import logger
from accounts.models import AuthUser


def count_artist_in_rank(mileage_rank):
    next_rank = mileage_rank.get_next_rank()
    if next_rank:
        if mileage_rank.is_default:
            return AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR,
                                           total_point__lt=next_rank.point).count()
        return AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR, total_point__gte=mileage_rank.point,
                                       total_point__lt=next_rank.point).count()
    if mileage_rank.is_default:
        return AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR).count()
    return AuthUser.objects.filter(is_active=True, role=AuthUser.CREATOR, total_point__gte=mileage_rank.point).count()


def get_artist_in_next_and_previous_rank(mileage, artists_count):
    # return ratio of next rank, previous rank
    context = {}
    next_rank = mileage.get_next_rank()
    if next_rank:
        artist_next_rank = count_artist_in_rank(next_rank)
        html_next_rank = render_to_string('mileage/_ratio_rank_item.html',
                                          {'artist_next_rank': artist_next_rank, 'artists_count': artists_count})
        context.update({'html_next_rank': html_next_rank})
    previous_rank = mileage.get_previous_rank()
    if previous_rank:
        artist_previous_rank = count_artist_in_rank(previous_rank)
        html_previous_rank = render_to_string('mileage/_ratio_rank_item.html',
                                              {'artist_next_rank': artist_previous_rank,
                                               'artists_count': artists_count})
        context.update({'html_previous_rank': html_previous_rank})
    return context


def get_artist_in_previous_rank(mileage, artists_count):
    # return ratio of previous rank
    context = {}
    previous_rank = mileage.get_previous_rank()
    if previous_rank:
        html_previous_rank = render_to_string('mileage/_ratio_rank_item.html',
                                              {'mileage': previous_rank,
                                               'artists_count': artists_count})
        previous_rank_id = previous_rank.pk if previous_rank else ''
        context.update({'previous_rank_id': previous_rank_id})
        context.update({'html_previous_rank': html_previous_rank})
    return context
