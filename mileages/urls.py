# -*- coding: utf-8 -*-
from django.contrib.auth.decorators import login_required
from django.urls import re_path
from mileages.views import *

urlpatterns = [
    re_path(r'^$', MileageRankSetting.as_view(), name='index'),
    re_path(r'^create_mileage$', login_required(ajax_create_mileage), name='ajax_create_mileage'),
    re_path(r'^delete_mileage$', login_required(delete_mileage), name='delete_mileage'),
    re_path(r'^get_mileage_rank_for_artist$', login_required(get_mileage_rank_for_artist),
        name='get_mileage_rank_for_artist'),

]
