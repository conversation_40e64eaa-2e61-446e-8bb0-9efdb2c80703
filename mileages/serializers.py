from rest_framework import serializers
from .models import MileageRank


class MileageRankSerializer(serializers.ModelSerializer):
    formatted_usage_fee = serializers.SerializerMethodField()
    next_rank = serializers.SerializerMethodField()
    previous_rank = serializers.SerializerMethodField()

    class Meta:
        model = MileageRank
        fields = [
            'mileage_id',
            'rank_name_jp',
            'rank_name_en',
            'point',
            'usage_fee',
            'formatted_usage_fee',
            'next_rank',
            'previous_rank',
            'image',
            'is_default',
            'created',
            'modified',
        ]

    def get_formatted_usage_fee(self, obj):
        """
        利用料金をフォーマットして表示する。
        """
        return f"{obj.get_usage_fee()} USD"

    def get_next_rank(self, obj):
        """
        次のランクの情報を取得。
        """
        next_rank = obj.get_next_rank()
        if next_rank:
            return {
                'mileage_id': str(next_rank.mileage_id),
                'rank_name_jp': next_rank.rank_name_jp,
                'rank_name_en': next_rank.rank_name_en,
                'point': next_rank.point,
            }
        return None

    def get_previous_rank(self, obj):
        """
        前のランクの情報を取得。
        """
        previous_rank = obj.get_previous_rank()
        if previous_rank:
            return {
                'mileage_id': str(previous_rank.mileage_id),
                'rank_name_jp': previous_rank.rank_name_jp,
                'rank_name_en': previous_rank.rank_name_en,
                'point': previous_rank.point,
            }
        return None
