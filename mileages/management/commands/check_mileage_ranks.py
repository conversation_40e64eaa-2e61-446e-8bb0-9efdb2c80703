from django.core.management.base import BaseCommand
from django.db.models import Q

from accounts.models import AuthUser
from mileages.models import MileageRank


class Command(BaseCommand):
    help = "Check mileage ranks configuration and diagnose issues"

    def handle(self, *args, **options):
        self.stdout.write("=== Mileage Ranks Configuration Check ===\n")
        
        # 1. 全てのマイレージランクを表示
        self.stdout.write("1. All Mileage Ranks:")
        ranks = MileageRank.objects.all().order_by('point')
        if not ranks.exists():
            self.stdout.write(self.style.ERROR("   No mileage ranks found!"))
        else:
            for rank in ranks:
                self.stdout.write(f"   - {rank.rank_name_jp} (EN: {rank.rank_name_en})")
                self.stdout.write(f"     Point: {rank.point}, Usage Fee: {rank.usage_fee}%, Default: {rank.is_default}")
        
        # 2. デフォルトランクの確認
        self.stdout.write("\n2. Default Rank Check:")
        default_ranks = MileageRank.objects.filter(is_default=True)
        if not default_ranks.exists():
            self.stdout.write(self.style.ERROR("   No default rank found! This will cause issues."))
            self.stdout.write("   Run: python manage.py create_default_mileage_rank")
        elif default_ranks.count() > 1:
            self.stdout.write(self.style.WARNING(f"   Multiple default ranks found ({default_ranks.count()})! Should be only one."))
        else:
            default_rank = default_ranks.first()
            self.stdout.write(self.style.SUCCESS(f"   Default rank: {default_rank.rank_name_jp} (Point: {default_rank.point})"))
        
        # 3. Point=0のランクの確認
        self.stdout.write("\n3. Zero-point Rank Check:")
        zero_ranks = MileageRank.objects.filter(point=0)
        if not zero_ranks.exists():
            self.stdout.write(self.style.WARNING("   No rank with point=0 found."))
        else:
            for rank in zero_ranks:
                self.stdout.write(f"   - {rank.rank_name_jp} (Default: {rank.is_default})")
        
        # 4. 問題のあるユーザーの確認
        self.stdout.write("\n4. Users with Potential Issues:")
        
        # マイナスポイントのユーザー
        negative_users = AuthUser.objects.filter(role=AuthUser.CREATOR, total_point__lt=0, is_active=True)
        if negative_users.exists():
            self.stdout.write(f"   - Users with negative points: {negative_users.count()}")
            for user in negative_users[:5]:  # 最初の5人のみ表示
                self.stdout.write(f"     * {user.get_full_name()} (ID: {user.pk}): {user.total_point} points")
                rank = user.get_mileage_for_artist()
                if rank:
                    self.stdout.write(f"       Rank: {rank.rank_name_jp}")
                else:
                    self.stdout.write(self.style.ERROR("       Rank: None (ERROR!)"))
        
        # 5. ランクの重複チェック
        self.stdout.write("\n5. Duplicate Points Check:")
        from django.db.models import Count
        duplicates = MileageRank.objects.values('point').annotate(count=Count('point')).filter(count__gt=1)
        if duplicates:
            self.stdout.write(self.style.ERROR("   Ranks with duplicate points found:"))
            for dup in duplicates:
                self.stdout.write(f"   - Point {dup['point']}: {dup['count']} ranks")
        else:
            self.stdout.write(self.style.SUCCESS("   No duplicate points found."))
        
        # 6. 推奨事項
        self.stdout.write("\n=== Recommendations ===")
        if not default_ranks.exists():
            self.stdout.write("1. Create a default rank: python manage.py create_default_mileage_rank")
        if not zero_ranks.exists():
            self.stdout.write("2. Create a rank with point=0 for new users")
        if negative_users.exists():
            self.stdout.write("3. Review users with negative points and their evaluation_point values") 