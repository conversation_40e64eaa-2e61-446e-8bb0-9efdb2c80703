from django.core.management.base import BaseCommand

from mileages.models import MileageRank


def create_default_mileage_rank():
    try:
        MileageRank.objects.create(is_default=True, rank_name_en='Standard', rank_name_jp='スタンダード', point=0,
                                   usage_fee=22.5)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "Create default mileage rank with point = 0"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        create_default_mileage_rank()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
