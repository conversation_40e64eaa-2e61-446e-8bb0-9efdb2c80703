from django.shortcuts import render, redirect

# Create your views here.
from django.conf import settings
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse, HttpResponseBadRequest
from django.views.generic import CreateView

from app.tasks import delete_folder_empty
from . import tasks
from . import forms
from . import models
from .models import ContactFolder, ContactInfo
from .services import save_form_contact_service


class ContactView(CreateView):
    template_name = "contacts/contact.html"
    form_class = forms.ContactInfoForm
    model = models.ContactInfo

    def get_success_url(self):
        return reverse_lazy('app:index')

    def get_context_data(self, **kwargs):
        context = {}
        from hashlib import sha256
        from datetime import datetime 
        hash = sha256(datetime.today().strftime("%Y-%m-%d").encode('utf-8')).hexdigest()
        form = super(ContactView, self).get_form()
        context.update({'sign': hash, 'form': form})
        return context

    def form_valid(self, form):
        self.object = form.save()

    def post(self, request, *args, **kwargs):

        if self.request.user.is_authenticated:
            return redirect('app:index')
        else:
            from hashlib import sha256
            from datetime import datetime 
            hash = sha256(datetime.today().strftime("%Y-%m-%d").encode('utf-8')).hexdigest()
            sign = request.POST.get('sign', None)
            if not hash == sign:
                return JsonResponse({'status': 'success'}, status=200)
        form = forms.ContactInfoForm(request.POST)
        if not form.is_valid():
            data = list(form.errors.items())
            return HttpResponseBadRequest(data, content_type='application/json')
        save_form_contact_service(form, request)
        return JsonResponse({'status': 'success'}, status=200)

    def get(self, request, *args, **kwargs):
        if self.request.user.is_authenticated:
            return redirect('app:index')
        return super(ContactView, self).get(request, *args, **kwargs)
