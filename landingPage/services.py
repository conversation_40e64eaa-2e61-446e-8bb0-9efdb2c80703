from django.conf import settings
from landingPage import tasks
from landingPage.models import ContactInfo, ContactFile


def save_form_contact_service(form, request):
    instance = form.save()
    contact = ContactInfo.objects.get(pk=instance.pk)
    list_file_id = request.POST.get('list_file_id', None)

    if list_file_id:
        list_file_id = list_file_id.split(",")
    if list_file_id:
        list_files = ContactFile.objects.filter(pk__in=list_file_id)
        for f in list_files:
            f.contact_info = contact
            f.save()
    host = settings.HOST
    tasks.send_email_when_user_contact.delay(contact.email, contact.pk, host)
