{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util %}
{% load i18n compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css"
        integrity="sha512-aOG0c6nPNzGk+5zjwyJaoRUgCdOrfSDhmMID2u4+OIslr0GjpLKo7Xm0Ao3xmpM4T8AmIouRkqwj1nrdVsLKEQ=="
        crossorigin="anonymous"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
  {% endcompress %}
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link href="{% static 'css/contact.css' %}" rel="stylesheet">
  {% endcompress %}
  <script src="https://www.google.com/recaptcha/api.js?hl=ja&onload=onloadCallback&render=explicit" async defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
{% endblock %}

{% block content %}
  <div class="contact-info">
    <div class="container">
      <div class="contact-info__main">
        <div class="contact-info__heading">
          <h3 class="heading--wrap">CONTACT</h3>
        </div>

        <div class="contact-info__content">
          <form class="contact-info__form row" id="form-contact" method="post"
                action="" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="account__form-group form-group col-sm-12">
              <h3 class="account__form-heading" style="padding: 0 0 16px; border-top: none;">{% trans "title body heading popup confirm contact" %}</h3>
              <div class="col-sm-12 form-group">
                <label class="col-sm-12" for="">
                  <div class="account__sub-group">
                    {% for choice in form.purpose %}
                      <div class="account__form-multi">
                        <label class="input-radio">
                          <input type="radio" name="{{ choice.data.name }}"
                                 {% if choice.data.selected %}checked{% endif %}
                                 value={{ choice.data.value }} index={{ choice.data.index }}
                                 required={{ choice.data.attrs.required }} data-value="{{ choice.data.label }}"
                                         id="{{ choice.data.attrs.id }}" />{{ choice.data.label }}
                          <div class="check-mark"></div>
                        </label>
                      </div>
                    {% endfor %}
                  </div>
                </label>
              </div>
            </div>

            <div class="account__form-group form-group col-sm-12">
              <h3 class="account__form-heading">{% trans "detail confirm contact" %}</h3>
              <div class="col-sm-12 form-group">
                <label class="col-sm-4" for="id_fullname">
                  <span class="account__field-label">{% trans "Full name" %} <span class="blue-label--8">{% trans "Required blue" %}</span></span>
                  {{ form.fullname }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-4" for="id_email">
                  <span class="account__field-label">{% trans "Email address" %} <span class="blue-label--8">{% trans "Required blue" %}</span></span>
                  {{ form.email }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-4" for="id_email_confirm">
                  <span class="account__field-label">{% trans "Email address confirmation" %} <span class="blue-label--8">{% trans "Required blue" %}</span></span>
                  {{ form.email_confirm }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-4" for="id_job_type">
                  <span class="account__field-label">役職・ポジション <span class="grey-label--8">{% trans "Required grey" %}</span></span>
                  {{ form.job_type }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-4" for="id_enterprise">
                  <span class="account__field-label">{% trans "Company Name" %} <span class="grey-label--8">{% trans "Required grey" %}</span></span>
                  {{ form.enterprise }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-4" for="id_company_url">
                  <span class="account__field-label">{% trans "Website" %} <span class="grey-label--8">{% trans "Required grey" %}</span></span>
                  {{ form.company_url }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-4" for="id_phone">
                  <span class="account__field-label">{% trans "Phone number (number that can be contacted during the day)" %} <span
                          class="grey-label--8">{% trans "Required grey" %}</span></span>
                  {{ form.phone }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-12" for="">
                  <span class="account__field-label">{% trans "Your preferred contact method" %}</span>
                  {% for choice in form.contact_channel %}
                    <div class="account__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="{{ choice.data.name }}"
                               {% if choice.data.selected %}checked{% endif %}
                               value={{ choice.data.value }} index={{ choice.data.index }}
                               required={{ choice.data.attrs.required }} data-value="{{ choice.data.label }}"
                                       id="{{ choice.data.attrs.id }}" />{{ choice.data.label }}
                        <div class="check-mark"></div>
                      </label>
                    </div>
                  {% endfor %}
                </label>
              </div>

            </div>

            <div class="account__form-group form-group col-sm-12">
              <h3 class="account__form-heading">{% trans "Contents of inquiry" %}</h3>
              <div class="col-sm-12 form-group">
                <label class="col-sm-12" for="id_message">
                  <span class="account__field-label">メッセージ <span class="blue-label--8">{% trans "Required blue" %}</span></span>
                  <div class="form-textarea">
                    {{ form.message|attr:'required:true' }}
                  </div>
                </label>
              </div>
            </div>

            <div class="account__form-group form-group col-sm-12">
              <div class="col-sm-12 form-group">
                <label class="col-sm-12" for="">
                  <span class="account__field-label">{% trans "Document" %} <span class="grey-label--8">{% trans "Required grey" %}</span></span>

                  <div class="account_upload-file mattach mattach-form">
                    <div class="mcomment-attached">
                      <div class="mattach-preview-container mattach-preview-container-form">
                        <div class="mattach-previews mattach-previews-form collection">
                          <div class="mattach-template mattach-template-form collection-item item-template">
                            <div class="mattach-info" data-dz-thumbnail="">
                              <div class="mcommment-file">
                                <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                        class="icon icon--sicon-close"></i>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="myDropZone" class="fallback dropzone">
                    </div>
                    <p class="account__field-text">{% trans "You can send the entire folder by dragging and dropping." %}</p>
                  </div>
                </label>
              </div>
            </div>
            
            <div class="account__form-group form-group col-sm-12" style="margin-bottom: 24px;">
              <div id="google_recaptcha"></div>
            </div>

            <div class="contact-info__submit col-sm-12 account__action">
              {% buttons %}
                <input type="button" value="次へ (内容の確認)" id="btn__ok" data-toggle="modal" data-target="#modalConfirm"
                       class="btn btn--primary"/>
              {% endbuttons %}
              <div class="account__field-text-link">{% trans "SOREMO complies with laws and regulations regarding the protection of personal information and at the same time." %}
                <a href="/privacypolicy" target="_blank" class="account__field-text-blue">{% trans "Privacy policy" %}</a>{% trans "We will handle your personal information based on." %}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <div class="upload-final-product-file upload-button-wrapper">
    <p>{% trans "Uploading" %}</p>
    <div class="fill">
      <div class="process"></div>
    </div>
    <div class="fa fa-check"></div>
  </div>

  <!-- Modal confirm -->
  <div class="modal popup-container popup-confirm fade" id="modalConfirm" role="dialog" style="z-index: 9998">
    <div class="modal-dialog popup-dialog">
      <div class="modal-content popup-content">
        <div class="popup-header">
          <h4 class="popup-title" style="margin-bottom: 12px;">{% trans "title popup confirm contact" %}</h4>
          <hr/>
        </div>
        <div class="popup-body">
          <div class="popup-body__wrap">
            <div class="popup-body__list">
              <div class="popup-body__item first-item-head">
                <div class="popup-body__heading" style="padding: 0; margin-bottom: 16px; margin-top: 0px;">{% trans "title body heading popup confirm contact" %}:</div>
                <div class="popup-body__text text-content" id="purpose">{% trans "text body heading popup confirm contact" %}</div>
              </div>
            </div>

          </div>
          <div class="popup-body__wrap">
            <div class="popup-body__heading">{% trans "detail confirm contact" %}</div>
            <div class="popup-body__list">
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Full name" %}:</div>
                <div class="popup-body__text text-content" id="fullname">{% trans "text body heading popup confirm contact" %}</div>
              </div>
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Email address" %}:</div>
                <div class="popup-body__text text-content" id="email"><EMAIL></div>
              </div>
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Job title / position"%}:</div>
                <div class="popup-body__text text-content" id="job_type">{% trans "Director" %}</div>
              </div>
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Company Name" %}:</div>
                <div class="popup-body__text text-content" id="enterprise">{% trans "Solemo Co., Ltd." %}</div>
              </div>
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Website" %}:</div>
                <div class="popup-body__text text-content" id="company_url">https://soremo.jp</div>
              </div>
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Phone number (number that can be contacted during the day)" %}:</div>
                <div class="popup-body__text text-content" id="phone">03-6457-1780</div>
              </div>
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Your preferred contact method" %}:</div>
                <div class="popup-body__text text-content" id="contact_channel">{% trans "Email" %}</div>
              </div>
            </div>
          </div>

          <div class="popup-body__wrap" style="border-bottom:none;">
            <div class="popup-body__heading">{% trans "Contents of inquiry" %}</div>
            <div class="popup-body__list">
              <div class="popup-body__item">
                <div class="popup-body__text text-title">{% trans "Message" %} :</div>
                <div class="popup-body__text text-content" id="message" style="white-space: break-spaces;">
                  {% trans "I would like to discuss the sound development of a new project. For the details of the project, we have sent you the specifications, so please check it. We look forward to hearing from you. Has sent you the specifications, so please check. Occasion" %}
                </div>
              </div>
            </div>
          </div>

          <div class="popup-body__wrap">
            <div class="popup-body__text text-title" style="margin-top: 24px;">{% trans "Document" %}</div>
            <div class="popup-body__list">
              <div class="popup-body__item" id="user-file">
              </div>
              <div class="popup-body__item">
                <div class="account__field-text-link" style="padding: 0; border: 0;">{% trans "SOREMO complies with laws and regulations regarding the protection of personal information and at the same time." %}
                  <a href="/privacypolicy" target="_blank" class="account__field-text-blue">{% trans "Privacy policy" %}</a>{% trans "We will handle your personal information based on." %}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="popup-footer">
          <button type="button" class="btn btn--tertiary btn-popup-close" data-dismiss="modal">{% trans "Return" %}</button>
          <button type="button" class="btn btn--primary btn-popup-send" id="submit--form">{% trans "Send" %}</button>
        </div>
      </div>
    </div>
  </div>
  <!-- End modal confirm -->


  <!-- Modal delete -->
  <div class="modal popup-container" id="modalOk" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog popup-dialog" role="document">
      <div class="modal-content popup-content">
        <div class="popup-header"></div>
        <div class="popup-body">
          <h5 class="popup-title" style="text-align: left !important;">{% trans "Send completely" %}</h5>
          <p class="popup-text" style="text-align: left !important;">{% trans "We have sent you an email, so please check it." %}。</p>
        </div>
        <div class="popup-footer" style="text-align: right !important;">
          <button type="button" class="btn btn--primary btn-popup-send" data-dismiss="modal">{% trans "Ok" %}</button>
        </div>
      </div>
    </div>
  </div>
  <!-- End modal delete -->

{% endblock content %}
{% block extra_script %}
  <script>
      let sign = "{{ sign }}"
  </script>
  <script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}"; </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
  <script src="{% url 'javascript-catalog' %}"></script>
  {% compress js inline %}
  <script src="{% static 'js/isInViewport.min.js' %}"></script>
  <script src="{% static 'js/main.js' %}"></script>

  <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
  <script src="{% static 'js/common_variable.js' %}"></script>
  <script src="{% static 'js/validate_contract.js' %}"></script>
  <script src="{% static 'js/contact_info.js' %}"></script>
  {% endcompress %}
{% endblock %}
