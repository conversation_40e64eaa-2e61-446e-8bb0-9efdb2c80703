# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-09-21 11:53
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContactFile',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('file_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(blank=True, max_length=1024, upload_to='file')),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ContactFolder',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('folder_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=512)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ContactInfo',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('contact_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('purpose', models.CharField(choices=[('project', 'お仕事のご相談'), ('recruitment', 'ご応募'), ('others', 'その他')], default='project', max_length=30, verbose_name='種類')),
                ('fullname', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='氏名')),
                ('email', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='メールアドレス')),
                ('email_confirm', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='メールアドレス（確認）')),
                ('job_type', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='役職・ポジション')),
                ('enterprise', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='会社名')),
                ('company_url', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='WEBサイト')),
                ('phone', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='電話番号（日中連絡がとれる番号）')),
                ('contact_channel', models.CharField(choices=[('email', 'メール'), ('phone', '電話')], default='email', max_length=20, verbose_name='ご希望の連絡方法')),
                ('message', models.TextField(blank=True, default='', max_length=500, null=True, verbose_name='メッセージ ')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='contactfolder',
            name='contact_info',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='folders', to='landingPage.ContactInfo'),
        ),
        migrations.AddField(
            model_name='contactfolder',
            name='parent',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_folders', to='landingPage.ContactFolder'),
        ),
        migrations.AddField(
            model_name='contactfile',
            name='contact_info',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='files', to='landingPage.ContactInfo'),
        ),
        migrations.AddField(
            model_name='contactfile',
            name='folder',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='landingPage.ContactFolder'),
        ),
    ]
