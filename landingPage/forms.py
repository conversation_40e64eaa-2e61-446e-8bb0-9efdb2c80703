import datetime
import re

from django import forms
from landingPage.models import *


class ContactInfoForm(forms.ModelForm):
    fullname = forms.CharField(max_length=60, required=True,
                               widget=forms.TextInput(
                                   attrs={'placeholder': '善里 信哉', 'class': 'form-control account__input-text'}))
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(
                                attrs={'placeholder': '<EMAIL>', 'class': 'form-control account__input-text'}))
    email_confirm = forms.CharField(max_length=50, required=True,
                                    widget=forms.TextInput(attrs={'placeholder': '<EMAIL>',
                                                                  'class': 'form-control account__input-text'}))
    job_type = forms.CharField(max_length=60, required=False,
                               widget=forms.TextInput(
                                   attrs={'placeholder': 'ディレクター', 'class': 'form-control account__input-text'}))
    enterprise = forms.Char<PERSON>ield(max_length=60, required=False,
                                 widget=forms.TextInput(
                                     attrs={'placeholder': '株式会社ソレモ', 'class': 'form-control account__input-text'}))
    company_url = forms.CharField(max_length=60, required=False,
                                  widget=forms.TextInput(attrs={'placeholder': 'https://soremo.jp',
                                                                'class': 'form-control account__input-text'}))
    phone = forms.CharField(max_length=30, required=False,
                            widget=forms.TextInput(
                                attrs={'placeholder': '03-6457-1780', 'class': 'form-control account__input-text'}))
    message = forms.CharField(max_length=1000, required=False,
                              widget=forms.Textarea(attrs={'class': 'form-textarea',
                                                           'placeholder': '新規プロジェクトのサウンド開発をご相談したいです。プロジェクト詳細は、仕様書をお送りしましたので、\nご確認ください。折り返しをお待ちしております。'}))

    class Meta:
        model = ContactInfo
        fields = ('purpose', 'fullname', 'email', 'email_confirm', 'job_type', 'enterprise', 'company_url', 'phone',
                  'contact_channel',
                  'message')
        widgets = {
            'purpose': forms.RadioSelect(),
            'contact_channel': forms.RadioSelect(),
        }
