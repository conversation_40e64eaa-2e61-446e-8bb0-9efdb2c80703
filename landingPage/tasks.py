import datetime
import os
import re
import redis

import vlc
import time
from celery.schedules import crontab
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist

from django.core.mail import send_mail
from django.db import transaction
from django.db.models import Prefetch, Q
from django.template import loader
from django.urls import reverse, reverse_lazy
from django.utils.html import strip_tags

from accounts.models import AuthUser
from app.tasks import get_user_url
from landingPage.models import ContactInfo
from voice.celery import app


@app.task
def send_email_when_user_contact(user_email, contact_info_id, host):
    template_email = 'email/template_mail_contact_info.html'

    contact_info = ContactInfo.objects.filter(pk=contact_info_id).first()
    if contact_info:
        master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
        from_email = os.getenv('EMAIL_ADDRESS')
        context = {}
        for user in master_admins:
            subject = "【SOREMO】お問い合わせが届いています"
            text2 = '内容を確認の上、折り返し対応を進めてください。'
            recipient_avt = get_user_url(user, host, 'medium')
            context = {}
            context.update({'recipient': user,
                            'text2': text2,
                            'recipient_avt': recipient_avt,
                            'host': host,
                            'contact_info': contact_info
                            })
            html_message = loader.render_to_string(template_email, context).strip()
            body = strip_tags(html_message)
            send_mail(subject, message=body, from_email=from_email,
                      recipient_list=[user.email], fail_silently=False, html_message=html_message)

        subject = "【SOREMO】お問い合わせを承りました"
        text2 = '内容を確認の上、折り返しご連絡いたします。'
        context = {}
        context.update({'recipient': None,
                        'text2': text2,
                        'host': host,
                        'contact_info': contact_info
                        })
        html_message = loader.render_to_string(template_email, context).strip()
        body = strip_tags(html_message)
        send_mail(subject, message=body, from_email=from_email,
                  recipient_list=[user_email], fail_silently=False, html_message=html_message)
