from __future__ import unicode_literals

from django.db import models
import datetime
import io
import re
import uuid

# Create your models here.


class ModelBase(models.Model):
    modified = models.DateTimeField(auto_now=True, )
    created = models.DateTimeField(auto_now_add=True, )

    class Meta:
        abstract = True


class ContactInfo(ModelBase):
    PURPOSE = (
        ('project', 'お仕事のご相談'),
        ('recruitment', 'ご応募'),
        ('others', 'その他'),
    )
    CONTACT_CHANNEL = (
        ('email', 'メール'),
        ('phone', '電話')
    )

    PLAN = (
        ('standard', 'STANDARD'),
        ('pro', 'PRO')
    )

    contact_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    purpose = models.CharField(choices=PURPOSE, default='project', max_length=30, verbose_name='種類')
    fullname = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='氏名')
    email = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='メールアドレス')
    email_confirm = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='メールアドレス（確認）')
    job_type = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='役職・ポジション')
    enterprise = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='会社名')
    company_url = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='WEBサイト')
    phone = models.CharField(default='', max_length=100, null=True, blank=True, verbose_name='電話番号（日中連絡がとれる番号）')
    contact_channel = models.CharField(choices=CONTACT_CHANNEL, default='email', max_length=20, verbose_name='ご希望の連絡方法')
    message = models.TextField(max_length=1000, default='', null=True, blank=True, verbose_name='メッセージ ')
    offer = models.ForeignKey('app.OfferProduct', related_name='contract_info', on_delete=models.CASCADE, blank=True,
                              null=True)
    plan = models.CharField(choices=PLAN, max_length=100, null=True, blank=True, verbose_name='サービスプラン')
    option = models.CharField(max_length=100, null=True, blank=True, verbose_name='サービスプラン')

class ContactFolder(ModelBase):
    folder_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent = models.ForeignKey('ContactFolder', related_name="child_folders", null=True, on_delete=models.CASCADE)
    name = models.CharField(max_length=512, null=False)
    contact_info = models.ForeignKey('ContactInfo', related_name="folders", null=True, on_delete=models.CASCADE)


class ContactFile(ModelBase):
    file_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    contact_info = models.ForeignKey('ContactInfo', related_name="files", null=True, on_delete=models.SET_NULL)
    file = models.FileField(upload_to='file', blank=True, max_length=1024)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    folder = models.ForeignKey('ContactFolder', related_name="children", null=True, on_delete=models.CASCADE)

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            self.real_name = name
        super(ContactFile, self).save(*args, **kwargs)
