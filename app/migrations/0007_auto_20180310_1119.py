# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2018-03-10 11:19
from __future__ import unicode_literals

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0006_auto_20171210_1752'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='product',
            options={'ordering': ['-created']},
        ),
        migrations.AlterModelOptions(
            name='scene',
            options={'ordering': ['-created']},
        ),
        migrations.AddField(
            model_name='product',
            name='last_update',
            field=models.DateTimeField(default=datetime.datetime.now, verbose_name='最終更新日'),
        ),
        migrations.AlterField(
            model_name='scene',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scene_product', to='app.Product'),
        ),
        migrations.AlterField(
            model_name='scenecomment',
            name='scene',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scene_comment_scene', to='app.Scene'),
        ),
    ]
