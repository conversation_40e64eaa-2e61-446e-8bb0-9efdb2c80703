# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-03-13 21:30
from __future__ import unicode_literals

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0208_product_contact_artist'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderData',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('data_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('message', models.CharField(default='', max_length=1000)),
                ('type_contact', models.CharField(choices=[('1', 'sale'), ('2', 'artist')], default='1', max_length=100)),
                ('sale_name', models.Char<PERSON>ield(default='', max_length=500)),
                ('budget', models.FloatField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)])),
                ('artist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('offer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='order_data', to='app.OfferProduct')),
                ('sale_content', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='app.SaleContent')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='productmessagefile',
            name='order_data',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='files', to='app.OrderData'),
        ),
        migrations.AddField(
            model_name='productmessagefolder',
            name='order_data',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='folders', to='app.OrderData'),
        ),
    ]
