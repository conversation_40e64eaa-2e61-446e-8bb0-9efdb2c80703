# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-10-30 22:44
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0223_auto_20221014_1355'),
    ]

    operations = [
        migrations.AddField(
            model_name='productcommentfile',
            name='acr_filescanning',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AddField(
            model_name='productcommentfile',
            name='acr_filescanning_id',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='productcommentfile',
            name='acr_status',
            field=models.CharField(choices=[('1', 'not_start'), ('2', 'uploaded'), ('3', 'has_result'), ('4', 'not_match')], default='1', max_length=100),
        ),
        migrations.AddField(
            model_name='scene',
            name='acr_filescanning',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AddField(
            model_name='scene',
            name='acr_filescanning_id',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='scene',
            name='acr_status',
            field=models.CharField(choices=[('1', 'not_start'), ('2', 'uploaded'), ('3', 'has_result'), ('4', 'not_match')], default='1', max_length=100),
        ),
        migrations.AddField(
            model_name='scenecommentfile',
            name='acr_filescanning',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AddField(
            model_name='scenecommentfile',
            name='acr_filescanning_id',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='scenecommentfile',
            name='acr_status',
            field=models.CharField(choices=[('1', 'not_start'), ('2', 'uploaded'), ('3', 'has_result'), ('4', 'not_match')], default='1', max_length=100),
        ),
    ]
