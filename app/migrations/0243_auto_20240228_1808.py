# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2024-02-28 18:08
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0242_creatorofferfile'),
    ]

    operations = [
        migrations.CreateModel(
            name='ColorProjectSetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('active', models.BooleanField(default=True)),
                ('code', models.CharField(max_length=50)),
                ('css_code', models.CharField(max_length=50)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FontProjectSetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('active', models.BooleanField(default=True)),
                ('code', models.CharField(max_length=50)),
                ('css_code', models.CharField(max_length=50)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='product',
            name='code_color',
        ),
        migrations.RemoveField(
            model_name='product',
            name='code_font',
        ),
        migrations.AddField(
            model_name='product',
            name='color_setting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.ColorProjectSetting'),
        ),
        migrations.AddField(
            model_name='product',
            name='font_setting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.FontProjectSetting'),
        ),
    ]
