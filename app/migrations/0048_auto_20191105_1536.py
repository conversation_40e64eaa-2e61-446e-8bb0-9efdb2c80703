# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-11-05 15:36
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0026_auto_20191031_1616'),
        ('app', '0047_merge_20191030_1125'),
    ]

    operations = [
        migrations.CreateModel(
            name='AudioSale',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('file', models.FileField(upload_to='audio/', verbose_name='オーディオデータ')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ContentSale',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('title', models.CharField(blank='', default='', max_length=200, null=True, verbose_name='タイトル')),
                ('price', models.IntegerField(blank=True, null=True, verbose_name='価格（税込）')),
                ('desc', models.TextField(max_length=255, verbose_name='説明（OPTIONAL）')),
                ('image', models.FileField(blank=True, upload_to='images')),
                ('x', models.FloatField(default=0)),
                ('y', models.FloatField(default=0)),
                ('width', models.FloatField(default=0)),
                ('height', models.FloatField(default=0)),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='content_owner', to='accounts.Creator')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='audiosale',
            name='sale',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='audio_sale', to='app.ContentSale'),
        ),
    ]
