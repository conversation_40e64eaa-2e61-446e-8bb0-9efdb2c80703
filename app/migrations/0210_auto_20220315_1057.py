# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-03-15 10:57
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0082_auto_20220210_1743'),
        ('app', '0209_auto_20220313_2130'),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemSectionCredit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('link_profile', models.BooleanField(default=True)),
                ('title', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('position', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('artist_name', models.Char<PERSON><PERSON>(blank=True, default='', max_length=256, null=True)),
                ('artist_name_en', models.Char<PERSON>ield(blank=True, default='', max_length=256, null=True)),
                ('order', models.IntegerField(default=1)),
                ('project_artist', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='item_sections', to='accounts.ProductUser')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='SectionCredit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('section_name', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('section_name_en', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('explanation', models.CharField(blank=True, default='', max_length=1000, null=True)),
                ('explanation_en', models.CharField(blank=True, default='', max_length=1000, null=True)),
                ('order', models.IntegerField(default=1)),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='app.Product')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='itemsectioncredit',
            name='section',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='items', to='app.SectionCredit'),
        ),
    ]
