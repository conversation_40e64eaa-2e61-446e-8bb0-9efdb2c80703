# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-08-19 16:00
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0086_offerproduct_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlanOffer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('content', models.TextField(verbose_name='コメント')),
                ('file', models.FileField(blank=True, null=True, upload_to='file')),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('status', models.CharField(choices=[('1', 'processing'), ('2', 'done')], default='1', max_length=100)),
                ('offer_product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='plan_offer', to='app.OfferProduct')),
                ('owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owner_plan', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
    ]
