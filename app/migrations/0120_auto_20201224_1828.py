# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-12-24 18:28
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0046_auto_20201224_1828'),
        ('app', '0119_auto_20201221_1737'),
    ]

    operations = [
        migrations.CreateModel(
            name='AlbumVariation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AlbumVersion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('1', 'approve'), ('2', 'editing'), ('3', 'old'), ('4', 'reject')], default='2', max_length=20)),
                ('file', models.FileField(upload_to='audio/', verbose_name='オーディオデータ')),
                ('album', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='album_versions', to='app.AlbumVariation')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleContent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleContentVersion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('title', models.CharField(blank='', default='', max_length=60, null=True, verbose_name='タイトル')),
                ('price', models.IntegerField(blank=True, null=True, verbose_name='価格（税込）')),
                ('desc', models.TextField(max_length=400, verbose_name='説明（OPTIONAL）')),
                ('status', models.CharField(choices=[('submitted', '申請中'), ('reviewed', '審査中'), ('publish', '公開中'), ('unpublished', '非公開'), ('deleted', 'deleted')], default='submitted', max_length=20)),
                ('image', models.FileField(blank=True, upload_to='images')),
                ('x', models.FloatField(default=0)),
                ('y', models.FloatField(default=0)),
                ('width', models.FloatField(default=0)),
                ('height', models.FloatField(default=0)),
                ('version_status', models.CharField(choices=[('1', 'approve'), ('2', 'editing'), ('3', 'old'), ('4', 'reject')], default='2', max_length=20)),
                ('sale_type', models.CharField(choices=[('1', 'approve'), ('2', 'editing'), ('3', 'old'), ('4', 'reject')], default='3', max_length=20)),
                ('sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_content', to='app.SaleContent')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='salecontent',
            name='last_published_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='published_content', to='app.SaleContentVersion'),
        ),
        migrations.AddField(
            model_name='salecontent',
            name='last_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='last_content', to='app.SaleContentVersion'),
        ),
        migrations.AddField(
            model_name='salecontent',
            name='profile',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='content_profile', to='accounts.CreatorProfile'),
        ),
        migrations.AddField(
            model_name='albumvariation',
            name='last_published_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='published_album', to='app.AlbumVersion'),
        ),
        migrations.AddField(
            model_name='albumvariation',
            name='last_version',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='last_album', to='app.AlbumVersion'),
        ),
        migrations.AddField(
            model_name='albumvariation',
            name='sale_content',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='album', to='app.SaleContentVersion'),
        ),
    ]
