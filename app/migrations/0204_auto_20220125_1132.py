# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-01-25 11:32
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0203_auto_20220120_1531'),
    ]

    operations = [
        migrations.AlterField(
            model_name='salecontentversion',
            name='song_attribute1_max',
            field=models.IntegerField(blank=True, default=4, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='song_attribute1_min',
            field=models.IntegerField(blank=True, default=2, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name='salecontentversion',
            name='song_attribute2_max',
            field=models.IntegerField(blank=True, default=4, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='song_attribute2_min',
            field=models.IntegerField(blank=True, default=2, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
    ]
