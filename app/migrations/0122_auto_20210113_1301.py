# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-01-13 13:01
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0121_auto_20201231_1225'),
    ]

    operations = [
        migrations.CreateModel(
            name='HashTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tag_name', models.CharField(max_length=60)),
            ],
        ),
        migrations.CreateModel(
            name='SaleContentTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='content_type',
            field=models.CharField(choices=[('music', 'MUSIC'), ('voice', 'VOICE'), ('sound_effect', 'SOUND EFFECTS')], default='1', max_length=20),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='default_thumbnail',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='end_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='max_price',
            field=models.IntegerField(blank=True, null=True, verbose_name='最高価格（税込）'),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='song_attribute1',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='song_attribute2',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='start_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='price',
            field=models.IntegerField(blank=True, null=True, verbose_name='開始価格（税込）'),
        ),
        migrations.AddField(
            model_name='salecontenttag',
            name='sale_content',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.SaleContentVersion'),
        ),
        migrations.AddField(
            model_name='salecontenttag',
            name='tag',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.HashTag'),
        ),
    ]
