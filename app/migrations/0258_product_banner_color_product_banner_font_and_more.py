# Generated by Django 5.2.2 on 2025-06-17 19:36

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0257_alter_formcontractandplan_creation_method_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='banner_color',
            field=models.CharField(blank=True, default='1', max_length=10, null=True, verbose_name='banner_color'),
        ),
        migrations.AddField(
            model_name='product',
            name='banner_font',
            field=models.CharField(blank=True, default='1', max_length=10, null=True, verbose_name='banner_font'),
        ),
        migrations.AlterField(
            model_name='albumvariation',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='albumversion',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='audio',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='blocklistartist',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='bookmarklistbookmarks',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='category',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='colorprojectsetting',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='contentsale',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creatorofferfile',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='fontprojectsetting',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='creation_method',
            field=models.CharField(choices=[('generate', '生成方法'), ('upload', 'アップロード方法')], max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='delivery_place',
            field=models.IntegerField(choices=[(1, 'SOREMO WEBサービス内'), (2, '御社指定')], default=None),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='form_type',
            field=models.IntegerField(choices=[(1, '見積書'), (2, '契約書'), (3, 'All')], default=None),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='pick_up_method',
            field=models.IntegerField(choices=[(1, 'SOREMO WEBサービス内'), (2, '御社指定')], default=None),
        ),
        migrations.AlterField(
            model_name='hashtag',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='itemsectioncredit',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='listbookmark',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='listwork',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='messagereceiver',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offercreator',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offermessagereceiver',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offerproduct',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offeruser',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='planoffer',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='product',
            name='artists_block',
            field=models.ManyToManyField(blank=True, through='app.BlockListArtist', to=settings.AUTH_USER_MODEL, verbose_name='blocked_users'),
        ),
        migrations.AlterField(
            model_name='product',
            name='client_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='end_client'),
        ),
        migrations.AlterField(
            model_name='product',
            name='code_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='code_name'),
        ),
        migrations.AlterField(
            model_name='product',
            name='color_setting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.colorprojectsetting', verbose_name='banner_color_old'),
        ),
        migrations.AlterField(
            model_name='product',
            name='current_heart',
            field=models.FloatField(default=0, verbose_name='approval_deliverbles_count'),
        ),
        migrations.AlterField(
            model_name='product',
            name='current_scene',
            field=models.IntegerField(default=0, verbose_name='current_deliverbles_count'),
        ),
        migrations.AlterField(
            model_name='product',
            name='description',
            field=models.TextField(blank=True, max_length=1000, null=True, verbose_name='overview'),
        ),
        migrations.AlterField(
            model_name='product',
            name='end_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='end_date'),
        ),
        migrations.AlterField(
            model_name='product',
            name='font_setting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.fontprojectsetting', verbose_name='banner_font_old'),
        ),
        migrations.AlterField(
            model_name='product',
            name='image',
            field=models.FileField(blank=True, upload_to='images', verbose_name='banner_image_original'),
        ),
        migrations.AlterField(
            model_name='product',
            name='image_resized',
            field=models.FileField(blank=True, upload_to='images', verbose_name='banner_image_optimized'),
        ),
        migrations.AlterField(
            model_name='product',
            name='last_update',
            field=models.DateTimeField(default=datetime.datetime.now, verbose_name='updated_at'),
        ),
        migrations.AlterField(
            model_name='product',
            name='max_scene',
            field=models.IntegerField(default=5, verbose_name='total_deliverbles_count'),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.CharField(max_length=256, verbose_name='final_work'),
        ),
        migrations.AlterField(
            model_name='product',
            name='start_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='start_date'),
        ),
        migrations.AlterField(
            model_name='productmilestone',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontent',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontentlistwork',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontentselection',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontenttag',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenecommentreceiver',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenesharelink',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenetaken',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenetakenscenes',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenetitlebookmark',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='sectioncredit',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='topiccategory',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='topictag',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='userproductcharge',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
    ]
