# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-10-26 22:40
from __future__ import unicode_literals

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0168_scenecomment_updated_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='DownloadedProductMessageFile',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('downloaded_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductMessageFile',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('file_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(blank=True, max_length=1024, upload_to='file')),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('peaks', models.TextField(blank=True, default='', null=True)),
                ('type_file', models.CharField(choices=[('1', 'default'), ('2', 'contract'), ('3', 'bill'), ('4', 'offer')], default='1', max_length=100)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductMessageFolder',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('folder_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=512)),
                ('type_file', models.CharField(choices=[('1', 'default'), ('2', 'offer')], default='1', max_length=100)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RenameField(
            model_name='productmessage',
            old_name='offer_product',
            new_name='offer',
        ),
        migrations.RenameField(
            model_name='productmessage',
            old_name='owner',
            new_name='user',
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='bgm',
            field=models.CharField(choices=[('yes', '新規制作したい'), ('prepare_with_license', '利用権（非独占）ライセンスで用意したい'), ('no', 'なし')], default='yes', max_length=50),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='condition',
            field=models.CharField(choices=[('1', 'new'), ('2', 'uploaded_contract'), ('3', 'checked_contract'), ('4', 'done_offer'), ('5', 'uploaded_bill'), ('6', 'paid'), ('7', 'deleted')], default='1', max_length=100),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='contract_type',
            field=models.CharField(choices=[('buyout', 'バイアウト（著作権譲渡）'), ('usage_right', '利用権（非独占）'), ('other', 'その他')], default='buyout', max_length=50),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='end_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='ownership_type',
            field=models.CharField(choices=[('provide_from_us', 'ドラフトを提示してほしい'), ('provide_from_client', 'ドラフトを提示するので、内容を検討してほしい')], default='provide_from_us', max_length=50),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='project_type',
            field=models.CharField(choices=[('branding', 'ソニックブランディング'), ('entertainment', 'エンタメ'), ('program', '番組')], default='entertainment', max_length=50),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='se',
            field=models.CharField(choices=[('yes', '新規制作したい'), ('no', 'なし')], default='yes', max_length=50),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='start_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='updated_at',
            field=models.DateTimeField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='voice',
            field=models.CharField(choices=[('yes', '新規制作したい'), ('edit_current_resource', '収録したものを整音したい'), ('no', 'なし')], default='yes', max_length=50),
        ),
        migrations.AddField(
            model_name='product',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='projects', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='productcomment',
            name='is_near',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productmessage',
            name='comment',
            field=models.TextField(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='productmessage',
            name='has_file',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productmessage',
            name='is_near',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productmessage',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_comment', to='app.ProductMessage'),
        ),
        migrations.AddField(
            model_name='productmessage',
            name='resolved',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productmessage',
            name='type_message',
            field=models.CharField(choices=[('1', 'new'), ('2', 'plan'), ('3', 'contract'), ('4', 'bill')], default='1', max_length=100),
        ),
        migrations.AddField(
            model_name='scenecomment',
            name='is_near',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='offerproduct',
            name='budget',
            field=models.IntegerField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)]),
        ),
        migrations.AlterField(
            model_name='offerproduct',
            name='description',
            field=models.TextField(default='', max_length=1000),
        ),
        migrations.AlterField(
            model_name='offerproduct',
            name='master_admin',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='master_admin', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='offerproduct',
            name='master_client',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='master_client', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='productmessagefolder',
            name='message',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='folders', to='app.ProductMessage'),
        ),
        migrations.AddField(
            model_name='productmessagefolder',
            name='offer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='folders', to='app.OfferProduct'),
        ),
        migrations.AddField(
            model_name='productmessagefolder',
            name='parent',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_folders', to='app.ProductMessageFolder'),
        ),
        migrations.AddField(
            model_name='productmessagefile',
            name='folder',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='app.ProductMessageFolder'),
        ),
        migrations.AddField(
            model_name='productmessagefile',
            name='message',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='files', to='app.ProductMessage'),
        ),
        migrations.AddField(
            model_name='productmessagefile',
            name='offer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='files', to='app.OfferProduct'),
        ),
        migrations.AddField(
            model_name='productmessagefile',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='files', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='downloadedproductmessagefile',
            name='file',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_downloaded', to='app.ProductMessageFile'),
        ),
        migrations.AddField(
            model_name='downloadedproductmessagefile',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_message_files', to=settings.AUTH_USER_MODEL),
        ),
    ]
