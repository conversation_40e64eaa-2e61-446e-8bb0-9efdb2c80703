# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-10-29 16:49
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0170_offerproduct_disclosure_rule'),
    ]

    operations = [
        migrations.AlterField(
            model_name='offerproduct',
            name='budget',
            field=models.FloatField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)]),
        ),
    ]
