# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2023-11-16 11:34
from __future__ import unicode_literals

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0237_auto_20231108_1730'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProductCharge',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('code_name', models.CharField(blank=True, max_length=255, null=True)),
                ('name', models.CharField(max_length=256)),
                ('total_budget', models.FloatField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)])),
                ('amount', models.FloatField(blank=True, null=True, verbose_name='価格')),
                ('status', models.CharField(blank=True, choices=[('1', 'process'), ('2', 'paid'), ('3', 'cancelled')], default='1', max_length=50)),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        # migrations.RunSQL('ALTER TABLE app_userproductcharge COLLATE=utf8mb4_general_ci;')
    ]
