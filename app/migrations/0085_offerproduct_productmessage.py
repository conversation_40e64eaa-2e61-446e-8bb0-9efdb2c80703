# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-08-17 15:44
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0084_previewproductcomment_productcommentdownloaded'),
    ]

    operations = [
        migrations.CreateModel(
            name='OfferProduct',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='file')),
                ('description', models.TextField(default='', max_length=500)),
                ('deadline', models.DateTimeField(blank=True, null=True)),
                ('master_admin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='master_admin', to=settings.AUTH_USER_MODEL)),
                ('master_client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='master_client', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['created'],
            },
        ),
        migrations.CreateModel(
            name='ProductMessage',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('message_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField(verbose_name='コメント')),
                ('seen_date', models.DateTimeField(null=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='file')),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('offer_product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='message_product', to='app.OfferProduct')),
                ('owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owner', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
    ]
