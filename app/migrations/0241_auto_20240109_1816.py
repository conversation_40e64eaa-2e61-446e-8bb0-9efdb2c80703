# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2024-01-09 18:16
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0240_auto_20231205_1613'),
    ]

    operations = [
        migrations.RenameField(
            model_name='userproductcharge',
            old_name='receipt_id',
            new_name='stripe_charge_id',
        ),
        migrations.AddField(
            model_name='userproductcharge',
            name='service_fee',
            field=models.FloatField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AddField(
            model_name='userproductcharge',
            name='stripe_receipt_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
