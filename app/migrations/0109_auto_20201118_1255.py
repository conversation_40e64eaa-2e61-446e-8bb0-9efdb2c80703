# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-11-18 12:55
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0108_auto_20201109_1245'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageReceiver',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('seen_date', models.DateTimeField(null=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.ProductMessage')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='productmessage',
            name='receivers',
            field=models.ManyToManyField(blank=True, through='app.MessageReceiver', to=settings.AUTH_USER_MODEL),
        ),
    ]
