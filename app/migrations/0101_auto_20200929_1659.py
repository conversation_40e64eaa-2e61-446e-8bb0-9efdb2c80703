# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-09-29 16:59
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0100_offerproduct_budget'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='allow_url_share',
            field=models.BooleanField(default=False, verbose_name='URLリンク共有を許可'),
        ),
        migrations.AddField(
            model_name='product',
            name='auto_use_last',
            field=models.BooleanField(default=False, verbose_name='動画ファイルのダウンロードを許可'),
        ),
        migrations.AddField(
            model_name='product',
            name='client_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='ブランド'),
        ),
        migrations.AddField(
            model_name='product',
            name='code_name',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='コードネーム'),
        ),
        migrations.AddField(
            model_name='product',
            name='genre',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='ジャンル'),
        ),
        migrations.AddField(
            model_name='product',
            name='total_budget',
            field=models.IntegerField(default=0, verbose_name='総予算'),
        ),
    ]
