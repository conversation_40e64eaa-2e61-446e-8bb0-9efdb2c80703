# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2023-11-16 14:00
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0238_userproductcharge'),
    ]

    operations = [
        migrations.AddField(
            model_name='userproductcharge',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='charged', to='app.Product'),
        ),
        migrations.AddField(
            model_name='userproductcharge',
            name='receipt_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='userproductcharge',
            name='amount',
            field=models.FloatField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)]),
        ),
    ]
