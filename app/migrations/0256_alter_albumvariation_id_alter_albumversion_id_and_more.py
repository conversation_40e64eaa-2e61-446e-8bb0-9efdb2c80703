# Generated by Django 4.2.16 on 2024-11-29 12:55

import datetime
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0255_auto_20240828_0453'),
    ]

    operations = [
        migrations.AlterField(
            model_name='albumvariation',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='albumversion',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='audio',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='blocklistartist',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='bookmarklistbookmarks',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='category',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='colorprojectsetting',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='contentsale',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='creatorofferfile',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='fontprojectsetting',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='hashtag',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='itemsectioncredit',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='listbookmark',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='listwork',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='messagereceiver',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offercreator',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offermessage',
            name='is_near',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='offermessagereceiver',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offerproduct',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='offeruser',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='planoffer',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='product',
            name='allow_url_share',
            field=models.BooleanField(default=False, verbose_name='allow_url_share（URLリンク共有を許可）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='auto_use_last',
            field=models.BooleanField(default=False, verbose_name='auto_use_last（最終動画ファイルの納品データに。未使用だが復活の可能性あり）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='client_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='client_name（エンドクライアント）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='code_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='code_name（コードネーム）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='description',
            field=models.TextField(blank=True, max_length=1000, null=True, verbose_name='description（概要）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='end_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='end_time（終了日）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='genre',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='genre（未使用）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='genres',
            field=models.CharField(blank=True, choices=[('1', 'サウンドロゴ  '), ('2', 'ポッドキャスト'), ('3', '広告'), ('4', 'エンタメ')], default='1', max_length=50, null=True, verbose_name='genres（未使用）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='information',
            field=models.FileField(blank=True, upload_to='file', verbose_name='information（旧スタッフクレジットPDF。復活可能性あり）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='is_active',
            field=models.BooleanField(blank=True, default=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='last_update',
            field=models.DateTimeField(default=datetime.datetime.now, verbose_name='last_update'),
        ),
        migrations.AlterField(
            model_name='product',
            name='max_scene',
            field=models.IntegerField(default=5, verbose_name='max_scene（チェックポイント）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.CharField(max_length=256, verbose_name='name（目的物）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owner_products', to=settings.AUTH_USER_MODEL, verbose_name='owner'),
        ),
        migrations.AlterField(
            model_name='product',
            name='scene_list',
            field=models.ManyToManyField(related_name='product_scene', to='app.productscene'),
        ),
        migrations.AlterField(
            model_name='product',
            name='share_link',
            field=models.BooleanField(default=True, verbose_name='share_link（URLリンク共有を許可）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='start_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='start_time（開始日）'),
        ),
        migrations.AlterField(
            model_name='product',
            name='total_budget',
            field=models.FloatField(default=0, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)], verbose_name='total_budget'),
        ),
        migrations.AlterField(
            model_name='productcomment',
            name='is_near',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='productcomment',
            name='resolved',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='productcomment',
            name='stamp',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='productmessage',
            name='is_first_message',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='productmessage',
            name='is_near',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='productmessage',
            name='resolved',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='productmilestone',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontent',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontentlistwork',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontentselection',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontenttag',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenecomment',
            name='is_near',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='scenecomment',
            name='resolved',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='scenecomment',
            name='stamp',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name='scenecommentreceiver',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenesharelink',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenetaken',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenetakenscenes',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='scenetitlebookmark',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='sectioncredit',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='topiccategory',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='topictag',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='userproductcharge',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='variation',
            name='name',
            field=models.CharField(max_length=500),
        ),
    ]
