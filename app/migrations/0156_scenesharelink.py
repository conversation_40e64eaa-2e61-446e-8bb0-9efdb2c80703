# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-08-03 18:43
from __future__ import unicode_literals

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0155_scenetitlebookmark'),
    ]

    operations = [
        migrations.CreateModel(
            name='SceneShareLink',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('expired_time', models.DateField(blank=True, default=datetime.date.today, null=True)),
                ('show_comment', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('scene', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='share_links', to='app.Scene')),
                ('user_share', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
