# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-07-07 11:48
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0075_auto_20200706_1241'),
    ]

    operations = [
        migrations.CreateModel(
            name='Variation',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('variation_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(default='', max_length=500)),
                ('scene_title', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='variations', to='app.SceneTitle')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='scene',
            name='variation',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scene', to='app.Variation'),
        ),
    ]
