# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-11-18 11:57
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0178_auto_20211112_1551'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlockListArtist',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='product',
            name='height_logo',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='product',
            name='image_name',
            field=models.CharField(blank=True, max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='logo',
            field=models.FileField(blank=True, upload_to='images'),
        ),
        migrations.AddField(
            model_name='product',
            name='logo_name',
            field=models.CharField(blank=True, max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='width_logo',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='product',
            name='x_logo',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='product',
            name='y_logo',
            field=models.FloatField(default=0),
        ),
        migrations.AlterField(
            model_name='product',
            name='real_name',
            field=models.CharField(blank=True, max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='blocklistartist',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='artist_blocked', to='app.Product'),
        ),
        migrations.AddField(
            model_name='blocklistartist',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_block', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='product',
            name='artists_block',
            field=models.ManyToManyField(blank=True, through='app.BlockListArtist', to=settings.AUTH_USER_MODEL),
        ),
    ]
