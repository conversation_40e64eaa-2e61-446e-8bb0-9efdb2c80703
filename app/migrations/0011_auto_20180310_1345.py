# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2018-03-10 13:45
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0010_remove_product_scene_list'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductScene',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('product_scene_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='シーン名')),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.AddField(
            model_name='product',
            name='scene_list',
            field=models.ManyToManyField(related_name='product_scene', to='app.ProductScene', verbose_name='シーンリスト'),
        ),
        migrations.AddField(
            model_name='scene',
            name='product_scene',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scene_product_scene', to='app.ProductScene'),
        ),
    ]
