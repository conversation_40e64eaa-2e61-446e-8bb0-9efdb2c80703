# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-09-21 15:55
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0218_formcontractandplan_valid_date'),
    ]

    operations = [
        migrations.AddField(
            model_name='offercreator',
            name='delivery_place',
            field=models.Char<PERSON>ield(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='offercreator',
            name='note',
            field=models.CharField(blank=True, default=None, max_length=1000, null=True),
        ),
        migrations.AddField(
            model_name='offercreator',
            name='note_type',
            field=models.Char<PERSON>ield(max_length=32, null=True),
        ),
        migrations.AddField(
            model_name='offercreator',
            name='pick_up_method',
            field=models.CharField(blank=True, max_length=500, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='offercreator',
            name='release_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='offercreator',
            name='valid_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
