# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-08-07 10:37
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0082_auto_20200729_1633'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductComment',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('comment_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('comment', models.TextField(max_length=255, verbose_name='コメント')),
                ('file', models.FileField(blank=True, null=True, upload_to='file_storage')),
                ('stamp', models.<PERSON><PERSON>anField(default=False)),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('resolved', models.BooleanField(default=False)),
                ('acr_result', models.TextField(blank=True, null=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_comments', to='app.ProductComment')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_owner_comments', to='app.Product')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='comment_in_projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['created'],
            },
        ),
    ]
