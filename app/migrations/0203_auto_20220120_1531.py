# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-01-20 15:31
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0202_auto_20220119_1026'),
    ]

    operations = [
        migrations.AddField(
            model_name='salecontentversion',
            name='created_year',
            field=models.CharField(blank=True, default='', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='credit',
            field=models.CharField(blank=True, default='', max_length=1000, null=True),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='customizable_sale_setting',
            field=models.CharField(blank=True, default='', max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='show_thumbnail',
            field=models.Char<PERSON>ield(choices=[('color', 'color'), ('image', 'image')], default='color', max_length=20),
        ),
        migrations.AddField(
            model_name='salecontentversion',
            name='tags',
            field=models.ManyToManyField(blank=True, through='app.SaleContentTag', to='app.HashTag'),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='content_type',
            field=models.CharField(choices=[('2mix', '2mix'), ('music', 'MUSIC'), ('voice', 'VOICE'), ('sound_effect', 'SOUND EFFECTS')], default='1', max_length=20),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='desc',
            field=models.TextField(blank=True, default='', max_length=1000, null=True, verbose_name='説明（OPTIONAL）'),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='max_price',
            field=models.FloatField(blank=True, default=0, null=True, verbose_name='最高価格（税込）'),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='price',
            field=models.FloatField(blank=True, default=0, null=True, verbose_name='開始価格（税込）'),
        ),
        migrations.AlterField(
            model_name='salecontentversion',
            name='sale_type',
            field=models.CharField(choices=[('1', 'そのまま販売'), ('2', 'セミオーダーで販売'), ('3', ' 利用権を販売'), ('4', '販売しない')], default='4', max_length=20),
        ),
    ]
