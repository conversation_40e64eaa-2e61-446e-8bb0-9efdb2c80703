# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-11-22 11:21
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('app', '0180_auto_20211119_1153'),
    ]

    operations = [
        migrations.CreateModel(
            name='FormContractAndPlan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('creation_method', models.CharField(choices=[('generate', 'Generate Method'), ('upload', 'Upload Method')], max_length=32, null=True)),
                ('subject', models.CharField(blank=True, max_length=256, null=True)),
                ('work_content', models.TextField(default='{}')),
                ('delivery_format', models.CharField(blank=True, max_length=512, null=True)),
                ('pick_up_method', models.IntegerField(choices=[(1, '利用規約'), (2, '御社指定')], default=None)),
                ('delivery_place', models.IntegerField(choices=[(1, '利用規約'), (2, '御社指定')], default=None)),
                ('release_time', models.DateTimeField()),
                ('start_schedule', models.DateTimeField()),
                ('end_schedule', models.DateTimeField()),
                ('deadline', models.DateTimeField()),
                ('note', models.CharField(blank=True, max_length=512, null=True)),
                ('allow_public_contract', models.BooleanField(default=False)),
                ('owner_info', models.TextField(default='{}')),
                ('producer_info', models.TextField(default='{}')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='productmessagefile',
            name='content_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType'),
        ),
        migrations.AddField(
            model_name='productmessagefile',
            name='object_id',
            field=models.PositiveIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='productmessagefile',
            name='file',
            field=models.FileField(blank=True, max_length=1024, upload_to='storage'),
        ),
        migrations.AlterField(
            model_name='productmessagefile',
            name='type_file',
            field=models.CharField(choices=[('1', 'default'), ('2', 'contract'), ('3', 'bill'), ('4', 'offer'), ('5', 'plan')], default='1', max_length=100),
        ),
    ]
