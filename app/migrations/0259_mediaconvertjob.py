# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2024-12-19 10:00
from __future__ import unicode_literals

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0258_product_banner_color_product_banner_font_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MediaConvertJob',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('original_object_key', models.CharField(max_length=512, verbose_name='Original S3 object key', db_index=True)),
                ('converted_media_key', models.Char<PERSON>ield(max_length=512, null=True, blank=True, verbose_name='Converted media S3 key')),
                ('waveform_data_key', models.<PERSON><PERSON><PERSON><PERSON>(max_length=512, null=True, blank=True, verbose_name='Waveform data S3 key')),
                ('status', models.CharField(max_length=20, default='pending', verbose_name='Conversion status', db_index=True, help_text='e.g: pending, processing, completed, failed, error')),
                ('job_id', models.CharField(max_length=128, null=True, blank=True, verbose_name='AWS MediaConvert job ID', db_index=True)),
                ('error_message', models.TextField(null=True, blank=True, verbose_name='Error message')),
                ('file_size', models.BigIntegerField(null=True, blank=True, verbose_name='File size in bytes')),
                ('duration', models.FloatField(null=True, blank=True, verbose_name='Duration in seconds')),
            ],
            options={
                'db_table': 'app_mediaconvertjob',
                'verbose_name': 'Media Convert Job',
                'verbose_name_plural': 'Media Convert Jobs',
            },
        ),
        migrations.RunSQL(
            # Create dedicated index for original_object_key - PRIMARY JOIN FIELD for left joins
            "CREATE INDEX idx_mcj_original_key ON app_mediaconvertjob (original_object_key);",
            reverse_sql="ALTER TABLE app_mediaconvertjob DROP INDEX idx_mcj_original_key;"
        ),
    ] 