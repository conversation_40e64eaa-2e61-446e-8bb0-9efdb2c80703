# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-08-17 13:43
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0159_auto_20210817_1604'),
    ]

    operations = [
        migrations.CreateModel(
            name='BookmarkListBookMarks',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.IntegerField(default=1)),
            ],
            options={
                'ordering': ['-order'],
            },
        ),
        migrations.CreateModel(
            name='ListBookMark',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.<PERSON>r<PERSON><PERSON>(default='Scene', max_length=512)),
                ('order', models.IntegerField(default=1)),
                ('created', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='list_bookmarks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-order'],
            },
        ),
        migrations.AddField(
            model_name='bookmarklistbookmarks',
            name='listbookmark',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_ids', to='app.ListBookMark'),
        ),
        migrations.AddField(
            model_name='bookmarklistbookmarks',
            name='scenetitlebookmark',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='list_ids', to='app.SceneTitleBookmark'),
        ),
    ]
