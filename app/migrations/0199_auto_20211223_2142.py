# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-12-23 21:42
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0198_auto_20211223_1707'),
    ]

    operations = [
        migrations.CreateModel(
            name='SelectionOffer',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('selection_offer_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('selection_content', models.TextField(default='')),
                ('toggle_content', models.TextField(default='{}')),
                ('work_name', models.TextField(default='')),
            ],
            options={
                'ordering': ['created'],
            },
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='topic',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='offers', to='app.TopicGallery'),
        ),
        migrations.AddField(
            model_name='selectionoffer',
            name='offer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='selections', to='app.OfferProduct'),
        ),
        migrations.AddField(
            model_name='selectionoffer',
            name='selection',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='offers', to='app.SelectionGallery'),
        ),
    ]
