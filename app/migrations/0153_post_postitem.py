# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-07-22 16:17
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0152_auto_20210713_1538'),
    ]

    operations = [
        migrations.CreateModel(
            name='Post',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('post_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, max_length=64, null=True)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='PostItem',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('post_item_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField(blank=True, null=True, verbose_name='コメント')),
                ('type', models.CharField(choices=[('1', 'new'), ('2', 'update')], default='1', max_length=20)),
                ('file', models.FileField(blank=True, null=True, upload_to='info')),
                ('post', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='items', to='app.Post')),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
    ]
