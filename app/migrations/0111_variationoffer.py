# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-11-23 14:57
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0110_auto_20201118_1317'),
    ]

    operations = [
        migrations.CreateModel(
            name='VariationOffer',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('variation_offer_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('plan', models.FileField(blank=True, null=True, upload_to='file')),
                ('plan_name', models.CharField(blank=True, max_length=512, null=True)),
                ('contract', models.<PERSON><PERSON>ield(blank=True, null=True, upload_to='file')),
                ('contract_name', models.CharField(blank=True, max_length=512, null=True)),
                ('bill', models.FileField(blank=True, null=True, upload_to='file')),
                ('bill_name', models.CharField(blank=True, max_length=512, null=True)),
                ('is_choosed', models.CharField(choices=[('1', 'processing'), ('2', 'choosed')], default='1', max_length=100)),
                ('offer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='variation_offer', to='app.OfferProduct')),
                ('owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owner_variation', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['modified'],
            },
        ),
    ]
