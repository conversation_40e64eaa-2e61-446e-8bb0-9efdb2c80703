# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-12-20 19:27
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0194_auto_20211216_1706'),
    ]

    operations = [
        migrations.CreateModel(
            name='ListWork',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('title', models.CharField(max_length=64)),
                ('description', models.TextField(null=True)),
                ('order', models.IntegerField(default=1)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='SaleContentListWork',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.IntegerField(default=1)),
                ('list_work', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.ListWork')),
                ('sale_content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.SaleContent')),
            ],
            options={
                'ordering': ['-order'],
            },
        ),
        migrations.AddField(
            model_name='offerproduct',
            name='artist',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='artist', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='listwork',
            name='sample_list_work',
            field=models.ManyToManyField(through='app.SaleContentListWork', to='app.SaleContent'),
        ),
    ]
