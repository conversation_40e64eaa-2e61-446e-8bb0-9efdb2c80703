# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-03-05 16:13
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0061_remove_product_users'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductDevelopPeriod',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('product_develop_period_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('period_start', models.DateTimeField(null=True)),
                ('period_end', models.DateTimeField(null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductMilestone',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('product_milestone_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('year', models.CharField(max_length=10)),
                ('month', models.CharField(max_length=10)),
                ('day', models.CharField(max_length=10)),
                ('version', models.CharField(max_length=255, null=True, verbose_name='')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductSetting',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('product_setting_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('project_name', models.CharField(max_length=255, null=True, verbose_name='プロジェクト名')),
                ('brand', models.CharField(max_length=255, null=True, verbose_name='ブランド')),
                ('project_description', models.TextField(null=True, verbose_name='プロジェクト説明')),
                ('delivery_format', models.TextField(null=True, verbose_name='納品形式')),
                ('code_name', models.CharField(max_length=255, null=True, verbose_name='コードネーム')),
                ('genre', models.CharField(max_length=255, null=True, verbose_name='ジャンル')),
                ('total_product', models.IntegerField(null=True, verbose_name='総演出数')),
                ('show_remain_day', models.BooleanField(default=False, verbose_name='マイルストーン迄の残り日数を表示')),
                ('show_project_name', models.BooleanField(default=False, verbose_name='バナーにプロジェクト名を表示')),
                ('show_progress', models.BooleanField(default=False, verbose_name='マイルストーン迄の残り日数を表示')),
                ('allow_url_share', models.BooleanField(default=False, verbose_name='URLリンク共有を許可')),
                ('allow_download_video', models.BooleanField(default=False, verbose_name='動画ファイルのダウンロードを許可')),
                ('auto_heart', models.BooleanField(default=False, verbose_name='自動でハート')),
                ('auto_heart_day', models.IntegerField(null=True, verbose_name='自動でハート日後')),
                ('product_id', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_setting', to='app.Product')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AlterField(
            model_name='productscene',
            name='name',
            field=models.CharField(max_length=64, verbose_name='Αバージョン完成'),
        ),
        migrations.AddField(
            model_name='productmilestone',
            name='product_setting_id',
            field=models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_setting_milestone', to='app.ProductSetting'),
        ),
        migrations.AddField(
            model_name='productdevelopperiod',
            name='product_setting_id',
            field=models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_setting_period', to='app.ProductSetting'),
        ),
    ]
