# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-10-10 12:34
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0025_creator_schedule'),
        ('app', '0045_auto_20191004_1619'),
    ]

    operations = [
        migrations.CreateModel(
            name='Audio',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(blank='', default='', max_length=200, null=True, verbose_name='サンプル名')),
                ('audio', models.FileField(upload_to='audio/', verbose_name='オーディオデータ')),
                ('type', models.<PERSON>r<PERSON><PERSON>(choices=[('music', 'MUSIC'), ('voice', 'VOICE'), ('sound effect', 'SOUND EFFECTS')], default='music', max_length=20, verbose_name='サンプルの種類')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='audio_creator', to='accounts.Creator')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
