# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-07-13 15:38
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0151_auto_20210707_1812'),
    ]

    operations = [
        migrations.CreateModel(
            name='SceneCommentReceiver',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('seen_date', models.DateTimeField(null=True)),
                ('message', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.SceneComment')),
                ('product_comment', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.ProductComment')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='productcomment',
            name='receivers',
            field=models.ManyToManyField(blank=True, through='app.SceneCommentReceiver', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='scenecomment',
            name='receivers',
            field=models.ManyToManyField(blank=True, through='app.SceneCommentReceiver', to=settings.AUTH_USER_MODEL),
        ),
    ]
