# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-02-09 16:44
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0253_offercreator_contract_file_raw'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReviewOffer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('1', 'OK'), ('2', '匿名ならOK'), ('3', '許可しない')], default='1', max_length=100)),
                ('review', models.CharField(choices=[('1', 'good'), ('2', 'bad')], default='1', max_length=100)),
                ('user_id', models.Char<PERSON>ield(null=False, blank=True, default=None, max_length=1000)),
                ('offer_id', models.Char<PERSON>ield(null=False, blank=True, default=None, max_length=1000)),
                ('note', models.CharField(null=False, blank=True, default=None, max_length=1000)),
            ],
            options={
                'ordering': ['created'],
            },
        ),
    ]
