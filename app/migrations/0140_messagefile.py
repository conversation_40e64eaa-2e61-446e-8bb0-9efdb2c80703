# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-05-13 10:27
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0139_scene_peaks'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageFile',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('file_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(blank=True, upload_to='file')),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('peaks', models.CharField(default='', max_length=1024)),
                ('message', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='files', to='app.OfferMessage')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
