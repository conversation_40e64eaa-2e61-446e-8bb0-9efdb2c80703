# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2020-03-29 05:01
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0063_auto_20200325_1151'),
    ]

    operations = [
        migrations.AddField(
            model_name='audio',
            name='acoustic',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
        migrations.AddField(
            model_name='audio',
            name='age',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(80)]),
        ),
        migrations.AddField(
            model_name='audio',
            name='elegant',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
        migrations.AddField(
            model_name='audio',
            name='gender',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(2)]),
        ),
        migrations.AddField(
            model_name='audio',
            name='key_note',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(40), django.core.validators.MaxValueValidator(88)]),
        ),
        migrations.AddField(
            model_name='audio',
            name='low_high',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(3)]),
        ),
        migrations.AddField(
            model_name='audio',
            name='realistic',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
        migrations.AddField(
            model_name='audio',
            name='sound',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)]),
        ),
        migrations.AddField(
            model_name='scenetitle',
            name='is_done',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='audio',
            name='type',
            field=models.CharField(choices=[('music', 'MUSIC'), ('voice', 'VOICE'), ('sound effect', 'SOUND EFFECTS'), ('vocal', 'VOCAL')], default='music', max_length=20, verbose_name='サンプルの種類'),
        ),
    ]
