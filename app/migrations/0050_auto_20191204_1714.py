# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-12-04 17:14
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0049_auto_20191111_1848'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImageSale',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('image', models.FileField(blank=True, upload_to='images')),
                ('x', models.FloatField(default=0)),
                ('y', models.FloatField(default=0)),
                ('width', models.FloatField(default=0)),
                ('height', models.FloatField(default=0)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='contentsale',
            name='height',
        ),
        migrations.RemoveField(
            model_name='contentsale',
            name='image',
        ),
        migrations.RemoveField(
            model_name='contentsale',
            name='width',
        ),
        migrations.RemoveField(
            model_name='contentsale',
            name='x',
        ),
        migrations.RemoveField(
            model_name='contentsale',
            name='y',
        ),
        migrations.AddField(
            model_name='audiosale',
            name='order',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='contentsale',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='content_parent', to='app.ContentSale'),
        ),
        migrations.AddField(
            model_name='contentsale',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reviewed_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='contentsale',
            name='status',
            field=models.CharField(choices=[('submitted', '申請中'), ('reviewed', '審査中'), ('publish', '公開中'), ('unpublished', '非公開'), ('deleted', 'deleted')], default='submitted', max_length=20),
        ),
        migrations.AddField(
            model_name='contentsale',
            name='version',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.RemoveField(
            model_name='audiosale',
            name='sale',
        ),
        migrations.AddField(
            model_name='audiosale',
            name='sale',
            field=models.ManyToManyField(blank=True, related_name='audio_sale', to='app.ContentSale'),
        ),
        migrations.AddField(
            model_name='imagesale',
            name='sale',
            field=models.ManyToManyField(blank=True, related_name='image_sale', to='app.ContentSale'),
        ),
    ]
