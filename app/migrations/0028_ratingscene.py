# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-03-27 16:27
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0027_auto_20190326_1852'),
    ]

    operations = [
        migrations.CreateModel(
            name='RatingScene',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('rating_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.PositiveIntegerField(default=0, null=True)),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owner_rating', to=settings.AUTH_USER_MODEL)),
                ('scene', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owner_rating_videos', to='app.Scene')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
