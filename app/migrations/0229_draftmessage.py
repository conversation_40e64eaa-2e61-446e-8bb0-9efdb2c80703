# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-12-16 18:30
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0228_offercreator_selected_job_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='DraftMessage',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type_comment', models.CharField(choices=[('1', 'ProductMessage')], default='1', max_length=100)),
                ('draft_content', models.CharField(default='', max_length=1000, null=True)),
                ('file_id', models.CharField(max_length=1000, null=True)),
                ('folder_id', models.CharField(max_length=100, null=True)),
                ('list_name_file', models.TextField(blank=True, max_length=100000)),
                ('offer_project', models.ForeignKey(db_column='offer_id', on_delete=django.db.models.deletion.CASCADE, related_name='draft_offer', to='app.OfferProject')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='draft_user', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
