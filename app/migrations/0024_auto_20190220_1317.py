# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-02-20 13:17
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0023_scenecomment_file'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='scene',
            options={'ordering': ['order', 'created']},
        ),
        migrations.AddField(
            model_name='scene',
            name='order',
            field=models.IntegerField(default=1),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.CharField(max_length=64, verbose_name='プロジェクト名'),
        ),
        migrations.AlterField(
            model_name='productscene',
            name='name',
            field=models.CharField(max_length=64, verbose_name='シーン名'),
        ),
    ]
