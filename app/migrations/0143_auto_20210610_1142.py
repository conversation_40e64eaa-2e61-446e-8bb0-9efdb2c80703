# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-06-10 11:42
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0142_auto_20210519_2016'),
    ]

    operations = [
        migrations.CreateModel(
            name='SceneCommentFile',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('file_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(blank=True, upload_to='file')),
                ('real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('peaks', models.CharField(default='', max_length=1024)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='scenecomment',
            name='has_file',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='scenecommentfile',
            name='message',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='files', to='app.SceneComment'),
        ),
    ]
