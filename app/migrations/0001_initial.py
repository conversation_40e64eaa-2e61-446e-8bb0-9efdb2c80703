# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2017-11-25 05:50
from __future__ import unicode_literals

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Scene',
            fields=[
                ('scene_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tag', models.CharField(max_length=100)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SceneComment',
            fields=[
                ('comment_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('comment', models.TextField()),
                ('user_id', models.CharField(max_length=100)),
                ('modified', models.DateTime<PERSON>ield(auto_now=True)),
                ('created', models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
        ),
    ]
