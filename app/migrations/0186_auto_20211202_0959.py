# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-12-02 09:59
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0185_product_text_note'),
    ]

    operations = [
        migrations.CreateModel(
            name='OfferMessageReceiver',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('seen_date', models.DateTimeField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='OfferProject',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('offer_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type_offer', models.CharField(choices=[('1', 'offer_creator'), ('2', 'offer_product')], default='1', max_length=20)),
                ('offer_status', models.CharField(choices=[('1', 'progress'), ('2', 'done'), ('3', 'deleted')], default='1', max_length=20)),
                ('offer_creator', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='offer', to='app.OfferCreator')),
                ('offer_product', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='offer', to='app.OfferProduct')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offers', to='app.Product')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='OfferUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('position', models.CharField(choices=[('master_admin', '管理者'), ('admin', 'ディレクター'), ('owner', 'オーナー'), ('creator', 'アーティスト')], default='admin', max_length=20, verbose_name='権限')),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.OfferProject')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='offermessage',
            name='comment',
            field=models.TextField(blank=True, null=True, verbose_name='コメント'),
        ),
        migrations.AddField(
            model_name='offermessage',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='offer_messages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='offermessagereceiver',
            name='message',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.OfferMessage'),
        ),
        migrations.AddField(
            model_name='offermessagereceiver',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='offermessage',
            name='receivers',
            field=models.ManyToManyField(blank=True, through='app.OfferMessageReceiver', to=settings.AUTH_USER_MODEL),
        ),
    ]
