# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-07-20 18:00
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0153_auto_20210720_1443'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageFolder',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('folder_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=512)),
                ('message', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='folders', to='app.OfferMessage')),
                ('parent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_folders', to='app.MessageFolder')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SceneCommentFolder',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('folder_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=512)),
                ('message', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='folders', to='app.SceneComment')),
                ('parent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_folders', to='app.SceneCommentFolder')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='messagefile',
            name='folder',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='app.MessageFolder'),
        ),
        migrations.AddField(
            model_name='scenecommentfile',
            name='folder',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='app.SceneCommentFolder'),
        ),
    ]
