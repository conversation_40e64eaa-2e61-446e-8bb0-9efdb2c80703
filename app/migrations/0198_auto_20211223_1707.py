# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2021-12-23 17:07
from __future__ import unicode_literals

import common.base_models
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0197_auto_20211217_1647'),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('category_name', models.CharField(max_length=60)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleContentSelection',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.IntegerField(default=1)),
                ('sale_content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.SaleContent')),
            ],
            options={
                'ordering': ['-order'],
            },
        ),
        migrations.CreateModel(
            name='SelectionGallery',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('selection_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(default='', max_length=255)),
                ('description', models.TextField(default='', max_length=1000)),
                ('selection_content', models.TextField(default='{}')),
                ('toggle_content', models.TextField(default='{}')),
                ('order', models.IntegerField(default=1)),
                ('sale_contents', models.ManyToManyField(through='app.SaleContentSelection', to='app.SaleContent')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TopicCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.Category')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TopicGallery',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('topic_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('video', models.FileField(blank=True, default=None, null=True, upload_to='movie', verbose_name='動画ファイル')),
                ('thumbnail', models.FileField(blank=True, null=True, upload_to='thumbnail')),
                ('x', models.FloatField(default=0)),
                ('y', models.FloatField(default=0)),
                ('width', models.FloatField(default=0)),
                ('height', models.FloatField(default=0)),
                ('title', models.CharField(default='', max_length=255)),
                ('overview', models.CharField(default='', max_length=255)),
                ('description', models.TextField(default='', max_length=1000)),
                ('file', models.FileField(blank=True, default=None, null=True, upload_to='file')),
                ('file_real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('thumbnail_real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('video_real_name', models.CharField(blank=True, max_length=512, null=True)),
                ('order', models.IntegerField(default=1)),
                ('is_deleted', models.BooleanField(default=False)),
                ('categories', models.ManyToManyField(blank=True, through='app.TopicCategory', to='app.Category')),
            ],
            options={
                'ordering': ['-order'],
            },
            bases=(models.Model, common.base_models.BaseModelCropImg),
        ),
        migrations.CreateModel(
            name='TopicTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('tag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.HashTag')),
                ('topic', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.TopicGallery')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='topicgallery',
            name='tags',
            field=models.ManyToManyField(blank=True, through='app.TopicTag', to='app.HashTag'),
        ),
        migrations.AddField(
            model_name='topiccategory',
            name='topic',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.TopicGallery'),
        ),
        migrations.AddField(
            model_name='selectiongallery',
            name='topic',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='selections', to='app.TopicGallery'),
        ),
        migrations.AddField(
            model_name='salecontentselection',
            name='selection',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.SelectionGallery'),
        ),
    ]
