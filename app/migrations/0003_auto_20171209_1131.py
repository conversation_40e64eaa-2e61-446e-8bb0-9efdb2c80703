# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2017-12-09 02:31
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0002_auto_20171126_1827'),
    ]

    operations = [
        migrations.CreateModel(
            name='Product',
            fields=[
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('product_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.TextField(verbose_name='製品名')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='scenecomment',
            name='scene',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scene_comment_scene', to='app.Scene'),
        ),
        migrations.AddField(
            model_name='scene',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scene_product', to='app.Product'),
        ),
    ]
