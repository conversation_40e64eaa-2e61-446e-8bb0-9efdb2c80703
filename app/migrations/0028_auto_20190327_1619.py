# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2019-03-27 16:19
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0027_auto_20190326_1852'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='current_scene',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='product',
            name='max_scene',
            field=models.IntegerField(default=500),
        ),
        migrations.AlterField(
            model_name='previewscene',
            name='comment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='preview_comment', to='app.SceneComment'),
        ),
        migrations.AlterField(
            model_name='previewscene',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owner_comment', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='previewvideo',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owner_video', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='previewvideo',
            name='scene',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owner_viewed_videos', to='app.Scene'),
        ),
        migrations.AlterField(
            model_name='scene',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scene_product', to='app.Product'),
        ),
        migrations.AlterField(
            model_name='scene',
            name='product_scene',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scene_product_scene', to='app.ProductScene'),
        ),
        migrations.AlterField(
            model_name='scene',
            name='title',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scene_title', to='app.SceneTitle'),
        ),
        migrations.AlterField(
            model_name='scenecomment',
            name='scene',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scene_comment_scene', to='app.Scene'),
        ),
    ]
