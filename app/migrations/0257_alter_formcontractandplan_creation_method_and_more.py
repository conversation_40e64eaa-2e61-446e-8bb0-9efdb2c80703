# Generated by Django 4.2.16 on 2024-12-05 13:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0256_alter_albumvariation_id_alter_albumversion_id_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='formcontractandplan',
            name='creation_method',
            field=models.CharField(choices=[('generate', 'Generate Method'), ('upload', 'Upload Method')], max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='delivery_place',
            field=models.IntegerField(choices=[(1, 'soremo_service'), (2, 'designated')], default=None),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='form_type',
            field=models.IntegerField(choices=[(1, 'plan'), (2, 'contract'), (3, 'all')], default=None),
        ),
        migrations.AlterField(
            model_name='formcontractandplan',
            name='pick_up_method',
            field=models.IntegerField(choices=[(1, 'soremo_service'), (2, 'designated')], default=None),
        ),
    ]
