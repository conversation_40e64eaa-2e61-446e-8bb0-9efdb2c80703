# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2024-08-28 04:53
from __future__ import unicode_literals

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0254_reviewoffer'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='reviewoffer',
            options={},
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='created',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='modified',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='note',
            field=models.CharField(blank=True, default=None, max_length=1500),
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='offer_id',
            field=models.Char<PERSON><PERSON>(blank=True, default=None, max_length=1000, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='reviewoffer',
            name='review',
            field=models.CharField(blank=True, choices=[('3', 'NO_ACTION'), ('1', 'LIKE'), ('2', 'DISLIKE')], default=None, max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='status',
            field=models.CharField(blank=True, choices=[('1', 'OK'), ('2', '匿名ならOK'), ('3', '許可しない')], default=None, max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='reviewoffer',
            name='user_id',
            field=models.CharField(blank=True, default=None, max_length=1000, null=True),
        ),
    ]
