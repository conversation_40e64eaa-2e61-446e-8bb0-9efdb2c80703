# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2023-07-28 10:38
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0234_scene_take_uploaded'),
    ]

    operations = [
        migrations.CreateModel(
            name='SceneTaken',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SceneTakenScenes',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('scene', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.Scene')),
                ('taken', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.SceneTaken')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='scenetaken',
            name='taken_scenes',
            field=models.ManyToManyField(blank=True, through='app.SceneTakenScenes', to='app.Scene'),
        ),
        migrations.AddField(
            model_name='scenetaken',
            name='title',
            field=models.OneToOneField(db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='title_taken_scene', to='app.SceneTitle'),
        ),
        migrations.AddField(
            model_name='scenetaken',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='taken_scenes', to=settings.AUTH_USER_MODEL),
        ),
    ]
