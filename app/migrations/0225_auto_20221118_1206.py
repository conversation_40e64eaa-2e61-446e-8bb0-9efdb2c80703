# -*- coding: utf-8 -*-
# Generated by Django 1.11.3 on 2022-11-18 12:06
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0224_product_acr_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='productcommentfile',
            name='acr_status',
            field=models.CharField(choices=[('1', 'not_start'), ('2', 'uploaded'), ('3', 'has_result'), ('4', 'not_match'), ('5', 'error')], default='1', max_length=100),
        ),
        migrations.AlterField(
            model_name='scene',
            name='acr_status',
            field=models.CharField(choices=[('1', 'not_start'), ('2', 'uploaded'), ('3', 'has_result'), ('4', 'not_match'), ('5', 'error')], default='1', max_length=100),
        ),
        migrations.Alter<PERSON>ield(
            model_name='scenecommentfile',
            name='acr_status',
            field=models.CharField(choices=[('1', 'not_start'), ('2', 'uploaded'), ('3', 'has_result'), ('4', 'not_match'), ('5', 'error')], default='1', max_length=100),
        ),
    ]
