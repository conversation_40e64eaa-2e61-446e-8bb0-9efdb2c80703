.audio-navi {
    height: 108px;
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    border-top: 1px solid #f0f0f0;
    z-index: 4;
    background: #fff;
    display: flex;
    flex-direction: column;
    padding: 12px 0;
    transform: translateY(120px);
    transition: 0.5s ease-in-out;
}

.audio-navi.showing {
    transform: translateY(0);
}

.audio-navi-titlebar {
    height: 26px;
    width: calc(100% - 32px);
    margin: 0 16px 4px;
}

.audio-navi-nowplayingbar {
    height: 42px;
    width: calc(100% - 32px);
    margin: 0 16px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 0 16px;
}

.audio-navi-titlebar {
    width: calc(100% - 32px);
    display: flex;
    position: relative;
}

.audio-navi-titlebar-info {
    width: calc(100% - 55px);
    display: flex;
}

.audio-navi-titlebar-info-title,
.audio-navi-titlebar-info-name {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-weight: 300;
    font-size: 13px;
    line-height: 200%;
    color: #000000;
    position: relative;
    max-width: calc(100% - 170px);
    min-width: 90px;
}

.audio-navi-titlebar-info-title {
    padding-right: 32px;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-weight: 400;
}

.audio-navi-titlebar-info-title:after {
    content: '|';
    position: absolute;
    top: 0;
    right: 16px;
    color: #f0f0f0;
}

.audio-navi-titlebar-copylink {
    display: flex;
    justify-content: center;
    align-items: center;
}

.audio-navi-titlebar-copylink {
    cursor: pointer
}

.audio-navi-titlebar-copylink.copied:after {
    content: 'リンクをコピーしました';
    position: absolute;
    top: -24px;
    font-size: 8px;
    font-weight: 300;
    line-height: 150%;
    padding: 4px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    white-space: nowrap;
}

.audio-navi-titlebar-copylink svg {
    width: 20px;
    height: 20px;
}

.audio-navi-titlebar-copylink:hover svg path {
    fill: #009ace;
}

.audio-navi-titlebar-bookmark {
    height: 24px;
    width: 24px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.audio-navi-titlebar-bookmark .icon--sicon-bookmark-o,
.audio-navi-titlebar-bookmark .icon--sicon-bookmark {
    font-size: 20px;
    color: #a7a8a9;
}

.audio-navi-titlebar-bookmark:hover .icon--sicon-bookmark-o,
.audio-navi-titlebar-bookmark:hover .icon--sicon-bookmark {
    cursor: pointer;
    color: #009ace;
}

.audio-navi-wave {
    padding: 8px 0;
    width: calc(100% - 100px);
}

@media screen and (max-aspect-ratio: 4/5) {
    .audio-navi-nowplayingbar {
        width: calc(100% - 32px);
        padding: 0 16px;
    }

    .audio-navi-wave {
        width: calc(100% - 84px);
    }

    .audio-navi-titlebar-info {
        width: calc(100% - 55px);
    }

    .audio-navi-titlebar-info-title {
        max-width: calc(100% - 55px);
    }

    .audio-navi-titlebar-info-name {
        max-width: calc(100% - 70px);
        min-width: 50px;
    }
}

.audio-navi-playpause {
    width: 26px;
    height: 26px;
    margin-right: 16px;
}

.audio-navi-time {
    width: 55px;
    text-align: right;
    font-weight: 300;
    font-size: 13px;
    line-height: 200%;
}

.audio-navi-playpause {
    cursor: pointer;
}

.audio-navi-playpause:hover path {
    fill: #009ace;
}

.icon-bookmark-navbar {
    position: relative;
}

.icon-bookmark-navbar:after {
    opacity: 0;
    content: '';
    width: 2px;
    height: 2px;
    background: var(--bg-album);
    border-radius: 4px;
    bottom: -80px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    transition: 0s;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.icon-bookmark-navbar.showing:after {
    opacity: 1;
    content: '';
    width: 48px;
    height: 48px;
    border-radius: 4px;
    bottom: -70px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    transition: 0.4s ease-in-out;
}

.icon-bookmark-navbar.add:after {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    bottom: 6px;
    position: absolute;
    z-index: 9999;
    transition: all 0.4s ease-in-out, transform 0.2s, left 0.2s;
}
