/* soremo style
*/
html {
    scroll-behavior: smooth
}

textarea, input {
    line-height: 200% !important;
}

body {
    line-height: 200%;
}

footer {
    background: #feffff;
}

.page-footer {
    display: flex;
    justify-content: flex-start;
    padding: 90px 0 0;
}

.footer-nav {
    flex-direction: column;
    width: 50%;
}

.footer-nav li {
    font-size: 1em;
    list-style: none;
    color: #333333;
    padding: 0 10px 15px;
}

.footer-nav a {
    font-size: 1em;
    list-style: none;
    color: #333333;
}

.footer-logo {
    background: #53565A;
    text-align: center;
    margin: 90px auto 0;
}

/*　END
------------------------------ */

.has-success .form-control {
    border-color: #ccc !important;
}

.has-success .control-label {
    color: #333 !important;
}

.has-success .help-block {
    color: #737373 !important;
}

.profile__content {
    position: relative;
}

.profile__avatar-img {
    box-shadow: 2px 8px 20px -5px rgba(0, 0, 0, 0.25);
    width: 160px !important;
    border-radius: 50%;
}

.user-info__notifi ul {
    display: inline-flex;
    list-style: none;
    padding-left: 0;
}

.user-info__images img {
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.height_auto {
    height: auto !important;
}

.border-width-button {
    border-width: inherit;
}

.editor-datetime {
    position: relative;
}

#creator_role {
    width: 100%;
    background-color: #555;
    color: white;
    margin-top: 0.5em;
    padding: 0.6em;
}

.upload .noUi-handle {
    border: 3px solid #009ace;
    box-shadow: none;
    border-radius: 50%;
    width: 18px;
    height: 18px;
}

.music-slider,
.voice-slider,
.vocal-slider,
.sound-effects-slider {
    opacity: 0;
    height: 0;
    margin-bottom: 0;
    transition: .2s;
}

.music-slider.show,
.voice-slider.show,
.vocal-slider.show,
.sound-effects-slider.show {
    opacity: 1;
    height: 20px;
    margin-bottom: 30px;
    transition: .4s ease;
    transition-delay: .2s;
}

.vocal-slider.m-35 .upload__slider-bar {
    margin: 0 35%;
}

.fas.fa-cog {
    font-size: 1.5em;
    transition: transform .4s;
}

.fas.fa-cog.rotate-45 {
    transform: rotate(45deg);
}

.fas.fa-cog:before {
    content: '\f013';
}

.fas.fa-cog:hover {
    color: #009ace;
}

.form-action {
    margin: 45px 0 20px;
    text-align: center;
}

.form-action .button {
    font-size: 20px;
    min-width: 200px;
    text-transform: uppercase;
}

.audio-player-title {
    margin-right: 10px;
}

.audio-player-bullets {
    padding-top: 10px;
}

.audio-player-component.editing .audio-player-title {
    opacity: 0;
}

.audio-player-edit-action {
    margin-top: 10px;
}

.sample-audio-item {
    height: 144px;
    width: auto;
    display: inline-block;
    margin: 16px 16px 64px;
}

.sample-audio-thumbnail[data-file-type="movie"] {
    height: 144px;
    width: 256px;
}

.sample-audio-thumbnail {
    position: relative;
    height: 144px;
    width: 144px;
    background-image: none;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 5px;
}

.upload-new-audio {
    background-image: url('../images/icon-upload-plus.svg');
    background-color: #f0f0f0;
    background-size: 100px;
    cursor: pointer;
}

.sample-audio-info {
    position: absolute;
    left: 4px;
    width: 100%;
    margin-top: 2px;
}

.sample-audio-title {
    font-size: 13px;
    white-space: nowrap;
    width: 144px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sample-audio-sale-type {
    font-size: 11px;
    line-height: 150%;
    color: #a7a8a9;
    max-width: 144px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sample-audio-thumbnail[data-file-type="movie"]+.sample-audio-info .sample-audio-title,
.sample-audio-thumbnail[data-file-type="movie"]+.sample-audio-info .sample-audio-sale-type {
    width: 270px;
    max-width: 270px !important;
}

.upload-new-audio:hover {
    background-color: #0076a5;
}

.sample-audio-container {
    display: flex;
    margin: 0 0 40px 0;
    position: relative;
}

@media (max-width: 768px) {
    .sample-audio-container {
        margin: 0 0 40px 0;
    }
}

.sample-audio-playpause-button {
    height: 100%;
    width: 100%;
    background-image: url('../images/icon_play.svg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 64px;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    border-radius: 0;
    cursor: pointer;
    mix-blend-mode: screen;
}

.sample-audio-thumbnail[data-file-type='audio']:hover .sample-audio-playpause-button {
    opacity: 1;
    z-index: 200;
}

.sample-audio-thumbnail:hover {
    box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.05);
}

.playing-navi .sample-audio-thumbnail[data-file-type='audio'] .sample-audio-playpause-button {
    background-image: url('../images/icon_pause.svg');
    opacity: 1;
}

.profile {
    font-size: 13px;
}

.profile__header {
    height: 45px;
    display: flex;
    align-items: center;
}

.profile__cover-image {
    background-size: cover;
    background-position: center center;
    position: relative;
    height: 20vw;
}

.profile__cover-image .button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.profile__top {
    display: flex;
    top: -80px;
    left: 0;
    position: absolute;
    z-index: 2;
}

@media (max-width: 768px) {
    .profile__top {
        top: -50px;
        left: 15px;
    }
}

.profile__top-left {
    display: flex;
}

@media (max-width: 768px) {
    .profile__top-left {
        display: block;
        width: 90px;
        text-align: center;
    }
}

.profile__top-right {
    display: flex;
    margin-right: 75px;
    flex-direction: row-reverse;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .profile__top-right {
        margin-right: 25px;
    }
}

.profile__top-edit {
    margin: 30px 0 45px;
}

.profile__avatar {
    text-align: center;
}

@media (max-width: 768px) {
    .profile__avatar-img {
        width: 90px !important;
    }
}

.profile__avatar-upload {
    margin-top: 15px;
    text-align: center;
}

.profile__avatar-link {
    margin-top: 25px;
    text-align: right;
}

.profile__info {
    display: flex;
    position: absolute;
    right: 75px;
    justify-items: center;
}

@media (max-width: 768px) {
    .profile__info {
        margin-top: 20px;
        right: 25px;
    }
}

.profile__name {
    font-size: 1.5em;
    color: #333;
    padding-bottom: 10px;
    word-break: break-word;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', sans-serif;
}

@media (max-width: 768px) {
    .profile__name {
        font-size: 1.4em;
    }
}

.profile__job {
    color: #53565a;
    font-size: 1em;
    padding: 7px 10px 10px;
    max-width: 400px;
    word-break: break-word;
}

@media (max-width: 768px) {
    .profile__job {
        font-size: .9em;
    }
}

.profile__social {
    padding: 0;
    margin: 0;
    list-style: none;
}

.profile__social-item {
    display: inline-block;
    margin: 0 8px;
}

.profile__social-item:hover svg path {
    fill: #0076a5;
}

.profile__social-item:hover .homepage svg circle {
    fill: #0076a5;
}

.profile__social-item:hover .homepage svg path {
    fill: #ffffff;
}

.profile__social-item:first-child {
    margin-left: 0;
}

.profile__social-item:last-child {
    margin-right: 0;
}

.profile__book-mark-link {
    display: block;
    padding-left: 18px;
    margin-left: 18px;
    border-left: 1px solid #e5e5e5;
}

.profile__audio {
    background-color: #fafbfb;
    padding: 30px 0;
}

@media (max-width: 768px) {
    .profile__audio {
        padding: 15px 0 30px;
    }
}

.profile__section-title {
    font-size: 20px;
    color: #333;
    margin-bottom: 35px;
}

@media (max-width: 768px) {
    .profile__section-title {
        font-size: 13px;
        color: #fff;
        background-image: linear-gradient(to right, #a7a8a9, #333);
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        margin-left: -15px;
        display: inline-block;
        min-width: 125px;
        padding: 1px 15px;
        margin-bottom: 25px;
    }
}

.profile__section-sub-title {
    font-size: 13px;
    color: #333;
    margin-bottom: 35px;
}

@media (max-width: 768px) {
    .profile__section-sub-title {
        font-size: 13px;
        margin-bottom: 15px;
    }
}

.profile__philosophy {
    padding: 10px 0 50px;
}

.profile__philosophy-quote {
    font-size: 20px;
    line-height: 30px;
    color: #333333;
    padding: 90px 10vw 10px;
    white-space: pre-line;
    word-break: break-word;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', sans-serif;
}

@media (max-width: 768px) {
    .profile__philosophy-quote {
        font-size: 16px;
        line-height: 24px;
        padding: 70px 5vw 10px;
    }
}

.profile__profile {
    padding: 16px 0 0px;
}

.profile__profile-title {
    border-top: 1px solid #f0f0f0;
    line-height: 200%;
    font-feature-settings: 'palt' on;
    font-size: clamp(1.125rem, 0.989rem + 0.68vw, 1.5rem);
}

.profile__profile-quote {
    /* font-size: 16px; */
    font-size: clamp(0.813rem, 0.744rem + 0.34vw, 1rem);
    color: #000;
    line-height: 200%;
    margin: 32px auto 128px;
    text-align: justify;
    max-width: 640px;
    min-height: 100px;
}

.profile__profile-quote.no-text {
    margin: 16px auto;
}

.profile__profile-quote span {
    /* font-size: 16px; */
    font-size: clamp(0.813rem, 0.744rem + 0.34vw, 1rem);
    color: #000;
    line-height: 200%;
    font-style: normal;
    font-weight: 300;
    text-align: justify;
    white-space: pre-line;
    word-break: break-word;
}

@media (max-width: 768px) {
    .profile__profile-quote {
        max-width: 100vw;
        margin: 32px auto 64px;
    }

    .profile__profile-quote span {
        /* font-size: 13px !important; */
        font-size: clamp(0.813rem, 0.744rem + 0.34vw, 1rem);
    }

    .profile__profile {
        padding: 20px 20px 160px;
    }
}

.profile__profile-quote.no-content span {
    display: none;
}

.profile__work {
    padding: 20px 0 60px;
    border-bottom: 1px solid #e5e5e5;
}

.profile__work-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
}

@media (max-width: 768px) {
    .profile__work-list {
        flex-wrap: nowrap;
        overflow-x: auto;
    }
}

.profile__work-load {
    margin-top: 20px;
    text-align: center;
}

.profile__original {
    padding-top: 20px;
}

.profile__original-title {
    font-size: 20px;
    color: #333;
    margin-bottom: 35px;
}

.profile__original-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -7.5px;
}

@media (max-width: 768px) {
    .profile__original-list {
        margin: 0;
    }
}

.profile__support {
    padding: 75px 0 80px;
    background-color: #a7a8a9;
    text-align: center;
}

@media (max-width: 768px) {
    .profile__support {
        padding: 20px 0 25px;
    }
}

.profile__support-text {
    color: #333;
    font-size: 32px;
}

@media (max-width: 768px) {
    .profile__support-text {
        font-size: 13px;
    }
}

.profile__support-button {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .profile__support-button {
        margin-top: 15px;
    }
}

.profile__support .button {
    font-size: 20px;
    min-width: 300px;
    height: 60px;
    line-height: 60px;
    border-radius: 100px;
}

@media (max-width: 768px) {
    .profile__support .button {
        height: 44px;
        line-height: 44px;
        width: calc(100% - 60px);
    }
}

.profile--edit .button--background {
    height: 30px;
}

.profile--edit .audio-remain {
    top: -10px;
}

.profile--edit .button--icon-add {
    position: absolute;
    right: 0;
    bottom: 0;
}

@media (max-width: 768px) {
    .profile--edit .button--icon-add {
        bottom: -20px;
    }
}

.profile__page-title {
    margin: 30px 0;
}

@media (max-width: 768px) {
    .profile__page-title {
        color: #fff;
        background-image: linear-gradient(to right, #a7a8a9, #333);
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        margin-left: -15px;
        display: inline-block;
        min-width: 125px;
        padding: 1px 15px;
    }
}

.profile__edit-other {
    margin: 20px 0 -25px;
}

.profile__edit-other .container {
    padding: 0 15vw;
}

@media (max-width: 768px) {
    .profile__edit-other .container {
        padding: 0 15px;
    }
}

.profile .profile-quote .form-control {
    min-height: 180px;
}

.profile__project {
    margin-bottom: 50px;
}

@media (max-width: 768px) {
    .profile__project {
        margin-bottom: 30px;
    }
}

.profile--works .profile__cover-image {
    margin-bottom: 15px;
}

.profile__project-cover {
    margin-bottom: 30px;
}

.profile__project-image {
    padding: 10% 0;
    background-size: cover;
    background-position: center center;
    position: relative;
}

.profile__order-statistic {
    background-color: #a7a8a9;
    padding: 20px;
    margin-bottom: 50px;
    text-align: center;
}

.profile__order-year {
    margin-bottom: 40px;
}

.profile__order-money {
    font-size: 30px;
    color: #009ace;
    margin-bottom: 20px;
}

.profile__order-compare {
    font-size: 16px;
    color: #009ace;
    margin-bottom: 20px;
}

.profile__order-chart {
    height: 200px;
}

@media (max-width: 768px) {
    .profile__order-chart {
        height: 100px;
    }
}

.profile__order-filter {
    background-color: #fcfcfc;
    padding: 6px 0;
    margin-top: 10px;
}

.profile__order-filter .container {
    display: flex;
    align-items: center;
}

.profile__order-left {
    display: flex;
    align-items: center;
}

.profile__order-monthy {
    margin-left: 8px;
}

.profile__order-right {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.profile__order-right .select-container {
    margin: 0;
}

.profile__order-right .sumo-select {
    min-width: 120px;
}

.profile__order-right .sumo-select .SumoSelect > .CaptionCont {
    background-color: transparent;
    border: none;
    text-transform: uppercase;
}

.profile__order-right .sumo-select .SumoSelect > .CaptionCont > label {
    opacity: 0;
}

.profile__order-arrow:hover {
    cursor: pointer;
}

.profile__order-arrow:before {
    content: '▼';
    display: inline-block;
}

.profile__order-arrow.active:before {
    content: '▲';
}

.profile__order-table {
    margin-top: 20px;
}

@media (max-width: 768px) {
    .profile__order-table.order-payment {
        margin-left: -15px;
        margin-right: -15px;
    }
}

.profile__order-total,
.profile__order-estimate {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
}

.profile__order-total {
    color: #000;
    margin-bottom: 15px;
}

.profile__order-estimate {
    color: #009ace;
}

.profile__sub-title {
    margin-bottom: 15px;
    border-top: 1px solid #a7a8a9;
    padding-top: 10px;
    margin-top: 45px;
    color: #333;
}

.profile__order-link {
    text-align: right;
}

.quote-mark {
    font-family: 'Noto Sans JP', sans-serif;
    font-size: 4em;
    height: 50px;
    color: #53565a;
}

.new {
    position: relative;
}

.new:not(footer, .statement):after,
.new.statement .new-button,
footer.new .new-button,
.new.header-fullscreen-container .new-button {
    content: 'NEW';
    display: block;
    background: #009ace;
    color: white;
    font-size: 10px;
    padding: 2px 10px;
    border-radius: 50px;
    position: absolute;
    bottom: -15px;
    left: calc(50% - 21px);
    line-height: 1.4;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    z-index: 2;
    cursor: pointer;
}

.new.statement .new-button,
footer.new .new-button,
.editable .edit-profile-statement-btn {
    bottom: 15% !important;
}

.new[data-approve=true]:after,
.new[data-edited=true]:after,
.new[data-rejected=true]:after,
.new[data-approve=true].header-fullscreen-container .new-button,
.new[data-edited=true].header-fullscreen-container .new-button,
.new[data-rejected=true].header-fullscreen-container .new-button,
footer.new[data-approve=true] .new-button,
footer.new[data-edited=true] .new-button,
footer.new[data-rejected=true] .new-button,
.new[data-approve=true].statement .new-button,
.new[data-edited=true].statement .new-button,
.new[data-rejected=true].statement .new-button {
    background: #0076a5 no-repeat center;
    background-size: 12px;
    height: 24px;
    width: 24px;
    border-radius: 50px;
    margin: 24px 10px 0;
    content: ' ';
}

.sample-audio-item.new[data-approve=true]:after,
.sample-audio-item.new[data-edited=true]:after,
.sample-audio-item.new[data-rejected=true]:after {
    background: #0076a5 no-repeat center;
    background-size: 12px;
    height: 24px;
    width: 24px;
    border-radius: 50px;
    margin: 24px 10px 0;
    content: ' ';
    top: 128px;
    left: 58px;
}

.new[data-edited=true]:after,
.new[data-edited=true].header-fullscreen-container .new-button,
footer.new[data-edited=true] .new-button,
.new[data-edited=true].statement .new-button {
    background-image: url(../images/icon-edit.svg) !important;
    color: transparent;
}

.new[data-rejected=true]:after,
.new[data-rejected=true].statement .new-button,
footer.new[data-rejected=true] .new-button,
.new[data-rejected=true].header-fullscreen-container .new-button {
    background-image: url(../images/icon-reject.svg) !important;
    background-size: 14px;
}

.new[data-approve=true]:after,
.new[data-approve=true].statement .new-button,
footer.new[data-approve=true] .new-button,
.new[data-approve=true].header-fullscreen-container .new-button {
    background-image: url(../images/icon-approve.svg) !important;
    background-size: 14px;
    color: transparent;
}

.statement .menu-checking,
footer .menu-checking {
    bottom: 15%;
}

.sample-audio-item.new:after {
    bottom: auto;
    top: 20px;
    left: 20px;
}

.profile__profile-quote .menu-checking,
.profile__profile .new:after {
    top: -15px;
    left: 5px;
    bottom: auto;
}

.profile__profile .new[data-approve=true]:after,
.profile__profile .new[data-edited=true]:after,
.profile__profile .new[data-rejected=true]:after {
    background: #0076a5 no-repeat center;
    background-size: 12px;
    height: 24px;
    width: 24px;
    border-radius: 50px;
    margin: 0 10px 0;
    content: ' ';
}

.profile__profile .profile__top-right .new:after {
    left: -9px;
}

.edit-avatar-btn {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
}

.editable.profile__cover-image .edit-banner-btn,
.editable.profile__avatar .edit-avatar-btn,
.edit-sale-content-image-btn {
    display: none;
    background-image: url('../images/icon-edit-image.svg');
    background-size: 25px;
    background-position: center;
    background-repeat: no-repeat;
    background-color: rgba(167, 168, 169, 0.51);
    height: 100%;
    width: 100%;
}

.editable.profile__cover-image:hover .edit-banner-btn,
.editable.profile__avatar:hover .edit-avatar-btn,
.sale-content-image:hover .edit-sale-content-image-btn {
    display: block;
    cursor: pointer;
}

.sale-content-content-type {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    width: 100%;
}

.sale-content-content-type select {
    padding: 10px;
}

.upload__range-slider {
    width: 100%;
}

.rel {
    position: relative;
}

.editable.sample-audio-item .sample-audio-info:hover .edit-sample-audio-btn,
.editable.sample-audio-item .sample-audio-info:hover .delete-sample-audio-btn,
.editable.sample-audio-item .sample-audio-info:hover .drag-sample-audio-btn {
    background-image: url(../images/icon-edit.svg);
    background-size: 16px;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #009ace;
    border: 1px solid #009ace;
    height: 32px;
    width: 32px;
    position: absolute;
    bottom: -16px;
    left: calc(50% - 16px);
    border-radius: 50%;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    cursor: pointer;
}

.editable.profile__philosophy-container:hover .edit-philosophy-btn,
.editable.profile__profile-quote:hover .edit-profile-profile-btn,
.editable.profile__name:hover .edit-profile-name-btn,
.editable.profile__job:hover .edit-profile-job-btn {
    background-image: url(../images/icon-edit.svg);
    background-size: 24px;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #009ace;
    border: 1px solid #009ace;
    height: 64px;
    width: 64px;
    position: absolute;
    bottom: -24px;
    left: calc(50% - 24px);
    border-radius: 50%;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    cursor: pointer;
}

.profile__profile-quote.no-content:is(.editable, .new) .edit-profile-profile-btn,
.no-content:is(.editable, .new) .edit-profile-statement-btn {
    width: min(100%, 1140px);
    left: 0;
    bottom: 0;
    position: relative;
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
    background-color: white !important;
    background-image: url(../images/icon-add-g.svg);
    background-position: center calc(50% - 8px);
    background-size: 32px;
    background-repeat: no-repeat;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    height: 64px;
    margin: 16px 0;
    text-align: center;
    padding: 32px 0 0;
    color: #000;
    font-size: 11px;
    cursor: pointer;
}

.profile__profile-quote.no-content:is(.editable, .new) .edit-profile-profile-btn::after {
    content: "プロフィールを設定";
}

.no-content:is(.editable, .new) .edit-profile-statement-btn::after {
    content: "ステートメントを設定";
}

.profile__profile-quote.no-content:is(.editable, .new) .edit-profile-profile-btn:hover,
.no-content:is(.editable, .new) .edit-profile-statement-btn:hover {
    color: #fff;
    background-image: url(../images/icon-add-w.svg) !important;
    background-color: #009ace !important;
    border: 1px solid #009ace !important;
    box-shadow: 0px 0px 8px 0px var(--soremo-blue) !important;
}

.profile__profile-quote.no-content:is(.editable, .new) .edit-profile-profile-btn:hover::after,
.no-content:is(.editable, .new) .edit-profile-statement-btn:hover::after {
    color: #fff;
}

.edit-sample-audio-btn,
.drag-sample-audio-btn,
.delete-sample-audio-btn {
    display: none;
}

/* .ui-sortable-placeholder {
    background: red;
    width: 200px;
    height: 150px;
    display: inline-block;
    position: relative;
} */

.editable.sample-audio-item .sample-audio-info:hover .edit-sample-audio-btn {
    top: 2px;
    left: auto;
    right: 56px;
    background-image: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #a7a8a9;
    background-color: #fcfcfc;
    border: 1px solid #f0f0f0;
}

.editable.sample-audio-item .sample-audio-info:hover .drag-sample-audio-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    top: 2px;
    left: auto;
    right: 0;
    background-image: none;
    background-position: center;
    background-repeat: no-repeat;
    color: #a7a8a9;
    background-color: #fcfcfc;
    border: 1px solid #f0f0f0;
}

.editable.sample-audio-item .sample-audio-info:hover .delete-sample-audio-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    top: 2px;
    left: auto;
    right: 28px;
    background-image: none;
    background-position: center;
    background-repeat: no-repeat;
    color: #a7a8a9;
    background-color: #fcfcfc;
    border: 1px solid #f0f0f0;
}

.editable.sample-audio-item .sample-audio-info .sample-audio-title {
    max-width: 144px;
    text-overflow: ellipsis;
    cursor: pointer;
}

.editable.sample-audio-item .sample-audio-info:hover .sample-audio-title {
    max-width: 80px;
}

.editable .edit-sample-audio-btn:hover,
.editable .drag-sample-audio-btn:hover,
.editable .delete-sample-audio-btn:hover {
    color: #fff !important;
    background-color: #009ace !important;
}

.editable.profile__philosophy-container:hover .edit-philosophy-btn:hover,
.editable.profile__profile-quote:hover .edit-profile-profile-btn:hover,
.editable.profile__name:hover .edit-profile-name-btn:hover,
.editable.profile__job:hover .edit-profile-job-btn:hover {
    background-image: url(../images/icon-edit.svg);
    background-color: #0076a5;
}

.edit-link-btn {
    background-image: url(../images/icon-edit-g.svg);
}

.open-link-btn {
    background-position: 5px center;
    background-image: url(../images/icon-new-tab-g.svg);
}

.edit-link-btn,
.open-link-btn {
    height: 100%;
    width: 50%;
    background-size: 16px;
    background-repeat: no-repeat;
}

.edit-link-btn:hover {
    background-image: url(../images/icon-edit.svg);
}

.open-link-btn:hover {
    background-image: url(../images/icon-new-tab.svg);
}

.editable.profile__social-item:hover .hover-menu {
    display: flex;
    height: 20px;
    width: 60px;
    border-radius: 20px;
    background: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    position: absolute;
    bottom: -12px;
    left: calc(50% - 30px);
}

.menu-checking .menu-btn {
    height: 100%;
    width: 50%;
    background-size: 16px;
    background-repeat: no-repeat;
}

.menu-btn:hover {
    background-color: #0076a5;
    cursor: pointer;
}

.side-rounded-menu .menu-btn {
    background-position: center;
}

.side-rounded-menu .menu-btn:first-child {
    background-position: 9px center;
}

.side-rounded-menu .menu-btn:last-child {
    background-position: 5px center;
}

.side-rounded-menu .menu-btn:not(:last-child) {
    border-right: 1px solid #f0f0f0;
}

.side-rounded-menu .menu-btn:last-child:hover {
    border-radius: 0 20px 20px 0;
}

.side-rounded-menu .menu-btn:first-child:hover {
    border-radius: 20px 0 0 20px;
}

.menu-checking {
    z-index: 3;
    display: flex;
    height: 20px;
    width: 60px;
    border-radius: 20px;
    background: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    position: absolute;
    bottom: -12px;
    left: calc(50% - 45px);
}

.edit-btn {
    background-image: url(../images/icon-edit-g.svg);
}

.approve-btn {
    background-image: url(../images/icon-approve-b.svg);
}

.reject-btn {
    background-image: url(../images/icon-reject-r.svg);
}

.edit-btn:hover {
    background-image: url(../images/icon-edit.svg);
}

.approve-btn:hover {
    background-image: url(../images/icon-approve.svg);
}

.reject-btn:hover {
    background-image: url(../images/icon-reject.svg);
}

.button--primary {
    font-size: 1.5em;
    width: 300px;
    margin: 0 auto;
    display: block;
    background: #009ace;
    padding: 10px 30px;
    text-align: center;
    color: white;
    cursor: pointer;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', sans-serif;
}

.button--primary.disable {
    background: #a7a8a9;
    cursor: auto;
    box-shadow: none;
}

.btn.btn--primary:focus.btn--disabled,
.btn--primary.btn--disabled {
    color: #A7A8A9 !important;
}

.button--primary:not(.disable):hover {
    background: #0076a5;
}

.modal-content {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: none;
    border-radius: 12px;
}

textarea.profile-edit-modal {
    border: 1px solid #F0F0F0;
    width: 100%;
    padding: 10px;
    max-height: 60vh;
    min-height: 150px;
    border-radius: 5px;
    outline-color: #D3D3D3;
}

input.profile-edit-modal-input {
    border: 1px solid #F0F0F0;
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    outline-color: #D3D3D3;
}

input.profile-edit-modal-input::placeholder,
textarea::placeholder {
    color: #d3d3d3 !important;
}

textarea {
    resize: none;
}

.modal-content .btn {
    border: none;
}

.modal-content .btn-basic {
    background: #a7a8a9;
    color: white;
}

.modal-content .btn-basic:hover {
    background: #53565a;
}

.modal-content .btn-primary {
    background: #009ace;
}

.modal-content .btn-primary:hover {
    background: #0076a5;
}

.sale-content-modal-row {
    display: flex;
    flex-wrap: wrap;
    margin: 10px 0;
}

.sale-content-modal-row.flex-column {
    flex-direction: column;
}

.sale-content-image {
    background: #a7a8a9 no-repeat center;
    background-size: cover !important;
}

.sale-content-type {
    margin: 0 0 0 20px;
}

.sale-content-modal-row {
    justify-content: flex-start;
    align-items: center;
}

.sale-content-modal-row.flex-column {
    justify-content: center;
    align-items: flex-start;
}

.sale-content-image {
    width: 300px;
    height: 300px;
}

.sale-content-type {
    margin: 10px 0 0 0;
}

@media (max-width: 576px) {
    .sale-content-image {
        width: 200px;
        height: 200px;
    }
}

.video-setting .sale-content-video-image {
    width: 100%;
    aspect-ratio: 9/5;
    height: auto;
    margin-left: 30px;
    background-size: cover;
}

.sale-content-type input[type=radio] {
    transform: scale(1.6);
    margin-bottom: 20px;
}

.sale-content-type label {
    font-weight: 500;
    margin-left: 5px;
}

.sale-content-type input.sale-content-price-input {
    margin-left: 10px;
    border: 1px solid #0076a5;
    color: #0076a5;
    padding: 10px 40px 10px 10px;
    height: 40px;
    border-radius: 5px;
    text-align: right;
}

.sale-content-type .sale-content-price {
    position: relative;
}

.sale-content-type .sale-content-price span {
    color: #a7a8a9;
    position: absolute;
    right: 20px;
    top: 10px;
}

.input-container.has-error .error-message {
    display: block !important;
    color: #2CC84D;
    font-size: 11px;
}

.input-container.has-error input {
    border-color: #2CC84D;
}

.input-container:not(.has-error) .error-message {
    display: none;
}

.sale-content-type input[readonly=readonly] {
    border: 1px solid #f0f0f0;
    color: #f0f0f0;
}

.sale-content-audio .sale-content-audio-play {
    width: 48px;
    height: 100%;
    background-image: url(../images/icon-play-g.svg);
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
}

.sale-content-audio .sale-content-audio-play:hover {
    background-image: url(../images/icon-play-b.svg);
}

.sale-content-audio .sale-content-audio-pause {
    width: 48px;
    height: 100%;
    background-image: url(../images/icon-pause-g.svg);
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
}

.sale-content-audio .sale-content-audio-pause:hover {
    background-image: url(../images/icon-pause-b.svg);
}

.sale-content-audio .sale-content-audio-loading {
    width: 48px;
    height: 100%;
    background-image: url(../images/icon-loading-b.svg);
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: 16px center;
    cursor: pointer;
}

.sale-content-audio .sale-content-audio-name {
    height: 100%;
    color: #53565a;
    width: calc(100% - 160px);
    display: flex;
    align-items: center;
    overflow: hidden;
}

.sale-content-audio .sale-content-audio-menu {
    width: 48px;
    height: 100%;
    background-image: url(../images/icon-menu-g.svg);
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    position: relative;
}

.sale-content-audio .sale-content-audio-menu:hover {
    background-image: url(../images/icon-menu-b.svg);
}

.sale-content-audio-menu-dropdown {
    display: none;
    background: white;
    border-radius: 5px;
    box-shadow: -1px 1px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    position: absolute;
    top: -30px;
    left: -40px;
}

.sale-content-audio-menu-dropdown .dropdown-menu-button {
    display: flex;
    align-items: center;
}

.sale-content-audio-menu-dropdown .dropdown-menu-button .icon-edit,
.sale-content-audio-menu-dropdown .dropdown-menu-button .icon-download,
.sale-content-audio-menu-dropdown .dropdown-menu-button .icon-delete {
    background-position: center;
    background-repeat: no-repeat;
    background-size: 16px;
    height: 36px;
    width: 36px;
}

.sale-content-audio-menu-dropdown .dropdown-menu-button .icon-edit {
    background-image: url(../images/icon-edit-g.svg);
}

.sale-content-audio-menu-dropdown .dropdown-menu-button .icon-delete {
    background-image: url(../images/icon-delete-g.svg);
}

.sale-content-audio-menu-dropdown .dropdown-menu-button .icon-download {
    background-image: url(../images/icon-download-3.svg);
}

.sale-content-audio-menu-dropdown .dropdown-menu-button:hover .icon-download {
    background-image: url(../images/icon-download-2.svg);
}

.sale-content-audio-menu-dropdown .dropdown-menu-button:hover .icon-edit {
    background-image: url(../images/icon-edit.svg);
}

.sale-content-audio-menu-dropdown .dropdown-menu-button:hover .icon-delete {
    background-image: url(../images/icon-delete.svg);
}

.sale-content-audio-menu-dropdown .dropdown-menu-button span {
    height: 36px;
    display: flex;
    padding-right: 10px;
    align-items: center;
}

.sale-content-audio-menu-dropdown .dropdown-menu-button:hover {
    background: #0076a5;
    color: white;
}

.sale-content-audio-menu-dropdown .dropdown-menu-button:hover:first-child {
    border-radius: 5px 5px 0 0;
}

.sale-content-audio-menu-dropdown .dropdown-menu-button:hover:last-child {
    border-radius: 0 0 5px 5px;
}

.sale-content-audio .sale-content-audio-menu:hover .sale-content-audio-menu-dropdown {
    display: block;
}

.sale-content-audio .sale-content-audio-time {
    height: 100%;
    color: #53565a;
    width: 64px;
    display: flex;
    align-items: center;
    font-size: .8em;
    padding: 0 12px;
}

.modal-header {
    border: none;
    padding: 15px;
}

.modal-header .modal-title {
    font-size: 16px !important;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
}

.modal__fullscreen .modal-header {
    padding: 15px 0;
}

.modal__fullscreen .btn--tertiary,
.modal__fullscreen .btn--tertiary:hover {
    background-color: transparent !important;
}

.modal-footer {
    border: none;
    padding: 32px 15px;
    border-top: 1px solid #F0F0F0;
    display: flex;
    justify-content: flex-end;
    flex-direction: row;
}

.bootbox:not(.modal__fullscreen) .modal-footer {
    justify-content: flex-end;
    margin: 0 32px;
    padding: 16px 0 32px;
}

.bootbox:not(.modal__fullscreen) .modal-header {
    padding: 32px 32px 8px;
}

.bootbox:not(.modal__fullscreen) .modal-body {
    padding: 0 32px 24px;
}

.modal__fullscreen .modal-footer {
    padding: 32px 0;
}

.btn-round {
    border-radius: 50px;
}

.btn-big {
    padding: 12px 64px;
    font-size: 1em;
}

button[type="button"] {
    border: none;
    outline: none !important;
}

.btn-center {
    display: block;
    margin: auto auto 5px!important;
}

.checkbox input[type='checkbox']:before {
    background-color: white;
    border: 2px solid #53565a;
    cursor: pointer;
}

.checkbox input[type='checkbox']:checked:after {
    border: solid #53565a;
    border-width: 0 2px 2px 0;
    transform: scale(.8) rotate(45deg);
    cursor: pointer;
}

.checkbox label {
    color: #53565a;
}

.sale-content-color-selection {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.sale-content-color-selection .row {
    display: flex;
}

.sale-content-color-selection .color-block {
    height: 60px;
    width: 60px;
    background: #aaa;
    margin: 10px;
    border: 2px solid white;
    border-radius: 4px;
    box-shadow: inset 0 0 0px 2px #fff;
}

@media (max-width: 576px) {
    .sale-content-color-selection .color-block {
        height: 45px;
        width: 45px;
        margin: 5px;
    }
}

.sale-content-color-selection .color-block.active,
.sale-content-color-selection .color-block:hover {
    border: 2px solid #009ace;
    cursor: pointer;
}

@media (max-width: 576px) {
    .sale-content-audio {
        margin: 5px 5px 0 5px;
        max-width: 100%;
    }

    .sale-content-audio-name {
        width: calc(100% - 120px) !important;
    }
}

.sample-audio-container {
    overflow-y: hidden;
    overflow-x: scroll;
}

.sample-audio-container::-webkit-scrollbar {
    background-color: #fff;
    width: 16px;
}

/* background of the scrollbar except button or resizer */
.sample-audio-container::-webkit-scrollbar-track {
    background-color: #fff;
}

/* scrollbar itself */
.sample-audio-container::-webkit-scrollbar-thumb {
    background-color: #f0f0f0;
    border-radius: 16px;
    border: 5px solid #fff;
}

/* set button(top and bottom of the scrollbar) */
.sample-audio-container::-webkit-scrollbar-button {
    display: none;
}

.audio-chart {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.chart-slider-container {
    margin: 4px;
    background-image: url('../images/audio_setting_graph.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    width: 200px;
}

.chart-slider {
    height: 80%;
    width: 80%;
    position: relative;
}

.square {
    border-radius: 4px;
    height: 25%;
    width: 25%;
    background-color: rgba(0, 154, 206, 0.75);
    position: absolute;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Float right  */
.btn-right {
    float: right;
}

.modal__title {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 24px;
    line-height: 150%;
    letter-spacing: -0.327273px;
    margin-bottom: 0;
    color: #000000;
}

#modal-detail-audio-setting.modal,
#modal-detail-video-setting.modal {
    padding: 20px 17px;
    overflow-y: hidden;
    max-height: 100vh;
}

#modal-detail-audio-setting .modal-dialog,
#modal-detail-video-setting .modal-dialog {
    width: calc(100% - 10px);
    max-height: auto;
    max-width: 480px;
    overflow-y: hidden;
}

#modal-detail-audio-setting .modal-content,
#modal-detail-video-setting .modal-content {
    border-radius: 12px;
    max-height: calc(100vh - 120px);
    overflow-y: scroll;
    overflow-x: hidden;
}

#modal-detail-video-setting .modal-content {
    max-width: 480px;
}

#modal-detail-audio-setting .modal-content::-webkit-scrollbar,
#modal-detail-video-setting .modal-content::-webkit-scrollbar {
	width: 8px;
	background-color: #F0F0F0;
    border-radius: 4px;
    margin: 4px;
}

#modal-detail-audio-setting .modal-content::-webkit-scrollbar-thumb,
#modal-detail-video-setting .modal-content::-webkit-scrollbar-thumb {
    background-color: #f0f0f0;
    border-radius: 4px;
}

.form-container {
    padding: 0px 43px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    box-sizing: border-box;
    border-radius: 12px;
}

.form-hint {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    letter-spacing: 0.144706px;
    color: #000000;
    font-weight: 400;
    margin-bottom: 7px;
}
.form-label {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-weight: 400;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
}
.form-label__required {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 8px;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.144706px;
    color: #009ACE;
    margin-left: 4px;
}

.form-label__optional {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 8px;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.144706px;
    color: #A7A8A9;
    margin-left: 4px;
}

.form-label__heading {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    width: 100%;
    margin: 0;
    padding: 8px 0 4px;
    color: var(--black1-color);
}

.form-control {
    padding: 16px 8px;
    color: #000;
    border: 1px solid #F0F0F0 !important;
    background-color: #FFFFFF;
    height: auto;
    font-size: 13px;
    border-radius: 4px !important;
    line-height: 20px;
}

input.form-control:focus,
textarea.form-control:focus {
    border: 1px solid #009ace !important;
    outline: none !important;
}

.input-form-container {
    max-width: 480px;
}

.input-radio {
    display: block;
    position: relative;
    padding-left: 27px;
    font-weight: 400;
    line-height: 20px;
    color: var(--black1-color);
    font-size: 13px;
    margin: 12px 0;
}

.sale-content-content-type .input-radio {
    margin: 2px 0;
}

.sale-content-content-type .br-8 {
    margin-bottom: 8px;
}

.input-radio input:checked ~ .check-mark {
    border: 1px solid var(--blue-color);
}

.input-radio .check-mark {
    width: 16px;
    height: 16px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

.input-radio .check-mark:after {
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
}

.input-radio .sale_type_text {
    margin-bottom: 8px;
}

.input-radio .sale_content_type_text,
.heading-text {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';;

}

.fallback .dz-button i {
    font-size: 18px;
}

.fallback .dz-button {
    color: #A7A8A9;
}

.dz-button {
    background: #FFFFFF;
}

.fallback {
    cursor: pointer;
    background: #FFFFFF;
}

.fallback:hover {
    background-color: #A7A8A9 !important;
    transition: none !important;
}

.fallback:hover .dz-button {
    background: #A7A8A9 !important;
    color: #FFFFFF;
    transition: none !important;
}

.fallback .dz-button p {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 400;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    margin-bottom: 0;
}

.dropzone.dz-drag-hover, .dropzone.dz-drag-hover .dz-button {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
    color: white !important;
}

.account__file {
    padding: 8px 16px !important;
}

.mattach-preview-container {
    margin-bottom: 5px;
}

.mattach-preview-container .determinate {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
}

.mcommment-file__delete {
    color: #000000;
}

.mattach-preview-container .mcommment-file {
    display: inline-flex;
    align-items: center;
    background-color: #F0F0F0 !important;
    border-radius: 4px;
    color: #000000;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    padding: 0 8px;
    position: relative;
    max-width: 250px;
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #000000;
    max-width: 150px;
    font-weight: 400;
}

.mattach-preview-container .mattach-previews {
    overflow-y: auto !important;
}

.mattach-preview-container .mcommment-file {
    padding: 8px 16px !important;
    height: 32px !important;
}

.mattach-preview-container .collection-item {
    margin: 0;
}

@media (max-width: 576px) {
    .modal {
        top: 0;
    }
}

.submit-profile-btn-container, .btn-contact-with-artist {
    position: fixed;
    bottom: 0;
    padding: 48px 12px;
    background: #fcfcfc;
    border-top: 1px solid #f0f0f0;
    width: 100%;
    z-index: 5;
}
.modal__fullscreen .modal-footer {
    position: sticky;
    bottom: 0;
    padding-top: 48px;
    padding-bottom: 48px;
    background: #fcfcfc;
    border-top: 1px solid #f0f0f0;
    width: 100%;
}

.modal__fullscreen .modal-footer:before {
    width: 1000px;
    position: absolute;
    height: 141px;
    left: -1000px;
    background: #fcfcfc;
    top: -1px;
    border-top: 1px solid #f0f0f0;
}

.modal__fullscreen .modal-footer:after {
    width: 1000px;
    position: absolute;
    height: 141px;
    right: -1000px;
    background: #fcfcfc;
    top: -1px;
    border-top: 1px solid #f0f0f0;
}

.flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
}

.submit-profile-btn-container .form-hint {
    margin-right: 8px;
    margin-bottom: 0;
}

#uploadFile {
    max-width: 480px;
}

#modal-create-success .modal-content {
    border-radius: 6px;
}

.hint-text--modal {
    color: #A7A8A9 !important;
    margin-top: 5px;
}

.profile-input-container {
    padding-top: 0;
}

.form-group .hint-text {
    line-height: 200%;
}

.switch-label {
    color: #000 !important;
}

.btn--disabled .switch-label {
    color: #a7a8a9 !important;
}

@media (max-width: 768px) {
    .profile-title-input:not(.override-form-control) {
        padding: 0 !important;
        margin: 0 0 12px 0 !important;
    }

    .profile-title-input.override-form-control:last-child  {
        margin: 0 !important;
        padding: 0 0 0 8px !important;
    }

    .profile-toggle-input {
        margin-top: 12px;
    }
}

.download-album-file {
    cursor: pointer;
}

.add-margin-to-footer {
    margin-bottom: 100px;
}

footer.additional_margin_bottom {
    margin-bottom: 140px;
}

.gallery__topics-container {
    margin-left: 16px;
    margin-top: 64px;
    margin-bottom: 80px;
}

.gallery__list-topics {
    justify-content: center;
}

.gallery__topics-container {
    display: flex;
    justify-content: center;
}

.sale-youtube-video {
    height: 144px;
    width: 256px;
    display: flex;
}

.block-link-youtube {
    position: relative;
}
.block-link-youtube .error-message {
    bottom: -17px;
    left: 0;
}
.block-link-youtube:has(.error-message.show) {
    margin: 10px 0 26px 0;
}