/* :root {
  --black1-color: #000000;
  --black2-color: #53565a;
  --grey1-color: #a7a8a9;
  --grey2-color: #d3d3d3;
  --grey3-color: #f0f0f0;
  --white-color: #ffffff;
  --blue-color: #009ace;
  --blue-color-hover: #0076a5;
  --background-color: #fcfcfc;
  --error-color: #2cc84d;
  --font-size-40: 40px;
  --line-height-60: 60px;
  --font-size-32: 32px;
  --line-height-48: 48px;
  --font-size-24: 24px;
  --line-height-36: 36px;
  --font-size-18: 18px;
  --line-height-27: 27px;
  --font-size-16: 16px;
  --line-height-24: 24px;
  --font-size-13: 13px;
  --line-height-20: 20px;
  --font-size-11: 11px;
  --line-height-17: 17px;
  --font-size-8: 8px;
  --line-height-12: 12px;
  --font-weight-300: 300;
  --font-weight-400: 400;
  --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
  --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
} */

.input-box {
  transition: all 0.3s ease;
}

.form-group.custom-input-component input.form-control,
.form-group.custom-input-component select.form-control {
  padding: 11px 28px 11px 15px;
  color: var(--black1-color);
  border: 1px solid var(--soremo-border);
  background-color: #fff;
  height: 50px !important;
  font-size: 13px;
  border-radius: 4px !important;
  line-height: var(--line-height-20);
  min-height: 50px;
  position: relative;
 
}

.custom-input-component {
  position: relative;
}

.form-group input.form-control:focus, .sselect-wrapper.component-select div[class~="open"] > .CaptionCont {
  border: 1px solid var(--blue-color) !important;
  border-radius: 4px;
  outline: none !important;
  box-shadow: none;
}


.custom-input-component .sselect-wrapper .SumoSelect:not(.open) > .CaptionCont {
  border: 1px solid var(--soremo-light-gray);
  box-shadow: none;
}

.custom-input-component .sselect-wrapper .SumoSelect:hover > .CaptionCont {
  box-shadow: none;
  cursor: pointer;
}

.custom-input-component .sselect-wrapper .SumoSelect.open > .CaptionCont {
  box-shadow: none;
  /* border: 1px solid var(--soremo-deep-blue); */
  cursor: pointer;
}

.form-group input.form-control.error, .form-group textarea.form-control.error {
  border: 1px solid var(--error-color) !important;
  border-radius: 4px;
  color: var(--error-color) !important;
}

.form-group input.form-control::placeholder,
.form-textarea textarea::placeholder {
  color: var(--soremo-placeholder) !important;
}

.form-group input.form-control.disabled,
.form-group select.form-control.disabled,
.form-group textarea.form-control.disabled {
  pointer-events: none;
  background-color: var(--soremo-border) !important;
  color: var(--soremo-light-gray) !important;
  background-image: none;
}

.form-group .error-input {
 
  font-style: normal;
  font-weight: 300;
  font-size: 11px;
  line-height: 200%;
  color: var(--error-color);
}

.form-group input.input-normal.error {
  padding: 11px 37px 11px 15px;
}

.form-group input.right-text.error {
  padding: 11px 15px 11px 37px;
  text-align: right;
}

.form-group input.right-text {
  text-align: right;
  padding: 11px 15px 11px 37px !important;
}

.form-group .error-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.sselect-wrapper {
  /* stylelint-disable */
  /* stylelint-enable */
}

.sselect-wrapper .SumoSelect > .CaptionCont {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px 16px !important;
  box-shadow: none;
  min-height: 50px;
  display: flex;
  align-items: center;
}

.sselect-wrapper.select-search .SumoSelect>.CaptionCont {
  padding: 12px 32px 12px 48px;
}

.sselect-wrapper.select-search {
  position: relative;  
}

.sselect-wrapper.select-search svg.icon-search-input {
  position: absolute;
  top: 50%;
  left: 19px;
  z-index: 1;
  transform: translate(0, -50%);
}

.sselect-wrapper.select-search svg.icon-close-input {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translate(0, -50%);
  z-index: 1;
  cursor: pointer;
  display: none;
}

.sselect-wrapper .SumoSelect.open .search-txt {
  padding: 12px 32px 12px 48px !important;
}

.form-group .input-select, .form-group.component-input .component-select .search-txt {
  /* font-family: 'AXIS Round 50 StdN' !important; */
  font-style: normal !important;
  font-weight: 300 !important;
  font-size: 13px !important;
  line-height: 200% !important;
}

.form-group.component-input-select {
  position: relative;
}

.form-group.component-input-select select {
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 4px;
  z-index: 1;
}

.form-group.component-input-select .input-select {
  padding: 12px 40px 12px 16px;
}

.form-group.component-input-select .input-select-container {
  position: relative;
}

.form-group.component-input-select .input-select-container svg {
  position: absolute;
  top: calc(50% - 11px);
  right: 13px;
  z-index: 1;
  padding: 6px;
  height: 22px;
  width: 22px;
  cursor: pointer;
}

.form-group.component-input-select ul {
  margin-top: 4px;
  border: 1px solid #F0F0F0;
  border-radius: 4px;
  position: absolute;
  padding: 0;
  display: none;
  pointer-events: all;
  max-height: 200px;
  z-index: 1000;
  overflow-y: auto;
  width: 100%;
  top: 100%;
  left: 0;
  box-shadow: 0 7px 24px rgba(0, 0, 0, 0.07);
}

.form-group.component-input-select ul::-webkit-scrollbar {
  background-color: transparent;
  width: 6px;
  height: 12px;
  border-radius: 8px;
  padding: 2px;
  margin-right: -6px;
}

/* background of the scrollbar except button or resizer */
.form-group.component-input-select ul::-webkit-scrollbar-track {
  background-color: #fff;
}

/* scrollbar itself */
.form-group.component-input-select ul::-webkit-scrollbar-thumb {
  background-color: #f0f0f0;
  border-radius: 16px;
  border: 1px solid transparent;
}

/* set button(top and bottom of the scrollbar) */
.form-group.component-input-select ul::-webkit-scrollbar-button {
  display: none;
}

.form-group.component-input-select ul li {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 8px 16px 8px 16px;
  /* border-bottom: solid 1px var(--soremo-border); */
  cursor: pointer;
  min-height: 42px;
  text-decoration: none;
  color: #000;
  pointer-events: all;
  display: flex;
  align-items: center;
  background-color: #fff;
}

.form-group.component-input-select ul li.selected {
  background: var(--soremo-border);
}

.form-group.component-input-select ul li:hover, .form-group.component-input-select ul li.is-hover {
  background-color: var(--soremo-border);
}

.form-group.component-input-select ul li:last-child {
  border-bottom: none;
}

.form-group.component-input-text-area textarea{
  resize: none;
  min-height: 110px;
  min-width: 192px;
  border-radius: 4px;
  border: 1px solid var(--soremo-border);
  font-size: 13px;
  color: var(--black1-color);
}

.form-group.component-input-text-area textarea:focus {
  border: 1px solid var(--soremo-light-gray);
}

.form-group.component-input-text-area .count-box-container {
  display: flex;
  justify-content: flex-end;
 
  font-style: normal;
   
  font-size: 11px;
  line-height: 200%;
}

.form-group.component-input-text-area .count-box-container span{
  color: var(--soremo-light-gray);
}

.form-group.component-input-text-area .count-box-container span:first-child {
  margin-right: 3px;
}

.form-group.component-input-text-area .count-box-container span:last-child {
  margin-left: 3px;
}

.sselect-wrapper.select-search .SumoSelect > .CaptionCont > label {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sselect-wrapper .SumoSelect > .CaptionCont > label:before {
  content: '';
  color: #a7a8a9;
  font-family: 'soremoicons' !important;
  /* stylelint-disable-line */
}

.sselect-wrapper .SumoSelect > .CaptionCont > label > i {
  display: none;
}

.sselect-wrapper .SelectClass,
.sselect-wrapper .SumoUnder {
  visibility: hidden;
}

.sselect-wrapper .SumoSelect > .CaptionCont > span.placeholder {
  font-style: normal;
  color: #d3d3d3;
}

.sselect-wrapper .SumoSelect > .CaptionCont > span {
  color: #000000;
  padding-right: 10px;
}

.sselect-wrapper .SumoSelect.open .search-txt {
  border: none;
  border-radius: 4px;
  padding: 12px 16px;
  height: 100%;
}

.sselect-wrapper .SumoSelect.open > .optWrapper {
  top: 54px;
  box-shadow: none;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.sselect-wrapper .SumoSelect.open > .optWrapper {
  box-shadow: 0 7px 24px rgba(0, 0, 0, 0.07) !important;
}

.daterangepicker {
  box-shadow: 0 7px 24px rgba(0, 0, 0, 0.07);
}

.sselect-wrapper .SumoSelect .select-all > label,
.sselect-wrapper .SumoSelect > .CaptionCont,
.sselect-wrapper .SumoSelect > .optWrapper > .options li.opt label {
  font-weight: 300;
  margin-bottom: 0;
  color: #000000;
}

.sselect-wrapper .SumoSelect > .optWrapper > .options li.opt {
  font-size: 11px;
  color: #000;
  padding: 8px 16px !important;
  border-bottom: 1px solid #f0f0f0;
  min-height: 50px;
  display: flex;
  align-items: center;
}

.sselect-wrapper .SumoSelect > .optWrapper > .options li.opt:last-child {
  border-bottom: none;
}

.sselect-wrapper .SumoSelect > .optWrapper > .options li.opt:hover,
.sselect-wrapper .SumoSelect > .optWrapper > .options li.opt.selected {
  background-color: #f0f0f0;
}

.sselect-wrapper .SumoSelect .select-all > span,
.sselect-wrapper .SumoSelect > .optWrapper.multiple > .options li.opt span {
  right: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sselect-wrapper .SumoSelect > .optWrapper > .options > li.opt:first-child {
  border-radius: 4px 4px 0 0;
}

.sselect-wrapper .SumoSelect > .optWrapper > .options > li.opt:last-child {
  border-radius: 0 0 4px 4px;
}

.sselect-wrapper .SumoSelect .select-all > span i,
.sselect-wrapper .SumoSelect > .optWrapper.multiple > .options li.opt span i {
  display: none;
}

.sselect-wrapper .SumoSelect .select-all.partial > span:before,
.sselect-wrapper .SumoSelect .select-all.selected > span:before,
.sselect-wrapper
  .SumoSelect
  > .optWrapper.multiple
  > .options
  li.opt.selected
  span:before {
  content: '';
  color: #53565a;
  font-family: 'soremoicons' !important;
}

.sselect-wrapper.select-black .SumoSelect.open > .optWrapper {
  background-color: #000;
  border: none;
  color: #fff;
}

.sselect-wrapper.select-black .SumoSelect > .optWrapper > .options li.opt {
  padding: 12px 16px;
  border-bottom: 1px solid #53565a;
}

.sselect-wrapper.select-black
  .SumoSelect
  > .optWrapper
  > .options
  li.opt:last-child {
  border-bottom: none;
}

.sselect-wrapper.select-black .SumoSelect > .optWrapper > .options li.opt:hover,
.sselect-wrapper.select-black
  .SumoSelect
  > .optWrapper
  > .options
  li.opt.selected {
  background-color: #232323;
}

.sselect-wrapper.select-black .SumoSelect .select-all.partial > span:before,
.sselect-wrapper.select-black .SumoSelect .select-all.selected > span:before,
.sselect-wrapper.select-black
  .SumoSelect
  > .optWrapper.multiple
  > .options
  li.opt.selected
  span:before {
  color: #fff;
}


.component-datetime-container.calendar-time {
  width: 96px;
}

.calendar-datetime-picker, .calendar-time-picker{
  font-size: 12px;
  border-radius: 6px;
  padding: 12px 40px 12px 16px !important;
  /* stylelint-disable */
  /* stylelint-enable */
}

.component-datetime-container {
  position: relative;
}

.component-datetime-container svg {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translate(0, -50%);
}

.component-datetime-container.calendar-time svg {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translate(0, -50%);
}

.calendar-datetime-picker .table-condensed {
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.datepicker {
  cursor: pointer;
}

.calendar-datetime-picker .datepicker-inline .icon {
  font-size: 14px;
  box-shadow: 0 7px 24px rgba(0, 0, 0, 0.07);
  border-radius: 6px;
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
}

.calendar-datetime-picker .datepicker-inline .next {
  padding-right: 12px;
}

.calendar-datetime-picker .datepicker-inline .prev {
  padding-left: 12px;
}

.calendar-datetime-picker .datepicker-inline .next,
.calendar-datetime-picker .datepicker-inline .prev {
  padding-top: 10px;
}

.calendar-datetime-picker .datepicker-inline .next:before,
.calendar-datetime-picker .datepicker-inline .prev:before {
  display: none;
}

.calendar-datetime-picker .datepicker-inline .next:hover,
.calendar-datetime-picker .datepicker-inline .prev:hover {
  cursor: pointer;
}

.calendar-datetime-picker .datepicker-inline .datepicker-switch {
  font-size: 14px;
  padding-top: 10px;
}

.calendar-datetime-picker .datepicker-inline .day {
  background: none !important;
  /* stylelint-disable-line */
  position: relative;
  z-index: 1;
}

.calendar-datetime-picker .datepicker-inline .day:before {
  content: '';
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.calendar-datetime-picker .datepicker-inline .day:not(.disabled):hover {
  cursor: pointer;
}

.calendar-datetime-picker .datepicker-inline .dow {
  border-bottom: none;
  color: #a7a8a9;
  padding: 14px;
}

.calendar-datetime-picker .datepicker-inline .old,
.calendar-datetime-picker .datepicker-inline .new {
  color: #f0f0f0;
}

.calendar-datetime-picker .datepicker-inline .day.today {
  color: #fff !important;
}

.calendar-datetime-picker .datepicker-inline .day.today:before {
  background-color: #53565a;
}

.calendar-datetime-picker .datepicker-inline .day.disabled {
  color: #D3D3D3 !important;
}

.calendar-datetime-picker .datepicker-inline .day.active {
  color: #fff;
}

.calendar-datetime-picker .datepicker-inline .day.active:before {
  content: '';
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #009ace;
  border: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.calendar-datetime-picker .datepicker-inline .day.active:after {
  display: none;
}

.calendar-datetime-picker .datepicker-inline .day.active-deadline {
  color: #009ace !important;
}

.calendar-datetime-picker .datepicker-inline .day.active-deadline.active {
  color: #fff !important;
}

.calendar-datetime-picker tbody tr td:nth-child(7) {
  color: #a7a8a9 !important;
  /* stylelint-disable-line */
}

.calendar-datetime-picker tfoot {
  display: none;
}

.calendar-datetime-picker--small .datepicker-inline .dow {
  padding: 8px;
}

@media (max-width: 992px) {
  .calendar-datetime-picker--small .datepicker-inline .dow {
      padding: 14px;
  }
}

.calendar-datetime-picker--small .datepicker .table-condensed > tbody > tr > td {
  padding: 8px;
}

.datepicker .table-condensed > tbody > tr > td.disabled.day {
  background: rgba(250, 148, 148, .08);
}

@media (max-width: 992px) {
  .calendar-datetime-picker--small .datepicker .table-condensed > tbody > tr > td {
      padding: 14px;
  }
}

.calendar-datetime-picker--black .table-condensed {
  background-color: rgba(255, 255, 255, 0.1);
}

.calendar-datetime-picker--black thead {
  background-color: rgba(255, 255, 255, 0.1);
}

.calendar-datetime-picker--black .datepicker-inline .icon {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 7px 24px rgba(255, 255, 255, 0.07);
  color: #a7a8a9;
}

.calendar-datetime-picker--black .datepicker-inline .prev {
  visibility: visible !important;
}

.calendar-datetime-picker--black .datepicker-inline .datepicker-switch {
  color: #fff;
}

.calendar-datetime-picker--black .datepicker-inline .day {
  color: #fff;
}

.calendar-datetime-picker--black .datepicker-inline .dow {
  color: #d3d3d3;
}

.calendar-datetime-picker--black .datepicker-inline .day.old,
.calendar-datetime-picker--black .datepicker-inline .day.new {
  color: #53565a;
}

.calendar-datetime-picker--black .datepicker-inline .day.today {
  color: #000;
}

.calendar-datetime-picker--black .datepicker-inline .day.today:before {
  background-color: #fff;
}

.calendar-datetime-picker--black .datepicker-inline .day.disabled {
  color: #53565a;
}

.calendar-datetime-picker--black .datepicker-inline .day.active {
  color: #fff;
}

.calendar-datetime-picker--black .datepicker-inline .day.active:before {
  content: '';
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #009ace;
  border: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.calendar-datetime-picker--black .datepicker-inline .day.active:after {
  display: none;
}

.calendar-datetime-picker--black .datepicker-inline .day.active-deadline {
  color: #009ace;
}

.calendar-datetime-picker--black .datepicker-inline .day.active-deadline.active {
  color: #fff;
}

.calendar-datetime-picker--black tbody tr td:nth-child(7) {
  color: #53565a !important;
}

.datepicker-dropdown {
  border: none !important;
  border-radius: 6px !important;
  box-shadow: 0 7px 64px rgba(0, 0, 0, 0.07) !important;
  padding: 10px 16px !important;
  font-size: 12px !important;
}

.datepicker-dropdown .datepicker-months table, .datepicker-dropdown .datepicker-years table {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.datepicker-dropdown .datepicker-months tbody , .datepicker-dropdown .datepicker-months tbody {
  display: flex;
  justify-content: center;
  align-items: center;
}

.datepicker-dropdown .datepicker-months tbody tr , .datepicker-dropdown .datepicker-years tbody tr {
  display: flex;
  justify-content: center;
  align-items: center;
}

.datepicker-dropdown .datepicker-months tbody tr td , .datepicker-dropdown .datepicker-years tbody tr td {
  width: 100%;
  display: grid;
}

.datepicker-dropdown .datepicker-months tbody tr td span , .datepicker-dropdown .datepicker-years tbody tr td span {
  padding: 15px;
  text-align: center;
}

.datepicker-dropdown .datepicker-months tbody tr td span:hover , .datepicker-dropdown .datepicker-years tbody tr td span:hover {
  background-color: var(--soremo-border);
}

.datepicker-dropdown .datepicker-months tbody tr td span:first-child() , .datepicker-dropdown .datepicker-years tbody tr td span:first-child() {
  grid-area: 1 / 1;
}

.datepicker-dropdown .datepicker-months tbody tr td span:nth-child(2) , .datepicker-dropdown .datepicker-years tbody tr td span:nth-child(2) {
  grid-area: 1 / 2;
}

.datepicker-dropdown .datepicker-months tbody tr td span:nth-child(3) , .datepicker-dropdown .datepicker-years tbody tr td span:nth-child(3) {
  grid-area: 1 / 3;
}

.datepicker-dropdown .datepicker-months tbody tr td span:nth-child(4) , .datepicker-dropdown .datepicker-years tbody tr td span:nth-child(3) {
  grid-area: 1 / 4;
}

.datepicker-dropdown .datepicker-months tbody tr td span:nth-child(5) , .datepicker-dropdown .datepicker-years tbody tr td span:nth-child(3) {
  grid-area: 1 / 5;
}

.datepicker-dropdown .datepicker-months thead , .datepicker-dropdown .datepicker-years thead {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.datepicker-dropdown .datepicker-months thead  tr, .datepicker-dropdown .datepicker-years thead  tr{
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.datepicker-dropdown.datepicker-orient-left .day.active {
  color: #fff;
}

.datepicker-dropdown.datepicker-orient-left .day.active:before {
  background-color: #009ace;
  border: 2px solid #009ace;
  z-index: -1;
  width: 32px;
  height: 32px;
}

.datepicker-dropdown.datepicker-orient-left .day:hover {
  background-color: #eeeeee;
}

.datepicker-dropdown .datepicker-switch {
  font-size: 14px;
}

.datepicker-dropdown .prev:before {
  background-image: none !important;
  content: "" !important;
  font-family: 'soremoicons';
  font-size: 16px;
}

.datepicker-dropdown .next:before {
  background-image: none !important;
  content: "" !important;
  font-family: 'soremoicons';
  font-size: 16px;
}

.busy-day {
  color: #afbac8 !important;
}

.custom-input-component input.form-control:not(:placeholder-shown):not(:focus), .custom-input-component textarea:not(:placeholder-shown, :focus) {
  border: 1px solid var(--soremo-light-gray);
}

/* Time pikcer */

.bootstrap-datetimepicker-widget {
  list-style: none;
}
.bootstrap-datetimepicker-widget.dropdown-menu {
  margin: 2px 0;
  padding: 4px;
  width: 19em;
}
@media (min-width: 768px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
@media (min-width: 992px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
@media (min-width: 1200px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}

.bootstrap-datetimepicker-widget.dropdown-menu.top:before {
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 7px solid #cccccc;
  border-top-color: rgba(0, 0, 0, 0.2);
  bottom: -7px;
  left: 6px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.top:after {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid white;
  bottom: -6px;
  left: 7px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:before {
  left: auto;
  right: 6px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:after {
  left: auto;
  right: 7px;
}
.bootstrap-datetimepicker-widget .list-unstyled {
  margin: 0;
}
.bootstrap-datetimepicker-widget a[data-action] {
  padding: 6px 0;
}
.bootstrap-datetimepicker-widget a[data-action]:active {
  box-shadow: none;
}
.bootstrap-datetimepicker-widget .timepicker-hour,
.bootstrap-datetimepicker-widget .timepicker-minute,
.bootstrap-datetimepicker-widget .timepicker-second {
  width: 54px;
  font-weight: bold;
  font-size: 1.2em;
  margin: 0;
}
.bootstrap-datetimepicker-widget button[data-action] {
  padding: 6px;
}
.bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Increment Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Increment Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Decrement Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Decrement Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Show Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Show Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Toggle AM/PM";
}
.bootstrap-datetimepicker-widget .btn[data-action="clear"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Clear the picker";
}
.bootstrap-datetimepicker-widget .btn[data-action="today"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Set the date to today";
}
.bootstrap-datetimepicker-widget .picker-switch {
  text-align: center;
}
.bootstrap-datetimepicker-widget .picker-switch::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Toggle Date and Time Screens";
}
.bootstrap-datetimepicker-widget .picker-switch td {
  padding: 0;
  margin: 0;
  height: auto;
  width: auto;
  line-height: inherit;
}
.bootstrap-datetimepicker-widget .picker-switch td span {
  line-height: 2.5;
  height: 2.5em;
  width: 100%;
}
.bootstrap-datetimepicker-widget table {
  width: 100%;
  margin: 0;
}
.bootstrap-datetimepicker-widget table td,
.bootstrap-datetimepicker-widget table th {
  text-align: center;
  border-radius: 4px;
  /* background-color: red; */
}

/* .bootstrap-datetimepicker-widget table td a {
  width: 40px;
  min-width: auto;
} */
.bootstrap-datetimepicker-widget table th {
  height: 20px;
  line-height: 20px;
  width: 20px;
  /* background-color: green !important; */
}
.bootstrap-datetimepicker-widget table th.picker-switch {
  width: 145px;
}
.bootstrap-datetimepicker-widget table th.disabled,
.bootstrap-datetimepicker-widget table th.disabled:hover {
  background: none;
  color: #777777;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget table th.prev::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Previous Month";
}
.bootstrap-datetimepicker-widget table th.next::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Next Month";
}
.bootstrap-datetimepicker-widget table thead tr:first-child th {
  cursor: pointer;
}
.bootstrap-datetimepicker-widget table thead tr:first-child th:hover {
  background: #eeeeee;
}
/* .bootstrap-datetimepicker-widget table td {
  height: 54px;
  line-height: 54px;
  width: 54px;
} */

.bootstrap-datetimepicker-widget table tr:nth-child(2) span {
  margin-left: 16%;
}

.bootstrap-datetimepicker-widget table td.cw {
  font-size: .8em;
  height: 20px;
  line-height: 20px;
  color: #777777;
}
.bootstrap-datetimepicker-widget table td.day {
  height: 20px;
  line-height: 20px;
  width: 20px;
}

.bootstrap-datetimepicker-widget table td.day:hover,
.bootstrap-datetimepicker-widget table td.hour:hover,
.bootstrap-datetimepicker-widget table td.minute:hover,
.bootstrap-datetimepicker-widget table td.second:hover {
  background: #eeeeee;
  cursor: pointer;
}
.bootstrap-datetimepicker-widget table td.old,
.bootstrap-datetimepicker-widget table td.new {
  color: #777777;
}
.bootstrap-datetimepicker-widget table td.today {
  position: relative;
}
.bootstrap-datetimepicker-widget table td.today:before {
  content: '';
  display: inline-block;
  border: solid transparent;
  border-width: 0 0 7px 7px;
  border-bottom-color: #337ab7;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}
.bootstrap-datetimepicker-widget table td.active,
.bootstrap-datetimepicker-widget table td.active:hover {
  background-color: #337ab7;
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.bootstrap-datetimepicker-widget table td.active.today:before {
  border-bottom-color: #fff;
}
.bootstrap-datetimepicker-widget table td.disabled,
.bootstrap-datetimepicker-widget table td.disabled:hover {
  background: none;
  color: #777777;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget table td span {
  display: flex;
  justify-content: center;
  width: 34px;
  height: 34px;
  line-height: 34px;
  margin: 2px 36px !important;
  cursor: pointer;
  border-radius: 4px;
  font-size: 13px !important;
}

.bootstrap-datetimepicker-widget table td{
  padding: 0 !important;
  height: 24px !important;
  width: 24px !important;
}

.bootstrap-datetimepicker-widget table td span:hover {
  background: #eeeeee;
}
.bootstrap-datetimepicker-widget table td span.active {
  background-color: #337ab7;
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.bootstrap-datetimepicker-widget table td span.old {
  color: #777777;
}
.bootstrap-datetimepicker-widget table td span.disabled,
.bootstrap-datetimepicker-widget table td span.disabled:hover {
  background: none;
  color: #777777;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget.usetwentyfour td.hour {
  height: 27px;
  line-height: 27px;
}
.bootstrap-datetimepicker-widget.wider {
  width: 21em;
}
.bootstrap-datetimepicker-widget .datepicker-decades .decade {
  line-height: 1.8em !important;
}
.input-group.date .input-group-addon {
  cursor: pointer;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}


/* End time pikcer */

.error-icon-input-right-text {
  position: absolute;
  top: 15px;
  left: 13px;
  display: none;
}

.error-icon-input {
  position: absolute;
  top: 15px;
  right: 13px;
  display: none;
}

.error-input .error-icon-input, .error-input .error-icon-input-right-text {
  display: block;
}

input.error:-webkit-autofill,
input.error:-webkit-autofill:hover,
input.error:-webkit-autofill:focus,
input.error:-webkit-autofill:active {
  -webkit-text-fill-color: var(--error-color) !important;
}

.custom-input-component textarea:focus {
  border: 1px solid var(--blue-color) !important;
}

.form-group.component-input-text-area textarea:disabled{
  cursor: not-allowed;
  cursor: -moz-not-allowed;
  cursor: -webkit-not-allowed;
  background-color: #f0f0f0 !important;
}

.pointer {
  cursor: pointer;
}
