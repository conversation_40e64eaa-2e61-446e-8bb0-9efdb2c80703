.updateinfo {
    margin-top: 65px;
}

/*   HEADER   */
.updateinfo__header {
    margin-bottom: 40px;
    padding-top: 10px;
    height: 40vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff !important;
}
/* Alert */
.updateinfo__alert{
    width: 300px;
    float:right;
    display: inline-flex;
    padding: 8px 16px;
    background-color: var(--color-soremo-green);
    border-radius: 6px;
    line-height: 24px;
    position: relative;
    margin-bottom: 0;
}

.updateinfo__alert::before {
    font-family: 'soremoicons';
    content: "\e932";
    font-size: 24px;
    color: #FFFFFF;
    display: flex;
    align-items: center;
}

.updateinfo__alert .alert-heading {
    color: #FFFFFF;
    font-size: 14px;
    margin-left: 8px;
    padding-right: 25px;
}

.updateinfo__alert .close {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #FFFFFF;
    opacity: 1;
}

.updateinfo__alert .close:focus {
    outline: none;
}

/* Logo */
.updateinfo__logo {
    width: 48px;
    height: 34px;
    display: block;
}

/* Heading */
.updateinfo__heading {
    margin: 16px 0 0;
    line-height: 60px;
    color: #000000;
}

/*  END HEADER  */

/*   CONTAINER   */
.updateinfo__container {
    color: #000000;
}

/* Button add version */
.btn--add, .btn--add:hover, .btn--add:focus {
    margin: 16px 0 40px;
    padding: 12px 24px;
    font-weight: 300;
    color: #FFFFFF;
    background-color: var(--color-soremo-blue);
}

.btn--add i {
    margin-right: 12px;
}

/* Container */
.updateinfo__tree {

}

.updateinfo__tree-container {
    height: 100%;
    display: inline-flex;
    margin-bottom: 40px;
    width: 100%;
}

.updateinfo__tree-container.active .updateinfo__tree-wrap-hide, .updateinfo__tree-container .updateinfo__tree-wrap {
    display: none;
}

.updateinfo__tree-container.active .updateinfo__tree-wrap, .updateinfo__tree-container .updateinfo__tree-wrap-hide {
    display: block;
}

.updateinfo__tree-collapse {
    position: relative;
}

.active .updateinfo__tree-toggle::before {
    content: '\e945';
    font-family: 'soremoicons';
}

.updateinfo__tree-toggle::before {
    content: '\e944';
    font-family: 'soremoicons';
    font-size: 26px;
    line-height: 26px;
    margin-right: 24px;
    color: var(--color-soremo-blue);
    cursor: pointer;
}

.updateinfo__tree-line {
    height: 175%;
    width: 2px;
    background-color: var(--color-soremo-blue);
    position: absolute;
    top: 25px;
    left: 12px;
}

.active .updateinfo__tree-line {
    height: calc(100% + 22px);
}

.updateinfo__tree-container.active:last-child .updateinfo__tree-line {
    height: calc(100% - 22px);
}

.updateinfo__tree-container:last-child .updateinfo__tree-line {
    height: 0;
}

/* Collape */
.updateinfo__tree-collapse .form-check-collapse {
    width: 46px;
}

.updateinfo__tree-collapse .form-check-collapse .updateinfo__tree-toggle:before {
    position: absolute;
    content: '';
    height: 26px;
    width: 26px;
    background-color: #FFFFFF;
    border-radius: 50%;
    border: 2px solid var(--color-soremo-blue);
}
.updateinfo__tree-collapse .form-check-collapse input {
    opacity: 0;
    width: 0;
    height: 0;
}

.updateinfo__tree-collapse .form-check-collapse input:checked+.updateinfo__tree-toggle:before {
    background-color: var(--color-soremo-blue);
}
/* End collapse */

.updateinfo__tree-container.active .updateinfo__tree-wrap {
    border: 1px solid #D3D3D3;
    border-radius: 16px;
    cursor: pointer;
    width: 100%;
}

.updateinfo__tree-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: relative;
}

.updateinfo__tree-time {
    font-size: 18px;
    line-height: 27px;
}

.updateinfo__tree-container.active .updateinfo__tree-time {
    color: #FFFFFF;
    background-color: var(--color-soremo-deepgray);
    border-radius: 16px 0px;
    padding: 8px 24px;
    max-height: 43px;
    margin-right: 0;
}

.updateinfo__tree-time {
    margin-right: 8px;
}

.updateinfo__tree-header .updateinfo__tree-action {
    padding: 16px 8px 0;
    display: none;
    position: absolute;
    right: 0;
    top: 0;
}

.updateinfo__tree-wrap:hover .updateinfo__tree-action {
    display: block;
}

.btn--edit, .btn--delete  {
    height: 36px;
    width: 36px;
    line-height: 36px;
    margin: 0 8px;
    border-radius: 50%;
    background-color: rgba(167, 168, 169, 0.2);
}

.btn--edit .icon, .btn--delete .icon {
    color: var(--color-soremo-gray);
    font-size: 18px;
}

.updateinfo__tree-body {
    padding: 16px 24px;
}

.updateinfo__tree-content {

}

.updateinfo__tree-status {
    font-size: 12px;
    line-height: 12px;
    color: #FFFFFF;
    padding: 7px 16px;
    border-radius: 4px;
}

.updateinfo__tree-status.tag-gray {
    background-color: var(--color-soremo-deepgray);
    margin: 0 8px;
}

.updateinfo__tree-status.tag-blue {
    margin: 0 8px;
}

.active .updateinfo__tree-status.tag-blue {
    margin: 0;
}

.active .updateinfo__tree-status.tag-gray {
    margin: 0;
}

.updateinfo__tree-list {
    padding-left: 0;
    margin-bottom: 0;
}

.updateinfo__tree-item {
    padding: 8px 0;
}

.updateinfo__tree-item::before {
    content: "\e943";
    font-family: 'soremoicons';
    display: inline-block;
    margin-right: 8px;
}

.updateinfo__tree-item .updateinfo__tree-text, .updateinfo__tree-item span {
    font-size: 14px;
    line-height: 21px;
}

.updateinfo__tree-img {
    display: block;
    padding: 8px;
    max-height: 300px;
    max-width: auto;
}
.updateinfo__tree--update {

}

.updateinfo__tree--new::before {
    color: var(--color-soremo-blue);
}
/*   END CONTAINER   */

/*   MODAL   */
.modal:before {
    display: none;
}
.updateinfo__popup-container {
    top: 0;
    background: rgba(0, 0, 0, 0.8);
}

#deleteInfo .popup-dialog {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
}

.popup-content {
    padding: 24px 28px;
}

.popup-content .popup-header {
    position: relative;
}

.popup-title {
    font-size: 16px;
    line-height: 24px;
    color: #000000;
    margin: 0;
}

.popup-close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0;
    background-color: #FFFFFF;
}

.popup-close .icon--sicon-close {
    font-size: 18px;
    color: var(--color-soremo-gray);
}

.popup-content .popup-body {
    padding-top: 10px;
}

.popup-body .row {
    padding: 13px 0;
}

.popup-rangetime {

}

.form-group {
    margin: 0;
}

.form-group .control-label {
    color: var(--color-soremo-deepgray);
    font-size: 14px;
    line-height: 21px;
    padding: 8px 0;
    margin-bottom: 0;
}

/* Rangetime */
.form-group__input-group {
    position: relative;
}

.form-control.control-datepicker {
    width: 100%;
    padding-right: 35px;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    background-color: #fff;
    font-size: 14px;
    padding: 0 16px;
    height: auto;
    line-height: 43px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-group__append {
    padding: 0 11px;
    position: absolute;
    top: 0;
    right: 0;
    width: auto;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0;
}

.form-group__append i {
    color: #a7a8a9;
    transition: color .15s ease;
}
/* End rangetime */

.popup-container {

}

.popup-wrap {
    border: 1px solid #F0F0F0;
    border-radius: 4px;
    margin: 0 15px 24px;
}

.popup-wheader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 8px;
    border-bottom: 1px solid #F0F0F0;
}

.icon--sicon-toggle-on {
    font-size: 32px;
    /* color: var(--color-soremo-blue); */
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f0f0f0;
    border-radius: 14px;
    transition: .3s;
}

.icon--sicon-toggle-off {
    font-size: 32px;
    color: var(--color-soremo-gray);
}

.popup-wheader .form-check-label {
    display: flex;
    align-items: center;
    margin-bottom: 0;
}

.popup-wheader .form-check-group {
    position: relative;
    display: inline-block;
    width: 33px;
    height: 20px;
    margin: 0 8px;
}

.popup-wheader .form-check-group .switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f0f0f0;
    border-radius: 14px;
    transition: .3s;
}

.popup-wheader .form-check-group .switch-slider:before {
    position: absolute;
    content: '';
    height: 19px;
    width: 19px;
    background-color: #A7A8A9;
    border-radius: 50%;
    transition: .3s;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 10%), 0 1px 2px 0 rgb(0 0 0 / 6%);
}
.popup-wheader .form-check-group input {
    opacity: 0;
    width: 0;
    height: 0;
}

.popup-wheader .form-check-group input:checked + .switch-slider {
    background-color: #e9f9ff;
}

.popup-wheader .form-check-group input:checked+.switch-slider:before {
    background-color: #009ace;
    transform: translateX(13px);
}

.popup-wheader .form-check-group input:focus+.switch-slider {
    box-shadow: none;
}

.popup-wheader .form-check-group input[disabled]~.switch-slider {
    background-color: #f0f0f0;
}

.popup-wheader .form-check-group input[disabled]~.switch-slider:before {
    background-color: #e7e7e7;
}

.popup-wheader .form-check-group input:checked[disabled]~.switch-slider {
    background-color: #f0f0f0;
}

.popup-wheader .form-check-group input:checked[disabled]~.switch-slider:before {
    background-color: #a7a8a9;
}

.popup-wheader .form-check-group input:checked[disabled]~.switch-slider:after {
    border-color: #d3d3d3;
    background: #d3d3d3;
}

.popup-wheader .switch-label {
    margin-left: 16px;
    font-weight: 400;
}


.popup-wbody {
    padding: 8px 16px;
}

.popup-wbody.disable {
    pointer-events: none;
    opacity: 0.4;
}

.popup-wbody-list {

}

.popup-wbody-item {
    display: flex;
    padding: 8px 0;
}

.popup-wbody-img {
    width: 65px;
    position: relative;
}

.popup-wbody-delete-img {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
}

.popup-wbody-delete-img .delete-img {
    font-size: 18px;
    color: #FFF;
}

.popup-wbody-img-insert {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    border: 1px dashed #D3D3D3;
    border-radius: 6px;
    cursor: pointer;
}

.popup-wbody-img-insert .icon {
    font-size: 17px;
    color: var(--color-soremo-gray);
}

.popup-wbody-img .popup-wbody-file {
    display: none;
    visibility: none;
}

/* .popup-wbody-img-show {
    position: relative;
    display: none;
}

.popup-wbody-img-show .icon {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    cursor: pointer;
}

.popup-wbody-img-show .icon::before {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
} */

#showImg {
    background-color: rgba(0, 0, 0, 0.6);
}

.popup-wbody-content {
    height: 100%;
    width: 100%;
}

.popup-wbody-content {
    width: 78%;
    padding: 12px;
    margin: 0 12px;
    border: 1px solid #F0F0F0;
    border-radius: 4px;
}

.popup-wbody-content textarea {
    height: 90%;
    width: 100%;
    font-size: 14px;
    line-height: 21px;
    color: #000000;
    resize: none;
    overflow-y: hidden;
    border: none;
}

.popup-wbody-content textarea:focus {
    outline: none;
}


.popup-wbody-content textarea:hover {
    overflow-y: scroll;
}

.popup-wbody-action {
    width: min-content;
}

.delete-row.icon--sicon-trash, .drag-row.icon--sicon-drag-indicator {
    display: block;
    padding: 10px 0;
    font-size: 18px;
    color: var(--color-soremo-gray);
}

.icon--sicon-trash::before, .icon--sicon-drag-indicator::before {
    cursor: pointer;
}

.popup-wbody-add {
    text-align: center;
}

.popup-wbody-add__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    background-color: rgba(167, 168, 169, 0.1);
    margin: 8px auto;
}

.popup-wbody-add__icon .icon {
    font-size: 20px;
    color: var(--color-soremo-gray);
}

.disable .popup-wbody-content{
    background-color: #F0F0F0;
}

/* Modal delete */
.popup-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 71px;
    width: 71px;
    margin: 16px auto 24px;
    background-color: #E3FFE9;
    border-radius: 50%;
}

.popup-icon .icon {
    font-size: 40px;
    color: #43DA63;
}

.popup-body .popup-heading {
    text-align: center;
    margin-bottom: 0;
}

.popup-body .popup-heading strong {
    font-size: 16px;
    line-height: 24px;
    color: #000000;
}

.popup-text {
    font-size: 13px;
    line-height: 21px;
    color: #53565A;
    padding: 16px 10px;
    text-align: left;
}

.popup-content .popup-footer {
    text-align: right;
    font-size: 13px;
}

.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: 0;
}

.btn {
    line-height: 19px;
    border-radius: 4px;
}

.btn-popup-save {
    padding: 12px 63px;
    color: #FFFFFF;
    background-color: var(--color-soremo-blue);
    margin-bottom: 16px;
}

.btn-popup-save:hover, .btn-popup-save:focus {
    background-color: #0076A5;
    color: #FFFFFF;
}

.btn-popup-close, .btn-popup-close:hover, .btn-popup-close:focus {
    padding: 12px 24px;
    margin: 0 8px;
    color: #FFFFFF;
    background-color: var(--color-soremo-blue);
}

.btn-popup-delete, .btn-popup-delete:hover, .btn-popup-delete:focus {
    padding: 12px 24px;
    margin: 0 8px;
    color: var(--color-soremo-blue);
    background-color: #FFFFFF;
    border: 1px solid var(--color-soremo-blue);
}
/*   END MODAL   */

/* Responsive */
@media (max-width: 992px) {
    .updateinfo__tree-header .updateinfo__tree-action {
        top: 35px;
    }
    .updateinfo__tree-container.active .updateinfo__tree-wrap {
        min-height: 100px;
    }
}

@media (max-width: 600px) {
    .updateinfo__tree-container.active .updateinfo__tree-time {
        padding: 8px;
    }
    .updateinfo__tree-collapse .form-check-collapse {
        width: 35px;
    }
}