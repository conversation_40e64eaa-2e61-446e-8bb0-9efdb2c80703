.messenger__item--not-selected {
    border: 1px solid transparent;
}

.messenger__seen-img {
    border-radius: 50%;
}

.messenger__textarea-file {
    position: absolute;
    top: -2.5em;
    left: 0;
    font-size: 10px;
    line-height: 3;
}

.messenger__textarea-file span {
    max-width: 120px;
    word-break: break-word;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    float: left;
}

.add-offer {
    width: 100%;
    text-align: center;
    margin-top: 20px;
    padding: 60px;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.button-add-offer {
    padding: 10px 50px;
    border: 1px solid grey;
    border-radius: 5px;
    font-size: 8em;
    color: grey;
    transition: all ease .2s;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.button-add-offer:hover {
    color: white;
    background: #1a97b7;
    border: 1px solid #1a97b7;
}

.button-add-offer:active {
    color: white;
    background: #0f81b7;
    border: 1px solid #0f81b7;
}

.messenger__item--selected {
    border: 2px solid #009ace;
}

.messenger__item {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 9px 10px 9px 10px;
    transition: background .6s ease;
    border-radius: 10px;
}

.messenger__item:hover {
    background: rgba(13, 162, 183, 0.15);
}

.messenger-detail__boxes {
    padding-bottom: 20px;
}

.messenger-detail__offer {
    padding-left: 100px;
    padding-right: 100px;
    margin: 20px 10px;
}

.messenger-detail__conditions {
    margin-top: 0;
    padding-top: 10px;
    position: relative;
}

.messenger-detail__status {
    padding: 4px 25px;
    position: absolute;
    right: 40px;
    top: 10px;
    border-radius: 50px;
    color: white;
    font-weight: bold;
}

.messenger-detail__status_new {
    background: red;
}

.messenger-detail__status_process {
    background: #0f9ca9;
}

.messenger-detail__status_done {
    background: #20a96b;
}

.messenger-detail__status_reject {
    background: #494d4c;
}

.messenger-detail__conditions-title {
    height: 100%;
    padding: 4px 25px;
}

.messenger-detail__button {
    display: flex;
    width: 100%;
    background: #a7a8a9;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.messenger-detail__button div {
    font-size: 2em;
    padding: 10px 40px 15px;
    margin: 20px 30px;
    border-radius: 20px;
    width: 50%;
    color: white;
    text-align: center;
}

.messenger-detail__button-reject {
    background: #ff434b;
}

.messenger-detail__button-reject:hover {
    background: #ff0000;
}

.messenger-detail__button-accept {
    background: #009ace;
}

.messenger-detail__button-accept:hover {
    background: #0084a8;
}

.messenger-detail__button-done {
    width: 100%;
    background: #009ace;
}

.messenger-detail__button-done:hover {
    background: #0081a7;
}

#messenger-scene-upload {
    display: none;
}

.messenger__download--right {
    float: right;
    clear: right;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.messenger__download--left {
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
    float: left;
    clear: left;
}

.messenger__textarea-file {
    line-height: 1;
}

.messenger__file_upload_temp {
    position: relative;
}

.messenger__file_upload_temp span {
    font-size: 1.2em;
    position: absolute;
    font-weight: bold;
    left: 25px;
    top: -1px;
    padding: 4px 10px 6px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}


.messenger__file_upload_temp i {
    font-size: 2em;
    position: absolute;
    left: 0;
    top: 0;
}

.messenger__file_upload_temp i:hover {
    color: #189ec2;
}

.messenger-detail__action-input {
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    border-radius: 5px;
}

.messenger-detail__action {
    border: 1px solid rgba(214, 214, 214, 0.3);
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.messenger-detail__button-send {
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
    min-width: 40px;
    padding: 5px 10px;
    border-radius: 10px;
    margin: 2px;
    transition: all .2s;
}

.messenger-detail__button-send a {
    font-weight: bold;
}

.messenger-detail__button-send.button--actived:hover {
    background: #258BCF;
}

.messenger-detail__button-send.button--actived:hover a {
    color: white !important;
}

.button--text.button--disabled {
    color: #b2b2b2;
}

.hidden-message {
    display: none;
}

.popup-loading {
    position: absolute;
    top:0;
    left: 40%;
    height: 20px;
    width: 100px;
    background: red;
    transform: scaleY(1);
}
