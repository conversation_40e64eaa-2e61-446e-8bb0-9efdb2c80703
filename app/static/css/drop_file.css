
.mupload-file {
    border: 1px dashed #d3d3d3 !important;
    border-radius: 8px;
    padding: 24px !important;
    position: relative;
    text-align: center;
}

.mupload--video .mupload-file {
    width: 142px;
    height: 142px;
    border-radius: 50%;
    border: 1px dashed #a7a8a9;
}

.mupload--video .mupload-file .icon--sicon-backup {
    color: #a7a8a9;
    margin-top: 8px;
    font-size: 24px;
}

.mupload--video .mupload-file .mupload-text {
    color: #a7a8a9;
}

.mupload-file.active {
    background-color: #fcfcfc;
}

.mupload-file .icon--sicon-backup {
    font-size: 20px;
    color: #000;
    margin-bottom: 12px;
    display: inline-block;
}

.mupload-file #mupload-label {
    display: inline-block;
    margin-top: 10px;
}

.mupload-file .mupload-text {
    margin-bottom: 8px;
}

.mupload-preview-container:not(.has-file) {
    visibility: hidden;
    height: 0;
}

.mupload-preview-container .collection-item {
    margin-bottom: 12px;
}

.mupload-preview-container .collection-item:last-child {
    margin-bottom: 0;
}

.mupload-preview-container .collection-item-info {
    color: #fff;
    display: flex;
}

.mupload-preview-container .collection-item-action {
    display: flex;
    align-items: center;
}

.mupload-preview-container .progress {
    background-color: transparent;
    border: 1px solid #fff;
    height: 8px;
    overflow: hidden;
    flex: 1;
}

.mupload-preview-container .determinate {
    background-color: #fff;
}

.mupload-preview-container .secondary-content {
    flex: 0 0 30px;
    text-align: right;
}

.mupload-preview-container .secondary-content a {
    color: #fff;
}

.mupload-preview-container span[data-dz-size] {
    display: none;
}

.mupload-preview-container .progress-text {
    margin-left: auto;
    margin-right: 30px;
}

.mupload-preview-container .mupload-previews {
    max-height: 400px;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: 6px;
    padding: 16px;
    margin-top: 18px;
}

.mupload-preview-container .mupload-previews:empty {
    padding: 0;
    margin-top: 0;
}

.mupload-preview-container .dz-error-message {
    font-size: 12px;
    color: #2cc84d;
    padding-right: 30px;
    text-align: left;
}

.mupload-preview-container .mupload-file-list {
    margin-top: 16px;
}

.mupload-preview-container .mupload-file-list .s-file {
    margin-bottom: 8px;
}

.mupload-drop {
    color: #a7a8a9;
    display: none;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 300px;
    z-index: 1;
    background-color: #e8ebef;
}

.mupload-text {
    color: #a7a8a9;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.mcontent .mattach, .mmessage-component .mattach  {
    z-index: 99;
    position: absolute;
    left: 0;
    right: 0;
    button: 0;
    height: calc(100% - 55px);
    display: none;
}

.mcontent .mattach .mattach-drop, .mmessage-component .mattach .mattach-drop{
    width: 100%;
    border-radius: 12px;
    height: 100%;
    display: none;
}

#modal-edit-offer .mattach, #modal-create-offer .mattach {
    position: relative;
    z-index: 99;
    margin-bottom: 30px;
}

#modal-edit-offer .mattach .mattach-drop, #modal-create-offer .mattach .mattach-drop {
    position: absolute;
    bottom: calc(100% + 15px);
    left: -35px;
    width: 508px;
    max-width: 100vw;
    height: 215px;
    background-color: #fff;
    border-radius: 12px;
}

.mattach .mattach-drop .border{
    position: absolute;
    bottom: -7px;
    width: 16px;
    height: 16px;
    left: 33px;
    transform: rotate(45deg);
    border-bottom: 2px dashed #b9b9b9;
    border-right: 2px dashed #b9b9b9;
    background: #dbdbdb;
    z-index: 99999;
}

.mattach .mattach-text {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px #b9b9b9 dashed;
    background: #c1c1c194;
    border-radius: 10px;
}

.mattach-label {
    display: inline-block;
    color: #a7a8a9;
    font-size: 16px;
}

.mattach .mattach-label:hover {
    cursor: pointer;
    color: #009ace;
}

.mattach-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
}

.mattach-preview-container .mcommment-file {
    display: inline-flex;
    align-items: center;
    background-color: #a7a8a9;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    padding: 0 8px;
    position: relative;
}

.mattach-preview-container .mcommment-file__name {
    z-index: 1;
}

.mattach-preview-container .mcommment-file__delete {
    margin-left: 12px;
    z-index: 1;
}

.mattach-preview-container .mcommment-file__delete:hover {
    cursor: pointer;
    color: #fff;
}

.mattach-preview-container .dz-error-message {
    font-size: 12px;
    color: #2cc84d;
    text-align: left;
    position: absolute;
    bottom: calc(100% + 12px);
    left: 0;
    background-color: #000;
    border-radius: 6px;
    padding: 6px 8px;
}

.mattach-preview-container .dz-error-message:after {
    content: '';
    border-top-width: 10px;
    border-top-style: solid;
    left: 50%;
    color: #000;
    position: absolute;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    top: 100%;
    transform: translateX(-50%);
}

.mattach-preview-container .dz-error-message span {
    word-break: break-word;
}

.mattach-preview-container .collection-item {
    font-size: 12px;
    display: inline-flex;
    margin: 4px;
}

.mattach-preview-container .collection-item:last-child {
    margin-bottom: 0;
}

.mattach-preview-container .collection-item-info {
    color: #fff;
    display: flex;
}

.mattach-preview-container .collection-item-action {
    display: flex;
    align-items: center;
}

.mattach-preview-container .progress {
    background-color: transparent;
    border: 1px solid #fff;
    height: 6px;
    overflow: hidden;
    flex: 1;
}

.mattach-preview-container .determinate {
    background-color: #53565a;
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
}

.mattach-preview-container .secondary-content {
    flex: 0 0 30px;
    text-align: right;
}

.mattach-preview-container .secondary-content a {
    color: #fff;
}

.mattach-preview-container span[data-dz-size] {
    display: none;
}

.mattach-preview-container .progress-text {
    margin-left: auto;
    margin-right: 30px;
}

.mattach-preview-container .mattach-previews {
    margin: 4px 12px 0 12px;
    max-height: 300px;
    overflow-y: scroll;
}

@media (max-width: 992px) {
    .mattach-preview-container .mattach-previews {
        max-height: 200px;
    }
}

.mattach-preview-container .mattach-previews:empty {
    margin: 0;
}

.mattach-preview-container .mattach-file-list {
    margin-top: 16px;
}

.mattach-preview-container .mattach-file-list .s-file {
    margin-bottom: 8px;
}


.dz-preview .dz-success-mark, .dz-preview .dz-error-mark {
    pointer-events: none;
    opacity: 0;
    z-index: 500;
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    margin-left: -27px;
    margin-top: -27px;
}

.dz-preview .dz-details {
    z-index: 20;
    left: 0;
    font-size: 13px;
    min-width: 100%;
    max-width: 100%;
    padding: 2em 1em;
    text-align: center;
    color: rgba(0, 0, 0, 0.9);
    line-height: 150%;
    background: linear-gradient(to bottom, #eee, #ddd);
    border-radius: 20px;
}

.account__file {
    position: relative;
    max-width: 170px;
    display: flex;
    align-items: center;
    padding: 8px 25px 8px 16px;
    border-radius: 6px;
    margin: 8px 0;
    background-color: #F0F0F0;
}

.account__file-name {
    font-size: 11px;
    line-height: 17px;
    display: block;
    margin: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--black1-color);
    max-width: 100px;
    font-weight: 400;
}
