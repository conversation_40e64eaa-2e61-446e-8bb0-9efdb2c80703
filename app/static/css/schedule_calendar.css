

/* body #schedulaCalendarModal {
    background-color: unset !important;
} */
.sc-block {
    position: absolute;
    margin: 8px 0 0 0;
    border-radius: 12px 12px 12px 12px;
    background: rgba(255, 255, 255, 0.84);
    padding: 16px 8px 32px;
    width: clamp(332px, 19.1vw, 640px);
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
    right: 8px;
    top: 0;
    box-sizing: border-box;
    backdrop-filter: blur(13px);
    z-index: 1;
}

/* .sc-block .sc-content {
    height: 100%;
} */

.schedule-modal.d-none-schedule .sc-block {
    transform: translateY(-150%);
    transition: transform 0.5s ease;
    animation: slideDown 0.5s forwards;
}

.schedule-modal:not(.d-none-schedule) .sc-block {
    transform: translateY(0);
    transition: transform 0.5s ease;
    animation: slideDown 0.5s forwards;
}

@keyframes slideDown {
    0% {
        top: -100%;
        opacity: 0;
    }
    100% {
        top: 0;
        opacity: 1;
    }
}

body #schedulaCalendarModal .sc-block .sc-content .sc-body {
    padding: 0;
    overflow-y: hidden;
    /* height: 100%; */
    /* max-height: 100dvh; */
}

.sc-content .sc-title {
    display: flex;
    padding: 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    border-radius: 4px;
    background: #009ACE;
    box-shadow: 2px 5px 8px 0 rgba(0, 154, 206, 0.10);
    height: 50px;
}

/* .sc-content .sc-title-txt {
    color: #FFF;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
} */

.sc-content .calendar-content {
    display: flex;
    max-width: 100%;
    height: auto;
    padding: 16px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    overflow-y: hidden;
    max-height: 640px;
    top: 2px;
    margin-top: 8px;
}

.line-border {
    display: flex;
    width: 100%;
    height: 1px;
    background: #F0F0F0;
    /* margin-top: 8px; */
}

/*.sc-content .calendar-scene-block {*/
/*    overflow: auto;*/
/*}*/

.icon-next-to-detail {
    color: #f0f0f0;
    visibility: hidden;
}

.calendar-scene-item {
    /*margin-top: 4px;*/
    display: flex;
    align-items: center;
    height: 32px;
    gap: 8px;
    padding: 2px 0;
}

.calendar-scene-item:hover, .user-info-item:hover {
    cursor: pointer;
    background-color: #fcfcfc;
}

.calendar-scene-item.item-new .calendar-scene-thumbnail {
    background: #009ace;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #fff;
    box-shadow: 2px 4px 10px 0px #E5E5E5;
    border-radius: 4px;
    width: 42px;
    aspect-ratio: 16/9;
}

.calendar-scene-item.item-new .calendar-scene-thumbnail .txt-thumbnail {
    color: #FFF;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
}


.calendar-scene-item .calendar-scene-thumbnail {
    min-height: 23.63px;
    width: 42px;
    aspect-ratio: 16 / 9;
    background: #f0f0f0;
    border-radius: 4px;
    box-shadow: 2px 4px 10px 0 #E5E5E5;
    display: flex;
    align-items: center;
}

.calendar-scene-item .calendar-scene-thumbnail img {
    aspect-ratio: 16 / 9;
    object-fit: cover;
    border: 1px solid #fff;
    width: 100%;
    height: 100%;
}

/*.calendar-scene-block-item:not(:first-child) {*/
/*    margin-top: 12px;*/
/*}*/

.calendar-scene-block-item .calendar-title-txt {
    color: #a7a8a9;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 200%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
    height: 26px;
    padding-top: 4px;
    padding-bottom: 4px;
}

.calendar-scene-2 .calendar-title-txt {
    border-top: 1px solid #F0F0F0;
    margin-top: 8px;
}

.calendar-scene-item .calendar-scene-name {
    width: 100%;
    /*margin: 0 8px 0 16px;*/
    color: #000;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.calendar-scene-item .calendar-scene-take {
    display: flex;
    padding: 5px 6px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    width: 18px;
    height: 18px;
}

.calendar-scene-item .calendar-scene-take .number-take {
    color: #FFF;
    font-feature-settings: 'palt' on, 'clig' off, 'liga' off;
    font-size: 8px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%;
}

#scheduleCalendar, #scheduleCalendar .table-condensed, #scheduleCalendar thead, #scheduleCalendar tr {
    display: block;
    width: 100%;
    height: 100%;
}
#scheduleCalendar .datepicker-inline{
    width: 100%;
}
#scheduleCalendar .table-condensed {
      flex-direction: column;
}

 #scheduleCalendar thead {
     flex-direction: column;
 }

#scheduleCalendar tbody {
    display: flex;
    flex-direction: column;
    width: 100%;
    /*height: 100%;*/
    /*width: 295px;*/
    /*margin: auto;*/
    padding: 0;
    outline: 0;
    text-align: left;
    transform: translate3d(0px, 0px, 0px);
    height: 264px;
}

#scheduleCalendar tbody tr {
    display: flex;
    justify-content: space-around;
}

#scheduleCalendar .datepicker-switch {
    color: #000;
    text-align: center;
    font-size: 18px;
    /* font-style: normal; */
    /* line-height: 100%; */
    letter-spacing: 2.5px;
    padding: 0px 0px 0px 0px;
    text-transform: uppercase;
}
#scheduleCalendar .prev, #scheduleCalendar .next {
    padding: 0 12px;
}

#scheduleCalendar thead tr .prev, #scheduleCalendar thead tr .next {
    display: inline-block;
}

#scheduleCalendar thead tr .prev:before, #scheduleCalendar thead tr .next:before {
    left: initial;
}

#scheduleCalendar thead tr:has(.datepicker-switch) {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#scheduleCalendar .table-condensed thead tr:has(.dow) {
    margin: 4px auto;
    display: flex;
    flex: 1;
    /*width: 295px;*/
    justify-content: space-around;
    padding-top: 12px;
}

#scheduleCalendar .table-condensed thead tr .dow {
    padding: 0;
    /*width: 24px;*/
    /*height: 24px;*/
    display: inline-block;
    border-bottom: none;
    color: #A7A8A9;
    text-align: center;
    /* font-feature-settings: 'clig' off, 'liga' off; */
    font-size: 11px;
    font-style: normal;
    line-height: 100%;
    flex: 1;
    cursor: initial;
}

#scheduleCalendar .table-condensed thead tr .dow:not(:first-child) {
    /*margin-left: 20px;*/
}

#scheduleCalendar .table-condensed tbody tr:not(:first-child) {
    /*margin-top: 18px;*/
}

#scheduleCalendar .table-condensed tbody tr .day {
    display: inline-block;
    padding: 0;
    width: 14.2857143%;
    max-width: 40px;
    height: 40px;
    border-radius: 50%;
    /*border: 1px solid #A7A8A9;*/
    /*background: #FCFCFC;*/
    color: black;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: clamp(13px,10px + 0.94vw, 16px);
    font-style: normal;
    font-weight: 300;
    line-height: 40px;
    flex-basis: 14.2857143%;
    border: 1px solid transparent;
}

#scheduleCalendar .datepicker .day.disabled.active {
    border: 1px solid #A7A8A9 !important;
}
#scheduleCalendar .table-condensed tbody tr .day.old {
    color: rgba(57, 57, 57, 0.3);
    background: unset;
}
#scheduleCalendar .table-condensed tbody tr .day.old:not(.past-date){
    pointer-events: initial;
    cursor: pointer;
}

#scheduleCalendar .table-condensed tbody tr .day.past-date {
    pointer-events: initial;
    cursor: default;
    background-color: unset;
    color: rgba(57, 57, 57, 0.3) !important;
}
/*#scheduleCalendar .datepicker .day.disabled:not(.old) {*/
/*    color: #A7A8A9 !important;*/
/*    background-color: #F0F0F0 !important;*/
/*}*/

/*#scheduleCalendar .table-condensed tbody tr .day.new, #scheduleCalendar .table-condensed tbody tr .day.old {*/
/*    !*background-color: #FFF;*!*/
/*    !*color: #A7A8A9;*!*/
/*    color: rgba(57, 57, 57, 0.3);*/
/*    background: transparent;*/
/*    border-color: transparent;*/
/*    cursor: default;*/
/*}*/

#scheduleCalendar .table-condensed tbody tr .day.new:hover {
    background-color: #009ACE !important;
    color: #FFF !important;
    border-color:  #009ACE !important;;
}

#scheduleCalendar .table-condensed tbody tr .day.past-date {
    pointer-events: none;
    cursor: initial;
}

#scheduleCalendar .table-condensed tbody tr .day.new:before, #scheduleCalendar .table-condensed tbody tr .day.old:before {
    font-size: 11px;
    content: unset;
}


#scheduleCalendar .table-condensed tbody tr td.day:not(:first-child) {
    /*margin-left: 20px;*/
}

.user-info-item {
    width: 100%;
    height: 32px;
    padding: 0px 0px 0px 18.5px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/*.user-info-block {*/
/*    margin-top: 4px;*/
/*    overflow: auto;*/
/*}*/

/*.user-info-item:not(:first-child) {*/
/*    margin-top: 4px;*/
/*}*/

.user-avatar-block {
    position: relative;
}

.user-avatar-block .user-avatar {
    min-width: 24px;
    min-height: 24px;
    width: 24px;
    height: 24px;
    border-radius: 10px;
    border: 1px solid #fff;
    box-shadow: 2px 4px 10px 0px #E5E5E5;
}

/*.user-avatar-block .user-message {*/
/*    display: flex;*/
/*    padding: 1.25px 2.5px;*/
/*    justify-content: center;*/
/*    align-items: center;*/
/*    position: absolute;*/
/*    right: -3px;*/
/*    bottom: -2.5px;*/
/*    border-radius: 8px;*/
/*    border: 1px solid #FFF;*/
/*    background: var(--soremo-blue, #009ACE);*/
/*}*/

.user-avatar-block .user-message .number-message {
    color: #FFF;
    text-align: center;
    font-feature-settings: 'palt' on, 'clig' off, 'liga' off;
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%;
}

.user-info-item .user-info-content {
    width: 100%;
    height: 22px;
    display: flex;
    max-width: 250px;
}

.user-info-item .user-info-content .user-info-name {
    margin: 0;
    padding-right: 4px;
    color: #000;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
    width: auto;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    display: inline-block;
    max-width: 130px;
}

.user-info-item .user-info-content .user-info-description {
    color: #000;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
    width: auto;
    display: inline-block;
    max-width: 90px;
}

.sc-content-block-2 {
    /*overflow: auto;*/
}

/*}*/

#schedulaCalendarModal .sc-content .datepicker .day.active {
    border: 1px solid #009ACE !important;
    background-color: #009ACE !important;
    /* Card drop shadow Blue */
    color: #FFF !important;
    text-align: center;
    font-style: normal;
    font-weight: 300;
}

#schedulaCalendarModal .sc-content .table-condensed .active.day.disabled-date:hover {
    color: #fff !important;
}

#schedulaCalendarModal .sc-content .table-condensed .day.disabled-date-weekend:not(.old, .new, .active) {
    background-color: #F0F0F0 !important;
    color: #A7A8A9 !important;
}

#schedulaCalendarModal .sc-content .table-condensed .day.disabled-date-weekend:hover {
    background-color: #009ACE !important;
    color: #FFF !important;
    cursor: pointer;
}

.sc-content .datepicker .day.active:before {
    border: none;
    background: none;
}

.sc-content .datepicker .day.active:after {
    content: none !important;
}

.sc-content .datepicker .today.day:not(.active) {
    border: 1px solid #A7A8A9 !important;
}

#schedulaCalendarModal .sc-content .datepicker .today.day:not(.active):hover {
    border-color: #a7a8a9 !important;
    background: #f0f0f0 !important;
    color: black !important;
}

.sc-content .datepicker .day:not(.active, .disabled-date, .disabled, .new):hover {
    cursor: pointer;
    outline: 0;
    background: #009ace !important;
    border-color: #009ace !important;
    color: white !important;
}

.sc-content .datepicker .dow {
    text-transform: uppercase;
}

.sc-content .datepicker .circle2 {
    display: block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #000;
}

.sc-content .datepicker .selected-day {
    background-color: #000;
    border: 1px solid #fff;
    border-radius: 50%
}

.sc-content .datepicker .disabled-date.day:not(.old) {
    color: #A7A8A9 !important;
    background-color: #F0F0F0;
}

.sc-content .datepicker .disabled-date.day:not(.old):hover {
    background-color: #009ACE !important;
    color: #FFF !important;
}

.sc-content .datepicker .disabled.day:not(.disabled-date) {
    /*color: #A7A8A9 !important;*/
    /*background-color: #FFF !important*/
    color: rgba(57, 57, 57, 0.3) !important;
    background: transparent !important;
    border-color: transparent !important;
    cursor: default !important;
}

.sc-content .datepicker .day {
    position: relative;
}

.sc-content .status-two-circle {
    display: flex;
    justify-content: center;
    margin-bottom: 2px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
}

.sc-content .status-two-circle .circle-blue {
    margin-left: 1px;
}

.sc-content .status-two-circle .circle-grey {
    margin-right: 1px;
}

.sc-content .circle-blue,
.circle-grey,
.status_circle {
    flex-shrink: 0;
}

.sc-content .status_circle {
    border-radius: 50%;
    transform: translate(-50%);
    left: 50%;
    bottom: 0;
    position: absolute;
    margin-bottom: 2px;
    margin-top: 0.5px;
}

.sc-content .datepicker .day.today:not(.active) {
    background-color: #FCFCFC;
}

.sc-content .circle_blue {
    fill: #009ACE;
    stroke-width: 1px;
    stroke: #FFF;
}

.sc-content .circle_grey {}

.sc-content .new.day {
    color: rgba(57, 57, 57, 0.3) !important;
    pointer-events: initial;
    background: unset !important;
    border-color: transparent;
}

#schedulaCalendarModal .datepicker-switch {
    font-weight: normal !important;
    pointer-events: none;
    cursor: none;
}

#schedulaCalendarModal .table-condensed thead tr:has(.datepicker-switch) {
    cursor: initial;
}

#schedulaCalendarModal .table-condensed thead tr .prev:not(.disabled),
#schedulaCalendarModal .table-condensed thead tr .next:not(.disabled) {
    cursor: pointer;
}

body.modal-open {
    overflow: auto;
}