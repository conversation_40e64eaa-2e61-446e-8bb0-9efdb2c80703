@import url("https://fonts.googleapis.com/earlyaccess/notosansjapanese.css");

:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
    --font-size-40: 40px;
    --line-height-60: 60px;
    --font-size-32: 32px;
    --line-height-48: 48px;
    --font-size-24: 24px;
    --line-height-36: 36px;
    --font-size-18: 18px;
    --line-height-27: 27px;
    --font-size-16: 16px;
    --line-height-24: 24px;
    --font-size-13: 13px;
    --line-height-20: 20px;
    --font-size-11: 11px;
    --line-height-17: 17px;
    --font-size-8: 8px;
    --line-height-12: 12px;
    --font-weight-300: 300;
    --font-weight-400: 400;
    --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
    --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
}

.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: 0;
}


.btn:active {
    box-shadow: none !important;
}

.btn.btn--primary, .btn.btn--secondary, .btn.btn--tertiary {
    font-family: var(--font-family-R) !important;
    font-size: 13px !important;
    line-height: var(--line-height-20);
    background-image: none;
    border-radius: 4px;
    border: none !important;
    padding: 12px 24px !important;
    cursor: pointer;
}

.btn.btn--tertiary .btn-text {
    font-family: var(--font-family-L) !important;
    background-color: transparent !important;
    background: transparent;
}

.btn-content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.btn.btn--primary.large, .btn.btn--secondary.large, .btn.btn--tertiary.large {
    padding: 20px 40px !important;
    min-width: 224px;
}

.btn.btn--primary.large {
    letter-spacing: 2.5px;
    line-height: 100%;
}

.btn--primary.large.disable .btn-content .icon-tick {
    height: 24px;
    width: 24px;
    background-image: url(../images/icon_tick_gray.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-color: transparent;
}

.btn--primary.large .btn-content .icon-tick {
    height: 24px;
    width: 24px;
    background-image: url(../images/icon_tick_white.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-color: transparent;
    margin-right: 4px;
}

.btn--primary.large.disable .btn-content .icon-send-plane {
    height: 24px;
    width: 24px;
    background-image: url(../images/icon_send_plane_gray.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-color: transparent;
}

.btn--primary.large .btn-content .icon-send-plane {
    height: 24px;
    width: 24px;
    background-image: url(../images/icon_send_plane_white.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-color: transparent;
    margin-right: 4px;
}

.btn--primary.large.disable .btn-content .icon-handshake {
    height: 24px;
    width: 24px;
    background-image: url(../images/icon_handshake_gray.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-color: transparent;
}

.btn--primary.large .btn-content .icon-handshake {
    height: 24px;
    width: 24px;
    background-image: url(../images/icon_handshake_white.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-color: transparent;
    margin-right: 4px;
}

.btn.btn--primary.large .btn-text, .btn.btn--secondary.large .btn-text, .btn.btn--tertiary.large .btn-text{
    font-style: normal;
    font-weight: 400;
    font-size: 24px;
    line-height: 100%;
}

.btn.btn--primary.small, .btn.btn--secondary.medium, .btn.btn--tertiary.medium {
    padding: 12px 24px !important;
    min-width: 192px;
}

.btn.btn--primary.medium .btn-text, .btn.btn--secondary.medium .btn-text, .btn.btn--tertiary.medium .btn-text{
    font-style: normal;
    font-size: 16px;
    line-height: 200%;
    letter-spacing: 2.5px;
    margin-right: 25px;
}

.btn.btn--primary.small, .btn.btn--secondary.small, .btn.btn--tertiary.small {
    padding: 8px 24px !important;
    min-width: 192px;
}

.btn.btn--primary.small .btn-text, .btn.btn--secondary.small .btn-text, .btn.btn--tertiary.small .btn-text{
    font-style: normal;
    font-weight: 300;
    font-size: 13px;
    line-height: 200%;
}


.btn.btn--primary.btn--disabled, .btn.btn--primary:focus.btn--disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #F0F0F0 !important;
}

.btn.component-button.btn--primary, .btn.component-button.btn--primary:focus{
    color: var(--white-color) !important;
    background-color: var(--blue-color) !important;
    min-width: 192px;
}

.btn.component-button.btn--secondary, .btn.component-button.btn--secondary:focus, 
.btn.component-button.btn--tertiary, .btn.component-button.btn--tertiary:focus {
    min-width: 192px;
}

.btn.btn--primary:hover {
    color: var(--white-color) !important;
    background-color: var(--soremo-deep-blue) !important;
}

.btn.btn--secondary, .btn.btn--secondary:focus {
    color: var(--white-color) !important;
    background-color: var(--grey1-color) !important;
}

.btn.btn--secondary:hover {
    color: var(--white-color) !important;
    background-color: var(--blue-color) !important;
}


.btn.btn--tertiary, .btn.btn--tertiary:focus {
    color: var(--black2-color) !important;
    background-color: transparent !important;
}

.btn.btn--tertiary:hover {
    color: var(--blue-color) !important;
    background-color: var(--white-color) !important;
}

.btn.btn--primary.disable, .btn.btn--primary.disabled,
.form-group input.form-control.disabled, .form-group select.form-control.disabled {
    pointer-events: none;
    background-color: var(--soremo-border) !important;
    color: var(--grey1-color) !important;
    background-image: none;
}

.btn.btn--primary i {
    font-size: 20px;
    position: relative;
    margin-left: 0 !important;
}

.btn .icon-end {
    margin-left: 16px !important;
    margin-right: 0px;
}

.btn .icon-start {
    margin-left: 0px !important;
    margin-right: 4px;
    
}

.btn.btn--primary .icon-end.icon:before {
    left: 0 !important;
    transform: translate(-10px, -50%) !important;
}

.btn.btn--primary .icon-start.icon:before {
    right: 4px !important;
    transform: translate(6px, -50%) !important;
}

.btn.btn--primary .icon:before {
    position: absolute;
    top: 50%;
}

.btn.btn--icon-blue {
    color: var(--blue-color);
    min-width: unset;
}

.btn.btn--icon-blue:hover {
    color: var(--soremo-deep-blue);
}

.btn.btn--icon-blue .btn-content {
    flex-direction: column;
}


.btn.btn--icon-blue .btn-content i {
    font-size: 53px;
    margin: 0;
    margin-bottom: 5.3px;
}

.btn.btn--icon-blue .btn-content span {
   
    font-style: normal;
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
}

.btn.btn--icon-blue i:hover {
    color: var(--soremo-deep-blue) !important;
}

.btn.btn--icon-blue .icon--sicon-icon-contract {
    background-color: var(--blue-color);
}

.btn.btn--icon-blue:hover .icon--sicon-icon-contract, .btn.btn--icon-blue i:hover.icon--sicon-icon-contract {
    background-color: var(--soremo-deep-blue) !important;
}

.btn.btn--icon-grey {
    color: var(--grey1-color);
}

.btn.btn--icon-grey:hover {
    color: var(--soremo-deep-blue);
}

.btn.btn--icon-grey .btn-content {
    flex-direction: column;
}


.btn.btn--icon-grey .btn-content i {
    font-size: 53px;
    margin: 0;
    margin-bottom: 5.3px;
}

.btn.btn--icon-grey .btn-content span {
   
    font-style: normal;
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
}

.btn.btn--icon-grey i:hover {
    color: var(--soremo-deep-blue) !important;
}

.btn.btn--icon-grey:hover .icon--sicon-icon-contract, .btn.btn--icon-grey i:hover.icon--sicon-icon-contract {
    background-color: var(--soremo-deep-blue) !important;
}

.icon--sicon-icon-contract {
    background-color: var(--grey1-color);
    mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTM4LjkwNjUgNi45MDY4M0MzNy44OTMyIDUuODkzNSAzNi41MzMyIDUuMzMzNSAzNS4xMTk4IDUuMzMzNUgxNS45OTk4QzEzLjA2NjUgNS4zMzM1IDEwLjY2NjUgNy43MzM1IDEwLjY2NjUgMTAuNjY2OFY1My4zMzM1QzEwLjY2NjUgNTYuMjY2OCAxMy4wMzk4IDU4LjY2NjggMTUuOTczMiA1OC42NjY4SDQ3Ljk5OThDNTAuOTMzMiA1OC42NjY4IDUzLjMzMzIgNTYuMjY2OCA1My4zMzMyIDUzLjMzMzVWMjMuNTQ2OEM1My4zMzMyIDIyLjEzMzUgNTIuNzczMiAyMC43NzM1IDUxLjc1OTggMTkuNzg2OEwzOC45MDY1IDYuOTA2ODNaTTM5Ljk5OTggNDguMDAwMkgyMy45OTk4QzIyLjUzMzIgNDguMDAwMiAyMS4zMzMyIDQ2LjgwMDIgMjEuMzMzMiA0NS4zMzM1QzIxLjMzMzIgNDMuODY2OCAyMi41MzMyIDQyLjY2NjggMjMuOTk5OCA0Mi42NjY4SDM5Ljk5OThDNDEuNDY2NSA0Mi42NjY4IDQyLjY2NjUgNDMuODY2OCA0Mi42NjY1IDQ1LjMzMzVDNDIuNjY2NSA0Ni44MDAyIDQxLjQ2NjUgNDguMDAwMiAzOS45OTk4IDQ4LjAwMDJaTTM5Ljk5OTggMzcuMzMzNUgyMy45OTk4QzIyLjUzMzIgMzcuMzMzNSAyMS4zMzMyIDM2LjEzMzUgMjEuMzMzMiAzNC42NjY4QzIxLjMzMzIgMzMuMjAwMiAyMi41MzMyIDMyLjAwMDIgMjMuOTk5OCAzMi4wMDAySDM5Ljk5OThDNDEuNDY2NSAzMi4wMDAyIDQyLjY2NjUgMzMuMjAwMiA0Mi42NjY1IDM0LjY2NjhDNDIuNjY2NSAzNi4xMzM1IDQxLjQ2NjUgMzcuMzMzNSAzOS45OTk4IDM3LjMzMzVaTTM0LjY2NjUgMjEuMzMzNVY5LjMzMzVMNDkuMzMzMiAyNC4wMDAySDM3LjMzMzJDMzUuODY2NSAyNC4wMDAyIDM0LjY2NjUgMjIuODAwMiAzNC42NjY1IDIxLjMzMzVaIiBmaWxsPSIjQTdBOEE5Ii8+Cjwvc3ZnPgo=");
    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTM4LjkwNjUgNi45MDY4M0MzNy44OTMyIDUuODkzNSAzNi41MzMyIDUuMzMzNSAzNS4xMTk4IDUuMzMzNUgxNS45OTk4QzEzLjA2NjUgNS4zMzM1IDEwLjY2NjUgNy43MzM1IDEwLjY2NjUgMTAuNjY2OFY1My4zMzM1QzEwLjY2NjUgNTYuMjY2OCAxMy4wMzk4IDU4LjY2NjggMTUuOTczMiA1OC42NjY4SDQ3Ljk5OThDNTAuOTMzMiA1OC42NjY4IDUzLjMzMzIgNTYuMjY2OCA1My4zMzMyIDUzLjMzMzVWMjMuNTQ2OEM1My4zMzMyIDIyLjEzMzUgNTIuNzczMiAyMC43NzM1IDUxLjc1OTggMTkuNzg2OEwzOC45MDY1IDYuOTA2ODNaTTM5Ljk5OTggNDguMDAwMkgyMy45OTk4QzIyLjUzMzIgNDguMDAwMiAyMS4zMzMyIDQ2LjgwMDIgMjEuMzMzMiA0NS4zMzM1QzIxLjMzMzIgNDMuODY2OCAyMi41MzMyIDQyLjY2NjggMjMuOTk5OCA0Mi42NjY4SDM5Ljk5OThDNDEuNDY2NSA0Mi42NjY4IDQyLjY2NjUgNDMuODY2OCA0Mi42NjY1IDQ1LjMzMzVDNDIuNjY2NSA0Ni44MDAyIDQxLjQ2NjUgNDguMDAwMiAzOS45OTk4IDQ4LjAwMDJaTTM5Ljk5OTggMzcuMzMzNUgyMy45OTk4QzIyLjUzMzIgMzcuMzMzNSAyMS4zMzMyIDM2LjEzMzUgMjEuMzMzMiAzNC42NjY4QzIxLjMzMzIgMzMuMjAwMiAyMi41MzMyIDMyLjAwMDIgMjMuOTk5OCAzMi4wMDAySDM5Ljk5OThDNDEuNDY2NSAzMi4wMDAyIDQyLjY2NjUgMzMuMjAwMiA0Mi42NjY1IDM0LjY2NjhDNDIuNjY2NSAzNi4xMzM1IDQxLjQ2NjUgMzcuMzMzNSAzOS45OTk4IDM3LjMzMzVaTTM0LjY2NjUgMjEuMzMzNVY5LjMzMzVMNDkuMzMzMiAyNC4wMDAySDM3LjMzMzJDMzUuODY2NSAyNC4wMDAyIDM0LjY2NjUgMjIuODAwMiAzNC42NjY1IDIxLjMzMzVaIiBmaWxsPSIjQTdBOEE5Ii8+Cjwvc3ZnPgo=");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
    height: 64px;
    width: 64px;
}
