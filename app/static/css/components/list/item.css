.item-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.item-main {
    width: calc(100% - 26px);
    position: relative;
    align-items: center;
    padding: 19px 16px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}

.item-main.add-item {
    justify-content: center;
    padding: 8px 16px;
    transition: 0.3s ease;
}

.item-main .add-item-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.item-main.add-item:hover {
    background: #009ace;
}

.item-main.add-item:hover span,
.item-main.add-item.btn--disabled span {
    color: #fff !important;
}

.item-main.add-item.btn--disabled {
    background: #F0F0F0;
}

.item-main.add-item.btn--disabled svg path {
    fill: #fff;
}

.item-button {
    width: 24px;
    display: flex;
    flex-direction: column; 
    justify-content: space-between;
}
 
.item-button .item-button-icon {
    height: 24px;
    width: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: 0.3s ease;
    cursor: pointer;
}
 
.item-button .item-button-icon svg {
    max-height: 100%;
    max-width: 100%;
}

.item-button .item-button-icon:hover svg path {
    fill: #009ace;
}
 
@media (max-aspect-ratio: 4/5) {
    .item-main {
        padding: 10px 16px;
    }
}
