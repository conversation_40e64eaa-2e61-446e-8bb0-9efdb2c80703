table>tbody>tr>td {
    vertical-align: center;
    white-space: nowrap;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    font-weight: 300;
    color: #000000;
    padding: 0 8px;
    margin: 2px;
}

table>thead>tr>th {
    white-space: nowrap;
    padding: 8px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #A7A8A9;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
}

table {
    border-collapse: separate;
    border-spacing: 0 1em;
    width: 85%;
    margin: auto;
}

td {
    border-top: 1px solid #F0F0F0;
    border-bottom: 1px solid #F0F0F0;
    margin: 8px 0;
}

td span {
    white-space: nowrap;
    display: inline-block;
    max-width: 200px;
    min-width: 100px;
    text-overflow: ellipsis;
    overflow-x: hidden;
}

tr td:first-child { 
    border-radius: 4px 0 0 4px;
    border-left: 1px solid #F0F0F0;
    padding-left: 0;
}

tr td:last-child { 
    border-radius: 0 4px 4px 0;
    border-right: 1px solid #F0F0F0;
    padding-right: 24px;
}
tr th:last-child { 
    padding-right: 24px;
}
