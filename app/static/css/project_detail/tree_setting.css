.stree {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tbutton {
    width: 36px;
    height: 36px;
    display: inline-flex;
    border-radius: 50%;
    background-color: rgba(167, 168, 169, 0.2);
    font-size: 20px;
    color: #a7a8a9;
    align-items: center;
    justify-content: center;
}

.tbutton:last-child {
    margin-left: 12px;
}

.tbutton:hover {
    color: #a7a8a9;
}

.tadd-button {
    width: 24px;
    height: 24px;
    background-color: #53565a;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #fff;
}

.tadd-button:hover {
    color: #fff;
}

.tchapter-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
}

.tchapter-content span {
    background-color: #53565a;
    border-radius: 4px;
    color: #fff;
    padding: 8px 24px;
}

.tchapter-content .tchapter-action {
    margin-left: 12px;
    display: flex;
    align-items: center;
}

.tscene-list {
    list-style: none;
    padding-left: 34px;
    margin: 0;
    overflow: hidden;
}

.tscene-item {
    margin-top: 16px;
}

.tscene-item:last-child {
    margin-bottom: 16px;
}

.tscene-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
}

.tscene-content:before {
    content: '';
    position: absolute;
    right: 100%;
    display: block;
    border-bottom: 2px solid #53565a;
    top: 50%;
    width: 16px;
    transform: translateY(-50%);
}

.tscene-content:after {
    content: '';
    position: absolute;
    left: -16px;
    bottom: 50%;
    display: block;
    border-left: 2px solid #53565a;
    height: 10000%;
}

.tscene-content span {
    background-color: #53565a;
    border-radius: 20px;
    color: #fff;
    padding: 8px 24px;
}

.tscene-content .tscene-action {
    margin-left: 12px;
    display: flex;
    align-items: center;
}

.tscene-add {
    display: inline-flex;
    transform: translateX(calc(-50% - 15px));
    position: relative;
    z-index: 9;
    padding: 16px 0;
}

.tscene-add:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: calc(50% + 12px);
    display: block;
    border-left: 2px solid #53565a;
    height: 10000%;
    transform: translateX(-50%);
}

.tscene-add:before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    display: block;
    border-left: 2px solid #53565a;
    height: 16px;
    transform: translateX(-50%);
}

.tvariation-list {
    list-style: none;
    padding-left: 34px;
    margin: 0;
    overflow: hidden;
}

.tvariation-item {
    margin-top: 16px;
}

.tvariation-item:last-child {
    margin-bottom: 16px;
}

.tvariation-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
}

.tvariation-content:before {
    content: '';
    position: absolute;
    right: 100%;
    display: block;
    border-bottom: 2px solid #53565a;
    top: 50%;
    width: 16px;
    transform: translateY(-50%);
}

.tvariation-content:after {
    content: '';
    position: absolute;
    left: -16px;
    bottom: 50%;
    display: block;
    border-left: 2px solid #53565a;
    height: 1000%;
}

.tvariation-content .tvariation-name {
    background-color: #53565a;
    border-radius: 20px;
    color: #fff;
    padding: 8px 24px;
}

.tvariation-content .tvariation-name.editing {
    padding: 0;
}

.tvariation-content .tvariation-name-new {
    border-radius: 20px;
    padding: 7px 23px;
    border: 1px solid #53565a;
    color: #53565a;
}

.tvariation-content .tvariation-name-new:focus {
    outline: none;
}

.tvariation-content .tvariation-action {
    margin-left: 12px;
    display: flex;
    align-items: center;
}

.tvariation-add {
    display: inline-flex;
    transform: translateX(calc(-50% - 15px));
    position: relative;
    z-index: 9;
    padding: 16px 0;
}

.tvariation-add:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: calc(50% + 12px);
    display: block;
    border-left: 2px solid #53565a;
    height: 10000%;
    transform: translateX(-50%);
}

.tvariation-add:before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    display: block;
    border-left: 2px solid #53565a;
    height: 16px;
    transform: translateX(-50%);
}

.tversion-list {
    list-style: none;
    padding-left: 34px;
    margin: 0;
    overflow: hidden;
}

.tversion-item {
    margin-top: 8px;
}

.tversion-item:last-child {
    margin-bottom: 8px;
}

.tversion-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
}

.tversion-content:before {
    content: '';
    position: absolute;
    left: 0;
    display: block;
    border-bottom: 2px solid #53565a;
    top: 50%;
    width: 24px;
    transform: translateY(-50%);
}

.tversion-content:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 50%;
    display: block;
    border-left: 2px solid #53565a;
    height: 1000%;
}

.tversion-content span {
    padding: 5px 0 5px 32px;
}

.tversion-content .tversion-action {
    margin-left: 12px;
    display: flex;
    align-items: center;
}

.tversion-add {
    display: inline-flex;
    transform: translateX(calc(-50% + 1px));
    position: relative;
    z-index: 9;
    padding: 16px 0;
}

.tversion-add:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    display: block;
    border-left: 2px solid #53565a;
    height: 16px;
    transform: translateX(-50%);
}

.tversion-add:before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    display: block;
    border-left: 2px solid #53565a;
    height: 16px;
    transform: translateX(-50%);
}

.tversion-input {
    display: none;
}
