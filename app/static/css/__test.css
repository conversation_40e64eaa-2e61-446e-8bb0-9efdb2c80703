#bg {
    background: linear-gradient(180deg, rgba(249, 255, 186, 0.13) 0%, rgba(255, 255, 255, 0.13) 100%), linear-gradient(0deg, #FFF 0%, #83F0FF 75.44%, #00E0FF 100%);
}
.c-parent-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.c-parent-tags li {
    display: inline-block;
    padding: 0px 0px;
    margin: 0px 4px;

    cursor: pointer;
}

.c-parent-tag--active {
    color: #009ace;
    border-bottom: 2px solid #009ace;
}

.c-child-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.c-child-tags li {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #a7a8a9;
    background-color: #fff;

    cursor: pointer;
}
