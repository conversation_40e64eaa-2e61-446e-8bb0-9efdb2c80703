/* Color, font-size */
:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
}
/* End color, font-size  */

/* Common */
.order-step__form {
    margin: 0;
}

.order-step__form .form-control {
    border-radius: 0 !important;
}

.order-step__content .order-step__form {
    max-width: 100%;
}

.order-step__form-group.form-group {
    margin-bottom: 0;
    padding: 0;
}

.order-step__form-group .form-group:last-child {
    margin-bottom: 24px;
}

.order-step__form-heading {
    margin: 0;
    padding-bottom: 24px;
}

.order-step__form-heading h3 {
    margin: 0;
}

.order-step__form-heading p {
    margin: 8px 0 0;
}

.order-step__heading h3 {
    padding: 40px 0 8px;
    margin: 0;
}

.order-step__heading p {
    margin: 0;
}

.order-step__heading {
    min-height: 64px;
}

.order-step__content {
    border: 1px solid var(--soremo-border);
    border-radius: 12px;
    padding: 32px 17px;
    background-color: var(--white-color);
    margin-bottom: 45px;
}

.order-step__form-tab {
    display: none;
    padding: 0 15px;
}

.order-step__form-tab.active {
    display: block;
}

.form-row .form-group label {
    margin: 0;
}

.order-step__heading-description {
    margin-bottom: 16px;
}
/* End common */

/* Order step 1 */
.order-step__option-wrap {
    padding: 24px 32px;
}

.order-step__option-header {
    text-align: center;
}

.order-step__option-img img {
    width: auto;
    height: auto;
}

.order-step__option {
    margin: 0 -12px 25px;
}

.order-step__option-list {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 0 0 320px;
}

.order-step__option-item {
    padding: 0;
    margin: 0 12px;
    border: 2px solid var(--soremo-border);
    border-radius: 8px;
}

.order-step__option-item.active, .order-step__option-item:hover {
    border: 2px solid var(--blue-color);
    border-radius: 8px;
}

.order-step__option-heading {
    margin-top: 17px;
    max-width: 200px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

.order-step__option-description {
    margin-top: 8px;
    margin-bottom: 24px;
    height: 100px;
    overflow-y: auto;
    word-break: break-word;
    white-space: pre-line !important;
}

.order-step__option-footer {
    text-align: right;
}

.order-step__option-footer .caption--11 {
    margin-left: -20px;
}

.order-step__option-price {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 4px;
}

.order-step__option-price .caption--11 {
    margin-left: 4px;
}

.order-step__option-radio {
    height: 0;
    width: 0;
}
/* End order step 1 */

/* Order step 2 */
.blue-label--8, .grey-label--8 {
    margin-left: 4px;
}

.order-step__form-label {
    margin-bottom: 8px;
}

.order-step__form-budget {
    display: flex;
    align-items: center;
}

.order-step__form-budget input {
    text-align: right;
    width: 70%;
}

.order-step__form-budget span {
    margin-left: 8px;
}

.form-group .btn {
    margin-right: 16px;
    padding: 12px 36px !important;
}

.form-group .btn:last-child {
    margin-right: 0;
}

.order-step__field-text {
    font-size: 11px;
    line-height: 17px;
    color: var(--grey1-color);
    margin-top: 8px;
    margin-bottom: 0;
}

.form-textarea textarea {
    width: 100%;
}
/* End order step 2 */

/* Order step 3 */
.sform-group__input-group {
    padding: 0;
}

.order-step__form-multi {
    display: block;
    padding: 6px 0;
}

.order-step__form-multi .input-radio {
    display: block;
    position: relative;
    padding-left: 27px;
    font-weight: 400;
    line-height: 20px;
    color: var(--black1-color);
    font-size: 13px;
    margin: 12px 0;
}

.order-step__form-multi .input-radio input:checked ~ .check-mark {
    border: 1px solid var(--blue-color);
}

.order-step__form-multi .input-radio .check-mark {
    width: 16px;
    height: 16px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

.order-step__form-multi .input-radio .check-mark:after {
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
}
.input-time {
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    padding: 8px 16px;
    color: var(--black1-color);
    width: 100%;
}

.sform-group__input-group .mcalendar, .sform-group__input-group .sform-group__append {
    cursor: pointer;
}
/* End order step 3 */


/* Button */
.order-step__submit.order-step__action {
    text-align: left;
    margin: 0;
}

.order-step__submit.order-step__action .form-group {
    margin-bottom: 0;
}

.order-step__submit.order-step__action .form-group .button {
    min-width: 100px;
    text-transform: uppercase;
    background-color: var(--blue-color);
    padding: 12px 39px;
    border-radius: 4px;
    border: none;
    color: var(--white-color);
}
/* End button */

/* Drag & drop file */
.order-step__upload label {
    color: white;
}

.order-step__upload {
    margin-top: 10px;
}

.order-step__upload-file {
    display: flex;
    flex-wrap: wrap;
}

.order-step__file {
    position: relative;
    max-width: 170px;
    display: flex;
    align-items: center;
    padding: 8px 25px 8px 16px;
    background-color: var(--soremo-border);
    border-radius: 6px;
    margin: 4px;
}

.order-step__file:first-child {
    margin-left: 0;
}

.order-step__file .icon {
    font-size: 15px;
    color: var(--grey1-color);
}

.order-step__file .icon--sicon-close {
    position: absolute;
    right: 8px;
    cursor: pointer;
}

.mattach-preview-container .mcommment-file {
    background-color: var(--soremo-border);
}

.mattach-preview-container .mcommment-file .determinate {
    background-color: rgba(0, 0, 0, 0.05);
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--black1-color);
    max-width: 100px;
}

.order-step__file-name {
    font-size: 11px;
    line-height: 17px;
    display: block;
    margin: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--black1-color);
    max-width: 100px;
}

.mcommment-file__delete .icon, .mcommment-file__name .icon {
    color: var(--black2-color);
}

.order-step__form-group .form-group .order-step_upload-file #uploadFile {
    width: 100%;
    padding: 24px;
    cursor: pointer;
    background: var(--white-color);
    border: 1px dashed var(--grey2-color);
    border-radius: 6px;
    text-align: center;
    min-height: 53px;
    margin-top: 8px;
}

.order-step__form-group .form-group .order-step_upload-file .dz-button .icon {
    font-size: 20px;
    color: var(--grey1-color);
}

.order-step__form-group .form-group .order-step_upload-file .dz-button p {
    font-size: 13px;
    line-height: 20px;
    color: var(--grey1-color);
    margin-top: 10px;
    margin-bottom: 0;
}

.dropzone .dz-default.dz-message {
    margin: 0;
}

.order-step_upload-file .account_file {
    display: none;
}

.mattach-preview-container .mattach-previews {
    margin: 4px 0 0;
    padding: 0;
    overflow-y: hidden !important;
}

.dropzone.dz-drag-hover {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
    color: white !important;
}

.dropzone.dz-drag-hover .icon, .dropzone.dz-drag-hover p {
    color: white !important;
}

.order-step_upload-file .fallback:hover {
    background-color: #A7A8A9 !important;
    transition: 0.2s;
}

.order-step_upload-file .fallback:hover .icon, .order-step_upload-file .fallback:hover p{
    color: white !important;
}

.upload-final-product-file.upload-button-wrapper {
    z-index: 9999;
    /* background-color: rgba(255, 255, 255, 0.7); */
}
/* End drag & drop */

@media (max-width: 992px) {
    .form-row__mobile {
        display: flex;
    }

    .form-row__mobile .form-row__mobile-date {
        width: 80%;
    }

    .order-step__wrap {
        display: none;
    }

    .order-step__main {
        width: 100%;
    }

    .order-step__content {
        border: none;
        padding: 0;
        margin: 0 -15px;
    }

    .form-textarea textarea {
        width: 100%;
    }

    .order-step__form-group .form-group .order-step_upload-file #myDropZone {
        width: 100%;
    }

    .order-step__submit.order-step__action {
        text-align: center;
    }

    .order-step__action .form-group .button {
        width: auto;
    }

    .order-step__option-item {
        flex: 0 0 250px;
        margin-bottom: 15px;
    }

    .modal-dialog {
        width: auto !important;
        min-width: 70%;
    }

    .order-step__option-wrap {
        padding: 16px;
    }
}

/* Modal */
.popup-confirm .modal-dialog {
    width: 1140px;
}

.popup-content {
    padding: 32px;
    width: 100% !important;
}

.popup-content .popup-header {
    position: relative;
    border: none;
}

.popup-header hr {
    margin-top: 0px;
    margin-bottom: 0px;
}

.popup-title {
    font-size: 13px;
    line-height: 27px;
    font-weight: 400;
    color: var(--black1-color);
    margin: 0 0 24px 0;
}

.popup-body__item {
    padding: 8px 0;
}

.p-0 {
    padding: 0;
}

.popup-text {
    text-align: center;
}

#user-file {
    flex-wrap: wrap;
}

.popup-body__heading {
    padding: 16px 0 8px;
}

.text-title {
    margin-bottom: 4px;
}

.msg-content {
    word-break: break-word;
    white-space: pre-line;
}

.popup-footer {
    text-align: left;
    padding: 34px 0 0;
}

.popup-footer .btn {
    cursor: pointer;
    border-radius: 4px;
}

.popup-footer .btn.btn-popup-send, .popup-footer .btn.btn-popup-send:hover {
    margin-left: 12px;
}
/* End modal confirm */

/* Modal ok */
#modal-create-success .modal-dialog {
    width: 430px !important;
    border-radius: 12px !important;
    position: relative;
    top: 0;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.account__popup-container {
    background: rgba(0, 0, 0, 0.8);
}

#modal-create-success .popup-text {
    text-align: center;
    padding: 0;
}

#modal-create-success .popup-footer {
    text-align: center;
    padding-top: 20px;
}

#modal-create-success .popup-footer .btn {
    margin: 0;
    padding: 12px 66px !important;
}
/* Modal */
