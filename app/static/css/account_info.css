/* :root{
    --soremo-black: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --white-color: #FFFFFF;
    --boder-color: #F0F0F0;
    --blue-color: #009ACE;
    --background-color: #FCFCFC;
} */

* {
    scroll-margin-top: 65px;
  }

.has-success .form-control {
    border-color: #ccc !important;
}

.has-success .control-label {
    color: #333 !important;
}

.has-success .help-block {
    color: #737373 !important;
}

.user-info__notifi ul {
    display: inline-flex;
    list-style: none;
    padding-left: 0px;
}

.user-info__images-wrap {
    display: flex;
    align-items: center;
    margin-top: 32px !important;
}

.user-info__images img {
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.user-info__name-file {
    display: block;
    font-size: 13px;
    font-weight: 300;
    line-height: 20px;
    color: #000;
    margin-bottom: 15px;
}

.user-info__cancel-img {
    margin-left: 24px;
}

.user-info__cancel {
    font-size: 11px;
    line-height: 17px;
    font-weight: 300;
    background: #fff;
    color: var(--black2-color);
    border: 1px solid var(--black2-color);
    border-radius: 4px;
    height: auto;
    padding: 4px 8px;
}

.user-info {
    position: relative;
    margin-top: 65px;
    background-color: var(--background-color);
}

.h3-title {
    font-size: 1.1em;
    color: #1a1a1a;
}

.user-info__upload label {
    color: white;
}

/* .user-info__form .form-control {
    border-radius: 0 !important;
} */

.user-info__form label {
    margin-left: 0;
}

.user-info__upload {
    margin-top: 10px;
}

/* Content tabs */
/* .user-info__heading {
} */

.user-info__heading h3 {
    padding: 40px 0 24px;
    margin: 0;
}

.user-info__content {
    border: 1px solid var(--boder-color);
    border-radius: 12px;
    padding: 0 17px 32px;
    background-color: #fff;
}

.user-info__images {
    width: 120px;
    position: relative;
}

.user-info__images img {
    width: 120px;
    height: 120px;
}

.user-info__upload.half-circle {
    width: 120px;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom-left-radius: 110px;
    border-bottom-right-radius: 110px;
    border-top: 0;
    position: absolute;
    bottom: 0;
    left: 0;
    text-align: center;
}

.user-info__upload .user-info__upload-icon {
    margin: 0;
    margin-top: 10px;
}

.user-info__upload .user-info__upload-icon .icon {
    color: #fff;
    font-size: 32px;
    cursor: pointer;
}

.user-info__content .user-info__form {
    max-width: 100%;
}

.account__form-group.form-group {
    margin-bottom: 0;
}

.account__form-group .form-group {
    margin: 0 -12px 24px;
    padding: 0;
}

.account__form-group .form-group:last-child {
    margin-bottom: 40px;
}

.account__form-group .form-group label {
    margin: 0;
    padding: 0 12px;
}

.account__form-group .form-group label.col-sm-5 {
    padding-right: 38px;
}

.account__field-label {
    display: block;
    min-height: 20px;
    margin-bottom: 4px;
}

.mcalendar-wrap {
    position: relative;
}

.mcalendar-wrap .icon {
    position: absolute;
    top: 39px;
    right: 27px;
    color: var(--soremo-light-gray);
    font-size: 16px;
}

.account__jp-astarisk, .account__jp-astarisk-op {
    margin-left: 8px;
}

.account__field-hint {
    display: block;
    margin-bottom: 8px
}

input:required {
    border: 1px solid #2CC84D;
    border-radius: 4px;
}

.account__form-heading {
    margin: 0;
    padding: 24px 0 16px;
    border-top: 1px solid var(--boder-color);
}

.account__field-description {
    font-size: 11px;
    font-weight: 300;
    line-height: 17px;
    color: #000;
    margin: 4px 0 0;
}

.account__field_input_text {
    font-size: 13px;
    font-weight: 300;
    line-height: 20px;
    color: #000;
}

.acc_action {
    text-align: left;
}

.acc_action .form-group {
    margin-bottom: 0;
}

.acc_action #btn__ok {
    width: auto;
    border-radius: 4px;
}

.acc_delete {
    text-align: right;
    margin-top: 48px;
    padding: 0;
}

.account__form-group .form-group.address-wrap {
    margin-bottom: 0;
    padding-left: 15px;
}
/* End content tabs */

/* Modal delete account */
#deleteAccount.modal:before {
    height: 0;
}

#deleteAccount .modal-dialog {
    width: 370px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    height: 100%;
}

.account__popup-container {
    top: 0;
    background: rgba(0, 0, 0, 0.8);
}

#deleteAccount .popup-title {
    font-size: 16px;
    line-height: 24px;
    color: #000;
    margin: 0;
    text-align: center;
}

.popup-text {
    font-size: 13px;
    line-height: 20px;
    color: var(--black2-color);
    padding: 16px 0;
    margin: 0;
    text-align: left !important;
}

#deleteAccount .popup-content {
    padding: 40px 24px;
}

.popup-content .popup-footer {
    text-align: right;
    font-size: 13px;
    margin-bottom: 16px;
}

.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: 0;
}

.btn {
    line-height: 19px;
    border-radius: 4px;
}

.btn.btn-popup-close, .btn.btn-popup-close:hover, .btn.btn-popup-close:focus {
    padding: 12px 30px !important;
    margin: 0 8px;
}

.btn.btn-popup-delete, .btn.btn-popup-delete:hover, .btn.btn-popup-delete:focus {
    padding: 12px 30px !important;
    margin: 0 8px;
}
/* End modal delete account */

/* Modal reset pass */
.btn.btn-popup-save {
    padding: 12px 45px !important;
}

.popup-pass {
    margin: 0;
    padding: 16px 0;
    width: 100%;
}

.form-group .form-control.account_input-pass {
    border: 1px solid var(--boder-color);
    border-radius: 4px;
    height: 45px;
}
/* End modal reset pass */

.address2.disable {
    pointer-events: none;
}

.address2.disable input {
    background-color: #F0F0F0!important;
    color: #D3D3D3!important;
    border: 0px solid var(--black2-color);
}

.address2.disable span {
    color: #D3D3D3!important;
}

.address2.disable input::-webkit-input-placeholder { /* Edge */
    color: #D3D3D3 !important;
}

.address2.disable input:-ms-input-placeholder { /* Internet Explorer */
    color: #D3D3D3 !important;
}

.address2.disable input::placeholder {
    color: #D3D3D3 !important;
}

/* Datepicker */
.datepicker .table-condensed .dow {
    border-bottom: none;
    color: var(--soremo-light-gray);
}
.datepicker .day.today {
    background-color: #fff !important;
    color: var(--soremo-blue);
}

.datepicker .day.active, .day.active.today {
    z-index: 2;
    color: #fff;
}

.datepicker-dropdown.datepicker-orient-left .day.active:before,
.datepicker-dropdown.datepicker-orient-left .day.active.today:before {
    background-color: var(--soremo-blue);
    border: 2px solid var(--soremo-blue);
    z-index: -1;
    width: 32px;
    height: 32px;
}
/* End datepicker */

@media (max-width: 992px) {
    .form-group label .col-sm-12 {
        padding-right: 0 !important;
    }

    #deleteAccount .modal-dialog {
        width: auto;
    }

    .user-info__heading h3 {
        font-size: 18px;
        line-height: 27px;
        padding: 12px 0;
    }
    .user-info__images {
        margin: auto;
    }
    .acc_delete {
        text-align: center;
    }

    .user-info__images-wrap {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .user-info__cancel-img {
        margin: 0;
        text-align: center;
    }

    .user-info__name-file {
        margin-top: 14px;
    }

    .nav-item {
        margin: 6px 0;
    }

    .user-info__wrap {
        display: none;
    }

    .user-info__main.account__info {
        width: 100%;
    }

    .mcalendar-wrap .icon {
        top: 44px;
        right: 33px;
    }

    .account__form-group .form-group.address-wrap {
        padding: 0 15px;
    }
}

@media (max-width: 739px) {
    .account__form-group .form-group label {
        width: 100%;
        padding-right: 12px !important;
    }
}

.user-info__name-file {
    font-size: 13px;
    font-weight: 300;
    line-height: 20px;
    color: #000;
    margin-bottom: 14px;
    max-width: 200px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

/* Artist */
.account__status {
    margin-left: 8px;
}

.account__status-confirmed {
    color: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
}

.account__status-normal {
    color: var(--black2-color);
    border: 1px solid var(--black2-color);
}

.account__file {
    position: relative;
    max-width: 170px;
    display: flex;
    align-items: center;
    padding: 8px 25px 8px 16px;
    background-color: var(--boder-color);
    border-radius: 6px;
    margin: 8px 0;
}

.account__file .icon {
    font-size: 15px;
    color: var(--soremo-light-gray);
}

.account__file .icon--sicon-close {
    position: absolute;
    right: 8px;
    cursor: pointer;
}

.account__file-name {
    font-size: 11px;
    line-height: 17px;
    display: block;
    margin: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #000;
    max-width: 100px;
}

.account__select-option {
    position: relative;
  }

.account__select-option:after {
    content: '\e920';
    font-family: 'soremoicons';
    color: var(--soremo-light-gray);
    right: 33px;
    bottom: 27px;
    position: absolute;
    pointer-events: none;
}

.account__select-option select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
}

@media (max-width: 992px) {
    .account__field-label {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 10px;
    }

    .account__status {
        margin: 4px;
    }
    
    .nav-item {
        margin: 6px 0;
    }

    .account__form-group .form-group .account_upload-file {
        width: 100% !important;
    }

    .account__form-group .form-group .account_upload-file #myId {
        margin-right: 0 !important;
    }
}

.account__form-group .form-group .account_upload-file {
    width: 100%;
}

.account__form-group .form-group .account_upload-file #myId {
    padding: 26px;
    cursor: pointer;
    background: #fff;
    border: 1px dashed var(--soremo-placeholder);
    border-radius: 6px;
    text-align: center;
    min-height: 53px;
    margin-right: -25px;
}

.account__form-group .form-group .account_upload-file .dz-button .icon {
    font-size: 20px;
    color: var(--soremo-light-gray);
}

.account__form-group .form-group .account_upload-file .dz-button p {
    font-size: 13px;
    line-height: 20px;
    color: var(--black2-color);
    margin-top: 10px;
    margin-bottom: 0;
}

.dropzone.dz-drag-hover {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
    color: white !important;
}

.dropzone.dz-drag-hover .icon, .dropzone.dz-drag-hover p {
    color: white !important;
}

.dropzone .dz-default.dz-message {
    margin: 0;
}

.account_upload-file .fallback:hover {
    background-color: #A7A8A9 !important;
    transition: 0.2s;
}

.account_upload-file .fallback:hover .icon, .account_upload-file .fallback:hover p{
    color: white !important;
}
.account_upload-file .account_file {
    display: none;
}

.dropzone .dz-preview {
    margin-top: -20px;
    margin-bottom: 30px;
}

.dropzone .dz-preview .dz-progress {
    width: 200px;
    position: absolute;
    top: 145px;
    left: 0;
}

.dropzone .dz-preview.dz-image-preview .dz-details {
    height: 100%;
}

.dropzone .dz-preview {
    z-index: 1000;
}

.dropzone .dz-preview .dz-image {
    border: 1px solid var(--soremo-placeholder)
}

.account__form-group .form-group .account_upload-file {
    margin-right: 0;
    display: block;
}

.account__form-group .form-group .account_upload-file #myDropZone {
    width: 100%;
}

.mattach {
    position: relative;
    z-index: 99;
}

.account__form-group .form-group .account_upload-file #myId {
    width: 100%;
    padding: 24px;
    cursor: pointer;
    background: #fff;
    border: 1px dashed var(--soremo-placeholder);
    border-radius: 6px;
    text-align: center;
    min-height: 53px;
    margin-top: 8px;
}

.dropzone .dz-default.dz-message {
    margin: 0;
}

.dropzone .dz-message .dz-button {
    background: none;
    color: inherit;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
}

.account__form-group .form-group .account_upload-file .dz-button .icon {
    font-size: 20px;
    color: var(--black2-color);
}

.mattach-preview-container .mcommment-file .determinate {
    background-color: rgba(0, 0, 0, 0.05);
}

.drag-over {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
}

.mattach-preview-container .mattach-previews {
    overflow-y: hidden !important;
}

.mattach-preview-container .mcommment-file {
    background-color: #F0F0F0 !important;
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
    max-width: 100px;
}

.mcommment-file__delete .icon, .mcommment-file__name .icon {
    color: #53565a;
}

#modal-image-popup .smodal-close, #modal-video-popup .smodal-close {
    top: 0;
}

@media (max-width: 992px){
    #modal-image-popup .smodal-download, #modal-video-popup  .smodal-download, #modal-document-popup .smodal-download {
        left: auto;
        right: 5px;
    }
}

@media (max-width: 992px) {
    .account__form-group .form-group .account_upload-file #myId {
        width: 100%;
    }
}