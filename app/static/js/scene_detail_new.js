$(document).ready(function () {
    let blockContentScene = $('.block-content-scene');
    let heightHeaderPage = $('.header-page').outerHeight();
    let sceneVideo = $('.block-scene-video');
    let percentPixel = blockContentScene.width();
    let windowWidth = $(window).width();
    let windowHeight = $(window).height();
    const maxWidthScreenSP = 767;
    const paddingTopScreen = 32;
    let heightBlockContentScene = windowHeight - (heightHeaderPage + paddingTopScreen + 2);
    if (windowWidth > maxWidthScreenSP) {
        percentPixel = windowWidth * percentMaxWidthPreviewPC;
        sceneVideo.css({
            'max-width': percentPixel + 'px',
            'width': percentPixel + 'px',
            'width': '100%',
        });
         blockContentScene.css({
            'max-height': `${heightBlockContentScene}px`,
            'height': `${heightBlockContentScene}px`,
        });
    } else {
        blockContentScene.css({
            'max-width': windowWidth + 'px',
            'width': windowWidth + 'px',
            'width': '100%',
        });
        $('.cscene--video--new').css({
             'max-width': windowWidth + 'px',
             'width': '100%',
        })
    }

    $('.share-scene-pc .txt-bellow-icon').text('シーンを共有')
    $('.can-share-new-pc .txt-bellow-icon').text('共有中')

    $('.btn-share-scene').off().on('click', function () {
        updateModalInfo($(this), true)
        scene_title_share_setting($(this));
    });

    $('.mcomment-input-text').on('focus', function () {
        $('.owner-top .btn-tutorial-sp').addClass('hidden');
    })
    $(document).mouseup(function (e) {
        let container = $('.mcomment-input-text');

        // if the target of the click isn't the container nor a descendant of the container
        if (!container.is(e.target) && container.has(e.target).length === 0) {
            $('.owner-top .btn-tutorial-sp').removeClass('hidden');
        }
    });
    $('.close-modal-edit-video').on('click', function () {
        $('#modal-take-scene').modal('hide');
    });
    $('.scene-thumbnail-back-new').on('click', function () {
        $('#modal-scene-thumbnail').modal('hide');
    });

    let messageLast = $('.message-list-new .mmessage');
    let showMessageBtn = $('.message-list-new .show-message-btn');
    if (messageLast.length === 0){
        showMessageBtn.css({
            'min-height': '36px'
        })
    }
    setPositionLineHeader($('.line-header'));
    const messageAudioBlock = $('.message-audio-block');
    messageAudioBlock.find('.s-audio.s-audio--audio-wave.s-audio--gray').css('border', 'none')
    const btnClose = $('#folderModal .modal-header .close');
    btnClose.on('click', function () {
        let e = new jQuery.Event("click");
        e.pageX = 10;
        e.pageY = 10;
        $('#folderModal').trigger(e)
    })
    calcPositionDropdownComment();
    if ($('.scene-style').length > 0) {
        calcMoreActionComment($('.scene-style .mmessage'));
    } else if ($('.main-talk-room').length > 0) {
        calcMoreActionComment($('.main-talk-room .mmessage'));
    }

    $('.block-back-scene-detail').on('click', function () {
        const searchParams = new URLSearchParams(window.location.search);
        let project_id = $('.project-item.active').attr('data-project-id');
        let chapter_id = null;
        if (searchParams.has('chapter_id')) {
            chapter_id = searchParams.get('chapter_id');
        } else {
            if ($('.txt-des-above').length > 0) {
                chapter_id = $('.txt-des-above').attr('data-product-scene-id');
            }
        }
        if (chapter_id) {
            window.location.href = `/top/project/${project_id}?tab=progress&chapter_id=${chapter_id}`;
        } else {
            window.location.reload()
        }
    })
});