let invalid_field = [];
let wave_init = false;
let questions = [
    {
        for: '0',
        content: '１分程度のBGM制作に要する制作期間の目安は？',
        answer: ['3日以内', '5日以内', '7日以内'],
        multi: false
    },
    {
        for: '0',
        content: 'JASRAC信託会員ですか？',
        answer: ['はい', 'いいえ'],
        multi: false
    },
    {
        for: '0,3',
        content: '作詞は可能ですか？',
        answer: ['はい', 'いいえ'],
        multi: false
    },
    {
        for: '0',
        content: '演奏可能な楽器は？（複数選択可）',
        answer: ['アコースティックギター',
            'エレクトリックギター',
            'ベース',
            'ドラム',
            '鍵盤楽器',
            '管楽器'],
        multi: true
    },
    {
        for: '0,1,5',
        content: '主なDAWは？（使用可能なDAWは？）（複数選択可）',
        answer: ['Nuendo / Cubase',
            'Studio One',
            'Protools',
            'Live'],
        multi: true
    },
    {
        for: '0,1',
        content: 'よく利用するプラグインを教えてください。（複数選択可）',
        answer: ['IZotope',
            'WAVES',
            'UAD'],
        multi: true
    },
    {
        for: '1',
        content: 'オーディオミドルウェアスキルを教えてください。（複数選択可）',
        answer: ['touch designer',
            'Wwise',
            'Unity',
            'Unreal Engine'],
        multi: true
    },
    {
        for: '0',
        content: 'コンペ情報を希望しますか？',
        answer: ['はい（参加費無償も含む）', 'はい（参加費有償のみ）', 'いいえ'],
        multi: false
    },
    {
        for: '2,3',
        content: 'オーディション情報を希望しますか？',
        answer: ['はい', 'いいえ'],
        multi: false
    },
    {
        for: '3',
        content: 'トップキー',
        answer: ['88(E#6)',
        '87(D#6)',
        '86(D6)',
        '85(C#6)',
        '84(C6)',
        '83(B5)',
        '82(A#5)',
        '81(A5)',
        '80(G#5)',
        '79(G5)',
        '78(F#5)',
        '77(F5)',
        '76(E5)',
        '75(D#5)',
        '74(D5)',
        '73(C#5)',
        '72(C5)',
        '71(B4)',
        '70(A#4)',
        '69(A4)',
        '68(G#4)',
        '67(G4)',
        '66(F#4)',
        '65(F4)',
        '64(E4)',
        '63(D#4)',
        '62(D4)',
        '61(C#4)',
        '60(C4)',
        '59(B3)',
        '58(A#3)',
        '57(A3)',
        '56(G#3)',
        '55(G3)',
        '54(F#3)',
        '53(F3)',
        '52(E3)',
        '51(D#3)',
        '50(D3)',
        '49(C#3)',
        '48(C3)',
        '47(B2)',
        '46(A#2)',
        '45(A2)',
        '44(G#2)',
        '43(G2)',
        '42(F#2)',
        '41(F2)',
        '40(E2)'],
        multi: false
    },
    {
        for: '3',
        content: 'ボトムキー',
        answer: ['88(E#6)',
        '87(D#6)',
        '86(D6)',
        '85(C#6)',
        '84(C6)',
        '83(B5)',
        '82(A#5)',
        '81(A5)',
        '80(G#5)',
        '79(G5)',
        '78(F#5)',
        '77(F5)',
        '76(E5)',
        '75(D#5)',
        '74(D5)',
        '73(C#5)',
        '72(C5)',
        '71(B4)',
        '70(A#4)',
        '69(A4)',
        '68(G#4)',
        '67(G4)',
        '66(F#4)',
        '65(F4)',
        '64(E4)',
        '63(D#4)',
        '62(D4)',
        '61(C#4)',
        '60(C4)',
        '59(B3)',
        '58(A#3)',
        '57(A3)',
        '56(G#3)',
        '55(G3)',
        '54(F#3)',
        '53(F3)',
        '52(E3)',
        '51(D#3)',
        '50(D3)',
        '49(C#3)',
        '48(C3)',
        '47(B2)',
        '46(A#2)',
        '45(A2)',
        '44(G#2)',
        '43(G2)',
        '42(F#2)',
        '41(F2)',
        '40(E2)'],
        multi: false
    },
    {
        for: '6',
        content: '録音環境はお持ちですか？',
        answer: ['はい', 'いいえ'],
        multi: false,
        upload: true
    },
    {
        for: '0,1,3,4',
        content: '得意なジャンルは？（複数選択可）',
        answer: ['J-POP',
            'ROCK',
            'EDM',
            '洋楽',
            'ジャズ',
            'アニソン'],
        multi: true
    },
    {
        for: '0,1,2,3,4',
        content: '提携スタジオの利用を希望されますか？',
        answer: ['はい', 'いいえ'],
        multi: false
    },
    {
        for: '5',
        content: '提携スタジオのハウスエンジニア登録を希望しますか',
        answer: ['はい', 'いいえ'],
        multi: false
    },
    {
        for: '0',
        content: '提携実演家による歌唱・演奏を希望されますか？',
        answer: ['はい', 'いいえ'],
        multi: false
    },
    {
        for: '0',
        content: '仮歌収録のボーカリスト手配は可能ですか？',
        answer: ['はい', 'いいえ'],
        multi: false
    },
    {
        for: '0,1,2,3,4,5',
        content: '現在プロダクションや事務所など、契約を結んでいる他社様はございますか？',
        answer: ['フリーランス', '事務所'],
        multi: false
    },
    {
        for: '0',
        content: '編曲のみのご依頼は可能ですか？',
        answer: ['はい', 'いいえ'],
        multi: false
    }
];
let answer = [];

function slideNext(e, from, to) {
    if (!e.find('.disabled').length) {
        from.removeClass('slide-in-right').addClass('slide-out-left');
        to.removeClass('slide-out-right slide-out-left').addClass('slide-in-right');
    }
}

$(document).ready(function () {
    let questionLoaded = false;
    let maxStep = 1;
    let stepList = $('.join-form__step-list .join-form__step')
    stepList.on('click', function() {
        if($(this).hasClass('complete')) {
            let to = $(this).attr('data-value');
            let from = $('.join-form__steps .current').attr('data-value');
            $('.step-input-info .step-input-' + from).removeClass('slide-in-right').addClass('slide-out-left');
            $('.step-input-info .step-input-' + to).removeClass('slide-out-left').addClass('slide-in-right');
            changeStep(parseInt(from), parseInt(to));
        }
    });

    function changeStep(fromStep, toStep) {
        let step1Btn = $('.join-form__step-list .step1');
        let step2Btn = $('.join-form__step-list .step2');
        let step3Btn = $('.join-form__step-list .step3');
        let title = $('.join-form__step-title')[0];
        stepList.removeClass('current disabled complete');
        switch (toStep) {
            case 1:
                step1Btn.addClass('current');
                title.innerText = 'アカウント情報の登録';
                step2Btn.addClass('complete');
                if(maxStep === 3) {
                    step3Btn.addClass('complete');
                }
                break;
            case 2:
                step2Btn.addClass('current');
                title.innerText = 'プロフィールの登録';
                step1Btn.addClass('complete');
                if(fromStep === 3 || maxStep === 3) {
                    step3Btn.addClass('complete');
                }
                break;
            case 3:
                title.innerText = '利用設定';
                step3Btn.addClass('current');
                step1Btn.addClass('complete');
                step2Btn.addClass('complete');
                break;
        }
    }

    function loadQuestion() {
        questionLoaded = true;
        $('.hidden select#id_role_creator').val($('.step-select-role .active').attr('data-select'));
        let role = $('.step-select-role .active').attr('data-value');
        questions.map(function(q, index) {
            if(q.for.includes(role)) {
                let select = 'sumo-select';
                let multiple = '';
                if(q.multi) {
                    select = 'sumo-select-multi';
                    multiple = 'multiple'
                }
                let option = null;
                q.answer.map(function(a, i) {
                    option += ` <option value="${i}">${a}</option>`
                });

                let dom = `
                    <div class="join-form__form-group">
                        <div class="form-group select-container ${select}">
                            <label>${q.content}</label>
                            <select data-question="${index}" class="select__value-list" ${multiple}>${option}</select>
                        </div>
                    </div>`;
                $('.join-form__qa').append(dom);
            }
        });

        $('.sumo-select select').SumoSelect({
            placeholder: '答えを選択してください。'
        });

        $('.sumo-select-multi select').SumoSelect({
            placeholder: '複数選択可',
            forceCustomRendering: true
        })
        let question_keyboard_dom = $('label:contains(鍵盤楽器)');
        if(question_keyboard_dom.length) {
            let parent_dom = question_keyboard_dom.parent();
            parent_dom.append('<input type="text" name="question_keyboard" id="id_question_keyboard">');
            parent_dom.css('display','flex');
        }
        let question_wind_dom = $('label:contains(管楽器)');
        if(question_wind_dom.length) {
            let parent_dom = question_wind_dom.parent();
            parent_dom.append('<input name="question_keyboard" id="id_question_wind">');
            parent_dom.css('display','flex');
        }
        let question_company_dom = $('option:contains(事務所)').parents('.join-form__form-group');
        if(question_company_dom.length) {
            question_company_dom.append(`<div class="question_company">事務所: <input name="question_company" id="id_question_company" placeholder="事務所名"></div>`);
        }

        $('input#id_question_keyboard, input#id_question_wind').on('click', function(e) {
            e.stopPropagation();
            let cur_option = $(this).closest('.opt:not(.selected)');
            if(cur_option.length && $(this).val().length) {
                cur_option.addClass('selected');
            }
        });

        $('li.opt:contains(事務所), option:contains(事務所), li.opt:contains(フリーランス), option:contains(フリーランス)').parents('div.SumoSelect').on('change', function() {
            if($(this).parent().find('p').attr('title') === ' 事務所') {
                if(!$('.question_company').hasClass('show')) {
                    $('.question_company').addClass('show')
                }
            } else {
                $('.question_company').removeClass('show')
            }
        });

        $('#id_question_keyboard, #id_question_wind').on('keyup', function(e) {
            if($(this).val().length) {
                let cur_option = $(this).closest('.opt:not(.selected)');
                if(cur_option.length) {
                    cur_option.click();

                }
            } else {
                let cur_selected = $(this).closest('.opt.selected');
                if(cur_selected.length) {
                    cur_selected.click()
                }
            }
        });
        let question_sample_dom = $('label:contains(録音環境はお持ちですか？)').next();
        if(question_sample_dom.length) {
            question_sample_dom[0].innerHTML ='<div><label class="text-center" style="width: 100%;"' +
            'for="id_sample_upload" id="upload_label"><span class="btn" style="width: 50%; padding: 5px 0; font-size: 1.2em">UPLOAD</span></label>' +
            '<input type="file" accept=".mp3,.ogg,.wav" class="hidden" name="sample_upload" id="id_sample_upload"></div>';
        }

        $('input#id_sample_upload').on('change', function() {
            if(this.files.length) {
                let f = this.files;
                for(var i = 0; i < f.length; i++) {
                    $(this).closest('.SumoSelect').append('<div class="sample_preview">'+ (i+1) + '. ' + f[i].name +
                        '<span class="sample-remove" data-value="'+ i +'">&times</span></div>')
                    $('.sample-remove').off().on('click', function() {
                         $('input#id_sample_upload')[0].files$(this).attr('data-value')
                    })
                }
            }
        });

        function getAnswer() {
            let answer = [];
            let keyboard = '', wind = '', company = '';
            let keyboard_dom = $("#id_question_keyboard"),
                wind_dom = $("#id_question_wind"),
                company_dom = $("#id_question_company");

            $('.step-input-3 select').each(function() {
                let question = questions[this.dataset['question']];
                let ans = $(this).val();
                answer.push({
                    question: question,
                    answer: ans
                })
            });

            if(keyboard_dom.length || wind_dom.length || company_dom.length)
                keyboard = keyboard_dom.val();
                wind = wind_dom.val();
                company = company_dom.val();

            answer.push({
                keyboard: keyboard,
                wind: wind,
                company: company
            });

            $('input#id_question').val(JSON.stringify(answer));
        }

        function handleCheckedButton(idInputRequest) {
            if($("#id_question_" + idInputRequest).val() == "") {
                $('.step-input-3').find('.join-form__form-group button').addClass('disabled');
                return false;
            } else {
                return true;
            }
        }

        function checkSelectedInnerText(dom, text) {
            return $(dom).parent().find(".selected")[0].innerText == text
        }


        function checkFormFillStatus() {
            let filled = true;
            let step_dom = $('.step-input-3');

            $('.step-input-3 select').each(function() {
                if(this.multiple) {
                    if(!$(this).val().length) {
                        filled = false;
                        step_dom.find('.join-form__form-group button').addClass('disabled');
                        return filled;
                    } else if(checkSelectedInnerText(this, "鍵盤楽器")) {
                        filled = handleCheckedButton("keyboard");
                        return filled;
                    } else if(checkSelectedInnerText(this, "管楽器")) {
                        filled = handleCheckedButton("wind");
                        return filled;
                    }
                } else {
                    if($(this).val() === '') {
                        filled = false;
                        step_dom.find('.join-form__form-group button').addClass('disabled');
                        return filled;
                    } else if(checkSelectedInnerText(this, "事務所")) {
                        filled = handleCheckedButton("company");
                        return filled;
                    }
                }
            });

            if(filled) {
                getAnswer();
                step_dom.find('.join-form__form-group button.disabled').removeClass('disabled');
            }
        }

        $('.step-input-3 textarea, .step-input-3 input').on('keyup', function() {
            checkFormFillStatus();
        });

        $('.step-input-3 select').on('change', function() {
            checkFormFillStatus();
        });
    }

    $('.step-intro .join-form__action').on('click', function () {
        $("html, body").animate({ scrollTop: 0 }, 100);
        slideNext($(this), $('.step-intro'), $('.step-select-role'));
    });

    $('.step-select-role .join-form__action').on('click', function () {
        $("html, body").animate({ scrollTop: 0 }, 100);
        slideNext($(this), $('.step-select-role'), $('.step-input-info'));
        maxStep = 1;
    });

    function formErrorMessage(e) {
        e.parent().find('p.form-error').remove();
        e[0].removeAttribute("style")
        e.parent().append(`<p class="form-error">メールのフォーマットが正しくありません。</p>`);
        e.css('border', '1px solid red');
        e.on('keydown', function() {
            e.parent().find('p.form-error').remove();
            e[0].removeAttribute("style")
        });
    }

    function validateStep1() {
        let email = $('#id_email');
        if(!email[0].checkValidity()) {
            formErrorMessage(email);
        } else {
            $.ajax({
                headers: {
                    'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
                },
                url: 'check_email_register',
                method: 'post',
                data: {
                    'email': email.val()
                },
                success: function(data) {
                    if(data.existed) {
                        formErrorMessage(email);
                        return false;
                    } else {
                        if(!wave_init) {
                            wave_init = true;
                            initWave();
                        }
                        $("html, body").animate({ scrollTop: 0 }, 100);
                        slideNext($('.step-input-1 .join-form__action .join-form__form-group'), $('.step-input-1'), $('.step-input-2'));
                        if(maxStep < 2) {
                            maxStep = 2;
                        }
                        changeStep(1,2)
                    }
                }
            })
        }
    }

    function validateStep2() {
        let validate = true;
        $('.account__profile-social input').each(function() {
            if(!this.checkValidity()) {
                $(this).parent().find('p.form-error').remove();
                $(this)[0].removeAttribute("style")
                $(this).parent().append(`<p class="form-error">URLのフォーマットが正しくありません。</p>`);
                $(this).css('border', '1px solid red');
                $(this).on('keydown', function() {
                    $(this).parent().find('p.form-error').remove();
                    $(this)[0].removeAttribute("style")
                });
                validate = false;
            }
        });
        if(validate) {
            $("html, body").animate({ scrollTop: 0 }, 100);
            slideNext($('.step-input-2 .join-form__action .join-form__form-group'), $('.step-input-2'), $('.step-input-3'));
            changeStep(2,3);
            if(maxStep < 3) {
                maxStep = 3;
            }
            if(!questionLoaded) {
                loadQuestion();
            }
        }
    };

    $('.step-input-1 .join-form__action').on('click', function () {
        validateStep1();
    });

    $('.step-input-2 .join-form__action').on('click', function () {
        validateStep2();

    });

    $("input#id_phone").keypress(function (e) {
        let keyCode = e.which;
        if (keyCode !== 43 && (keyCode < 48 || keyCode > 57)) {
            return false;
        }
    });

    function checkFilledStep(e) {
        if(!e.parents('.step-input-3').length) {
            let filled = true;
            let step_dom = e.closest('[class^=step-input-]');
            step_dom.find('input#id_files, input#id_image, input, textarea').each(function () {
                if (this.required && !$(this).val().length) {
                    filled = false;
                    step_dom.find('.join-form__form-group a').addClass('disabled');
                    return false;
                }
            });
            if(filled) {
                step_dom.find('.join-form__form-group a.disabled').removeClass('disabled');
            }
        }
    }

    $('input, textarea').on('keyup', function () {
        checkFilledStep($(this));
    });

    $('input#id_files, input#id_image').on('change', function() {
        checkFilledStep($(this));
    })

    $('input.dob-datepicker').datepicker({
        startView: 2,
        format: 'yyyy/mm/dd',
        defaultViewDate: {
            year: 1990,
        }
    });

    $('#id_image').attr({accept: 'image/*'});

    $('#id_image').on('change', function(){
        var image_dom = $('.profile__avatar-img');
        if (this.files && this.files[0] && this.files[0].name.match(/\.(jpg|jpeg|png|gif|JPG|PNG|JPEG|GIF)$/)) {
            let reader = new FileReader();
            reader.onload = function(e){
                $('#image').attr('src', e.target.result);
                $('#modalCrop').modal('show');
            };
            reader.readAsDataURL(this.files[0]);
        } else if (this.files.length == 0 ){
            return false;
        } else {
            alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
            $(this).val('').clone(true);
        }
    })

    $("#modalCrop").modal({
        show: false,
        backdrop: 'static'
    });

    var $image = $('#image');
    var cropBoxData;
    var canvasData;
    let event = null;
    $('#modalCrop').on('shown.bs.modal', function () {
        $image.cropper({
            viewMode: 1,
            rotatable: false,
            aspectRatio: 1,
            minCropBoxWidth: 200,
            minCropBoxHeight: 200,
            minContainerHeight: 300,
            ready: function () {
                $image.cropper('setCanvasData', canvasData);
                $image.cropper('setCropBoxData', cropBoxData);
            }
        });
        event = $(document).keypress(function(e){
            if($('.modal').css('display') === 'block') {
                e.preventDefault()
                var code = e.which; // recommended to use e.which, it's normalized across browsers
                if(code === 13) {
                    $('.js-crop-and-upload').click();
                    return false;
                }
            }
        });
    }).on('hidden.bs.modal', function () {
        $(document).unbind('keypress');
        cropBoxData = $image.cropper('getCropBoxData');
        canvasData = $image.cropper('getCanvasData');
        if ($('.profile__avatar-img')[0].src.match('/default-avatar-creator.png')) {
            $('#id_image').val('').clone(true);
        }
    });

    // Enable zoom in button
    $('.js-zoom-in').click(function () {
        $image.cropper('zoom', 0.1);
    });

    // Enable zoom out button
    $('.js-zoom-out').click(function () {
        $image.cropper('zoom', -0.1);
    });

    $('.js-crop-and-upload').click(function () {
        var cropData = $image.cropper("getData");
        var croppedImageDataURL = $image.cropper('getCroppedCanvas', {fillColor: '#fff'}).toDataURL("image/png");
        var image_dom = $('.profile__avatar-img');
        image_dom.attr('src', croppedImageDataURL);

        $('#id_x').val(cropData['x']);
        $('#id_y').val(cropData['y']);
        $('#id_height').val(cropData['height']);
        $('#id_width').val(cropData['width']);
        $image[0].height = cropData['height'];
        $image[0].width = cropData['width'];
        $('#modalCrop').modal('hide');
    });

    let wave_surfer = null;
    let $player = $('.audio-player');
    let links = $player.find('.audio-list__items a');
    let currentTrack = 0;


    function initWave() {
        $('wave').remove();
        if ($('.audio-waveform').length > 0 ) {
            wave_surfer = WaveSurfer.create({
                container: $('.audio-waveform')[0],
                waveColor: '#a7a8a9',
                progressColor: '#009ace',
                cursorColor: 'transparent',
                barWidth: 2,
                barHeight: 1,
                barGap: 4,
                barRadius: 5,
                mediaControls: true,
                height: 50,
            });

            //form on change
            $('input#id_files').on('change', function() {
                if(this.files.length) {
                    $('.audio-list__items').empty();
                    for(var i=0;i<this.files.length;i++) {
                        $('.audio-list__items').append('<a class="audio-list__item" title="' + this.files[i].name +
                            '" href="javascript:void(0)" data-src="' + URL.createObjectURL(this.files[i])+ '"></a>');
                    }
                    links = $('.audio-player').find('.audio-list__items a');
                    setCurrentSong(this.files.length - 1);
                    $('.audio-sample-title').addClass('hide');
                    $('.audio-controls .audio-control').removeClass('hide');
                }
            // Load the track on click
                links.map(function(index, link){
                    $(link).off().on('click', function(e) {
                        e.preventDefault();
                        setCurrentSong(index);
                    });
                })
            });

            // Load a track by index and highlight the corresponding link
            var setCurrentSong = function(index) {
                links[currentTrack].classList.remove('active');
                currentTrack = index;
                links[currentTrack].classList.add('active');
                let dataTitle = links[currentTrack].title;
                if(dataTitle.length >=  10){
                    $('.audio-list__title').prop("title", dataTitle)
                    dataTitle = dataTitle.substring(0, 10).concat('...');
                }
                $player.find('.audio-list__title').text(dataTitle);
                wave_surfer.load(links[currentTrack].dataset['src']);
            };

                // Play on audio load
            wave_surfer.on('ready', function() {
                $player.find('.audio-remain').text( formatTime(wave_surfer.getDuration()) );
                $player.find('.audio-control__playpause').off().on('click', function(){
                    wave_surfer.playPause();
                });

                if ( currentTrack < (links.length - 1) ) {
                    $player.find('.audio-control__next').removeClass('disabled')
                    $player.find('.audio-control__next').off().on('click', function(){
                        setCurrentSong((currentTrack + 1));
                    });
                } else {
                    $player.find('.audio-control__next').off()
                    $player.find('.audio-control__next').addClass('disabled');
                }

                if(currentTrack > 0) {
                    $player.find('.audio-control__prev').removeClass('disabled')
                    $player.find('.audio-control__prev').off().on('click', function(){
                        setCurrentSong((currentTrack - 1));
                    });
                } else {
                    $player.find('.audio-control__prev').off()
                    $player.find('.audio-control__prev').addClass('disabled')
                }
            });

            // Check Playpause button
            wave_surfer.on('pause', function () {
                $player.find('.audio-control__playpause').removeClass('active');
            });

            wave_surfer.on('play', function () {
                $player.find('.audio-control__playpause').addClass('active');
            });

            // Display player time
            wave_surfer.on('audioprocess', function () {
                if ( wave_surfer.getDuration() - wave_surfer.getCurrentTime() > 0 ) {
                    $player.find('.audio-remain').text( formatTime(wave_surfer.getDuration() - wave_surfer.getCurrentTime()) );
                } else {
                    $player.find('.audio-remain').text('0:00' );
                }
            });

            var formatTime = function (time) {
                return [
                    Math.floor((time % 3600) / 60), // minutes
                    ('00' + Math.floor(time % 60)).slice(-2) // seconds
                ].join(':');
            };

            wave_surfer.on('error', function(e) {
                console.warn(e);
            });

            // Load the first track
            if ($('.audio-list__item').length) {
                setCurrentSong(currentTrack);
            }
        }
    }
});
