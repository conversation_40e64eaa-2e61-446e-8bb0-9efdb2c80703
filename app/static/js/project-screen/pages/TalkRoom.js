import submitRequest from "../common/api.js";
import {after_complete_get_list_comment, after_get_list_comment} from "./trigger/talkRoom.js";
import PdProductComment from "../components/container/PdProductComment.js";
import PdProductFile from "../components/container/PdProductFile.js";
import NavigationTopBar from "../components/block/NavigationTopBar.js";
import { addLoadingAnimation } from "../common/utils.js";
let totalPage;
let loadData;
let wsID;
let user_role;
let is_pc_device;
const TalkRoom = {
    render: async (projectPK, callBack) => {
        if (!window.localStorage.getItem("performance") && projectPK) {
            $(".project-item__content.refactor").prepend(
                `<div id="loading_animation" class="loading_animation_container"></div>`
            );
            addLoadingAnimation();
            //call api 1st
            await submitRequest({
                url: "/api/get-list-comment-data",
                method: "GET",
                datatype: "json",
                params: {
                    project_id: projectPK,
                    type: "project",
                },
                callBackOnSuccess: (data) => {
                    user_role = data?.current_user_role;
                    wsID = data?.current_user_id;
                    loadData = data;
                    totalPage = data?.total_page;
                    is_pc_device = data?.is_pc_device;
                    let listProductComment = document.getElementsByClassName(
                        "project-tab-product-comment"
                    );

                    if (listProductComment.length > 0) {
                        let projectItemVideoList =
                            document.getElementsByClassName(
                                "project-item__video-list"
                            );
                        let innerProjectItemVideoList = "";
                        if (projectItemVideoList.length > 0) {
                            innerProjectItemVideoList =
                                projectItemVideoList[0].innerHTML;
                        }

                        let pdComment = PdProductComment({
                                    is_seen: data?.is_seen,
                                    user_role: data?.current_user_role,
                                    comments: data?.comments?.map((comment) => {
                                        return {
                                            ...comment,
                                            ...data?.list_same_role?.find((valueData) => valueData.comment_id ===comment.comment_id),
                                            fileList: data?.comment_files?.map((file) => {
                                                let extraInfo = data?.list_extra_file_info?.find((dataFile) => dataFile?.file_id == file.file_id);
                                                if (file.message_id === comment.comment_id) {
                                                    return {
                                                        ...file,
                                                        ...extraInfo,
                                                    };
                                                } else {
                                                    return false;
                                                }
                                            }).filter(Boolean),
                                    folders: data?.folder?.map((folder) => {
                                        if (folder.message_id === comment.comment_id) {
                                            let fileList = data?.comment_files?.map((file) => {
                                                let extraInfo = data?.list_extra_file_info?.find((dataFile) => dataFile?.file_id == file.file_id);
                                                if (file.folder_id === folder.folder_id) {
                                                    return {
                                                        ...file,
                                                        ...extraInfo,
                                                    };
                                                } else {
                                                    return false;
                                                }
                                            }).filter(Boolean);
                                            return {
                                                ...folder,
                                                children: fileList,
                                            };
                                        } else {
                                            return false;
                                        }
                                    })
                                    .filter(Boolean),
                                    parentComment: {
                                                ...data?.parent_comments?.find(
                                            (valueData) =>
                                                valueData.comment_id ===
                                                comment.parent_id
                                        ),
                                    },
                                };
                            }),
                            projectPk: projectPK,
                            currentUserId: data?.current_user_id,
                            is_pc_device: data?.is_pc_device,
                        });

                        let pdFile = PdProductFile({
                                    current_user_role: data?.current_user_role,
                                    folders: data?.folder ?? [],
                            comment_files:
                                        data?.comment_files?.map((file) => {
                                    let extraInfo =
                                                data?.list_extra_file_info?.find(
                                            (dataFile) =>
                                                dataFile?.file_id ==
                                                file.file_id
                                        );
                                    return {
                                        ...file,
                                        ...extraInfo,
                                    };
                                }) ?? [],
                                    comments: data?.comments?.map((comment) => {
                                return {
                                    ...comment,
                                    ...data?.list_same_role?.find((valueData) =>
                                            valueData.comment_id ===
                                            comment.comment_id
                                        ),
                                    fileList: data?.comment_files?.map((file) => {
                                        let extraInfo =
                                                    data?.list_extra_file_info?.find(
                                                (dataFile) =>
                                                    dataFile?.file_id ==
                                                    file.file_id
                                            );
                                        if (file.message_id === comment.comment_id) {
                                            return {...file, ...extraInfo,};
                                        } else {
                                            return false;
                                        }
                                    })
                                    .filter(Boolean),
                                    folders: data?.folder.filter((folder) => folder.message_id === comment.comment_id),
                                    parentComment: {
                                                ...data?.parent_comments?.find(
                                            (valueData) =>
                                                valueData.comment_id ===
                                                comment.comment_id
                                        ),
                                    },
                                };
                            }),
                            projectPk: projectPK,
                            currentUserId: data?.current_user_id,
                            is_pc_device: data?.is_pc_device,
                        });

                        listProductComment[0].innerHTML = `
                            ${NavigationTopBar({
                                        user_role: data?.current_user_role,
                                offer_status: "",
                                        current_user_role: data?.current_user_role
                            })}
                            <div class="project-item__video-list">
                                ${innerProjectItemVideoList}
                            </div>
                            <div class="pd-section pd-section--detail pd-product-comment show-comment-unresolved" data-product-id="${projectPK}">
                                ${pdComment}
                                ${pdFile}
                        </div>`;
                    }
                },
            });
        }
    },
    after_render: async (projectPK) => {
        if (!window.localStorage.getItem("performance")) {
            await after_get_list_comment({totalPage, loadData, wsID, user_role, is_pc_device});
            await after_complete_get_list_comment();
            $('#navigation-top-bar').addClass('hide');
            $('.switch-talk-room.text-right').on('click', function () {
                if (!$('.switch-talk-room.text-right').hasClass('navbar-active')){
                    $('.pd-product-comment').removeClass('show-comment-unresolved').addClass('show-comment-all')
                    $('.switch-talk-room.text-left').removeClass('navbar-active');
                    $('.switch-talk-room.text-right').addClass('navbar-active');
                }
            });

            $('.switch-talk-room.text-left').on('click', function () {
                if (!$('.switch-talk-room.text-left').hasClass('navbar-active')){
                    $('.pd-product-comment').removeClass('show-comment-all').addClass('show-comment-unresolved')
                    $('.switch-talk-room.text-right').removeClass('navbar-active');
                    $('.switch-talk-room.text-left').addClass('navbar-active');
                }
            });
        }
    },
};

export default TalkRoom;
