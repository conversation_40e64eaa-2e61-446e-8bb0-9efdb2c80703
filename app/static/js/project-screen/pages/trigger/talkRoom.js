import {
    seenComment,
    initmCustomScrollbar,
    sScrollbarBottom,
    initProjectComment,
    commentInput,
    getDownloadAudio,
    createMessage,
    resolveComment,
    newWavesurferInit,
    removeDuplicatedDate,
	scrollProductComment,
	scrollListComment,
	autoLoadMore,
    initSocket,
    messengerActionEdit,
    editMessage,
    clickBtnActionMessage,
    calcMoreActionComment,
    calcPositionDropdownComment2,
    projectToggleResolved,
    reloadLinkAudio,
    getDownloadFileScene
} from "../../common/utils.js";
const max_width_resize_sp = 695;

export const after_get_list_comment = async ({totalPage, loadData, wsID, user_role: userRole, is_pc_device: is_pc}) => {
    let target = $(".project-tab.project-tab-product-comment");

    if (!$(".mcomment-top").hasClass("comment-top-area")) {
        $(".mcomment-top").hide();
    }

    $(".load-more-loading").remove();
    $('.loading_animation_container').remove();
    reloadLinkAudio();
    // $(".pd-file-heading").trigger("click");
    // $(".pd-file-heading").trigger("click");

    initSocket(wsID, userRole, is_pc)
    seenComment("project");
    initmCustomScrollbar();
    sScrollbarBottom();
    initProjectComment(target);
    commentInput();
    getDownloadAudio();
    createMessage(target);
    editMessage(target);
    resolveComment(target);
    projectToggleResolved();
    getDownloadFileScene();

    newWavesurferInit();
    // projectToggleResolved();

    target.on("click", ".mcomment-bottom", function () {
        seenComment("project");
    });

    function updateWindowSize() {
      var windowWidth = window.innerWidth;
      if (windowWidth > max_width_resize_sp) {
        //$(".switch-talkroom").css("margin-left", "-17.5rem");
      }
    }

    window.addEventListener("resize", updateWindowSize);
    updateWindowSize();
    
    $(".mmessage").last().addClass("load-lasted-message");
    scrollProductComment({totalProductComment: parseInt(totalPage), oldData: loadData});
    autoLoadMore({oldData: loadData});
    scrollListComment($(".mmessage-list"));
};
export const after_complete_get_list_comment = async () => {
    scrollCommentBar();
    let messages = $(".prdt .new-video-menu .mmessage");
    if (messages.length > 0) {
        calcMoreActionComment(messages);
    }
    calcPositionDropdownComment2();
    clickBtnActionMessage();
    calcMoreActionComment($(".prdt .mmessage"));
    messengerActionEdit();
};
