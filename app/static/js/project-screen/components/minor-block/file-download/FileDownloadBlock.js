import { PROJECT_DEFAULT_FILE_HOST } from "../../../constant/index.js";

const FileDownloadBlock = ({ file }) => {
	switch (file?.type_file_name){
		case 'image':
			return `
			<div class="image-preview-comment active-view">
      	<img src="${ PROJECT_DEFAULT_FILE_HOST }/${file?.file}" alt="${file?.real_name}" loading="lazy">
      </div>`
		case 'video':
			return `
			<div class="block-video-cmt" style="width: 100%">
        <video height="144px" preload="${ PROJECT_DEFAULT_FILE_HOST }/${file?.file}" alt="${file?.real_name}" loading="lazy">
          <source src="${ PROJECT_DEFAULT_FILE_HOST }/${file?.file}" alt="${file?.real_name}" type="video/mp4"/>
        </video>
      </div>`
		case 'document':
			let fileInfo = {};
			let parsed = false;
			
			try {
				fileInfo = JSON.parse(file?.file_info);
				parsed = true;
			}catch(e){
				//data is not JSON
			}
			return `
			<div class="block-pdf-image">
				${parsed ? `<img src="${ PROJECT_DEFAULT_FILE_HOST }/${file?.file}" class="pdf-image" alt="pdf image">` : ''}
      </div>`
		default:
			return '';
	}
}
	
export default FileDownloadBlock;
