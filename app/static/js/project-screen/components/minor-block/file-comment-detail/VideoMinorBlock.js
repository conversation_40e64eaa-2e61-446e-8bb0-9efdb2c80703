import { PROJECT_DEFAULT_FILE_HOST } from "../../../constant/index.js";
import UserDownloaded from "../file-info/UserDownloaded.js";

const VideoMinorBlock = ({
  fileInfo,
  file,
  version,
  is_pc_device
}) => {
  let dataFileInfo = fileInfo;
  let parsed = false;

  if (Object.keys(dataFileInfo).length > 0){
    parsed = true;
  }else{
    try {
      dataFileInfo = JSON.parse(file?.file_info);
      parsed = true;
    }catch(e){
      //data is not JSON
    }
  }

  if (version == 2){
    const videoUrl = `${PROJECT_DEFAULT_FILE_HOST}/${file?.file}`;
    return `
    <div class="block-video-cmt" style="width: 100%">
      <video height="144px" preload="metadata" loading="lazy"
             data-video-src="${videoUrl}"
             data-is-hls="false"
             data-fallback-src="${videoUrl}">
        <source src="${videoUrl}" type="video/mp4"/>
      </video>
    </div>
    <div class="comment-file-content ">
      <p style="word-break: break-all;" class="file-name">${file?.real_name}</p>
      <div class="action-right-video">
        ${["1","2","3"].includes(file?.acr_status) ? file?.acr_status == '3' ? `
        <a href="javascript:void(0);" class="acr-result-icon btn-finger-print active" data-file-id="${ file?.file_id }">
          <img src="/static/images/scene-detail/icon-finger-print-active.svg" class="" alt="finger print">
        </a>
        ` : `
        <a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
          <img src="/static/images/scene-detail/icon-finger-print.svg" class="" alt="finger print">
        </a>
        ` : ``}
        <a href="javascript:void(0);" class="btn-download-file">
          <span class="material-symbols-rounded scene-file-download">
						download
					</span>
        </a>
      </div>
    </div>
    <div class="info-message-video info-message-file">
      <div class="size-file-message">
        ${parsed ? `
        <span>${ dataFileInfo?.width } x ${ dataFileInfo?.height }px </span>
        <span>${ dataFileInfo?.fps }fps</span>` : ''}
      </div>
      <div class="user-download-file">
        <div class="has_user_downloaded">
          ${UserDownloaded({
            file,
            type_comment: 'messenger',
            is_pc_device
          })}
        </div>
      </div>
    </div>`
  }else{
    return `
    <div class="size-file-message">
      ${parsed ? `
      <span>${ dataFileInfo?.width } x ${ dataFileInfo?.height }px </span>
      <span>${ dataFileInfo?.fps }fps</span>` : ''}
    </div>`;
  }
};

export default VideoMinorBlock;
