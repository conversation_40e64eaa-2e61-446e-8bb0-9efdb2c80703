import CountUserSeen from "../../block/CountUserSeen.js";
import UserDownloadedAvatar from "./UserDownLoadedAvatar.js";

const UserDownloaded = ({
	file,
	type_comment,
	is_pc_device
}) => {
	let maxLoop = is_pc_device ? 7 : 4;
	let count = 0;
	return `
	<div class="sview-user">
		${file?.list_user_downloaded?.length > 0 ? file?.list_user_downloaded?.map((user_downloaded) => {
			count++;
			if(count < maxLoop) {
				return UserDownloadedAvatar({display_name: user_downloaded?.display_name, avatar: user_downloaded?.avatar})
			}
			return "";
		}).join('') : ""}
		${CountUserSeen({
			user_count: file?.list_user_downloaded?.length ? file?.list_user_downloaded?.length - maxLoop + 1 : 0 - 6
		})}
	</div>
	`
}

export default UserDownloaded;