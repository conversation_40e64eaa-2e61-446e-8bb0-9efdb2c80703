const ItemReceivedBlock = ({
  comment, typeFile
}) => {
  return typeFile === 'audio' ? 
    `<div class="comment-audio-content"> 
        <div class="mmessenger mmessenger--audio-wave mmessenger--gray audio-message-block" data-file-id="{{ file.pk }}">
        <div class="messenger-content">
            <div class="s-audio s-audio--audio-wave s-audio--gray"
            <div class="s-audio-control
                                        {% if type not in 'product, messenger_owner, messenger' %}{% if message.pin_video %}video-pin-time{% endif %}{% endif %}">
                                    <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                            play_circle
                                        </span>
                                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                </div>`
        
    : ""
    };

export default ItemReceivedBlock;