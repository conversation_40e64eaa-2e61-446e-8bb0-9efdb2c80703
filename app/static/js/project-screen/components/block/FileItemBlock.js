import { dayOfWeek } from "../../common/utils.js";
import FileInfoBlock from "../minor-block/file-info/FileInfoBlock.js";
import FolderInfoBlock from "../minor-block/file-info/FolderInfoBlock.js";

const FileItemBlock = ({comments, fileList, folderList, is_pc_device}) => {
    let dateTime = new Date("1900-01-01T00:00:00.000");
	let currentDate = new Date();
    let newListFileAndFolder = [...fileList];
	if(folderList.length > 0){
		const sFolders = folderList.map(folder => {
			const listFile = fileList.filter((file) => file.folder_id === folder.folder_id);
			if(listFile.length > 0){
				listFile.forEach((file) => {newListFileAndFolder = newListFileAndFolder.filter((f) => f.file_id !== file.file_id)});
				return {...folder, children: listFile};
			}
			return folder;
		})
		newListFileAndFolder = [...newListFileAndFolder, ...sFolders];
	}

	newListFileAndFolder = newListFileAndFolder.sort(function (a, b) {
        return new Date(b.modified) - new Date(a.modified);
	})

    return `<div class="tfile">
        <div class="tfile-list">
            <div class="mlast__file"></div>
            ${newListFileAndFolder.map((objectData) => {
                let dataDate = new Date(objectData?.modified);
                let isFirstTime = false;
                let isCurrentDate = false;
                let isFirstHour = false;
                let isDayOfWeek = false;
                if (dataDate.getMonth() === currentDate.getMonth() && dataDate.getDate() === currentDate.getDate()) {
                    isCurrentDate = true;
                }
                if (isCurrentDate && (dataDate.getHours() !== dateTime.getHours() || dataDate.getMinutes() !== dateTime.getMinutes())) {
                    isFirstHour = true;
                }
                
                if((currentDate - dataDate)/(24*3600*1000) < 7){
                    isDayOfWeek = true;
                }
                if ((dateTime?.getMonth() !== dataDate.getMonth() || dataDate.getDate() !== dateTime?.getDate())) {
                    isFirstTime = true;
                }
                dateTime = new Date(objectData?.modified);
                if (objectData?.file_id) {
                    return `
                        <div class="tfile-item">
                            <div class='tfile-item-time ${isFirstTime || (isCurrentDate && isFirstHour) ? "" : "hide"}'>
                                ${isCurrentDate 
                                ? `${new Date(objectData?.modified).getHours().toString().padStart(2, '0')}:${new Date(objectData?.modified).getMinutes().toString().padStart(2, '0')}`
                                : isDayOfWeek ? dayOfWeek[dataDate.getDay()] : `${new Date(objectData?.modified).getMonth() + 1}/${new Date(objectData?.modified).getDate()}`}
                            </div>
                            <div class="tfile-item-content">
                                ${FileInfoBlock({ file: objectData, comments, is_pc_device })}
                            </div>
                        </div>`;
                } else if (objectData?.folder_id) {
                    return `<div class='tfile-item-time ${isFirstTime || (isCurrentDate && isFirstHour) ? "" : "hide"}'>
                                ${isCurrentDate 
                                ? `${new Date(objectData?.modified).getHours().toString().padStart(2, '0')}:${new Date(objectData?.modified).getMinutes().toString().padStart(2, '0')}`
                                : isDayOfWeek ? dayOfWeek[dataDate.getDay()] : `${new Date(objectData?.modified).getMonth() + 1}/${new Date(objectData?.modified).getDate()}`}
                            </div>
                            <div class="tfile-item-content">
                                <div class="tfile-file">
                                    ${FolderInfoBlock({ folder: objectData, comments, comment_files: fileList })}
                                </div>
                            </div>`;
                }
            })
            .join("")}
        </div>
    </div>`
    }
export default FileItemBlock;
