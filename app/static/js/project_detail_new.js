$(document).ready(function () {
    let lastScrollTop = 0;
    const grobalHeader = $(".sheader");
    const topAppBar = $('#top-app-bar');
    const resizeHandle = document.querySelector(".resize-handle");


    // window.addEventListener(
    //     "scroll",
    //     () => {
    //         let currentScroll = window.scrollY;
    //
    //         if (currentScroll > lastScrollTop) {
    //             grobalHeader.style.top = "-65px";
    //             topAppBar.style.top = "65px";
    //         } else {
    //             grobalHeader.style.top = "0px";
    //             topAppBar.style.top = "140px";
    //         }
    //         lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
    //     },
    //     false
    // );
    $("#left-sidebar-open").click(function (e) {
        e.preventDefault();
        $("#left-sidebar").toggleClass("toggled");
    });
    $("#left-sidebar-close").click(function (e) {
        e.preventDefault();
        $("#left-sidebar").removeClass("toggled");
    });

    // $('.switch-navbar-item').click(function() {
    // // Xóa class `navbar-active` khỏi tất cả các item
    // $('.switch-navbar-item').removeClass('navbar-active');
    // // Thêm class `navbar-active` vào item được click
    //     $(this).addClass('navbar-active');
    //     let filter_offer = 'waiting';
    //
    //     if ($(this).hasClass('item-not-resolved')) {
    //         filter_offer = 'processing';
    //         $('.pd-product-comment').removeClass('show-comment-unresolved').addClass('show-comment-all')
    //     } else {
    //         $('.pd-product-comment').removeClass('show-comment-all').addClass('show-comment-unresolved')
    //     }
    //      $('.search-delete').trigger('click');
    //     let project_id = $('.project-item.active').attr('data-project-id');
    //     get_messenger_artist(project_id, null, filter_offer);
    // });

    // $(document).on('click', '.block-user-avatar', function (e) {
    //     let $project_item = $(this).parents('.project-item');
    //     let projectId = $project_item[0].dataset.projectId;
    //     $.ajax({
    //         type: "GET",
    //         data: {projectId: projectId},
    //         url: "/top/project_admin_new",
    //         success: function (data) {
    //             console.log('data admin234: ')
    //             console.log(data)
    //             $('#modalUsersInProject').html(data.html);
    //             $('.member-manage__content').attr('data-product', data.product_id);
    //         },
    //         error: function () {
    //             $('.modalUsersInProject').html('');
    //             $('.member-manage__content').attr('data-product', '');
    //         }
    //     });
    // })

        $(document).on('click', '.block-user-avatar', function (e) {
              e.preventDefault();
        let projectId;
        let $modal_manage = $('.modal-users-in-project');
         if ($(this).hasClass('sproject__user-btn')) {
            projectId = $(this).parents('.sprojects-item').attr('data-project');
        } else {
            projectId = $(this).parents('.project-item').attr('data-project-id');
        }
            $.ajax({
                type: "GET",
                data: {projectId: projectId},
                url: "/top/project_members",
                success: function (data) {
                    $modal_manage.html(data.html);
                    $modal_manage.attr('data-product', data.product_id);
                    selectAction();
                    inviteUser();
                },
                error: function () {
                    $modal_manage.html('');
                    $modal_manage.attr('data-product', '');
                },
                complete: function () {
                    let modal_user_project = $('.modal-users-in-project');
                    setTimeout(function () {
                        modal_user_project.removeClass('d-none-el');
                    }, 100)
                }
            });
        });

    $(window).on("resize", function (event) {
       calcWidthAndPositionInputComment()
    });

    function calcWidthAndPositionInputComment() {
        if ($(window).width() > max_width_sp_device) {
            let main_block = $('.pd-section__content.main-talk-room')
            if (main_block.length < 1) {
                main_block = $('.offer-content-message')
            }
            if (main_block.length > 0) {
                let width_main_block = main_block.width();
                let left_main_block = main_block.offset().left;
                let footer_comment_block = $('.footer-comment-block');
                let width_footer_comment_block = footer_comment_block.width();
                let fixedBlockLeft = left_main_block + (width_main_block - width_footer_comment_block) / 2;
                footer_comment_block.css({
                    'width': width_main_block + 'px',
                    'left': fixedBlockLeft + 'px',
                });
            }
        }
    }
})

document.addEventListener('click', function (event) {
    let schedulaCalendarModal = document.getElementById('schedulaCalendarModal');
    let schedulaCalendarContent = document.getElementsByClassName('sc-block');
    let staffModal = document.getElementById('modalUsersInProject');
    let staffModalContent = document.getElementsByClassName('block-users-in-project');
    let clickedElement = event.target;
    let iconSchedule = $('.schedule-project-detail')
    // if (!schedulaCalendarModal.contains(clickedElement) && schedulaCalendarModal !== clickedElement && schedulaCalendarModal !== iconSchedule[0]) {
    //     if ($(schedulaCalendarModal).hasClass('in')) {
    //         $(schedulaCalendarModal).modal('toggle');
    //     }
    // }
     if (!$(schedulaCalendarModal).hasClass('d-none-schedule')){
        if (!schedulaCalendarContent[0].contains(clickedElement) && schedulaCalendarContent[0] !== clickedElement) {
            $(schedulaCalendarModal).addClass('d-none-schedule');
        }
    }
    if (!$(staffModal).hasClass('d-none-el')) {
        if (!staffModalContent[0].contains(clickedElement) && staffModalContent[0] !== clickedElement && !$('body').hasClass('modal-open')) {
            $(staffModal).addClass('d-none-el');
        }
    }
});