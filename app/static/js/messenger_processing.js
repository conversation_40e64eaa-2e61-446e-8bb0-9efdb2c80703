$(document).ready(function () {
    let list_contacts = $('.messenger__item');
    initCustomScrollbar();

    //load message
    list_contacts.on('click', function() {
        let is_opening = false;
        if(is_pc === 'False' && $('.col-md-5.col-sm-5.messenger__column-left').is('.opening')) {
            is_opening = true;
        }

        if (is_opening) {
            $('.col-md-5.col-sm-5.messenger__column-left .messenger__item.hide').removeClass('hide');
            $('.col-md-5.col-sm-5.messenger__column-left').removeClass('opening');
            $('.col-md-7.col-sm-7.messenger__column-right').addClass('hide');
        } else {
            if(is_pc === 'False') {
                $('.col-md-5.col-sm-5.messenger__column-left .messenger__item').addClass('hide');
                $(this).removeClass('hide');
                $('.col-md-5.col-sm-5.messenger__column-left').addClass('opening');
                $('.col-md-7.col-sm-7.messenger__column-right').removeClass('hide');
            }
            let offer_id = $(this).data('offer');
            updateURL(offer_id, $(this));
            loadMessage(offer_id);
        }
    });

    let url_string = window.location.href;
    let url = new URL(url_string);
    let offer_active = url.searchParams.get("offer");

        if (offer_active) {
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/messenger/get_offer",
            async: false,
            data: {
                'offer_id': offer_active,
            },

            success: function (data) {
                if (url_string.includes(data.url)) {
                    $('.messenger__item[data-offer^='+ data.offer_id +']').click();
                } else {
                    window.location.href = data.url;
                    $('.messenger__item[data-offer^='+ data.offer_id +']').click();
                }

                setTimeout(function () {
                    let top_length = $('.messenger__item--selected').offset().top - 400;
                    $(".messenger__list").mCustomScrollbar("scrollTo", top_length);
                }, 1000)

            },
            error: function (data) {
                window.location.href = '/warning/404'
            }
        });
        }

    $(".button-reject-offer").on('click', function(e) {
        e.stopPropagation();
        let offer_id = $(this).parents('.messenger__item').data('offer');

        if (offer_id) {
            $.ajax({
                type: "POST",
                url: '/messenger/update_reject_offer',
                data: {
                    'offer_id': offer_id
                },
                success: function (data) {
                    // toastr.success('完了いたしました!');
                    $('.messenger-accept').remove();
                    $('.offer-infor').append(`<div class="align-center">
                                   <a class="messenger-accept messenger-reject " href="javascript:void(0)">御取引がキャンセルしました</a>
                               </div>`);
                    window.location.href = data.url
                },
                error: function (data) {
                    toastr.error('エラーが発生しました');
                }
            });
        }
    });

});


function loadMessage(offer_id) {
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/messenger/load_offer",
        async: false,
        data: {
            'offer_id': offer_id,
        },
        success: function (data) {
            $(".messenger__column-right").append("<div class='offer-" +offer_id+"'><div class=\"messenger-detail\" ></div></div>");
            let active_offer = $('.offer-'+offer_id);
            active_offer.find(' .messenger-detail').html(data.html);
            initCustomScrollbar();
            $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom');
            if(!data.is_review) {
                $('#modal-vote').modal();
            }

            //send_button
            active_offer.find(' .messenger-detail__input textarea').on('keyup', function () {
                $(this).height('inherit');
                var height =  parseInt(this.scrollHeight)
                    + parseInt($(this).css('border-top-width'))
                    - parseInt($(this).css('padding-top'))
                    + parseInt($(this).css('border-bottom-width'))
                    - parseInt($(this).css('padding-bottom'));
                if ($(this).val().length) {
                    if(height < 38) {
                        height += 20
                    }
                    $(this).parent().find('.messenger-detail__button-send .button--disabled').removeClass('button--disabled');
                    $(this).parents('.messenger-detail__input').height(height + 'px');
                    $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo','bottom');

                } else {
                    $(this).parent().find('.button.button--text.button--text-primary').addClass('button--disabled');
                    $(this).parents('.messenger-detail__input').height('38px');
                }
            });

            //create_message
            $(".messenger__column-right").off('click').on("click", ".messenger-detail__button-send", function () {
                let messageContent = active_offer.find('.messenger-detail__input textarea').val();
                let offer_id = $(this).parents('.messenger-director__list').data('offer');

                let upload_final_elements = active_offer.find('#upload_final_product-'+offer_id);
                if(upload_final_elements.length > 0 && upload_final_elements[0].files.length) {
                    data = new FormData();
                    data.append('message', '');
                    data.append('offer_id', offer_id);
                    data.append('file', active_offer.find('#upload_final_product-'+offer_id)[0].files[0]);
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: '/offer_message/create',
                        beforeSend: function (data) {
                            // toastr.info('アップロード中…');
                            $('.upload-button-wrapper').css('display', 'flex');
                            $('.upload-button-wrapper').addClass('clicked');
                            $('.upload-button-wrapper .fill .process').css('width', '2%');
                        },
                        xhr: function () {
                            let xhr = new window.XMLHttpRequest();
                            xhr.upload.addEventListener("progress", function (evt) {
                                if (evt.lengthComputable) {
                                    let percentComplete = (evt.loaded / evt.total) * 70;
                                    $('.upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                                }
                            }, false);
                            return xhr;
                        },
                        success: function (data) {
                            let current_url = window.location.href;
                            $('.messenger-accept').remove();
                            $('.offer-infor').append(`<div class="align-center">
                                   <a class="messenger-accept" href="javascript:void(0)">検収中</a>
                               </div>`);

                            if (data.url_page && !current_url.includes(data.url_page)) {
                                window.location.href = data.url_page
                            }

                            active_offer.find('.messenger-detail__input textarea').val('');
                            active_offer.find('.messenger-detail__button-send a.button--text-primary').not('.button--disabled').addClass('button--disabled');
                            active_offer.find('.selected_file').html('');
                            active_offer.find('#upload_final_product-' + offer_id).val('');

                            $('.upload-button-wrapper .fill .process').css('width', '100%');
                            setTimeout(function () {
                                // toastr.success('納品データーをアップロードしました。');
                                $('.upload-button-wrapper').removeClass('clicked').addClass('success')
                            }, 1000);
                            setTimeout(function () {
                                $('.upload-button-wrapper').removeClass('success').css('display', 'none');
                                $('.upload-button-wrapper .fill .process').css('width', '0');
                            }, 2000);
                        }
                    });
                    } else if (messageContent !== '') {
                        $.ajax({
                            type: "POST",
                            data: {
                                'message': messageContent,
                                'offer_id': offer_id
                            },
                            url: '/offer_message/create',
                            success: function (data) {
                                active_offer.find('.messenger-detail__input textarea').val('');
                                active_offer.find('.messenger-detail__button-send a.button--text-primary').not('.button--disabled').addClass('button--disabled');
                            }
                        });
                    }
                    active_offer.insertBefore($(".messenger__column-right").children().eq(0));
                    $(window).scrollTop(0);
            });

            // check seen message
            $('.messenger-detail__input textarea').on('click', function () {
                if ($(this).parents('.messenger-director__list').hasClass('not-seen')) {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $.ajax({
                        type: "POST",
                        datatype: "json",
                        url: "/offer_message/update_seen",
                        data: {
                            'offer_id': offer_id,
                        },
                        success: function () {
                            console.log('update seen successful');
                        }
                    })
                }
            });

            //check back
            active_offer.find('.messenger-director__item-action-checkback').off('click').on('click', function () {
                let button_dom = $(this);
               if($(this).find('a').not('.disabled').length) {
                   $(this).find('a').addClass('disabled');
                   bootbox.confirm({
                       message: "このオファーを検収したいでしょうか?",
                       buttons: {
                           confirm: {
                               label: 'はい',
                               className: 'btn-success'
                           },
                           cancel: {
                               label: 'いいえ',
                               className: 'btn-danger'
                           }
                       },
                       callback: function (result) {
                           if (result) {
                               $.ajax({
                                   type: "POST",
                                   data: {
                                       'offer_id': offer_id,
                                       'status': 4
                                   },
                                   url: '/messenger/update_status_offer',
                                   success: function (data) {
                                       if (data.type === 'load') {
                                           window.location.href = data.url;
                                       } else {
                                            $(`<div class="align-center"><a class="messenger-accept" href="javascript:void(0)">検収が完了しました</a></div>`)
                                                .insertAfter(active_offer.find('.messenger-director__item-action-checkback'));
                                            active_offer.find('.messenger-director__item-action-checkback').remove();
                                            $('.messenger-accept').remove();
                                            $('.offer-infor').append(`
                                                <div class="align-center">
                                                    <a class="messenger-accept messenger-done" href="javascript:void(0)">検収が完了しました</a>
                                                </div>`);
                                            $('#modal-vote').modal()
                                       }
                                   }
                               });
                           } else {
                               button_dom.find('a').removeClass('disabled');
                           }
                       }
                   });
               }
            });

            //accept_offer
            active_offer.find(".messenger-director__item-action-accept").on("click", function () {
                if(!$(this).children().is('.disabled')) {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $.ajax({
                        type: "POST",
                        data: {
                            'offer_id': offer_id
                        },
                        url: '/messenger/offer_accept',
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            let offer_id = response.offer_id;
                            active_offer.find('.input-checkbox').remove();
                            $(`<div class="messenger-director__item-action messenger-form__action messenger-director__item-action-upload">
                                  <label class="button button--gradient button--gradient-primary button--round"
                                         for="upload_final_product-${offer_id}" role="button">データを納品</label>
                                  <form action="" class="form-upload-final-product">
                                        <input type="file" class="hidden" id="upload_final_product-${offer_id}" name="upload_final_product_input">
                                  </form>
                                  <div class="align-center selected_file">
                                  </div>
                                  </div>`).insertAfter(active_offer.find('.messenger-director__item-action-accept'));
                            $(`<div class="messenger-director__item-mess align-center">
                                  <a class="button button--border button--border-primary button--round button--small see-contract"
                                      href="javascript:void(0)" role="button" data-toggle="modal" data-target="#contract-modal"
                                      style="margin: 10px auto;">契約書を見る</a>
                              </div>`).insertBefore(active_offer.find('.messenger-form__confirm'));

                            $(`<div class="align-center">
                                   <a class="messenger-accept" href="javascript:void(0)">御取引が成立しました</a>
                               </div>`).insertBefore(active_offer.find('.messenger-form__confirm'));

                            active_offer.find('.messenger-director__item-action-accept').remove();
                            let target_offer = $('.messenger__item[data-offer^='+ response.offer_id + ']');
                            target_offer.find('.messenger__tag').remove();
                            target_offer.find('.messenger__count').html('0');
                            target_offer.find('.messenger__count').addClass('hide');
                        }
                    });
                }
            });

            active_offer.find('.messenger-form__confirm input[type="checkbox"]').off().on('click', function(){
              if ( $(this).is(':checked') ) {
                active_offer.find('.messenger-form__action .button').removeClass('disabled');
              } else {
                active_offer.find('.messenger-form__action .button').addClass('disabled');
              }
            });

            active_offer.find('.button-like, .button-no-comment, .button-dislike').on('click', function() {
                if(!$(this).is('.disabled') || !$(this).is('.button--disabled')) {
                    $(this).parent().find('.button').addClass('disabled');
                    let offer_id = $('#modal-vote').data('offer');
                    let value = $(this).data('value');
                    $.ajax({
                        type: "POST",
                        data: {
                            'offer_id': offer_id,
                            'value': value
                        },
                        url: '/messenger/update_review_offer',
                        success: function (data) {
                            // toastr.success('評価しました。');
                            let target_offer = $('.messenger__item[data-offer^='+ data.offer_id + ']');
                            $('#modal-vote').modal('hide');
                            $('.button-show-modal').remove();
                        }
                    });
                }
            });

            $(".messenger__column-right").off('change').on("change", '#upload_final_product-'+offer_id, function () {
                if($(this)[0].files.length) {
                    active_offer.find('.selected_file').html($(this)[0].files[0].name);
                    $('.messenger-detail__button-send .button--disabled').removeClass('button--disabled');
                    active_offer.find('.messenger-detail__button-send').trigger('click');
                } else {
                    active_offer.find('.selected_file').html('');
                }
            });


            $('.messenger__column-right').on('click', '.messenger-director__item-file', function () {
                let message_id = $(this).attr('data-message-id');
                if (message_id) {
                    $.ajax({
                        type: "GET",
                        datatype: "json",
                        url: "/get_product_file_download_link",
                        data: {
                            'id': message_id,
                        },
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            window.location.href = response.url;
                            // toastr.success('ダウンロードしました。');
                        }
                    })
                }
            });

            $('.messenger__column-right').on('click', '.see-contract', function () {
                let offer_id = $(this).parents('.messenger-director__list ').data('offer');
                let link_download = '/download_content_contract_modal/' + offer_id;
                $('.contract-modal__download-btn').attr('href', link_download);
                if (offer_id) {
                    $.ajax({
                        type: "GET",
                        datatype: "json",
                        url: "/get_content_contract_modal",
                        data: {
                            'id': offer_id,
                        },
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            $('.modal-contract-detail').html(response.html);
                        }
                    })
                }
            });

            let message = active_offer.find('.messenger-director__item-mess');
            $.each(message, function(i,v) {
                if(!$(v).is('.align-center')) {
                    let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
                    v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
                }
            });
        }
    })
}

function initCustomScrollbar() {
    $('.custom-scrollbar').mCustomScrollbar({
        theme: 'minimal-dark',
        scrollEasing: 'linear',
        scrollInertia: 0
    });

    $('.custom-scrollbar-horizontal').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'x'
    });
}

function updateURL(offer_id, target) {
    $(".messenger__column-right").html("");
    $('.messenger__item--selected').removeClass('messenger__item--selected');
    target.addClass('messenger__item--selected');
    let url = new URL(window.location);
    url.searchParams.set("offer", offer_id);
    let refresh = url.toString();
    window.history.pushState({ path: refresh }, '', refresh);
}
