var dropZones = [];
var dropZoneBill = false;
var dropZoneContract = false;
var key_file = "";
var real_name = "";
var is_delete_file = false;
var xhr;
var file_offer;
var real_name_offer = "";
var request_counter = 0;
var waiting_cancel_request = []
var svg_upload = `<svg class="icon_upload_svg" width="65" height="64" viewBox="0 0 65 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.233 26.6666H24.473V39.9999C24.473 41.4666 25.673 42.6666 27.1397 42.6666H37.8063C39.273 42.6666 40.473 41.4666 40.473 39.9999V26.6666H44.713C47.0863 26.6666 48.2863 23.7866 46.6063 22.1066L34.3663 9.86661C33.3263 8.82661 31.6463 8.82661 30.6063 9.86661L18.3663 22.1066C16.6863 23.7866 17.8597 26.6666 20.233 26.6666ZM13.833 50.6666C13.833 52.1333 15.033 53.3333 16.4997 53.3333H48.4997C49.9663 53.3333 51.1663 52.1333 51.1663 50.6666C51.1663 49.1999 49.9663 47.9999 48.4997 47.9999H16.4997C15.033 47.9999 13.833 49.1999 13.833 50.6666Z" fill="#F0F0F0"/>
</svg>
`

Dropzone.autoDiscover = false;
$(document).ready(function () {
    actionConfirmOffer();
    deleteOfferProduct();
    validateAmount();
    chargeOffer();
    handleChangeRequiredFields();
    autoFillWorkContent();
    initHandleActionUploadContract();
    closedOfferCreator();
    customSwitchPublic();
    initActionChangeValueTab();
    initDateRangeValid();
});

function reloadFunctionForm(){
    $(document).find('.calendar-datetime-picker').each(function() {
        const elementId = $(this).attr('id');
        // $(this).datepicker({
        //     format: 'yyyy/m/d',
        //     locale: 'ja',
        //     forceParse: false,
        // });
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const nextDay = new Date();
        nextDay.setDate(nextDay.getDate() + 1);

        flatpickr($(this), {
            mode: "single",
            dateFormat: "Y/m/d",
            showMonths: 1,
            onOpen: function (selectedDates, dateStr, instance) {
                $(instance.element)
                .next(".c-icon-date-range")
                .addClass("is-icon-active");
            },
            onClose: function (selectedDates, dateStr, instance) {
                $(instance.element)
                .next(".c-icon-date-range")
                .removeClass("is-icon-active");
            },
            onChange: function (selectedDates, dateStr, instance) {
                let startDate = selectedDates[0];
                let endDate = selectedDates[1];
                
                $(instance.element).attr(
                "data-start-time",
                moment(startDate).format("yyyy-MM-DD HH:mm:ss")
                );
                if (endDate)
                $(instance.element).attr(
                    "data-end-time",
                    moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                );
            },
            minDate: elementId === 'id_valid' ? new Date() : undefined,
            maxDate: elementId === 'id_valid' ? today > flatpickr($('#id_pre_deadline')).selectedDates[0] ? nextDay : flatpickr($('#id_pre_deadline')).selectedDates[0] : undefined
        });  
    })

    $(document).find('.calendar-rangetime-picker').each(function() {
        // $(this).datepicker({
        //     format: 'yyyy/m/d',
        //     locale: 'ja',
        //     forceParse: false,
        // });
        const today = new Date();
        const nextMonth = new Date();
        nextMonth.setMonth(nextMonth.getMonth() + 1)
        flatpickr($(this), {
            mode: "range",
            dateFormat: "Y/m/d",
            defaultDate: [today, nextMonth],
            locale: "ja",
            showMonths: 1,
            onOpen: function (selectedDates, dateStr, instance) {
                $(instance.element)
                .next(".c-icon-date-range")
                .addClass("is-icon-active");
            },
            onClose: function (selectedDates, dateStr, instance) {
                $(instance.element)
                .next(".c-icon-date-range")
                .removeClass("is-icon-active");
            },
            onChange: function (selectedDates, dateStr, instance) {
                console.log(selectedDates);
                
                let startDate = selectedDates[0];
                let endDate = selectedDates[1];
                
                $(instance.element).attr(
                "data-start-time",
                moment(startDate).format("yyyy-MM-DD HH:mm:ss")
                );
                if (endDate)
                $(instance.element).attr(
                    "data-end-time",
                    moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                );
            },
        });
    })
}

function actionConfirmOffer() {
    // action add data into modal
    $(document).on('click', '.get-data-offer', function () {
        if ($('#id-form-contract-and-plan').data('value')) {
            $('.contract__form-price input').trigger('input');
        }
        let offer_active = $('.mitem.mactive');
        if (offer_active.length) {
            let offer_id = offer_active.attr('data-offer');
            let project_id = $('.project-item.active').attr('data-project-id');
            $('.btn-confirm-contract-offer, #modal-confirm-done-offer').attr('data-offer', offer_id);
            $('.btn-confirm-contract-offer, #modal-confirm-done-offer').attr('data-project', project_id);
            let is_upload_bill = $('.mitem.mactive').length > 0
            $('#modal-drag-file').toggleClass('hide', !is_upload_bill);
            $('#modal-upload-contract-plan').toggleClass('hide', is_upload_bill);
        }
        reloadFunctionForm();
        let target = $(this).attr('data-target')
        let btn = '.btn-upload-file-messenger-owner';
        $(btn).toggleClass('disable', !validRequiredFields(target))
        if(target == '#modal-drag-file') {
            $(btn).addClass('disable')
        }
        $('.old-bill-preview').remove();
        $('#modal-upload-plan, #modal-upload-contract, #modal-drag-file').removeAttr('data-type')
    });

    // action checked contract
    $(document).on('click', '.open-file-preview-only, .open-preview-plan-approve-modal, .open-preview-contract-approve-modal, .open-preview-bill-approve-modal', function () {
        // let buttonDom = $(this);
        // ajaxChangeStatusOfferConfirm(buttonDom);
        $('#modal-confirm-upload').removeAttr('data-project-id')
        if ($(this).is('.open-file-preview-only')) {
            previewPDF($(this).attr('data-file-id'), 'view_only', true)
        } else if ($(this).is('.open-preview-plan-approve-modal')) {
            previewPDF($(this).attr('data-file-id'), 'plan_approve', false)
        } else if ($(this).is('.open-preview-contract-approve-modal')) {
            previewPDF($(this).attr('data-file-id'), 'contract_approve', false)
        } else if ($(this).is('.open-preview-bill-approve-modal')) {
            previewPDF($(this).attr('data-file-id'), 'bill_approve', false)
        }
    });

    $(document).on('click', '.open-file-preview-artist-contract-only, .open-preview-artist-contract-approve-modal', function () {
        // let buttonDom = $(this);
        // ajaxChangeStatusOfferConfirm(buttonDom);
        if ($(this).is('.open-file-preview-artist-contract-only')) {
            previewPDFArtist($(this).attr('data-file-id'), 'view_only', true)
        } else {
            previewPDFArtist($(this).attr('data-file-id'), 'contract_approve_artist', false)
        }
    });

    $(document).on('click', '.open-edit-plan-modal, .open-edit-contract-modal, .open-edit-bill-modal', function() {
        reloadFunctionForm()
        let offer_active = $('.mitem.mactive');
        if (offer_active.length) {
            let offer_id = offer_active.attr('data-offer');
            let project_id = $('.project-item.active').attr('data-project-id');
            $('.btn-confirm-contract-offer, #modal-confirm-done-offer').attr('data-offer', offer_id);
            $('.btn-confirm-contract-offer, #modal-confirm-done-offer').attr('data-project', project_id);
            let is_upload_bill = $('.mitem.mactive').length > 0
            $('#modal-drag-file').toggleClass('hide', !is_upload_bill);
            $('#modal-upload-contract-plan').toggleClass('hide', is_upload_bill);
        }
        let btn = '.btn-upload-file-messenger-owner';
        let target = '#modal-upload-plan';
        if ($(this).is('.open-edit-plan-modal')) {
            $('#modal-upload-plan').modal('show')
            $('#modal-upload-plan').attr('data-type', 'edit-plan')
            $('.contract__form-quantity').trigger('input');
        } else if ($(this).is('.open-edit-contract-modal')) {
            $('#modal-upload-contract').modal('show')
            $('#modal-upload-contract').attr('data-type', 'edit-contract')
            target = '#modal-upload-contract';
        } else if ($(this).is('.open-edit-bill-modal')) {
            $('#modal-drag-file').modal('show')
            $('#modal-drag-file').attr('data-type', 'edit-bill')
            let file_name = $(this).find('.text-content-left').text()
            let amount = $(this).attr('data-bill-amount')
            target = '#modal-drag-file';
            $(target).find('input#id_amount').val('')
            $(target).find('.mattach-previews.mattach-previews-form.collection').empty()
            $(target).find('input#id_amount').val(amount)
            $(target).find('.mattach-previews.mattach-previews-form.collection').append(`
            <div class="mattach-template mattach-template-form collection-item item-template old-bill-preview">
                <div class="mattach-info" data-dz-thumbnail="">
                    <div class="mcommment-file">
                        <div class="determinate" style="width: 100%;" data-dz-uploadprogress=""></div>
                        <div class="mcommment-file__name mcommment-file__name-form" data-dz-name="">
                            <i class="icon icon--sicon-clip"></i>${file_name}
                        </div>
                        <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                            <i class="icon icon--sicon-close delete-old-bill"></i>
                        </div>
                    </div>
                </div>
            </div>`)
            $('.delete-old-bill').on('click', function() {
                $('.old-bill-preview').remove();
                $(btn).addClass('disable')
            })
        }
        
        $(btn).toggleClass('disable', !validRequiredFields(target))
    })

    $(document).on('click', '.download-approved-file-pdf, .download-approved-file-pdf .icon--sicon-download', function () {
        data = new FormData();
        let file_id = $(this).attr('data-file-id')
        if($(this).is('.icon--sicon-download')) {
            file_id = $(this).parents('.download-approved-file-pdf').attr('data-file-id')
        }
        data.append('file_id', file_id)
        downloadFile(data)
    });

    $(document).on('change', '#form-approve-checkbox', function () {
        if($('#form-approve-checkbox').is(':checked')) {
            $(this).parents('.checkbox-and-button-container').find('.component-button.btn--primary.large').removeClass('disable')
        } else {
            $(this).parents('.checkbox-and-button-container').find('.component-button.btn--primary.large').addClass('disable')
        }
    });

    $(document).on('click', '#btn__submit-upload', function () {
        if($('#form-approve-checkbox').is(':checked')) {
            $(this).parents('.checkbox-and-button-container').find('.component-button.btn--primary.large').removeClass('disable')
        } else {
            $(this).parents('.checkbox-and-button-container').find('.component-button.btn--primary.large').addClass('disable')
        }
        let file_id = $('#modal-confirm-upload').attr('data-file-id')
        let is_waiting_confirm = $('#modal-confirm-upload').attr('data-waiting-confirm')

        if (is_waiting_confirm) {
            let old_form_id = $('#modal-confirm-upload').attr('data-old-form')
            let new_form_id = $('#modal-confirm-upload').attr('data-new-form')
            let message_file_id  = $('#modal-confirm-upload').attr('data-new-file-message')
            let type  = $('#modal-confirm-upload').attr('data-type')
            $('#modal-confirm-upload').removeAttr('data-waiting-confirm data-old-form data-new-form data-new-file-message data-type')
            console.log('accept', $(this).attr('data-waiting-confirm'))
            confirmFileUpload(old_form_id, new_form_id, message_file_id, type)
        } else if ($('#modal-confirm-upload').attr('data-offer-id') || $('#modal-confirm-upload').attr('data-project-id')) {
            //offerCreator
            if($('#modal-confirm-upload').attr('data-modal-type') == 'contract_approve_artist') {
                // accept offer contract
                $('#btn__submit-upload').addClass('disable')
                let offer_id = $('#modal-confirm-upload').attr('data-file-id');
                ajaxAcceptOffer(offer_id, $('.offer-' + offer_id));
            } else {
                // confirm send offer creator
                $('#btn__submit-upload').addClass('disable')
                if($('#modal-confirm-upload').attr('data-type') == 'edit-contract-artist') {
                    $.ajax({
                        type: 'POST',
                        url: '/messenger/offer_creator_edit_confirm',
                        data: {
                            'new_offer_id': $('#modal-confirm-upload').attr('data-new-offer-id'),
                            'offer_id': $('#modal-confirm-upload').attr('data-offer-id'),
                            'is_delete_file': $('#modal-confirm-upload').attr('data-is-delete-file'),
                        },
                        datatype: "json",
                        success: function (data) {
                            console.log('uploaded file ok');
                            // toastr.success(gettext('I made an offer.'));
                            setTimeout(function () {
                                sScrollbarBottom();
                                $("html, body").animate({scrollTop: $('.mcommment').height() + 200}, 1000);
                            }, 2000);
                            $('#modal-edit-offer').modal('hide');
                            $('.infor-offer').html(data.infor_offer);
                            is_editing = false;
                            intitActionForm();
                        },
                        statusCode: {
                            400: function (data) {
                                toastr.info(data.responseJSON.error);
                            },
                            500: function (data) {
                                toastr.info(gettext('Something went wrong!'));
                            },
                        },
                        complete: function() {
                            $('#modal-confirm-upload').modal('hide');
                            $('#modal-confirm-upload').removeAttr('data-modal-type')
                            $('#btn__submit-upload').removeClass('.btn--disabled')
                        }
                    });
                } else {
                    $.ajax({
                        type: 'POST',
                        url: '/direct/offer_creator_confirm',
                        data: {
                            'project_id': $('#modal-confirm-upload').attr('data-project-id'),
                            'offer_id': $('#modal-confirm-upload').attr('data-offer-id'),
                            'real_names': $('#modal-confirm-upload').attr('data-real-name').split('/'),
                            'form_create_type': $('#modal-confirm-upload').attr('data-type')
                        },
                        datatype: "json",
                        success: function (data) {
                            console.log('uploaded file ok');
                            // toastr.success(gettext('I made an offer.'));
                            if(data.form_create_type == 'create_contract_artist') {
                                setTimeout(function () {
                                    window.location.href = data.url;
                                }, 1000);
                            }
                        },
                        statusCode: {
                            400: function (data) {
                                toastr.info(data.responseJSON.error);
                            },
                            500: function (data) {
                                toastr.info(gettext('Something went wrong!'));
                            },
                        },
                        complete: function() {
                            $('#modal-confirm-upload').modal('hide');
                            $('#modal-confirm-upload').removeAttr('data-modal-type')
                            $('#btn__submit-upload').removeClass('.btn--disabled')
                            is_editing = false;
                        }
                    });
                }
                
                $('#modal-confirm-upload').removeAttr('data-project-id data-real-name data-offer-id data-type data-new-offer-id data-is-delete-file')
            }
        } else {
            if($('#modal-confirm-upload').attr('data-modal-type') == 'bill_approve') {
                $.ajax({
                    type: "POST",
                    datatype: "json",
                    url: "/direct/check_payment",
                    data: {
                        'file_id': file_id,
                    },
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        $('#modal-confirm-upload').modal('hide')
                        let modalChooseCard = $('#modal-choose--card');
                        let modalAddCard = $('#modal-add-card');
                        if (response.status === 'success') {
                            if (response.card === 'no_card') {
                                modalAddCard.find('.link-add--card').attr('href', response.url);
                                modalAddCard.modal()
                            }
                            if (response.card === 'has_card') {
                                modalChooseCard.find('.total-bill').html(response.total + ' 円');
                                modalChooseCard.find('.card-select-container').remove();
                                modalChooseCard.find('.messenger-popup__form').prepend(response.cards_html);
                                modalChooseCard.modal();
                            }
                        }
                    }
                })
            } else {
                ajaxChangeStatusOfferConfirm(file_id);
            }
        }
    });

    $(document).on('hide.bs.modal', '#modal-confirm-upload', function() {
        if($(this).attr('data-waiting-confirm'))  {
            rejectFileUpload($(this).attr('data-old-form'), $(this).attr('data-new-form'), $(this).attr('data-new-file-message'), $(this).attr('data-type'))
            $(this).removeAttr('data-waiting-confirm data-old-form data-new-form data-new-file-message data-type')
        } else if($(this).attr('data-project-id')) {
            if($(this).attr('data-type') == 'create-contract-artist') {
                $.ajax({
                    type: 'POST',
                    datatype: "json",
                    url: '/direct/offer_creator_reject',
                    data: {
                        'project_id': $('#modal-confirm-upload').attr('data-project-id'),
                        'offer_id': $('#modal-confirm-upload').attr('data-offer-id'),
                        'real_names': $('#modal-confirm-upload').attr('data-real-name').split('/'),
                        'form_create_type': $('#modal-confirm-upload').attr('data-type')
                    },
                    success: function(data) {
                        console.log('successfully cancelled offer')
                        $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                    },
                    error: function() {
                        console.log('error when cancel offer')
                        $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                    }
                })
            } else {
                var new_offer_id = $('#modal-confirm-upload').attr('data-new-offer-id')
                if (!new_offer_id) return;
                $.ajax({
                    type: 'POST',
                    datatype: "json",
                    url: '/messenger/offer_creator_edit_reject',
                    data: {
                        'new_offer_id': new_offer_id
                    },
                    success: function(data) {
                        console.log('successfully cancelled offer edit')
                        $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                    },
                    error: function() {
                        console.log('error when cancel offer edit')
                        $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                    }
                })
            }
            $(this).removeAttr('data-project-id data-offer-id data-real-name data-type data-new-offer-id data-is-delete-file')
        } else {
            console.log('added' + $(this).attr('data-request-id') + 'to cancel')
            waiting_cancel_request.push(String($(this).attr('data-request-id')))
            $(this).removeAttr('data-request-id')
        }
    })

    // action checked done offer
    $(document).on('click', '.button-confirm-done-offer', function () {
        let buttonDom = $(this);
        let modalConfirm = buttonDom.parents('#modal-confirm-done-offer');
        ajaxChangeStatusOffer(buttonDom, modalConfirm)
    });

    // action upload file into offer product
    $(document).on('click', '#modal-drag-file .btn-upload-file-messenger-owner', function () {
        let buttonDom = $(this);
        $('#id_amount').removeClass('error-border');
        $('.error-amount').remove();
        if (!$('.popup-form-budget').hasClass('hide')) {
            ajaxUploadFileOffer(buttonDom);
        }
    });

    //TODO: button submit step1 for plan
    $(document).on('click', '#modal-upload-plan .btn-upload-file-messenger-owner', function() {
        var error = validatePlanSubmitForm();
        if (!error) {
            $('#modal-confirm-upload').removeClass('no-button')
            $('#modal-confirm-upload .checkbox-and-button-container').removeClass('hide')
            $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
            $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックしました')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-text').text('お見積りを送る')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').removeClass('icon-send-plane icon-handshake icon-tick')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-send-plane disable')
            $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox').siblings('label.form-check-label').addClass('btn--disabled')
               
            getPDFtoPreviewBeforeConfirmSend('#modal-upload-plan')
        }
    });

    //TODO: button submit step1 for contract
    $(document).on('click', '#modal-upload-contract .btn-upload-file-messenger-owner', function() {
        var error = validateContractSubmitForm();
        if (!error) {
            $('#modal-confirm-upload').removeClass('no-button')
            $('#modal-confirm-upload .checkbox-and-button-container').removeClass('hide')
            $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
            $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックしました')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-text').text('契約書を送信')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').removeClass('icon-send-plane icon-handshake icon-tick')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-send-plane disable')
            $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox').siblings('label.form-check-label').addClass('btn--disabled')
            getPDFtoPreviewBeforeConfirmSend('#modal-upload-contract')
        }
    });

    paymentOffer();

    // reset modal
    $(document).on('hidden.bs.modal', '#modal-confirm-done-offer, #modal-drag-file', function (e) {
        $(this).attr('data-offer', '');
        $(this).attr('data-project', '');
        dropZones.forEach((element) => { element.removeAllFiles();} )
        $('#id_amount').val('');
        $('#id_amount').removeClass('error-border');
        $('.error-amount').remove();
    });

    $(document).on('click', '#modal-upload-plan .btn-popup-close', function(e) {
        resetFormContract();
        $('#modal-upload-plan').removeAttr('data-type')
    })

    $(document).on('click', '#modal-upload-contract .btn-popup-close', function(e) {
        resetFormContract();
        $('#modal-upload-contract').removeAttr('data-type')
    })
}

function paymentOffer() {
    
}

function ajaxChangeStatusOfferConfirm(file_id, status='3') {
    $('#btn__submit-upload').addClass('disable')
    if (file_id) {
        $.ajax({
            type: "POST",
            datatype: "json",
            url: '/direct/accept_contract',
            async: false,
            data: {
                'file_id': file_id,
                'status': status
            },
            success: function (data) {
                console.log('ok');
            },
            complete: function () {
                $('#btn__submit-upload').removeClass('disable')
                $('#modal-confirm-upload').modal('hide')
                $('#modal-confirm-upload').removeAttr('data-modal-type')
                $('#modal-confirm-upload').attr('data-file-id', '')
            },
            error: function (data) {
                console.log('error');
            }
        });
    }
}

function ajaxChangeStatusOffer(button_dom, modal_confirm) {
    let offer_id = modal_confirm.attr('data-offer');
    let project_id = modal_confirm.attr('data-project');
    if (offer_id && project_id && !button_dom.hasClass('disable')) {
        button_dom.addClass('disable');
        let status = '4';
        $.ajax({
            type: "POST",
            datatype: "json",
            url: '/direct/accept_contract',
            async: false,
            data: {
                'offer_id': offer_id,
                'project_id': project_id,
                'status': status
            },

            success: function (data) {
                console.log('ok');
            },
            complete: function () {
                button_dom.removeClass('disable');
                modal_confirm.modal('hide');
            },
            error: function (data) {
                console.log('error');
            }
        });
    }
}

function ajaxUploadFileOffer(buttonDom) {
    let offer_id = $('.mitem.mactive').attr('data-offer');
    let project_id = $('.project-item.active').attr('data-project-id');
    let amount;
    if (!$('.popup-form-budget').hasClass('hide')) {
        let amountValue = $('#id_amount').val().replaceAll(',', '');
        amount = amountValue !== '' ? parseInt(amountValue) : 0;
        if (!showErrorAmount()) {
            buttonDom.addClass('disable');
            return
        }
    }
    let form_create_type = '';
    if(buttonDom.parents('#modal-drag-file').length) {
        form_create_type = buttonDom.parents('#modal-drag-file').attr('data-type')
    }
    if (offer_id && project_id && !buttonDom.hasClass('disable')) {
        buttonDom.addClass('disable');
        $.ajax({
            type: "POST",
            datatype: "json",
            url: '/direct/upload_contract',
            async: false,
            data: {
                'offer_id': offer_id,
                'project_id': project_id,
                'file': file_offer,
                'real_name': real_name_offer,
                'amount': amount,
                'form_create_type': form_create_type
            },
            success: function (data) {
                console.log('ok');
            },
            complete: function () {
                buttonDom.removeClass('disable');
                $('#modal-drag-file').modal('hide');
                $('#modal-drag-file').attr('data-type', '');
            },
            error: function (data) {
                console.log('error');
                resetFormContract();
            }
        });
    }
}

function dragDropMessengerOwner() {
    if (!$('#uploadFile').length) {
        return
    }

    initDropZone(['#modal-upload-contract', '#modal-drag-file']);
}

function initDropZone(formIds) {
    formIds.forEach(function (item){
        if (!$(item + ' #uploadFile').length) {
            return
        }
        var previewNode = $(item + ' .mattach-template-form');
        var previewTemplate = previewNode.parent().html();

        if (item != '#modal-upload-contract' || (item == '#modal-upload-contract-' &&
                (!$('#modal-upload-contract #id-form-contract-and-plan').data('value') ||
                    $('#modal-upload-contract #creation-method-original').data('value') == 'generate' ||
                    !$('#modal-upload-contract #attach-file-id').data('value')))) {
            previewNode.parent().empty();
        }

        var dropzoneOwner = new Dropzone(item + ' #uploadFile', {
            maxFilesize: 4500,
            timeout: 900000,
            autoDiscover: false,
            acceptedFiles: 'application/pdf',
            previewsContainer: item + ' .mattach-previews-form',
            previewTemplate: previewTemplate,
            url: "/",
            autoProcessQueue: false,
            autoQueue: false,
            clickable: item + ' #uploadFile',
            maxFiles: 1,
            dictDefaultMessage: svg_upload + '\n' + '<p>ファイルを選択</p>'
        });
        if (item == '#modal-drag-file') {
            dropZoneBill = dropzoneOwner;
        } else if (item == '#modal-upload-contract') {
            dropZoneContract = dropzoneOwner;
        }
        dropZones.push(dropzoneOwner);

        dropzoneOwner.on("maxfilesexceeded", function (file) {
            dropzoneOwner.removeAllFiles();
            dropzoneOwner.addFile(file);
        });

        dropzoneOwner.on('removedfile', function (file) {
            file_offer = '';
            real_name_offer = '';
            if (item === '#modal-upload-contract') {
                 $(item + ' .btn-upload-file-messenger-owner').toggleClass('disable', !validRequiredFields('#modal-upload-contract'));
            } else {
                $(item + ' .btn-upload-file-messenger-owner').addClass('disable');
            }
        });

        dropzoneOwner.on('addedfile', function (file, e) {
            if (dropzoneOwner.files && dropzoneOwner.files[0] && dropzoneOwner.files[0].name.match(/\.(pdf|PDF)$/)) {
                let file_dom = $(file.previewElement);
                real_name_offer = file.name;
                let file_preview = $(item + ' .mattach-preview-container-form').find(".mcommment-file__name-form");
                $(item + ' .mattach-preview-container-form .mattach-template.mattach-template-form').removeClass('hide')
                for (let i = 0; i < file_preview.length; i++) {
                    $(file_preview[i]).text('');
                    $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_name_offer);
                    break;
                }
                $(item + " .mattach-info-file").addClass("hide");
                if ($(item + " .mattach-template").length == 2) {
                    $($(item + " .mattach-template")[0]).remove();
                }
                file_name = this.files[0].name;
                if ($(item + ' .account__file').length) {
                    $(item + ' .account__file').hide();
                    $(item + ' .account__file').next().hide();
                }
                uploadFileS3OfferProduct(file, file_dom, item);
            } else if (!dropzoneOwner.files) {
                return false;
            } else {
                alert('PDFのみアップロードできます。');
                dropzoneOwner.removeAllFiles(true);
            }
        });

        $(item + ' .mattach-info-file .icon--sicon-close').on('click', function () {
            $(item + ' .mattach-info-file').addClass('hide');
            is_delete_file = true;
        })
    })
}


function uploadFileS3OfferProduct(file, file_dom, item) {
    file_dom.find('.determinate').css('width', '0%');
    $.ajax({
        type: 'GET',
        datatype: 'json',
        url: '/get_presigned_url',
        data: {
            'file_name': 'storage/' + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            file_offer = data.presigned_post.fields['key'];
            let postData = new FormData();
            for (key in data.presigned_post.fields) {
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            if (xhr) {
                xhr.abort();
            }
            xhr = new XMLHttpRequest();
            xhr.open('POST', url);
            xhr.upload.addEventListener('progress', function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css({'transition': '0 1s', 'width': percentComplete});
                    let uploadModal = $(item + ' .upload-button-wrapper');
                    if (uploadModal && uploadModal.hasClass('clicked')) {
                        uploadModal.css('display', 'flex');
                        uploadModal.addClass('clicked');
                        $(item + ' .upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                    }
                }
            }, false);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 204) {
                        file_dom.find('.determinate').css('width', '100%');
                        if (!$(item + ' #id_amount').length) {
                            $(item + ' .btn-upload-file-messenger-owner').toggleClass('disable', !validRequiredFields('#modal-drag-file'));
                        } else if ($(item + ' #id_amount').length && $('#id_amount').val().replaceAll(',', '').replace(/\D/g, '') >= MIN_AMOUNT_STRIPE) {
                            $(item + ' .btn-upload-file-messenger-owner').removeClass('disable');
                        } else {
                            $(item + ' .btn-upload-file-messenger-owner').addClass('disable');
                        }
                    }
                }
            };
            xhr.send(postData);

        }
    })
}

function deleteOfferProduct() {
    $(document).on('click', '#delete-offer .btn-popup-delete', function () {
        let offer_id = $('.mitem.mactive').attr('data-offer');
        let project_id = $('.project-item.active').attr('data-project-id');
        let buttonDom = $(this);
        if (offer_id && project_id && !buttonDom.hasClass('disable')) {
            buttonDom.addClass('disable');
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/direct/delete_offer_product",
                data: {
                    'offer_id': offer_id,
                    'project_id': project_id
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    console.log('ok');
                },
                complete: function () {
                    $('#delete-offer').modal('hide');
                    buttonDom.removeClass('disable');
                },
                error: function () {
                    toastr.error(gettext('Something went wrong!'));
                }
            })
        }
    })
}


function closedOfferCreator() {
    $(document).on('click', '#closed-offer .btn-popup-closed-offer', function () {
        let offer_id = $('.mitem.mactive').attr('data-offer');
        let project_id = $('.project-item.active').attr('data-project-id');
        let buttonDom = $(this);
        if (offer_id && project_id && !buttonDom.hasClass('disable')) {
            buttonDom.addClass('disable');
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/messenger/ajax/update_offer_closed",
                data: {
                    'offer_id': offer_id,
                    'project_id': project_id
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    console.log('ok');
                },
                complete: function () {
                    $('#closed-offer').modal('hide');
                    buttonDom.removeClass('disable')
                },
                error: function () {
                    toastr.error(gettext('Something went wrong!'));
                }
            })
        }
    })
}

function validateAmount() {
    $(document).on('input', '#id_amount', function (e) {
        let amountDom = $('#id_amount');
        amountDom.removeClass('error-border');
        $('.error-amount').remove();
        let intValue = this.value.replaceAll(',', '').replace(/\D/g, '');
        showErrorAmount();
        this.value = intValue.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        let has_file = dropZoneBill.files.length > 0 || $('#modal-drag-file .old-bill-preview').length
        if (intValue >= MIN_AMOUNT_STRIPE && has_file) {
            $('#modal-drag-file .btn-upload-file-messenger-owner').removeClass('disable');
        } else {
            $('#modal-drag-file .btn-upload-file-messenger-owner').addClass('disable');
        }
    })
}

function showErrorAmount() {
    let amountDom = $('#id_amount');
    let intValue = amountDom.val().replaceAll(',', '').replace(/\D/g, '');
    if (intValue < MIN_AMOUNT_STRIPE || intValue > MAX_AMOUNT_STRIPE) {
        amountDom.addClass('error-border');
        $('<ul class="errorlist error-amount" style="margin-left: 8%">' +
            '<li>'+ gettext('Please enter the correct value. (100 yen ↔ 99,999,999 yen)')+'</li>' +
            '</ul>').insertAfter(amountDom.parents('.order-step__form-budget'));
        return false
    }
    return true
}

function chargeOffer() {
    let modalChooseCard = $('#modal-choose--card');
    $(document).on('change', '#modal-choose--card .messenger-popup__form .InputGroup input[type=radio]', function () {
        if ($(this).is(':checked')) {
            $(this).parents('.messenger-popup__form').find('button#payment_submit').removeClass('disable');
        }
    });

    $(document).on('hidden.bs.modal', '#modal-choose--card', function () {
        $(this).find('button#payment_submit').addClass('disable');
    });
    $(document).on('click', '#modal-choose--card button#payment_submit', function (e) {
        e.stopPropagation();
        let buttonDom = $(this);
        let offer_id = $('.mitem.mactive').attr('data-offer');
        let card_id = $(this).parents('#modal-choose--card').find('.InputGroup input[type=radio]:checked').get(0).value;
        if (offer_id && card_id) {
            buttonDom.addClass('disable');
            $.ajax({
                url: '/direct/payment/charge',
                type: 'POST',
                data: {
                    'card_id': card_id,
                    'offer_id': offer_id
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    if (response.code === 302) {
                        window.location.replace(response.url_redirect);
                    } else if (response.code === 500) {
                        toastr.error(response.message);
                    } else {
                        // toastr.success(response.message);
                        $('#modal-choose--card').modal('hide');
                        $('#payment-success-popup').modal();
                        window.open(response.receipt_url, '_blank');
                    }
                },
                error: function (xhr, status, error) {
                    toastr.warning(xhr.responseJSON.message);
                },
                complete: function () {
                    modalChooseCard.modal('hide');
                    buttonDom.addClass('disable');
                }
            });
        }
    });
}

function previewPDF(file_id, type, only_preview=false) {
    $('#modal-confirm-upload').attr('data-file-id', file_id)
    $('#modal-confirm-upload').find('iframe').attr('src', 'about:blank');
    if(only_preview) {
        $('#modal-confirm-upload .checkbox-and-button-container').addClass('hide')
        $('#modal-confirm-upload').addClass('no-button')
    } else {
        $('#modal-confirm-upload').removeClass('no-button')
        $('#modal-confirm-upload .checkbox-and-button-container').removeClass('hide')
        $('#modal-confirm-upload .checkbox-and-button-container .btn.component-button').addClass('disable')
        $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
        $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックし、同意しました')
        $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').removeClass('icon-send-plane icon-handshake icon-tick')
        $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-handshake')
        $('#modal-confirm-upload').attr('data-modal-type', type)
        if (type=='plan_approve') {
            $('#modal-confirm-upload .checkbox-and-button-container .btn-text').text('注文')
        } else if (type=='contract_approve') {
            $('#modal-confirm-upload .checkbox-and-button-container .btn-text').text('契約を締結')
        } else if (type=='bill_approve') {
            $('#modal-confirm-upload .checkbox-and-button-container .btn-text').text('支払う')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').removeClass('icon-send-plane icon-handshake icon-tick')
            $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-tick')
            $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックしました')
        }
    }
    $('#modal-confirm-upload').modal('show')
    $.ajax({
        type: 'POST',
        datatype: 'json',
        url: '/get_link_pdf_contract_plan',
        data: {
            'file_id': file_id
        },
        success: function (data) {
            $('#modal-confirm-upload').attr('data-file-id', file_id)
            $('#modal-confirm-upload').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.url) + '#zoom=page-width');
        }
    })
}

function previewPDFArtist(file_id, type, only_preview=false) {
    $('#modal-confirm-upload').attr('data-file-id', file_id)
    $('#modal-confirm-upload').attr('data-project-id', file_id)
    $('#modal-confirm-upload').find('iframe').attr('src', 'about:blank');
    if(only_preview) {
        $('#modal-confirm-upload .checkbox-and-button-container').addClass('hide')
        $('#modal-confirm-upload').addClass('no-button')
    } else {
        $('#modal-confirm-upload').removeClass('no-button')
        $('#modal-confirm-upload .checkbox-and-button-container').removeClass('hide')
        $('#modal-confirm-upload .checkbox-and-button-container .btn.component-button').addClass('disable')
        $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
        $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックし、同意しました')
        $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').removeClass('icon-send-plane icon-handshake icon-tick')
        $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-handshake')
        $('#modal-confirm-upload').attr('data-modal-type', type)
        if (type=='contract_approve_artist') {
            $('#modal-confirm-upload .checkbox-and-button-container .btn-text').text('オファーを請ける')
        }
    }
    $('#modal-confirm-upload').modal('show')
    $.ajax({
        type: 'POST',
        datatype: 'json',
        url: '/direct/get_link_pdf_contract_artist',
        data: {
            'file_id': file_id
        },
        success: function (data) {
            $('#modal-confirm-upload').attr('data-file-id', file_id)
            $('#modal-confirm-upload').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.url) + '#zoom=page-width');
            scrollBottomPDFArtist($('#modal-confirm-upload').find('iframe'), $('#modal-confirm-upload'));
        }
    })
}

let count = 0

function scrollBottomPDFArtist(iframe, modal) {
    countPage = iframe[0].contentWindow.document.querySelectorAll('.page').length
    if(!countPage) {
        if(count > 100) {
            return;
        }
        count++;
        setTimeout(() => {
            return scrollBottomPDFArtist(iframe, modal);
        }, 200);
    } else {
        count = 0;
        modal.find('iframe').attr('src', modal.find('iframe').attr('src') + `&page=${countPage}`);
    }
}
