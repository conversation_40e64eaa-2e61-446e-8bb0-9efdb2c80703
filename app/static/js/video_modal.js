$(document).ready(function (e) {
    let video_modal = $('#video-modal');
    let processing_scenemodal = $('#processingSceneModal');

    video_modal.on('click', '.video-modal-btn.video-save-btn', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let btn_save = this;
        let video_variations = $(this).parents('.modal-body').find('.modal-tree .video-variation');
        let productscene_id = $(this).parents('.modal-body').find('.modal-tree-info .chapter-name').first().attr('data-product-scene-id');
        let scenetitle_id = $(this).parents('.modal-body').find('.modal-tree-info .scene-name').first().attr('data-scene-title-id');
        let data_variations = [];
        let len_of_variation = video_variations.length
        video_variations.each(function (index, item) {
            let variation_id = $(item).attr('data-variation-id');
            data_variations.push({variation_id: variation_id, index: len_of_variation - index});
        });
        //data upload
        let _data = {
            'data_variations': JSON.stringify(data_variations),
            'scenetitle_id': scenetitle_id,
            'productscene_id': productscene_id
        };

        //thumbnail info
        let scene_id = $(this).parents('.video-modal__right').find('.canvas #id_scene_id').first().val().trim();
        let thumbnail_base64 = $(this).parents('.video-modal__right').find('.canvas #id_thumbnail_base64').first().val().trim();

        if (scene_id && thumbnail_base64) {
            _data.scene_id = scene_id;
            _data.thumbnail_base64 = thumbnail_base64;
        }

        $.ajax({
            url: '/top/update_variation',
            type: 'POST',
            datatype: "json",
            data: _data,
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $(".loader").show();
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                // toastr.success(response.message);
                let target = $('.pd-scene-title-detail');
                $(btn_save).parents('#video-modal').modal('hide');
                $('.pd-scene').empty();
                $('.pd-scene').append(response.html);
                setActionForProjectItem(target);
                newWavesurferInit();
                projectScene();
                actionBottomVideo()
                //location.reload();
            },
            error: function (response) {
                toastr.error(response.responseJSON.message);
            },
            complete: function (response) {
                $(".loader").hide();
            }
        });
    })

    video_modal.on('hide.bs.modal', function () {
        let scene_name = $(this).find('.scene-name[data-scene-title-id]').first();
        if (scene_name.length > 0) {
            let scene_title_id = scene_name.attr('data-scene-title-id');
            reloadSceneTittleAfterEdit(scene_title_id);
        }
        stop_video_audio();
    });

    processing_scenemodal.on('hide.bs.modal', function () {
        let project_video_item_el = $(this).find('.project-video-item').first();
        if ((project_video_item_el.length > 0 && project_video_item_el.attr('updated') === 'updated') || project_video_item_el.hasClass('hide')) {
            let scene_title_id = project_video_item_el.attr('data-scene-title-id');
            reloadSceneTitleInProgressTab(scene_title_id);
        }
    });
});

function deleteSceneOnVideoModal(target) {
    let scene_id = target.attr('data-scene-id');
    bootbox.confirm({
        message: "本当に削除しますか？",
        buttons: {
            confirm: {
                label: 'はい',
                className: 'btn btn-danger btn--tertiary btn-delete-message'
            },
            cancel: {
                label: 'いいえ',
                className: 'btn btn-light btn--primary btn-cancel-message'
            }
        },
        callback: function (result) {
            if (result) {
                $.ajax({
                    type: "POST",
                    url: "/top/delete_scene",
                    data: {
                        'scene_id': scene_id
                    },
                    datatype: "json",
                    success: function (data) {
                        if (!data.is_deleted) {
                            let scene_id = data.scene_id;

                            //video-modal-version-item
                            let deleted_video_item = target.parents('.modal-body').find('.video-modal-version-item[data-scene-id=' + scene_id + ']').first();
                            let data_max_version = parseInt(deleted_video_item.attr('data-max-version'));
                            //reset max-version for sibling
                            deleted_video_item.siblings().attr('data-max-version', data_max_version - 1);
                            let video_item_siblings_count = deleted_video_item.siblings().length;
                            let deleted_video_siblings = deleted_video_item.siblings();

                            deleted_video_item.remove();
                            deleted_video_siblings.each(function (index, item) {
                                $(item).attr('data-index', video_item_siblings_count - index - 1);
                                //update version tag
                                $(item).find('.video-modal-version').text(video_item_siblings_count - index);
                            });
                            //update version tag
                            let first_video_item = deleted_video_siblings.first().find('.video-modal-version').first();
                            if (first_video_item.hasClass('gray-background')) {
                                first_video_item.removeClass('gray-background');
                            }
                            //video-version
                            let deleted_version_el = target.parents('.video-version').first();
                            let version_data_max_version = parseInt(deleted_version_el.attr('data-max-version'));
                            deleted_version_el.siblings().attr('data-max-version', version_data_max_version - 1);
                            let video_version_siblings_count = deleted_version_el.siblings().length;
                            let video_version_siblings = deleted_version_el.siblings();
                            //remove video-version
                            video_version_siblings.each(function (index, item) {
                                $(item).attr('data-index', video_version_siblings_count - index - 1);
                            });
                            //check scene is root
                            if (data.is_root_scene) {
                                let video_variation_el = target.parents('.modal-body').find('.video-variation[data-variation-id=' + scene_id + ']').first();
                                let current_index = video_variation_el.index();
                                current_index = (current_index - 1 < 0) ? 0 : current_index - 1;
                                let data_variation = video_variation_el.attr('data-variation');
                                let video_version_children = video_variation_el.find('li.video-version');
                                let left_el = target.parents('.modal-body').find('.video-modal__left');
                                let right_el = target.parents('.modal-body').find('.video-modal__right');
                                if (video_version_children.length <= 1) {
                                    right_el.find('.video-item-bullet[data-variation=' + data_variation + ']').remove();
                                    right_el.find('.video-item-component[data-variation=' + data_variation + ']').remove();
                                    video_variation_el.remove();

                                    //update value for next/prev
                                    let variation_order = [];

                                    left_el.find('.video-variation').each(function (index, item) {
                                        $(item).attr('data-variation', index);
                                        $(item).find('[data-variation]').attr('data-variation', index);
                                        variation_order.push(index);
                                    });
                                    left_el.find('.modal-tree').attr('data-variation-order', variation_order.toString());
                                    //update bullet index
                                    right_el.find('.video-item-bullet-list .video-item-bullet').each(function (index, item) {
                                        $(item).attr('data-variation', index);
                                    });
                                    //update video item list index
                                    right_el.find('.video-item-list .video-item-component').each(function (index, item) {
                                        $(item).attr('data-variation', index);
                                        $(item).find('[data-variation]').attr('data-variation', index);
                                    });
                                    //click current index
                                    left_el.find('.video-variation').eq(current_index).find('.video-version span').trigger('click');
                                } else {
                                    video_variation_el.attr('data-variation-id', data.new_root_id);
                                    video_variation_el.find('.variation-upload').attr('data-variation-id', data.new_root_id);
                                }
                            }
                            //delete element
                            deleted_version_el.remove();
                            //replace variation on address if removed
                            let current_url = new URL(window.location.href);
                            let url_params = new URLSearchParams(current_url.search);
                            let variation_param = url_params.get('variation');
                            if (variation_param) {
                                if (variation_param === scene_id) {
                                    let video_version = $('.video-version').first();
                                    if (video_version.length > 0) {
                                        url_params.set('variation', video_version.attr('data-scene-id'));
                                    } else {
                                        url_params.delete('variation');
                                    }

                                    current_url.search = url_params.toString();
                                    let new_url = current_url.toString();
                                    window.history.pushState({path: new_url}, '', new_url);
                                }
                            }
                            //click first
                            video_version_siblings.first().find('span').trigger('click');

                            let scenes = $('.pd-scene-title-detail');
                            $('.pd-scene').empty();
                            $('.pd-scene').append(data.html);
                            setActionForProjectItem(scenes);
                            newWavesurferInit();
                            projectScene();
                            actionBottomVideo(scenes)
                        } else {
                            let new_url = window.location.href.split('/scene')[0];
                            window.location.href = new_url
                        }

                    },
                    error: function (data) {
                        toastr.error("エラーが発生しました", "動画削除");
                    }
                })
            }
        }
    });
}

function reloadSceneTittleAfterEdit(scenetitle_id) {
    let project_video_item = $('.project-video-item[data-scene-title-id=' + scenetitle_id + ']');

    $.ajax({
        url: '/top/get_scenetitle_videos',
        type: 'GET',
        data: {scene_title_id: scenetitle_id},
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            project_video_item.replaceWith(response.html);
            let new_project_video_items = $('.project-video-item[data-scene-title-id=' + scenetitle_id + ']')
            new_project_video_items.addClass('show-comment');
            new_project_video_items.attr('updated', 'updated');
            $(new_project_video_items).each(function (index, item) {
                setActionForProjectItem($(item));
            });
        },
        error: function (response) {
            toastr.error(response.responseJSON.message);
            if (response.responseJSON.scene_count === 0) {
                let new_project_video_items = $('.project-video-item[data-scene-title-id=' + scenetitle_id + ']');
                new_project_video_items.addClass('hide');
                let processingSceneModal = $('#processingSceneModal');
                if (processingSceneModal.is(':visible')) {
                    processingSceneModal.modal('hide');
                }
            }
        }
    });
}

function reloadSceneTitleInProgressTab(scene_title_id) {
    if (!scene_title_id) {
        return
    }
    let project_process_els = $(`.project-delivery-item-content[data-scene-title=${scene_title_id}], .project-chapter-video-item-content[data-scene-title=${scene_title_id}]`);
    if (project_process_els.length > 0) {
        $.ajax({
            url: '/top/get_scenetitle_process_videos',
            type: 'GET',
            data: {
                scene_title_id: scene_title_id
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                project_process_els.each(function (index, item) {
                    let _parent = $(item).parent();
                    if (_parent.length > 0) {
                        if (_parent.is('.project-chapter-video-item')) {
                            _parent.parents('.project-chapter-item').find('.productscene-name').text(response.product_scene_name);
                        }
                        _parent.replaceWith(response.html);
                    }
                });
            },
            error: function (response) {
                toastr.error(response.responseJSON.message);
                let scene_count = response.responseJSON.scene_count;
                if (scene_count === 0) {
                    project_process_els.each(function (index, item) {
                        let _parent = $(item).parent();
                        if (_parent.is('.project-chapter-video-item')) {
                            _parent_siblings = _parent.siblings('.project-chapter-video-item');
                            if (_parent_siblings.length === 0) {
                                _parent.parents('.project-chapter-item').remove();
                            } else {
                                _parent.remove();
                            }
                        } else {
                            _parent.remove();
                        }
                    });
                }
            }
        });
    }
}
