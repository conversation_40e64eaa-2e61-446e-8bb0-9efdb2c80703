//=require malihu-custom-scrollbar-plugin/jquery.mCustomScrollbar.concat.min.js

$(document).ready(function () {
    editInforOffer();
    editOffer();
});

var is_editing = false;

function fillValueToModal(offer_id){
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/messenger/get_information_offer",
        data: {
            'offer_id': offer_id,
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            $("#deadline-date").val(
              moment(new Date(response.deadline)).format("YYYY/M/D")
            );
            // $('#deadline-date').datepicker('setDate', moment(new Date(response.deadline)).format('YYYY/M/D'));
            $('.select-deadline_time').datetimepicker({ format: 'HH:mm' });
            $('.select-deadline_time').val(response.time_deadline);
            if(response.selected_job_type && response.selected_job_type !== null){
                const listJobType = response.selected_job_type?.split(',');
                console.log("listJob", listJobType);
                $('.modal-create-edit-offer').find('.job-title-container .tab-content-offer .skills-item-offer').each(function() {
                    if(listJobType.includes($(this).attr('data-id'))) {
                        const indexTab = $(this).closest('.tab-pane-offer').attr('data-index');
                        const tabId = $(this).closest('.tab-pane-offer').attr('id');
                        countSkillsOffer(tabId, $(`.tabs-skill-offer .offer-form[data-index="${indexTab}"]`).find('.selected').length);
                        $(this).trigger('click');
                    }
                })
            }
            response.allow_subcontracting ? $('.allow_subcontracting').addClass('checked'): $('.allow_subcontracting').removeClass('checked');
            response.allow_subcontracting ? $('#allow_subcontracting').attr('checked', true) : $('#allow_subcontracting').attr('checked', false);
            $('#input-scenes').val(response.scenes);
            $('#input-quantity').val(response.quantity);
            $('#id_data_format').val(response.data_format);
            $('#id_data_format').closest('.input-select-container').find('ul .selected').removeClass('selected');
            $('#id_data_format').attr('data-type', 'input');
            $('#id_data_format').closest('.input-select-container').find('ul .list-input-select').each(function(){
                if($(this).attr('data-value') === response.data_format) {
                    $(this).addClass('selected');
                    $('#id_data_format').attr('data-type', 'select');
                }
            });
            $('#input-message').val(response.message);
            $('#id_total_amount').html(parseFloat(response.reward).toLocaleString(undefined));
            $('#modal-edit-offer').attr('data-offer', offer_id);
            
            const valTime = response.valid_date ? response.valid_date.split('T')[1].split(':')[0] + ":"+ response.valid_date.split('T')[1].split(':')[1] :
            response.deadline.split('T')[1].split(':')[0] + ":"+ response.deadline.split('T')[1].split(':')[1]
            $('#valid_time_offer').val(valTime);
            $('#input-remarks').val(response.note);

            let valMethod = response.pick_up_method
            $(document).find('#id_pickup_offer').attr('value', valMethod);
            if(!!valMethod && valMethod!=='None' && valMethod!=="undefined") {
                $('#id_pickup_offer').val(valMethod);
                $('#id_pickup_offer').closest('.input-select-container').find('ul .selected').removeClass('selected');
                $('#id_pickup_offer').attr('data-type', 'input');
                $('#id_pickup_offer').closest('.input-select-container').find('ul .list-input-select').each(function(){
                    if($(this).attr('data-value') === valMethod) {
                        $(this).addClass('selected');
                        $('#id_pickup_offer').attr('data-type', 'select');
                    }
                });
            }

            let valDelivery = response.delivery_place
            $(document).find('#id_delivery_place_offer').attr('value', valDelivery);
            if(!!valDelivery && valDelivery!=='None' && valDelivery!=="undefined") {
                $('#id_delivery_place_offer').val(valDelivery);
                $('#id_delivery_place_offer').closest('.input-select-container').find('ul .selected').removeClass('selected');
                $('#id_delivery_place_offer').attr('data-type', 'input');
                $('#id_delivery_place_offer').closest('.input-select-container').find('ul .list-input-select').each(function(){
                    if($(this).attr('data-value') === valDelivery) {
                        $(this).addClass('selected');
                        $('#id_delivery_place_offer').attr('data-type', 'select');
                    }
                });
            }

            $(document).find('.tab-note-form-offer', function() {
                $(this).find('.component-tab .nav-item.active').removeClass('active')
                $(this).find('.component-tab .nav-item .nav-link[data-target="#'+response.note_type+'"]').addClass('active')
                $(this).find('.tab-content .tab-pane.active').removeClass('active')
                $(this).find('.tab-content .tab-pane[id="'+response.note_type+'"]').addClass('active')
            })

            let reward = response.reward;
            let startDate = response.range_deadline ? moment(new Date(response.range_deadline?.split(' ')[0])) : moment(new Date(response.start_time));
            let endDate = response.range_deadline ? moment(new Date(response.range_deadline?.split(' ')[2])) : moment(new Date(response.deadline));
            let valid_date = response.valid_date ? new Date(response.valid_date?.split('T')[0]) :  new Date(endDate.format('YYYY/MM/DD'))
            var defaultDate = [
              new Date(),
              moment(new Date()).add(1, "months").toDate(),
            ];
            if (startDate && endDate) {
              defaultDate = [
                moment(startDate).toDate(),
                moment(endDate).toDate(),
              ];
            }
            // $('#id_valid_offer').datepicker('remove');
            flatpickr("#deadline-date", {
              mode: "single",
              dateFormat: "Y/m/d",
              showMonths: 1,
              onOpen: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .addClass("is-icon-active");
              },
              onClose: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .removeClass("is-icon-active");
              },
              onChange: function (selectedDates, dateStr, instance) {
                let startDate = selectedDates[0];
                let endDate = selectedDates[1];
                $(instance.element).attr(
                  "data-start-time",
                  moment(startDate).format("yyyy-MM-DD HH:mm:ss")
                );
                if (endDate)
                  $(instance.element).attr(
                    "data-end-time",
                    moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                  );
              },
              // minDate: "today",
              // maxDate: new Date().fp_incr(120),
            });
            if(new Date(moment(valid_date).format("YYYY/MM/DD")).getTime() <= new Date().getTime()) {
                $("#id_valid_offer").val(formatDate(new Date()));
            } else {
                $("#id_valid_offer").val(moment(valid_date).format("YYYY/MM/DD"));
            }
            // $('#id_valid_offer').val(moment(valid_date).format('YYYY/MM/DD'));
            // $('#id_valid_offer').datepicker('setStartDate', new Date(valid_date));
            // $('#id_valid_offer').datepicker('setEndDate', new Date(valid_date));
            flatpickr("#id_valid_offer", {
              mode: "single",
              dateFormat: "Y/m/d",
              defaultDate:  $('#id_valid_offer').val(),
              showMonths: 1,
              onOpen: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .addClass("is-icon-active");
              },
              onClose: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .removeClass("is-icon-active");
              },
              onChange: function (selectedDates, dateStr, instance) {
                let startDate = selectedDates[0];
                let endDate = selectedDates[1];
                $(instance.element).attr(
                  "data-start-time",
                  moment(startDate).format("yyyy-MM-DD HH:mm:ss")
                );
                if (endDate)
                  $(instance.element).attr(
                    "data-end-time",
                    moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                  );
              },
              // minDate: "today",
              // maxDate: new Date().fp_incr(120),
            });
            flatpickr("#id_period_offer", {
              mode: "range",
              dateFormat: "Y/m/d",
              defaultDate: defaultDate,
              showMonths: 1,
              onOpen: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .addClass("is-icon-active");
              },
              onClose: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .removeClass("is-icon-active");
              },
              onChange: function (selectedDates, dateStr, instance) {
                let startDate = selectedDates[0];
                let endDate = selectedDates[1];
                $(instance.element).attr(
                  "data-start-time",
                  moment(startDate).format("yyyy-MM-DD HH:mm:ss")
                );
                if (endDate)
                  $(instance.element).attr(
                    "data-end-time",
                    moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                  );
              },
              // minDate: "today",
              // maxDate: new Date().fp_incr(120),
            });
            let total_amount = parseFloat(reward);
            let tmp_reward = Math.round(total_amount / 1.1);
            let tax_amount = Math.round(tmp_reward * 0.1);
            $('#budget').val(tmp_reward.toLocaleString(undefined));

            $('#id_tax_amount').html((tax_amount).toLocaleString(undefined));

            $('.mattach-info-file').remove();
            let files = response.files;
            let file_names = files.map((file) => {
                return file.real_name;
            })
            let file_html = file_names.map((name) => {
                return  `<div class="mattach-info mattach-info-file" data-dz-thumbnail="">
              <div class="mcommment-file">
                <div class="mcommment-file__name mcommment-file__name-form" data-dz-name="">
                  <i class="icon icon--sicon-clip"></i><p class="file-name">${name}</p>
                </div>
                <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                  <i class="icon icon--sicon-close hide"></i>
                </div>
              </div>
            </div>`
            }).join('')
            $(`<div class="upload_file_container">${file_html}</div>`).insertAfter($('.sform-group__label[for="create-offer-upload-form"]'));

            if (response.can_edit_reward) {
                $('.modal-create-edit-offer #budget').attr('readonly', false)
            } else {
                $('.modal-create-edit-offer #budget').attr('readonly', true)
            }
            $('#id_contract').val(response.contract);

            addDataList($('#offer_scenes'), response.list_scenes);
            addDataList($('#offer_format'), response.list_data_format);
            addDataList($('#offer_quantity'), response.list_quantity);
            if (response.offer_status !== '1') {
                $(document).find('.modal-create-edit-offer .skills-item-offer').each(function(){
                    $(this).addClass('disabled');
                })
                let array_values = ['id_data_format', 'id_contract', 'input-message'];
                for (i = 0; i < array_values.length; i++) {
                    $('#' + array_values[i]).attr('disabled', true);
                }
                $('#modal-edit-offer .account_upload-file.mattach, .mattach-info-file .icon--sicon-close').addClass('hide');
            } else {
                $(document).find('.modal-create-edit-offer .skills-item-offer').each(function(){
                    $(this).removeClass('disabled');
                })
                let array_values = ['id_data_format', 'id_contract', 'input-message'];
                for (i = 0; i < array_values.length; i++) {
                    $('#' + array_values[i]).attr('disabled', false);
                }
                $('#modal-edit-offer .account_upload-file.mattach, .mattach-info-file .icon--sicon-close').addClass('hide');
            }
            is_editing = true;
            intitActionForm();
        }
    })
}

function editInforOffer() {
    $(document).on('click', '.button-edit_offer', function (e) {
        $('.accordion-heading').attr('data-toggle', '');
        let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
        if (!offer_id) {
            offer_id = $(this).parents('.mcontent').find('.maction').attr('data-offer');
        }
        if (offer_id) {
            fillValueToModal(offer_id);
        }
    });

    // $(document).on('click', '.button-delete_offer', function (e) {
    //     $('.accordion-heading').attr('data-toggle', '');
    //     let offer_id = $('.mitem.mactive').attr('data-offer');
    //     if (offer_id) {
    //         $('#delete-offer').attr('data-offer', offer_id);
    //         rejectOfferCreator();
    //     }
    // })

}

function rejectOfferCreator() {
    $(document).on('click', '#delete-offer .btn-popup-delete', function () {
        let offer_id = $('#delete-offer').attr('data-offer');
        let button_dom = $(this);
        if (offer_id && !button_dom.hasClass('disable')) {
            button_dom.addClass('disable');
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/messenger/ajax/update_reject_offer",
                data: {
                    'offer_id': offer_id,
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    console.log('ok');
                },
                complete: function () {
                    $('#delete-offer').modal('hide');
                    button_dom.removeClass('disable')
                }
            })
        }
    })
}

function editFormOffer() {
    let id_form = 'modal-edit-offer';
    let modal_edit = $('#' + id_form);
    actionForm(modal_edit);
    resetForm(modal_edit);
}

function editOffer() {
    $(document).on('click', '.edit-offer-submit:not(.disabled)', function () {
        $('.errorlist').remove();
        $('.error-border').removeClass('error-border');
        let buttom_dom = $(this);
        let data_form = new FormData();
        let deadline = $('#deadline-date').val();
        let time_deadline = $('.select-deadline_time').val();
        let contract = $('#id_contract').val();
        let scenes = $('#input-scenes').val();
        let quantity = $('#input-quantity').val();
        let data_format = $('#id_data_format').val();
        let message = $('#input-message').val();
        let reward = $('#id_total_amount').html();
        let offer_id = $('#modal-edit-offer').attr('data-offer');
        let valid_date = $('#id_valid_offer').val();
        let valid_time = $('#valid_time_offer').val();
        let note = $('#input-remarks').val() ?? '';
        let pick_up_method = $('#id_pickup_offer').val();
        let delivery_place = $('#id_delivery_place_offer').val();
        let period = $('#id_period_offer').val();
        let allow_subcontracting = $('.allow_subcontracting').hasClass('checked') ? 1 : 0;
        let selected_job_type = []
        $('.skills-item-offer.selected').each(function() {
            selected_job_type.push($(this).attr('data-id'));
        })
        valid_date = valid_date + ' ' + valid_time;
        let is_blank = checkValidateBlank(['#budget', '#id_contract']);
        if (is_blank) {
            return false
        }

        $(this).addClass('disabled');

        data_form.append('range_deadline', period);
        data_form.append('contract', contract);
        data_form.append('scenes', scenes);
        data_form.append('quantity', quantity);
        data_form.append('data_format', data_format);
        data_form.append('message', message);
        data_form.append('reward', reward);
        data_form.append('time_deadline', time_deadline);
        data_form.append('offer_id', offer_id);
        data_form.append('valid_date', valid_date);
        data_form.append('note', note);
        data_form.append('pick_up_method', pick_up_method);
        data_form.append('delivery_place', delivery_place);
        data_form.append('allow_subcontracting', allow_subcontracting);
        data_form.append('selected_job_type', selected_job_type);
        data_form.append('deadline', deadline);

        $.ajax({
            type: "POST",
            contentType: false,
            processData: false,
            cache: false,
            url: "/messenger/check_edit_offer",
            data: data_form,
            success: function (data) {
                data_form.append('key_file', key_file);
                data_form.append('real_name', real_name);
                data_form.append('is_delete_file', is_delete_file);
                if (data.budget === 'over' || data.reward === 'less' || data.deadline === 'less' || data.status === 'failed') {
                    if (data.budget === 'over') {
                        $("#over-budget").modal();
                    } else {
                        let modal_error = $("#modal-reward");
                        modal_error.modal();
                        modal_error.find('.text1').html('');
                        modal_error.find('.text2').html('');
                        if (data.reward === 'less' || data.deadline === 'less') {
                            modal_error.find('.text1').html(data.message);
                        } else {
                            modal_error.find('.text1').html(data.message1);
                            modal_error.find('.text2').html(data.message2);
                        }
                    }
                } else {
                    if (is_uploading) {
                        let timeoutUpload = setTimeout(function () {
                            clearInterval(waiting_file_loading);
                            toastr.error(gettext('Something went wrong!'));
                        }, 900000);
                        let waiting_file_loading = setInterval(function () {
                            let progress = getProgressUploaded();
                            let progressDom = $('.upload-button-wrapper');
                            activeProgress();
                            progressDom.find('.fill .process').css('width', progress + '%');
                            if (!is_uploading) {
                                clearTimeout(timeoutUpload);
                                clearInterval(waiting_file_loading);
                                sendAjaxCreateEditOffer(data_form, buttom_dom, true, 'edit')
                            }
                        }, 100);
                    } else {
                        sendAjaxCreateEditOffer(data_form, buttom_dom, false, 'edit')
                    }
                }
            },
            error: function (data) {
                toastr.error(gettext('Something went wrong!'));
                $(this).removeClass('disabled');
            },
            complete: function () {
                buttom_dom.removeClass('disabled');
            }
        });
    });
}


function resultEditOffer(data) {
    if (data.infor_offer) {
        setTimeout(function () {
            // toastr.success(gettext('I made an offer.'));
        }, 1000);
        setTimeout(function () {
            sScrollbarBottom();
            $("html, body").animate({scrollTop: $('.mcommment').height() + 200}, 1000);
        }, 2000);
        $('#modal-edit-offer').modal('hide');
        $('.infor-offer').html(data.infor_offer);
        is_editing = false;
        intitActionForm();
    }
}

function resultEditOfferWithUploading(data) {
    let progressDom = $('.upload-button-wrapper');
    if (data.infor_offer) {
        progressDom.find('.fill .process').css('width', '100%');
        setTimeout(function () {
            // toastr.success(gettext('I made an offer.'));
            progressDom.removeClass('clicked').addClass('success')
        }, 1000);
        setTimeout(function () {
            removeProgress();
            sScrollbarBottom();
            $("html, body").animate({scrollTop: $('.mcommment').height() + 200}, 1000);
        }, 2000);
        $('#modal-edit-offer').modal('hide');
        $('.infor-offer').html(data.infor_offer);
    }
}

function get_content_modal_contract(offer) {
    offer_id = offer.split(" ")[0];
    offer_status = offer.split(" ")[1];
    data = {'id': offer_id};
    if(offer_status){
        data = {'id': offer_id, "status": offer_status}
    }
    if (offer_id) {
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/get_content_contract_modal",
            data: data,
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                $('#modal-pdf-approve-popup').find('iframe').attr('src', 'about:blank');
                setTimeout(() => {
                    $('#modal-pdf-approve-popup').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(response.file) + '#zoom=page-width');
                    $('#modal-pdf-approve-popup').modal('show');
                    if(user_role === 'admin' || user_role === 'master_admin') {
                        scrollBottomPDFArtist($('#modal-pdf-approve-popup').find('iframe'), $('#modal-pdf-approve-popup'));
                    }
                }, 100)
            }
        })
    }
}

function ajaxAcceptOffer(offer_id, active_offer) {
    $('.mmessenger-director__item-action-accept').addClass('disable');
    $.ajax({
        type: "POST",
        data: {
            'offer_id': offer_id
        },
        url: '/messenger/offer_accept',
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            $('.mcomment-attach-label').removeClass('hide');
            $('.mcomment-input-text').trigger('click');
            setTimeout(function () {
                active_offer.find('.mcomment-input-placeholder').show();
                active_offer.find('.mcomment-top').hide();
            }, 100);
            $("html, body").animate({scrollTop: $('.mcommment').height() + 200}, 1000);
            $('#modal-confirm-upload').modal('hide')
            $('#modal-confirm-upload').removeAttr('data-modal-type data-project-id data-file-id data-message-id')
        },
        error: function () {
            $('.mmessenger-director__item-action-accept').removeClass('disable');
            toastr.error(gettext('Something went wrong!'));
        }
    });
}
