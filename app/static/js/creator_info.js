$(document).ready(function(){
    $('#id_dob').datepicker({
        format: 'yyyy/mm/dd',
        locale: 'ja'
    });
    let geo_info = [$('#id_province'), $('#id_city')];
    function comboDate() {
      
        var self = $('#id_dob');
        var format = self.data('format');
        var template = 'YYYY MMM D';
        var value = self.data('value');

        self.combodate({
            format: format,
            template: template,
            value: value,
            firstItem: 'name',
            smartDays: true,
            minYear: 1930,
            maxYear: moment().format('YYYY')  
        }).on('change', function() {
            self.attr('data-value', self.val());
        });
          
      };
      comboDate();

    function fixJaCombodate() {
        let year_elements = $('.combodate select.year option');
        $(year_elements).each(function () {
            let year = $(this).text();
            if (!(isNaN(year))) {
                $(this).text(year + '年');
            }
        });

        let month_elements = $('.combodate select.month option');
        $(month_elements).each(function () {
            let month = $(this).text();
            if (!(isNaN(month))) {
                $(this).text(month + '月');
            }
        });

        let day_elements = $('.combodate select.day option');
        $(day_elements).each(function () {
            let day = $(this).text();
            if (!(isNaN(day))) {
                $(this).text(day + '日')
            }
        });
    }

    fixJaCombodate();

    $('input[id^="id_checkout_setting"]').on('click', function(){
        $(this).attr('checked', 'checked');
        index= $(this).attr('index');
        index_sib = index == 1 ? 0 : 1
        $('#id_checkout_setting_' + index_sib).removeAttr('checked')
    })

    $('.input-checkbox').on('click', function(){
        $(this).find('input').attr('checked', !$(this).find('input').attr("checked"))
    })

    $('#get_zip_code').on('click', function(){
        let zip_code = $('#id_post_number').val();
        if(zip_code) {
            zip_code = zip_code.replace(/[^0-9]/gm, '');
            let url = 'https://apis.postcode-jp.com/api/v4/postcodes/' + zip_code + '?apiKey=ng0RDOveBSGo11VtLNlaV2gdviVzBb3e6hUvFhn';
            $.ajax({
                url: url,
                dataType: 'jsonp',
                beforeSend: function() {
                    $('.loading-icon').removeClass('hide');
                },
                success: function(data) {
                    $('.loading-icon').addClass('hide');
                    $('#id_post_number').val(zip_code);
                    $('#id_post_number').data('value', zip_code);
                    $('#id_province').val(data[0].pref);
                    $('#id_city').val(data[0].city);
                    $('#id_province').parent().find('p.form-error').remove();
                    $('#id_province')[0].removeAttribute("style");
                    $('#id_city').parent().find('p.form-error').remove();
                    $('#id_city')[0].removeAttribute("style");
                },
                complete: function() {
                    $('.loading-icon').addClass('hide');
                }
            })
        }
    });

    $('#id_phone').on('input', function(e){
        if (this.value.length > 30) {
            this.value = this.value.slice(0, 30);
        }
    })

    $('#creator_info_form').submit(function (e) {
        //check post number
        var post_number = $('#id_post_number').val().trim();
        if (post_number) {
            $('#id_post_number').val(post_number.replace(common_not_number_regex, ''));
        }

        //check phone
        var phone = $('#id_phone').val().trim();
        if (phone) {
            $('#id_phone').val(phone.replace(common_not_number_regex, ''));
        }
        phone = $('#id_phone').val().trim();

        if (phone.length > 30) {
            $('#id_phone').val(phone.slice(0, 30));
        }
    });
});


