var max_length_message = 1000;
var work_contents = {
    "1": {"price": "800000", "unit": "1"},
    "2": {"price": "800000", "unit": "1"},
    "3": {"price": "800000", "unit": "1"},
    "4": {"price": "800000", "unit": "1"},
    "5": {"price": "100000", "unit": "2"},
    "6": {"price": "30000", "unit": "3"},
    "7": {"price": "50000", "unit": "4"},
    "8": {"price": "10000", "unit": "5"},
    "9": {"price": "100000", "unit": "6"},
    "10": {"price": "40000", "unit": "7"},
    "11": {"price": "40000", "unit": "7"},
    "12": {"price": "300000", "unit": "2"},
    "13": {"price": "200000", "unit": "2"},
    "14": {"price": "100000", "unit": "2"},
    "15": {"price": "50000", "unit": "2"},
    "16": {"price": "50000", "unit": "4"},
    "17": {"price": "125000", "unit": "2"}
}

var list_units = [
    {"id": "1", "value": "人月"},
    {"id": "2", "value": "曲"},
    {"id": "3", "value": "演出"},
    {"id": "4", "value": "名"},
    {"id": "5", "value": "時間"},
    {"id": "6", "value": "式"},
    {"id": "7", "value": "人日"}
]

var no_changed_creation_method = true;

function autoFillWorkContent() {
    $(document).on('click', '#modal-upload-plan .contract__form-input.input_item .input-select-container ul li', function(){
        setTimeout(() => {
            let typeInput = $(this).parents('.input-select-container').find('.input-select').attr('data-type');
            if(typeInput == 'select') {
                let workType = $(this).attr('data-value');
                $(this).parents('.contract__form-content-item').find('.contract__form-price input').val(work_contents[workType]['price']).trigger('input');
                $(this).parents('.contract__form-content-item').find(`.contract__form-input.input_unit .input-select-container ul li[data-value=${work_contents[workType]['unit']}]`).trigger('click');
            }
        }, 100);
    })
}


function validateContractForm() {
    // Maxlength msg
    let noteDom = $('.tab-note-form-contract .tab-pane.active #id_note');
    let noteValue = noteDom.val();

    if (typeof noteValue === "undefined") {
        return
    }

    let isChrome = window.chrome;
    if (isChrome) {
        noteValue = noteDom.val().replace(/(\r\n|\n|\r)/g, '  ');
    }
    if (noteValue.length > 1000) {
        noteDom.val(noteValue.slice(0, max_length_message))
    }

    let initial_atesaki = $('label[for="id_owner_infor"]').attr('data-initial');
    if(!$('input#id_owner_infor').val()) {
        $('input#id_owner_infor').val(initial_atesaki)
    }

    // Total money
    totalMoney();

    // Range time
    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    };
    let startDate = moment(new Date()).add(7, 'days');
    let endDate = moment(startDate).add(1, 'months');
    let idFormContractAndPlan = $('#modal-upload-contract #id-form-contract-and-plan').data('value');
    var schedule = $(document).find('.tab-option-form-contract .tab-content .tab-pane.active .js-daterangepicker');

    $(document).on('click', '.tab-option-form-contract .nav-item', function(){
        var target = $(this).find('a.nav-link').attr('data-target')
        var schedule1 = $(document).find('.tab-option-form-contract .tab-content .tab-pane.active .js-daterangepicker');
        let currentSchedule1 = $('#modal-upload-contract .tab-option-form-contract .tab-content .tab-pane:not(' + target +') .js-daterangepicker').val();
        schedule1.addClass('custom-datepicker')
        if (currentSchedule1) {
            startDate = new Date(currentSchedule1.split(' - ')[0]);
            endDate = new Date(currentSchedule1.split(' - ')[1]);
            // schedule1 = schedule1.daterangepicker({
            //     startDate: startDate,
            //     endDate: endDate,
            // });
        } else {
            schedule1 = schedule.daterangepicker({});
            schedule1.val('');
        }
        // }
        $('.tab-option-form-contract .tab-content .tab-pane.active #id_deadline_d').datepicker( "destroy");
        schedule1.on('change', function () {
            if (!$('.tab-option-form-contract .tab-content .tab-pane.active #id_deadline_d').val()) {
                $('.tab-option-form-contract .tab-content .tab-pane.active #id_deadline_d').datepicker('setDate', formatDate($('.tab-option-form-contract .tab-content .tab-pane.active .js-daterangepicker').data('daterangepicker').endDate)).datepicker( "destroy");
            }
        });
        
        if (!$('.tab-option-form-contract .tab-content .tab-pane.active #id_deadline_d').val()) {
            $('.tab-option-form-contract .tab-content .tab-pane.active #id_deadline_d').datepicker('setDate', formatDate(endDate)).datepicker( "destroy");
        } else {
            // $('.tab-option-form-contract .tab-content .tab-pane.active #id_deadline_d').datepicker('setDate', formatDate($('.tab-option-form-contract .tab-content .tab-pane.active #id_deadline_d').val().split(':')[0])).datepicker( "destroy");
        }
    })

    // Datepicker
    if ($('#modal-upload-contract .mcalendar.mcalendar--small, #modal-upload-plan .mcalendar.mcalendar--small').length > 0) {
        var date = new Date();
        $('#modal-upload-contract .mcalendar.mcalendar--small, #modal-upload-plan .mcalendar.mcalendar--small').each(function () {
            $(this).datepicker({
                inline: true,
                weekStart: 1,
                format: 'yyyy/mm/dd',
                locale: 'ja',
                todayHighlight: true,
                todayBtn: true,
                debug: true,
                autoclose: true,
                forceParse: false,
                icons: {
                    previous: 'icon icon--sicon-prev',
                    next: 'icon icon--sicon-next',
                },
            });
        })

        // $(document).on('show.bs.modal', '#modal-upload-contract, #modal-upload-plan', function() {
        let default_value = $('#modal-upload-contract #id-form-contract-and-plan').attr('default-value-time-form');
        $(document).find('.tab-option-form-contract .tab-pane #id_time').each(function() {
            if (!$(this).val()) {
                $(this).datetimepicker({ format: 'HH:mm', ignoreReadonly: true });
                let time_deadline_default = default_value
                if(!time_deadline_default) {
                    time_deadline_default = '10:00';
                } else {
                    time_deadline_default = time_deadline_default.split(';')[0].split(' ')[1];
                }
                $(this).val(time_deadline_default);
            } else {
                $(this).datetimepicker({ format: 'HH:mm', ignoreReadonly: true })
                $(this).val($(this).val());
            }
        });

        $(document).find("#valid_time").datetimepicker({ format: 'HH:mm', ignoreReadonly: true });

        if(!$("#valid_time").val() && $("#id_time").val()) {
            $("#valid_time").val($("#id_time").val())
        }
        $('#id_pre_deadline').datepicker("destroy");
        if (!idFormContractAndPlan) {
            let default_value_deadline_date = default_value;
            if(!default_value_deadline_date) {
                default_value_deadline_date = endDate;
            } else {
                default_value_deadline_date = default_value_deadline_date.split(';')[0].split(' ')[0];
            }
            if(!$('#id_pre_deadline').val()) {
                $('#id_pre_deadline').datepicker('setDate', formatDate(default_value_deadline_date)).datepicker("destroy");
                $('#id_pre_deadline').val(formatDate(default_value_deadline_date))
            }

            if(!$('#id_deadline_d').val()) {
                $('#id_deadline_d').datepicker('setDate', formatDate($('#id_pre_deadline').val().split(':')[0])).datepicker("destroy");
            }
        } else {
            if(!$('#id_deadline_d').val() && $('#id_pre_deadline').val()) {
                $('#id_deadline_d').datepicker('setDate', formatDate($('#id_pre_deadline').val().split(':')[0])).datepicker("destroy");
            } else {
                $('#id_deadline_d').datepicker('setDate', formatDate($('#id_deadline_d').val().split(':')[0])).datepicker("destroy");
            }
        }

        if (!$('#id_valid').val()) {
            if ($('#id_pre_deadline').length < 1 || !$('#id_pre_deadline').val()) {
                var valid_date = date;
                valid_date = new Date(valid_date.setMonth(valid_date.getMonth()+1));
                $('#id_valid').datepicker('setDate', formatDate(valid_date));
            } else {
                $('#id_valid').datepicker('setDate', formatDate($('#id_pre_deadline').val().split(':')[0]));
                $('#id_valid').val(formatDate($('#id_pre_deadline').val().split(':')[0]));
            }
        } else {
            $('#id_valid').datepicker('setDate', formatDate($('#id_valid').val().split(':')[0]));
        }

        if (!$('#id_issue').val()) {
            $('#id_issue').datepicker('setDate', formatDate(date)).datepicker( "destroy");  ;
        } else {
            $('#id_issue').datepicker('setDate', formatDate($('#id_issue').val().split(':')[0])).datepicker( "destroy");
        }

        if ($('#id_pre_deadline').val()) {
            date = new Date();
            setRangeDateValid(date, $('#id_pre_deadline').val().split(':')[0]);
        }
        
        if (!idFormContractAndPlan) {
            let schedule_value = default_value
            let start_schedule = startDate;
            let end_schedule = endDate;
            if(!!schedule_value){
                start_schedule = schedule_value.split(';')[1].split(' - ')[0];
                end_schedule = schedule_value.split(';')[1].split(' - ')[1];
            }
            schedule.addClass('custom-datepicker')
            // schedule = schedule.daterangepicker({
            //     startDate: start_schedule,
            //     endDate: end_schedule,
            // });
        } else {
            let currentSchedule = $('#modal-upload-contract-plan .tab-option-form-contract .tab-content .tab-pane.active .js-daterangepicker').val();
            if (currentSchedule) {
                startDate = new Date(currentSchedule.split(' - ')[0]);
                endDate = new Date(currentSchedule.split(' - ')[1]);
                schedule.addClass('custom-datepicker')
                // schedule = schedule.daterangepicker({
                //     startDate: startDate,
                //     endDate: endDate,
                // });
            } else {
                if(default_value) {
                    let schedule_value = default_value
                    let start_schedule = startDate;
                    let end_schedule = endDate;
                    if(!!schedule_value){
                        start_schedule = schedule_value.split(';')[1].split(' - ')[0];
                        end_schedule = schedule_value.split(';')[1].split(' - ')[1];
                    }
                    schedule.addClass('custom-datepicker')
                    // schedule = schedule.daterangepicker({
                    //     startDate: start_schedule,
                    //     endDate: end_schedule,
                    // });
                } else {
                    schedule.addClass('custom-datepicker')
                    // schedule = schedule.daterangepicker({
                    //     startDate: startDate,
                    //     endDate: endDate,
                    // });
                }
            }
        }
        // })
        $(document).on('click', '#modal-upload-contract .component-tab-container ul.nav.component-tab li .nav-link[data-target="#upload"], #modal-upload-contract .component-tab-container ul.nav.component-tab li .nav-link[data-target="#generate"]', function(e) {
            $('#modal-upload-contract .btn-upload-file-messenger-owner').toggleClass('disable', !validRequiredFields('#modal-upload-contract'));
        })
    }

    // Toggle
    $(document, '.label_public').on('change', function () {
        if ($('#id_public').is(':checked')) {
            $('#id_public').attr('checked', 'checked');
            $('#id_public_confirm').attr('checked','checked');
        } else {
            $('#id_public').removeAttr('checked');
            $('#id_public_confirm').removeAttr('checked');
        }
    })

    $(document, '.label_delivery').on('change', function () {
        if ($('#id_delivery_checked').is(':checked')) {
            $('#id_delivery_checked').attr('checked', 'checked');
            $('#id_delivery').addClass('disabled');
            $('#id_delivery_checked_confirm').attr('checked','checked');
        } else {
            $('#id_delivery_checked').removeAttr('checked');
            $('#id_delivery').removeClass('disabled');
            $('#id_delivery_checked_confirm').removeAttr('checked');
        }
    })

    // Add row content
    createRowContent();

    $(document).on('input', '#id_deadline_d, #id_issue, #id_valid, #id_pre_deadline', function(e) {
        if (this.value.length > 10) {
            this.value = this.value.slice(0, 10);
        }
        // this.value = this.value.replace(/[^/0-9]/gm, '');
    });

    // $(document).on('focusout', '#id_deadline_d, #id_issue, #id_valid, #id_pre_deadline, #valid_time, #id_time', function(e) {
    //     if (!$(this).val()) {
    //         setInitialDate($(this))
    //     } else {
    //         let selected_date = $(this).val();
    //         if($(this).is('#id_time') || $(this).is('#valid_time')) {
    //             if (selected_date != '' && !moment(selected_date, 'HH:mm', true).isValid()) {
    //                 setInitialDate($(this))
    //             }
    //         } else {
    //             if (selected_date != '' && !moment(selected_date, 'YYYY/MM/DD', true).isValid()) {
    //                 setInitialDate($(this))
    //             }
    //         }
    //     }
    // });

    onChangeContent();
}

function setInitialDate(target_input) {
    let startDate = moment(new Date()).add(7, 'days');
    let endDate = moment(startDate).add(1, 'months');
    let default_value = $('#modal-upload-contract #id-form-contract-and-plan').attr('default-value-time-form');
    let default_value_deadline_date = default_value;
    if(!default_value_deadline_date) {
        default_value_deadline_date = endDate;
    } else {
        default_value_deadline_date = default_value_deadline_date.split(';')[0].split(' ')[0];
    }
    if(target_input.is('#id_deadline_d')) {
        if($('#id_pre_deadline').val()) {
            target_input.datepicker('setDate', formatDate($('#id_pre_deadline').val().split(':')[0])).datepicker("destroy");
        } else {
            target_input.datepicker('setDate', formatDate(default_value_deadline_date)).datepicker("destroy");
        }
    } else if (target_input.is('#id_issue')) {
        target_input.datepicker('setDate', formatDate(moment(new Date()))).datepicker("destroy");
    } else if (target_input.is('#id_valid')) {
        if ($('#id_pre_deadline').length < 1 || !$('#id_pre_deadline').val()) {
            $('#id_valid').datepicker('setDate', formatDate(endDate)).datepicker( "destroy");
        } else {
            if(moment($('#id_pre_deadline').val()).diff(new Date(), 'days') < 0) {
                let todayMoment = moment(new Date())
                target_input.datepicker('setDate', formatDate(todayMoment.add(1,'days'))).datepicker("destroy");
                target_input.val(formatDate(todayMoment));
            } else {
                $('#id_valid').datepicker('setDate', formatDate($('#id_pre_deadline').val().split(':')[0])).datepicker( "destroy");
                $('#id_valid').val(formatDate($('#id_pre_deadline').val().split(':')[0]))
            }
        }

        let time_deadline_default = default_value
        if(!time_deadline_default) {
            time_deadline_default = '10:00';
        } else {
            time_deadline_default = time_deadline_default.split(';')[0].split(' ')[1];
        }
        $('#valid_time').val(time_deadline_default);
    } else if (target_input.is('#id_pre_deadline')) {
        target_input.datepicker('setDate', formatDate(default_value_deadline_date)).datepicker("destroy");
    } else if (target_input.is('#id_time') || target_input.is('#valid_time')) {
        let time_deadline_default = default_value
        if(!time_deadline_default) {
            time_deadline_default = '10:00';
        } else {
            time_deadline_default = time_deadline_default.split(';')[0].split(' ')[1];
        }
        
        if(target_input.is('#id_time')) {
            $('input[name=deadline_hours]').val(time_deadline_default);
        } else {
            target_input.val(time_deadline_default);
        }
    }
}

function setRangeDateValid(startDate, endDate) {
    $('#id_valid').datepicker('setStartDate', startDate).datepicker( "destroy");
    if(Date.parse(endDate) < Date.parse(startDate)) {
        let endMoment = moment(Date.parse(startDate))
        $('#id_valid').datepicker('setEndDate', endMoment.add(1, 'days').format('YYYY/MM/DD', true)).datepicker( "destroy");
        setTimeout(() => {setInitialDate($('#id_valid'))}, 100)
    } else {
        $('#id_valid').datepicker('setEndDate', endDate).datepicker( "destroy");
    }
}

function initDateRangeValid () {
    $(document).on('change', '#id_pre_deadline, #id_issue', function() {
        if ($('#id_issue').val() && $('#id_pre_deadline').val() && moment($('#id_pre_deadline').val(), 'YYYY/MM/DD', true).isValid()&& moment($('#id_issue').val(), 'YYYY/MM/DD', true).isValid()) {
            setRangeDateValid($('#id_issue').val().split(':')[0], $('#id_pre_deadline').val().split(':')[0]);

            let deadline = new Date($('#id_pre_deadline').val().split(':')[0]);
            let valValid = new Date($('#id_valid').val());
    
            if(valValid > deadline) {
                $('#id_valid').datepicker('setDate', formatDate($('#id_pre_deadline').val().split(':')[0])).datepicker( "destroy");
            }
        }
    })

}

// Delete content
$(document).on('click', '.delete-content', function () {
    if($('.contract__form-content-item').length > 1) {
        $(this).parents('.contract__form-content-item').remove();
    }
    $('.contract__form-content-list .contract__form-content-item:last-child .contract__form-quantity').trigger('input');
    reorderContents();
});

function reorderContents(){
    let new_items = $(`.contract__form-content-list`).children();
    if(new_items.length){
        new_items.each(function(item){
            $(this).attr('data-index-order', item+1);
        });
    }
    if(new_items.length < 2) {
        $('.contract__form-action-content').addClass('disabled');
    }

    $('.btn-upload-file-messenger-owner').toggleClass('disable', new_items.hasClass('blank_item') || !validRequiredFields('#modal-upload-plan'));
}

function totalMoney() {
    var price = 0;
    var quantity = 0;
    var total = 0;

    $(document).on('input', '.contract__form-price input', function(e) {
        let intValue = cleanIntValue(this.value.replaceAll(',', ''));
        if (intValue.length > this.maxLength) {
            this.value = intValue.slice(0, this.maxLength - 4);
        }
        this.value = intValue.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        price = intValue;
        quantity =  $(this).parents('.contract__form-content-item').find('.contract__form-quantity').val();

        if(price && quantity) {
            total = parseInt(price) * parseFloat(quantity);
            total = total.toLocaleString('ja');
        }
        else {
            if(price || quantity) {
                total = '0';
            }
        }
        $(this).parents('.contract__form-content-item').find('.contract__form-total span:first-child').html(total);

        // Total
        var total_row = 0;
        var total_tax = 0;
        var total_all = 0;
        var $dataRows=$('.contract__form-content-list>div');

        $dataRows.each(function() {
            $(this).find('.contract__form-total span:first-child').each(function(i){
                total_tmp = $(this).text().replaceAll(',', '');
                total_row += parseInt(total_tmp);
            });
        });
        total_tax = total_row * 0.1;
        total_all = total_row + total_tax;

        $('#id_total_row').html(total_row.toLocaleString('ja'));
        $("#id_tax_pj").html(total_tax.toLocaleString('ja'));
        $("#id_total_pj").html(total_all.toLocaleString('ja'));
    });

    $(document).on('input', '.contract__form-quantity', function(e) {
        var total_row = 0;
        var total_tax = 0;
        var total_all = 0;
        quantity = $(this).val();
        var not_number_regex = /[^0-9\.]/gm;
        if (quantity.length > 30) {
            quantity = quantity.slice(0, 30);
        }
        quantity = quantity.replace(not_number_regex, '');
        quantity = quantity === '' ? quantity : cleanFloatValue(quantity)
        $(this).val(quantity);

        price =  $(this).parents('.contract__form-content-item').find('.contract__form-price input').val();
        let intValue = cleanIntValue(price.replaceAll(',', ''));
        price = intValue;

        if(price && quantity) {
            total = parseInt(price) * parseFloat(quantity);
            total = total.toLocaleString('ja');
        }
        else {
            if(price || quantity) {
                total = '0';
            }
        }
        $(this).parents('.contract__form-content-item').find('.contract__form-total span:first-child').html(total);

        // Total
        var $dataRows=$('.contract__form-content-wrap .contract__form-content-item .contract__form-input');
        $dataRows.each(function() {
            $(this).find('.contract__form-total span:first-child').each(function(i){
                total_tmp = $(this).text().replaceAll(',', '');
                total_row += parseInt(total_tmp);
            });
        });
        total_tax = total_row * 0.1;
        total_all = total_row + total_tax;

        $('#id_total_row').html(total_row.toLocaleString('ja'));
        $("#id_tax_pj").html(total_tax.toLocaleString('ja'));
        $("#id_total_pj").html(total_all.toLocaleString('ja'));
    });

    function cleanFloatValue(val) {
        let quantity_float = val.split('.');
        val = quantity_float.length <= 1 ? replaceZero(quantity_float[0]) : replaceZero(quantity_float.shift()) + '.' + quantity_float.join('');
        return val
    }

    function replaceZero(i) {
        i = i.replace(/^0+/, '')  === '' ? '0' : i.replace(/^0+/, '');
        return i
    }

    function cleanIntValue(val) {
        return (val === '') ? '0' : val
    }
}

function getValueContract() {
    let listValue = ['#id_subject', '.tab-option-form-contract .tab-pane.active #id_delivery', '.tab-option-form-contract .tab-pane.active #id_date_schedule', 
                    '.tab-note-form-contract .tab-pane.active #id_note', '.tab-option-form-contract .tab-pane.active #id_deadline_d', '.tab-option-form-contract .tab-pane.active #id_time', 
                    '#id_address', '#id_company_name', '#id_job_title', '#id_fullname', '#id_address_owner', '#id_company_name_owner', '#id_job_title_owner', '#id_fullname_owner'];
    let listText = ['#id_total_pj'];
    listValue.forEach((x) => {
        let current_value = $(x).val();
        let idValue = x.split(' ')[x.split(' ').length - 1] + '_value';
        if (x === '.tab-option-form-contract .tab-pane.active #id_time' && $('#id_deadline_d_value').text() === '') {
            $(idValue).text('');
        } else {
            $(idValue).text(current_value);
        }
    });
    listText.forEach((x) => {
        let current_value = $(x).text();
        let idValue = x + '_value';
        $(idValue).text(current_value)
    });
}

function getValueConfirm() {
    $('#modal-confirm-upload').on('shown.bs.modal', function () {
        if($('#id_schedule').val()) {
            $('#id_schedule_value').text($('#id_schedule').val() + ' ' + $('#id_time').val());
        }

        if($('#id_total_pj').text()) {
            $('#id_tax_value').text($('#id_total_pj').text());
        }

        var $dataRows=$('.contract__form-content-wrap .contract__form-content-list .contract__form-content-item');
        var work_content = '<table class="table">'
        $dataRows.each(function() {
                var row = '';
                row += '<tr>' +
                '<td>' + $(this).find('.form-control.input-select.work-type').val() + '</td>' +
                '<td>' + $(this).find('.contract__form-quantity').val() + '</td>' +
                '<td>' + $(this).find('.form-control.input-select.unit-select').val() + '</td>' +
                '<td>' + $(this).find('.contract__form-content_note').val() + '</td>' +
                '</tr>';
                work_content += row;
        });
        work_content += '</table>'
        $('#id_business_content_value').html(work_content);

        if ($('#id_public').is(':checked')) {
            $('#id_public').attr('checked', 'checked');
            $('#id_public_confirm').attr('checked','checked');
        } else {
            $('#id_public').removeAttr('checked');
            $('#id_public_confirm').removeAttr('checked');
        }
        
        var selectedTab = $('#modal-upload-contract .tab-option-form-contract .component-tab.segment').find('.active .nav-link').attr('data-target').trim();
        if(selectedTab == '#quasi-delegation_type') {
            $('#id_delivery_checked_confirm').attr('checked', 'checked');
        } else {
            $('#id_delivery_checked_confirm').removeAttr('checked');
        }

        getValueContract();
    });
}

function validatePlanSubmitForm() {
    var error = false;
    var subjectDom = $('#id_subject').val().trim();
    var priceDom = $('.contract__form-price input').val();
    var quantityDom = $('.contract__form-quantity').val();
    var inputCompanyProducer = $('input#id_company_name')
    var trimmedCompanyProducer = inputCompanyProducer.val().trim()
    var max_length = 30
    inputCompanyProducer.val(trimmedCompanyProducer.length > max_length ? this.substring(0, max_length) : trimmedCompanyProducer)
    if(subjectDom === '') {
        $('#id_subject').after('<small class="errorlist">この項目は必須です。</small>');
        $('#id_subject').addClass('error-border');
        $($('input.error-border')[0]).focus();
        error = true
    } else {
        $('#id_subject').removeClass('error-border');
        $('small.errorlist').remove();
    }

    if(priceDom === '') {
        $('.contract__form-price input').addClass('error-border');
        $($('input.error-border')[0]).focus();
        error = true
    } else {
        $('.contract__form-price input').removeClass('error-border');
    }

    if(quantityDom === '') {
        $('.contract__form-quantity').addClass('error-border');
        $($('input.error-border')[0]).focus();
        error = true
    } else {
        $('.contract__form-quantity').removeClass('error-border');
    }
    return error
}

function validateContractSubmitForm() {
    var error = false;
    var inputCompanyClient = $('input#id_company_name_owner')
    var trimmedCompanyClient = inputCompanyClient.val().trim()
    var max_length = 30
    inputCompanyClient.val(trimmedCompanyClient.length > max_length ? this.substring(0, max_length) : trimmedCompanyClient)

    return error
}

$(document).on('keyup', '#id_subject', function () {
    let dataInput = $(this).val() || "";
    $(this).removeClass('error-border');
    $('small.errorlist').remove();

    if (dataInput.trim() == "") {
        $(this).after('<small class="errorlist">この項目は必須です。</small>');
        $(this).addClass('error-border');
        return
    }
})

function validateInputContent($dataInput) {
    let dataInput = $dataInput.val() || "";
    $dataInput.removeClass('error-border');

    if (dataInput.trim() == "") {
        $dataInput.addClass('error-border');
        return
    }
}

$(document).on('keyup', '.contract__form-price input', function () {
    validateInputContent($(this));
})

$(document).on('keyup', '.contract__form-quantity', function () {
    validateInputContent($(this));
})

function check_blank_item(contract_comp){
    let list_items = contract_comp.find('.contract__form-content-item');
    if(!list_items.length){
        return true;
    }
    var result = true;
    list_items.each(function(i, item){
            let value = $(item).find('input').val();
            if (!value){
                result =  false;
                return;
            }
        })
    return result;
}

function onChangeContent(){
    $('.contract__form-input input').on('keyup', function(){
        let parent = $(this).parents('.contract__form-content-item');
        if (!$(this).val()){
            parent.addClass('blank_item');
        }else{
            parent.removeClass('blank_item');
        }
    })
}

function initRowContent() {
    let item_before = $('.contract__form-content-list').children().length + 1;
    var add_new = $($('.contract__form-content-list').children()[0]).clone(true);
    add_new.find('.form-control.input-select').val('');
    $(document).find('.input-select-container ul').each(function() {
        $(this).css({ display: 'none', 'pointer-events': 'none' });
    })
    add_new.find('.input-select-container ul').css({ display: 'none', 'pointer-events': 'none' });
    add_new.find('.form-control.input-select').attr('data-value', '');
    add_new.find('.contract__form-price input').val('');
    add_new.find('.contract__form-input.input_quantity input').val('');
    add_new.find('.contract__form-input.input_note input').val('');
    add_new.find('.input-select-container ul li.selected').removeClass('selected');
    add_new.find('.input-select-container ul').each(function() {
        $(this).find('.is-hover').removeClass('is-hover');
        $(this).find('.selected').removeClass('selected');
    })
    if(!add_new.hasClass('blank_item')) {
        add_new.addClass('blank_item');
    }
    add_new.attr('data-index-order', item_before);
    $('.contract__form-content-list').append(add_new);
}

function createRowContent() {
    $('#add_blocklist').click(function(){
        let parent = $(this).parents('.contract__form-content-wrap')
        let check = check_blank_item(parent);
        if(!check){
            return;
        }
        initRowContent();
        onChangeContent();
        totalMoney();

        $('#modal-upload-plan .btn-upload-file-messenger-owner').addClass('disable');
        $('.contract__form-action-content').removeClass('disabled');
        $('.contract__form-content-list .contract__form-content-item:last-child .contract__form-quantity').trigger('input');
    });
}

// Drag row content
function dragRowContent() {
    $(document).on('mouseenter mouseleave click', '#modal-upload-plan .contract__form-content-item', function(e) {
        if(e.type === 'mouseenter' && $(this).parent().children().length > 1){
            $(this).find('.contract__form-action-content').addClass('show-action');
            $(".contract__form-content-list").sortable({
                axis: 'y',
                items: "> div:not('.blank_item'), > i",
                connectWith: '.contract__form-content-list',
                handle: 'i.drag-content',
                tolerance: 'pointer',
                cursor: 'move',
                scroll: false,
                start: function (event, ui) {
                    ui.placeholder.height(ui.helper.outerHeight());
                    let index = ui.item.index();
                    $(this).find('.contract__form-action-content').removeClass('show-action');
                },
                change: function (event, ui) {
                },
                update: function (event, ui) {
                    reorderContents();
                },
            }).disableSelection();
        } else if (e.type === 'click' && $(this).parent().children().length > 1) {
            if(!$(this).find('.contract__form-action-content').hasClass('show-action')) {
                $(this).find('.contract__form-action-content').addClass('show-action');
            }
        } else {
            $(this).find('.contract__form-action-content').removeClass('show-action');
        }
    });
}


function initActionChangeValueTab () {
    elementId='#id_delivery, #id_date_schedule, #id_deadline_d, #id_note';
    elementId.split(', ').forEach(function(item) {
        $(document).on('change', item, function(){
            var $this = $(this)
            // console.log(" this", $this.parents('.tab-content').find('.tab-pane:not(.active) ' + item));
            $this.parents('.tab-content').find('.tab-pane:not(.active) ' + item).each(function() {
                $(this).val($this.val());
                if(item === '#id_date_schedule') {
                    let startDate = moment(new Date()).add(7, 'days');
                    let endDate = moment(startDate).add(1, 'months');
                    if(!moment($(this).val(), 'YYYY/MM/DD - YYYY/MM/DD', true).isValid()) {
                        let schedule_value = $('#modal-upload-contract #id-form-contract-and-plan').attr('default-value-time-form');
                        if(!!schedule_value){
                            startDate = schedule_value.split(';')[1].split(' - ')[0];
                            endDate = schedule_value.split(';')[1].split(' - ')[1];
                        }
                    } else {
                        startDate = new Date($this.val().split(' - ')[0]);
                        endDate = new Date($this.val().split(' - ')[1]);
                    }
                    
                    // $(this).daterangepicker({
                    //     startDate: startDate,
                    //     endDate: endDate,
                    // });
                }
            })
        });

        if(item === '#id_delivery') {
            $(document).on('click', '.tab-option-form-contract .input-select-container ul li', function(){
                var valSelect = $(this).attr('data-value')
                const $this = $(this);
                $(this).parents('.tab-content').find('.tab-pane:not(.active) ' + item).each(function() {
                    $(this).val(valSelect);
                    const $thisChild = $(this);
                    setTimeout(() => {
                        if ($this.closest('.input-select-container').find('input').attr('data-type') === 'select') {
                            $thisChild.attr('data-type', 'select');
                            $thisChild.parent().find('ul li').each(function () {
                                if ($(this).text() === $this.text()) {
                                    $(this).closest('.input-select-container').find('input').attr('data-value', $(this).val())
                                    $(this).parent().find('.selected').removeClass('selected');
                                    $(this).parent().find('.is-hover').removeClass('is-hover');
                                    $(this).addClass('selected');
                                    return;
                                }
                            })
                        }
                    }, 300);
                })
            })
        }
    });

    $(document).find('.issue-container .component-search-select select').each(function() {
        $(this).SumoSelect({
            showTitle: false,
            forceCustomRendering: true,
        });
    })


    $(document).on('dp.change', '#modal-upload-contract #id_time', function(){
        var $this = $(this)
        $('#modal-upload-contract .tab-pane:not(.active) #id_time').each(function() {
            $(this).val($this.val());
        })
    })

    $(document).find('.component-search-select select').each(function() {
        let valueSelect = $(this).attr('value');
        if(!!valueSelect && valueSelect!=='None' && valueSelect!=="undefined") {
            $(this).val(valueSelect.toString());
            let name = $(document).find($(this)).find('option[value="'+valueSelect.toString()+'"]').text();
            $(document).find($(this)).parent().find('.optWrapper ul li.selected').removeClass('selected')
            $(document).find($(this)).parent().find('.optWrapper ul li:contains("'+name+'")').addClass('selected')
            $(document).find($(this)).parent().find('.CaptionCont span').text(name)
        }
    })
}

function cleanFormData(formData, real_name_offer, file_offer, form) {
    formData.append('project_id', $('.project-item.active').attr('data-project-id'))
    formData.append('offer_id', $('.mitem.mactive').attr('data-offer'))
    formData.append('real_name', real_name_offer)
    formData.append('file', file_offer)

    var ignoreKeys = []

    // form contract and plan id
    let idObj = $('#modal-upload-contract #id-form-contract-and-plan').data('value');
    if (idObj) {
        formData.append('form_contract_and_plan_id', idObj);
    }
    if (form == '#modal-upload-contract') {
        //creation_method
        var selectedCreationTab = $('#modal-upload-contract .form-upload-contract-tab .component-tab').find('.active .nav-link').attr('data-target').trim()
        formData.append('creation_method', selectedCreationTab.replace('#', ''));

        //semi_delegate
        var selectedSemiTab = $('#modal-upload-contract .tab-option-form-contract .component-tab.segment').find('.active .nav-link').attr('data-target').trim()
        formData.append('semi_delegate', selectedSemiTab == '#quasi-delegation_type');

        //note_type
        var selectedNoteTab = $('#modal-upload-contract .tab-note-form-contract .tab-content').find('.tab-pane.active').attr('id').trim()
        formData.append('note_type', selectedNoteTab);

        // schedule
        var schedule = formData.get('schedule')
        ignoreKeys.push('schedule')
        if (schedule !== '') {
            schedule = schedule.replaceAll('/', '-');
            formData.append('start_schedule', schedule.split(' - ')[0] + ' 00:00:00');
            formData.append('end_schedule', schedule.split(' - ')[1] + ' 00:00:00');
        }

        // deadline
        var deadline = formData.get('deadline_d').replaceAll('/', '-')
        deadline = deadline ? deadline + ' '  + formData.get('deadline_hours') : ''
        formData.set('deadline', deadline);
        ignoreKeys.push('deadline_date')
        ignoreKeys.push('deadline_hours')

        // delivery format
        if ($('#id_delivery_checked').is(':checked')) {
            formData.set('delivery_format', '');
        }
        formData.append('pre_deadline', '');
        formData.append('owner_infor', '');
        formData.append('form_type', 2);
    } else if (form == '#modal-upload-plan') {
        formData.append('creation_method', 'generate')
        // release time
        formData.set('release_time', formData.get('release_time').replaceAll('/', '-'))

        // valid time
        var validTime = formData.get('valid_date').replaceAll('/', '-')
        validTime = validTime ? validTime + ' ' + formData.get('valid_hours') : ''
        formData.set('valid_date', validTime);
        ignoreKeys.push('valid_hours')
        formData.append('note_type', 'free_text');
        formData.append('form_type', 1);
        formData.append('pre_deadline', formData.get('pre_deadline').replaceAll('/', '-'));
        formData.append('owner_infor', formData.get('owner_infor'));
    }
    let form_create_type = '';
    if($(form).attr('data-type')) {
        form_create_type = $(form).attr('data-type')
        formData.append('form_create_type', form_create_type);
    }
    ignoreKeys.forEach(key => formData.delete(key));

    var objectJson = {};
    formData.forEach(function(value, key){
        objectJson[key] = value;
    });

    if (form == '#modal-upload-contract') {
        // owner info
        objectJson.owner_info = makeJsonUserInfo('_owner');
        objectJson.work_content = JSON.stringify([])
        objectJson.producer_info = JSON.stringify([])
    } else if (form == '#modal-upload-plan') {
        objectJson.owner_info = JSON.stringify([])
        // work content
        objectJson.work_content = makeJsonWorkContentData();

        // producer info
        objectJson.producer_info = makeJsonUserInfo();
    }

    return objectJson;
}


function makeJsonWorkContentData() {
    var $dataRows = $('.contract__form-content-wrap .contract__form-content-list .contract__form-content-item');
    var workContents = [];

    $dataRows.each(function() {
        let dataJson = {
            'work_type': $(this).find('input.input-select.work-type').attr('data-type') == 'select' ? $(this).find('input.input-select.work-type').attr('data-value') : '',
            'work_type_dsp': $(this).find('input.input-select.work-type').attr('data-value'),
            'price': parseInt($(this).find('.contract__form-price>input').val().replaceAll(',', '')),
            'quantity': parseFloat($(this).find('.contract__form-quantity').val()),
            'unit': $(this).find('input.input-select.unit-select').attr('data-type') == 'select' ? $(this).find('input.input-select.unit-select').attr('data-value') : '',
            'unit_dsp': $(this).find('input.input-select.unit-select').attr('data-value'),
            'note': $(this).find('.contract__form-content_note').val()
        };
        workContents.push(dataJson);
    });
    return JSON.stringify(workContents);
}


function makeJsonUserInfo(userTypeSubFix='') {
    dataInfo = {
        'address': $('#id_address' + userTypeSubFix).val(),
        'company_name': $('#id_company_name' + userTypeSubFix).val(),
        'job_title': $('#id_job_title' + userTypeSubFix).val(),
        'fullname': $('#id_fullname' + userTypeSubFix).val(),
    }
    return JSON.stringify(dataInfo)
}


function handleChangeOptionUploadFile() {
    $(document).on('change', '#modal-upload-contract [name="creation_method"]', function(e) {
        no_changed_creation_method = false;
        let radio_btn_1 = $('#modal-upload-contract #id_upload_1').is(':checked');
        let radio_btn_2 = $('#modal-upload-contract #id_upload_2').is(':checked');

        $('#modal-upload-contract .dropzone.dz-clickable').toggleClass('hide', radio_btn_1);
        $('#modal-upload-contract .mcommment-file__delete>i').click();

        $('#modal-confirm-upload .popup-body__export-item:nth-child(2)').toggleClass('checked disable-click', radio_btn_2);
        $('#modal-confirm-upload .popup-body__export-item:nth-child(1)').removeClass('checked');
        $('#modal-upload-contract .file-original').remove();
        $('#btn__submit-upload').toggleClass('disable', !radio_btn_2);
    })
}


let inputs = '#id_subject, #id_pickup, #id_delivery_place, #id_issue';
function handleChangeRequiredFields() {
    let btn = '.btn-upload-file-messenger-owner';
    $(document).on('change', '#modal-upload-contract [name="creation_method"]', function() {
        $(btn).toggleClass('disable', !validRequiredFields('#modal-upload-contract'))
    })

    $(document).on('change', inputs, function() {
        $(btn).toggleClass('disable', !validRequiredFields('#modal-upload-plan'));
    })

    $(document).on('change', '#modal-upload-plan .contract__form-content-list .form-control:not(.not-required)', function(){
        $(btn).toggleClass('disable', !validRequiredFields('#modal-upload-plan'));
    })

    $(document).on('change', '#modal-upload-contract .component-tab-container.tab-option-form-contract .tab-content .tab-pane.active .form-control.required-field', function(){
        $(btn).toggleClass('disable', !validRequiredFields('#modal-upload-contract'));
    })

    $(document).on('click', '#modal-upload-contract .component-tab-container.tab-option-form-contract .nav-item', function(){
        setTimeout(() => {
            $(btn).toggleClass('disable', !validRequiredFields('#modal-upload-contract'));
        }, 100);
    })

    $(document).on('click', '.form-group.component-input-select .list-input-select', function() {
        let target = '#modal-upload-contract'
        if($(this).parents('form.modal').is('#modal-upload-plan')) {
            target = '#modal-upload-plan'
        }
        setTimeout(() => {
            $(btn).toggleClass('disable', !validRequiredFields(target));
        }, 5);
    })
}

function validRequiredFields(valid_for="#modal-upload-plan") {
    var input_valid = true;

    $(document).find(valid_for + ' .component-tab-container.tab-option-form-contract .tab-content .tab-pane.active .form-control.required-field').each(function(){
        if ($(this).val().trim() === '') {
            input_valid = false;
            return;
        }
    })

    inputs.split(', ').forEach(function(item) {
        if ($(item).val() === '' && $(item).parents(valid_for).length) {
            input_valid = false;
            return;
        }
    });

    $('#modal-upload-plan .contract__form-content-list .form-control:not(.not-required)').each(function() {
        if ($(this).val().trim() === '') {
            input_valid = false;
            return;
        }
    })

    if($('#modal-upload-plan.in').length) {
        return input_valid
    } else {
        return ($('#modal-upload-contract .tab-pane.active#upload').length && dropZoneContract.files.length > 0
            || $('#modal-upload-contract .tab-pane.active#generate').length && input_valid) 
    }
}

function resetFormContract(open_modal=false) {
    file_offer = (function () { return; })();
    real_name_offer = '';
    let productId = $('.project-item.active').data('project-id');
    $.ajax({
        type: 'GET',
        url: '/form_upload_and_plans/get_current_form_upload_and_plan',
        data: {
            'product_id': productId,
        },
        datatype: 'json',
        success: function (data) {
            appendCurrentFormContractAndPlan(data, data.file_type);
            // if(open_modal) {
            //     if(data.file_type == 'plan') {
            //         $('#modal-upload-plan').modal('show')
            //     } else if (data.file_type == 'contract') {
            //         $('#modal-upload-contract').modal('show')
            //     }
            // }
        },
        error: function (data) {
            console.log(data);
        }
    });
}

//TODO
function initHandleActionUploadContract() {
    // Submit form contract and plan
    // $(document).on('click', '#btn__submit-upload', function (e) {
    //     e.preventDefault();
    //     if (!$(this).hasClass('disable')) {
    //         let form = $('#modal-upload-contract-plan');
    //         let url = detectUrlSubmit();
    //         $(this).addClass('disable');
    //         var objectJson = cleanFormData(new FormData(form[0]), real_name_offer, file_offer);
    //         console.log(objectJson);
    //         toastr.info('処理中…');

    //         $.ajax({
    //             type: 'POST',
    //             url: url,
    //             data: objectJson,
    //             datatype: "json",
    //             success: function (data) {
    //                 console.log('ok');
    //             },
    //             statusCode: {
    //                 400: function (data) {
    //                     toastr.info(data.responseJSON.error);
    //                 },
    //                 500: function (data) {
    //                     toastr.info(gettext('Something went wrong!'));
    //                 },
    //             },
    //             complete: function () {
    //                 $('#modal-upload-contract-plan').modal('hide');
    //                 $('#modal-confirm-upload').modal('hide');
    //                 resetFormContract();
    //                 $('#btn__submit-upload').removeClass('disable');
    //             },
    //         });
    //     }
    //     return
    // });

    $(document).on('click', '#modal-confirm-upload .popup-body__export-item', function () {
        $(this).toggleClass('checked');
        $('#btn__submit-upload').toggleClass('disable', !validateGenerateOption())
    });

    $(document).on('click', '#modal-confirm-upload .btn--tertiary', function () {
        $('#modal-confirm-upload .contract__form-wrap:last() .popup-body__export-item.error-border').removeClass('error-border');
        let radio_btn_2 = $('#modal-upload-contract-plan #id_upload_2').is(':checked');
        if ($('#modal-upload-contract-plan #id-form-contract-and-plan').data('value') && no_changed_creation_method) {
            let formTypeOriginal = $('#modal-upload-contract-plan #form-type-original').data('value');
            let creationMethodOriginal = $('#modal-upload-contract-plan #creation-method-original').data('value');

            $("#modal-confirm-upload .popup-body__export-item:nth-child(1)").toggleClass('checked', formTypeOriginal == 1 || formTypeOriginal == 3);
            if (creationMethodOriginal == 'upload') {
                $("#modal-confirm-upload .popup-body__export-item:nth-child(2)").addClass('checked disable-click');
            } else if (formTypeOriginal == 2 || formTypeOriginal == 3) {
                $("#modal-confirm-upload .popup-body__export-item:nth-child(2)").addClass('checked');
            }
        } else {
            $('#modal-confirm-upload .popup-body__export-item:nth-child(2)').toggleClass('checked disable-click', radio_btn_2);
            $('#modal-confirm-upload .popup-body__export-item:nth-child(1)').removeClass('checked');
            $('#btn__submit-upload').toggleClass('disable', !radio_btn_2);
        }
    });

    handleChangeOptionUploadFile();

    $(document).on('click', '#modal-upload-contract .close-file-original', function() {
        $('#modal-upload-contract .file-original').remove();
    })

    dragRowContent();
}


function validateGenerateOption() {
    let condition = $('#modal-confirm-upload .popup-body__export-item').hasClass('checked');
    $('#modal-confirm-upload .popup-body__export-item').toggleClass('error-border', !condition)
    return condition
}


function appendCurrentFormContractAndPlan(data, file_type) {
    var form = $('#modal-upload-contract');
    if(file_type == 'plan') {
        var form = $('#modal-upload-plan');
    }

    form.empty();
    form.append(data.form_upload_html);

    if(file_type == 'contract') {
        initDropZone(['#modal-upload-contract']);
    }
    initDateRangeValid();
    validateContractForm();
    initActionChangeValueTab();

    // $(document).find('.tab-option-form-contract .tab-content #id_time').each(function() {
    //     let idFormContractAndPlan = $('#modal-upload-contract-plan #id-form-contract-and-plan').data('value');
    //     if (!idFormContractAndPlan) {
    //         $(this).val('10:00')
    //         $(this).datetimepicker({
    //             format: 'HH:mm'
    //         });
    //     } else {
    //         let currentTime = $(this).val();
    //         if (currentTime) {
    //             $(this).datetimepicker({ format: 'HH:mm' });
    //             $(this).val(currentTime);
    //         } else {
    //             $(this).val('10:00');
    //             $(this).datetimepicker({ format: 'HH:mm' });
    //         }
    //     }
    // })
    no_changed_creation_method = true;
}


function detectUrlSubmit() {
    return '/direct/upload_contract'
}

function customSwitchPublic() {
    setTimeout(() => {
        let toggleDom = $(document).find('#modal-upload-contract-plan .switch-checkbox-public #id_public')
        let toggleStatus = toggleDom.is(':checked');
        // console.log("check toggle", toggleDom);
        if(toggleStatus) {
            toggleDom.parents('.switch-checkbox-public').addClass('checked');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_active_share').removeClass('hide');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_inactive_share').addClass('hide');
        } else {
            toggleDom.parents('.switch-checkbox-public').removeClass('checked');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_active_share').addClass('hide');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_inactive_share').removeClass('hide');
        }
    }, 1000);

    $(document).on('change', '#modal-upload-contract .switch-checkbox-public #id_public', function(e){
        if($(this).is(':checked')) {
            $(this).parents('.switch-checkbox-public').addClass('checked');
            $(this).parents('.switch-checkbox-public').find('svg.svg_active_share').removeClass('hide');
            $(this).parents('.switch-checkbox-public').find('svg.svg_inactive_share').addClass('hide');
        } else {
            $(this).parents('.switch-checkbox-public').removeClass('checked');
            $(this).parents('.switch-checkbox-public').find('svg.svg_active_share').addClass('hide');
            $(this).parents('.switch-checkbox-public').find('svg.svg_inactive_share').removeClass('hide');
        }
    })
}

$(document).on('click', '.button-scroll-top-container', function(e) {
    $(document).find('#modal-upload-contract .popup-body, #modal-upload-plan .popup-body').animate({
        scrollTop: 0
    }, 500);
})


function getPDFtoPreviewBeforeConfirmSend(form='#modal-upload-plan') {
    if (!$(form).find('.btn-upload-file-messenger-owner').hasClass('disable')) {
        $('#modal-confirm-upload').find('iframe').attr('src', 'about:blank');
        $('#btn__submit-upload').addClass('disable');
        $('#modal-confirm-upload').modal('show');
        let url = detectUrlSubmit();
        $(this).addClass('disable');
        var objectJson = cleanFormData(new FormData($(form)[0]), real_name_offer, file_offer, form);
        if(objectJson.start_schedule) {
            const splittedDates = objectJson.start_schedule.split(" から ");
            if(splittedDates.length > 1){
                objectJson.start_schedule = moment(splittedDates[0]).format("YYYY-MM-DD HH:mm:ss");
                objectJson.end_schedule = moment(splittedDates[1]).format("YYYY-MM-DD HH:mm:ss");
            }
        }
        addLoadingAnimation();
        request_counter++
        let current_counter = String(request_counter)
        $('#modal-confirm-upload').attr('data-request-id', current_counter);
        $.ajax({
            type: 'POST',
            url: url,
            data: objectJson,
            datatype: "json",
            beforeSend: function() {
                     $('.scene-take-container').append(`<div id="loading_animation" class="loading_animation_container"></div>`);
                    addLoadingAnimation();
                },
            success: function (data) {
                if(waiting_cancel_request.includes(current_counter)) {
                    rejectFileUpload(data.current_form, data.new_form, data.message_file, data.form_create_type)
                    const index = waiting_cancel_request.indexOf(current_counter);
                    if (index > -1) {
                        waiting_cancel_request.splice(index, 1);
                    }
                } else {
                    console.log(data);
                    $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
                    $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-send-plane disable')
                    $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックしました')
                    $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox').siblings('label.form-check-label').removeClass('btn--disabled')
                    $('#modal-confirm-upload').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.pdf_url) + '#zoom=page-width');
                    $('#modal-confirm-upload').attr('data-waiting-confirm', true)
                    $('#modal-confirm-upload').attr('data-old-form', data.current_form)
                    $('#modal-confirm-upload').attr('data-new-form', data.new_form)
                    $('#modal-confirm-upload').attr('data-new-file-message', data.message_file)
                    $('#modal-confirm-upload').attr('data-type', data.form_create_type)
                    $('#modal-upload-plan, #modal-upload-contract, #modal-drag-file').attr('data-type', '');
                }
            },
            complete: function() {
                $(".loading_animation_container").remove();
            },
            statusCode: {
                400: function (data) {
                    toastr.info(data.responseJSON.error);
                },
                500: function (data) {
                    toastr.info(gettext('Something went wrong!'));
                },
            },
        });
    }
}

function confirmFileUpload(old_form_id, new_form_id, message_file_id, type) {
    $('#btn__submit-upload').addClass('disable')
    $.ajax({
        type: 'POST',
        url: '/direct/upload_contract_confirm',
        data: {
            'old_form_id': old_form_id,
            'new_form_id': new_form_id,
            'message_file_id': message_file_id,
            'form_create_type': type
        },
        datatype: "json",
        success: function (data) {
            console.log('uploaded file ok');
            $('#modal-upload-plan, #modal-upload-contract').modal('hide')
            $('#btn__submit-upload').removeClass('disable')
        },
        statusCode: {
            400: function (data) {
                toastr.info(data.responseJSON.error);
            },
            500: function (data) {
                toastr.info(gettext('Something went wrong!'));
            },
        },
        complete: function() {
            $('#modal-confirm-upload').modal('hide');
            $('#modal-confirm-upload').removeAttr('data-modal-type')
            $('#btn__submit-upload').removeClass('.btn--disabled')
        }
    });
}

function rejectFileUpload(old_form_id, new_form_id, message_file_id, type) {
    $.ajax({
        type: 'POST',
        url: '/direct/upload_contract_reject',
        data: {
            'old_form_id': old_form_id,
            'new_form_id': new_form_id,
            'message_file_id': message_file_id,
            'form_create_type': type
        },
        datatype: "json",
        success: function (data) {
            console.log('uploaded canceled successfully');
        },
        statusCode: {
            400: function (data) {
                toastr.info(data.responseJSON.error);
            },
            500: function (data) {
                toastr.info(gettext('Something went wrong!'));
            },
        },
        complete: function() {
            $('#modal-confirm-upload').removeAttr('data-modal-type')
            $('#modal-upload-contract, #modal-upload-plan').attr('data-type', type)
        }
    });
}