$(document).ready(function () {
    $.ajaxSetup({
        headers: {
            'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
        }
    });
    // Menu Click
    $('.nav-toggle').click(function () {
        $('.menu-sp').toggleClass('open');
    });

    $('#id_image').closest('.col-xs-12').append('<br><span class="btn btn-sm btn-default" style="padding: 5px 5px;">ファイルを選択</span><span class="no-file">まだファイルを選択していません。</span>')
    $('#id_information').closest('.col-xs-12').append('<span class="btn btn-sm btn-default" style="padding: 5px 5px;">ファイルを選択</span><span class="no-file">まだファイルを選択していません。</span><span class="have-file"></span>')

    let scenes = $("[id*='id_scene']");

    function checkInput(str) {
        return str === null || str.match(/^ *$/) !== null;
    }

    if (scenes.length > 0) {
        scenes.each(function () {
            if (this.type === 'text') {
                $('#' + this.id).on('keyup', function (e) {
                    console.log(checkInput($(this).val()))
                    if (checkInput($(this).val())) {
                        this.setCustomValidity('正しいシーン名を入力してください。')
                    } else {
                        this.setCustomValidity('')
                    }
                })
            }
        })
    }

    $('input.btn').on('click', function () {
        if (parseInt($('#id_max_scene').val()) < parseInt($('#id_current_scene').val())) {
            $('#id_max_scene')[0].setCustomValidity('現在のシーン数(' + $('#id_current_scene').val() + ')の以上のバリューを入力してください。')
        } else {
            $('#id_max_scene')[0].setCustomValidity('')
        }
    });

    $(document).on('click', '#export-acr', function () {
        let href = $(this).attr('export-url');
        let redirect_url = $(this).attr('update-project-url');
        // toastr.info('楽曲認識照合レポートを作成しています');
        $.ajax({
            url: href,
            type: "GET",
            contentType: 'application/json',
            success: function (response, status, xhr) {
                let filename = "";
                let disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    let matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '').replace('UTF-8', '');
                }
                let content_type = xhr.getResponseHeader('Content-Type')
                let blob = new Blob([response], {type: content_type});
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    // IE workaround for "HTML7007: One or more blob URLs were revoked by closing the blob
                    // for which they were created. These URLs will no longer resolve as
                    // the data backing the URL has been freed."
                    window.navigator.msSaveBlob(blob, filename);
                } else {
                    let URL = window.URL || window.webkitURL;
                    let downloadUrl = URL.createObjectURL(blob);
                    if (filename) {
                        let a = document.createElement('a');
                        if (typeof a.download === 'undefined') {
                            window.location.href = downloadUrl;
                        } else {
                            a.href = downloadUrl;
                            a.download = filename;
                            document.body.appendChild(a);
                            a.click();
                        }
                    } else {
                        window.location.href = downloadUrl;
                    }
                }
            },
            fail: function (e) {
                console.log("fail: ", e);
            },
            error: function (data) {
                let json_data = data.responseJSON;
                if (json_data.status === 302) {
                    toastr.error(data.responseJSON.message);
                    if (redirect_url !== undefined) {
                        setTimeout(function () {
                            window.location.assign(redirect_url);
                        }, 4000);
                    }

                } else if (json_data.status === 500) {
                    toastr.warning(data.responseJSON.message);
                } else {
                    toastr.error("Something error!");
                }
            }
        });
    });

    $('.project-item').on('click', '.max_scene_edit', function (e) {
        e.stopPropagation();
        e.preventDefault();
        let max_scene_el = $(this);
        let project_id = max_scene_el.parents('.project-item').attr('data-project-id');
        let max_scene = $(this).parents('.project-item__progress-total').attr('data-max-scene');
        let current_scene_el = $(this).parents('.project-item__progress-percent').find('.project-item__progress-warning');
        let done_scene_el = $(this).parents('.project-item__progress-percent').find('.project-item__progress-success');
        let current_scene = parseInt(current_scene_el.text());
        let done_scene = parseInt(done_scene_el.text());
        bootbox.prompt({
            title: "総シーン数更新 (進行中シーン数:" + (current_scene + done_scene) + ")",
            message: "<label> 総シーン数 * </label>",
            size: 'small',
            value: parseInt(max_scene),
            inputType: 'number',
            min: parseInt(current_scene + done_scene),
            required: true,
            closeButton: false,
            buttons: {
                confirm: {
                    label: '更新',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'キャンセル',
                    className: 'btn-danger'
                }
            },
            callback: function (result) {
                let new_max_scene = result;
                if (new_max_scene) {
                    if (!project_id) {
                        toastr.error("入力した総シーン数が正しくありませんので、更新できませんでした。");
                    } else {
                        $.ajax({
                            url: '/top/update_max_scene',
                            method: 'POST',
                            data: {'project_id': project_id, "max_scene": new_max_scene},
                            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                // toastr.success('総シーン数を更新しました。');
                                max_scene_el.parents('.project-item__progress-total').attr('data-max-scene', result);
                                max_scene_el.parents('.project-item__progress-total').find('span').first().html(result);
                                if(response.status === 'done') {
                                    window.location.href = '/top?is_done=1'
                                }
                            },
                            error: function (response) {
                                toastr.error('入力した総シーン数が正しくありませんので、更新できませんでした。');
                            }
                        });
                    }
                }
            }
        });
    });
    $('.project-item').on('click', '.project-item__more-left-top', function (e) {
        e.preventDefault();
        $(this).toggleClass('active');
    });
    $(document).on('click', '#delete-item', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let delete_el = $(this);
        bootbox.confirm({
            message: "本当に削除しますか?",
            buttons: {
                confirm: {
                    label: '削除',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'キャンセル',
                    className: 'btn-light'
                }
            },
            callback: function (result) {
                if (result) {
                    let delete_url = delete_el.parents('a').first().attr('href');
                    $.ajax({
                        url: delete_url,
                        method: "DELETE",
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            // toastr.success(response.message);
                            window.location.replace(response.success_url);
                        },
                        error: function (response) {
                            toastr.error("エラーが発生しました");
                        }
                    });
                }
            }
        });
    });
    priceSetting();
    selectAdmin();
    $('.project-setting-price__button').on('click', function () {
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/get_list_admin",
            data: {
                'product_id': $('.project-item').attr('data-project-id'),
            },
            success: function (data) {
                $('#select-admin-modal .select-admin__list').html('');
                $('#select-admin-modal .select-admin__list').prepend(data.html);
                $('#select-admin-modal').modal('show');
            }
        })

    })
    $('#id_start_time, #id_end_time').datepicker({
        format: 'yyyy/mm/dd',
    })

    var today = new Date();
    var nextweek = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 7);
    if (!$('#id_start_time').val()) {
        $('#id_start_time').attr('placeholder', today.getFullYear() + '/' + (today.getMonth() + 1) + '/' + today.getDate());
    } else {
        // $('#id_start_time').datepicker('setDate', formatDate($('#id_start_time').val()));
    }

    if (!$('#id_end_time').val()) {
        $('#id_end_time').attr('placeholder', nextweek.getFullYear() + '/' + (nextweek.getMonth() + 1) + '/' + nextweek.getDate());
    } else {
        // $('#id_end_time').datepicker('setDate', formatDate($('#id_end_time').val()));
    }
})

function priceSettingRemain(element) {
    var total_price = parseFloat(element.find('#id_total_budget').val());
    var total_lim = 0;
    var price_set = 0;
    var price_remain = 0;

    element.find('.price-user__input').each(function () {
        price_set = parseFloat($(this).val());

        if (!isNaN(price_set)) {
            total_lim += price_set;
            price_remain = total_price - total_lim;

            if (price_remain < 0) {
            }
        }
    });
    let currentInactive = element.find('.project-setting-price__current-number').attr('data-total-inactive');
    element.find('.project-setting-price__current-number').text(price_remain-currentInactive);

    return total_lim;
}

function priceSetting() {
    $('.project-setting-price').each(function () {
        var $this = $(this);
        var total_price = parseFloat($this.find('#id_total_budget').val());

        priceSettingRemain($this);

        $this.off('change').on('change', '.price-user__input', function () {
            var price_input = $(this);
            var current_user = price_input.parents('.price-user');
            var price_min = parseInt(current_user.find('.price-user__min-number').html());
            var new_price = $(this).val();

            if (new_price < price_min) {
                $(this).val(price_min);
            }

            let total_lim = priceSettingRemain($this);
            if (total_lim > total_price) {
                let new_price_set = new_price - (total_lim - total_price);
                $(this).val(new_price_set);
            }

            priceSettingRemain($this);
            $.ajax({
                method: "POST",
                url: "/add_edit_admin_product",
                data: {
                    'budget': $(this).val(),
                    'budget_project': total_price,
                    'product_id': $('.project-item').attr('data-project-id'),
                    'user_id': current_user.attr('data-admin-id')
                },
                success: function () {
                    // toastr.success('予算上限を更新しました')
                }
            })
        });

        $this.find('#id_total_budget').on('change', function () {
            let total_lim = priceSettingRemain($this);
            let new_price_total = $(this).val();
            if (new_price_total < total_lim) {
                $(this).val(total_lim);
            }
            priceSetting();
        });
    });

    $(document).find('#id_total_budget').on('keyup', function () {
        let total_budget = $(this).val() ? $(this).val() : 0;
        let currentSpentBudget = $('.price-user__currency').attr('data-current-total-in-active');
        if (total_budget < currentSpentBudget) {
            $('input[type="submit"]').addClass('disabled');
            if (!$('.error-budget').length) {
                $(this).addClass('error-border');
                $('<ul class="errorlist error-budget">' +
                    '<li>BUDGET ERROR</li>' +
                    '</ul>').insertAfter($(this));
            }

        } else {
            $(this).removeClass('error-border');
            $('.error-budget').remove();
            $('input[type="submit"]').removeClass('disabled')
        }
    });
}

function priceSettingFormat(number, decPlaces, decSep, thouSep) {
    decPlaces = isNaN(decPlaces = Math.abs(decPlaces)) ? 0 : decPlaces,
        decSep = typeof decSep === "undefined" ? "." : decSep;
    thouSep = typeof thouSep === "undefined" ? "," : thouSep;
    var sign = number < 0 ? "-" : "";
    var i = String(parseInt(number = Math.abs(Number(number) || 0).toFixed(decPlaces)));
    var j = (j = i.length) > 3 ? j % 3 : 0;

    return sign +
        (j ? i.substr(0, j) + thouSep : "") +
        i.substr(j).replace(/(\decSep{3})(?=\decSep)/g, "$1" + thouSep) +
        (decPlaces ? decSep + Math.abs(number - i).toFixed(decPlaces).slice(2) : "");
}

function selectAdmin() {
    $('.select-admin__form').each(function () {
        var $this = $(this);

        $(this).find('.select-admin__input').on('keyup', function () {
            var name = $(this).val();
            if (name !== '') {
                $(this).siblings('.select-admin__list').addClass('searching');
                $this.find('.select-admin__item').removeClass('search-found selected');
                $this.parents('.select-admin').find('.btn-add-admin').addClass('disabled');
                $this.parents('.select-admin').find('.select-admin__price').attr('disabled', 'true');
                $this.find('.select-admin__item[data-name*="' + name + '"]').addClass('search-found');
                if (!$('.search-found').length) {
                    $('.no-search-result').removeClass('hide');
                } else {
                    $('.no-search-result').addClass('hide');
                }
            } else {
                $this.find('.select-admin__item').addClass('search-found');
                $('.no-search-result').addClass('hide');
            }
        });

        $this.on('click', '.select-admin__item', function () {
            $(this).siblings().removeClass('selected');
            $(this).toggleClass('selected');

            if ($(this).hasClass('selected')) {
                $this.parents('.select-admin').find('.select-admin__price').removeAttr('disabled');
                $this.parents('.select-admin').find('.btn-add-admin').removeClass('disabled');
            } else {
                $this.parents('.select-admin').find('.btn-add-admin').addClass('disabled');
                $this.parents('.select-admin').find('.select-admin__price').attr('disabled', 'true');
            }
        });

        $this.find('.select-admin__price').on('change', function () {
            let price_set = parseFloat($(this).val());
            let price_remain = parseFloat($('.project-setting-price__current-number').text());
            if (price_set > price_remain) {
                $(this).val(price_remain);
            }
        });

        $this.parents('.select-admin').find('.btn-add-admin').on('click', function () {
            let avt = $('.select-admin__item.selected .select-admin__avatar-img')[0].src;
            let name = $('.select-admin__item.selected').attr('data-name');
            let work = $('.select-admin__item.selected').attr('data-work');
            let company = $('.select-admin__item.selected').attr('data-company');
            let admin_id = $('.select-admin__item.selected').attr('data-admin-id');
            let product_id = $('.project-item').attr('data-project-id');
            let product_budget = $('.total-price input#id_total_budget').val();
            let set_price = parseInt($this.find('.select-admin__price').val());

            if (isNaN(set_price)) {
                set_price = 0;
            }

            $.ajax({
                method: "POST",
                url: '/add_edit_admin_product',
                data: {
                    'user_id': admin_id,
                    'budget': set_price,
                    'budget_project': product_budget,
                    'product_id': product_id,
                    'is_new': 1
                },
                success: function (data) {
                    let item = `<div class="price-user" data-admin-id="${admin_id}">
                <div class="price-user__avatar">
                  <img class="price-user__avatar-img" src="${avt}" alt="">
                </div>
                <div class="price-user__info">
                  <div class="price-user__name">${name}</div>
                  <div class="price-user__work">${work}</div>
                  <div class="price-user__company">${company}</div>
                </div>
                <div class="price-user__money">
                  <div class="price-user__set">
                    <span class="price-user__set-number rel">
                      <input class="price-user__input right-align-input w-150" type="number" min="0" value="${set_price}">
                      <span class="price-user__currency">円</span>
                    </span>
                  </div>
                  <div class="price-user__min">
                    <span class="price-user__min-number pr-5">
                      0
                    </span>円
                  </div>
                </div>
              </div>`;
                    // toastr.success('ディレクターを追加しました');
                    $('.project-setting-user-list').append(item);
                    priceSettingRemain($('.project-setting-price'));
                    $('.modal').modal('hide');
                }
            })

            priceSetting();
            resetModal();
        });
    });
}

function resetModal() {
    $('input.select-admin__price').val('');
    $('input.select-admin__price').attr('disabled', true);
    $('.btn-add-admin').addClass('disabled');
}
