function actionForm(modal_create) {
    modal_create.find('#budget').on('keyup', function () {
        let tmp_reward = parseFloat(this.value);
        if (tmp_reward < 0 || isNaN(tmp_reward)) {
            $(this).val('');
            tmp_reward = 0
        }
        let tax_amount = Math.round(tmp_reward * 0.1);
        let total_amount = Math.round(tmp_reward * 1.1);
        modal_create.find('#id_tax_amount').html((tax_amount).toLocaleString(undefined));
        modal_create.find('#id_total_amount').html((total_amount).toLocaleString(undefined));
    });

    modal_create.find('#budget').on('change', function () {
        $(this).trigger('keyup')
    });

    modal_create.find('#id_contract').on('change', function () {
        let contract = $('#id_contract option').filter(':selected').val();
        let type_contract;
        switch (contract) {
            case '4':
                type_contract = 'サウンドデザイナー';
                break;
            case '5':
                type_contract = 'オーディオエンジニア';
                break;
            case '6':
                type_contract = '声優・ナレーター';
                break;
            case '7':
                type_contract = 'ボーカリスト';
                break;
            case '8':
                type_contract = '演奏家';
                break;
            case '9':
                type_contract = 'ディレクター';
                break;
            default:
                type_contract = 'コンポーザー';
        }
        $('#id_type_contract').attr('value', type_contract);
    });

}

function validateBudget() {
    $(document).on('input', '.modal-create-edit-offer #budget', function () {
        let valueBudget = this.value;
        this.value = valueBudget.replace(/\D/g, '')
    })
}

function dragDropSearch() {
    if ($('#create-offer-upload-form').length > 0) {
        var zdrop = [];
        Dropzone.autoDiscover = false;
        $("#create-offer-upload-form").append(csrf);

        var previewNode = $('.modal-create-edit-offer .account_upload-file .mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();

        myDropzone = new Dropzone("#create-offer-upload-form", {
            maxFilesize: 4500,
            timeout: 900000,
            autoDiscover: false,
            previewsContainer: '.modal-create-edit-offer .mattach-previews-form',
            previewTemplate: previewTemplate,
            url: "/",
            autoProcessQueue: false,
            autoQueue: false,
            clickable: '#create-offer-upload-form',
            maxFiles: 1,
            dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
        });

        myDropzone.on("maxfilesexceeded", function (file) {
            myDropzone.removeAllFiles();
            myDropzone.addFile(file);
        });

        $(window).on('dragover', function (e) {
            var dt = e.originalEvent.dataTransfer;
            if (dt.types && (dt.types.indexOf ? dt.types.indexOf('Files') != -1 : dt.types.contains('Files'))) {
                $('.mupload-drop').show();
            }
        });

        myDropzone.on('drop', function (e) {
            $('.mupload-drop').hide();
        });

        $(window).on('drop', function (e) {
            $('.mupload-drop').hide();
        });

        myDropzone.on('removedfile', function (file) {
           key_file = "";
           real_name = "";
        });

        myDropzone.on('addedfile', function (file, e) {
            $('.file_offer').remove();
            let file_dom = $(file.previewElement);
            real_name = file.name;
            let file_preview = $('#modal-create-edit-offer .mattach-preview-container-form').find(".mcommment-file__name-form");
            for (let i = 0; i < file_preview.length; i++) {
                $(file_preview[i]).text('');
                $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_name);
                break;
            }
            $("#modal-create-edit-offer .mattach-info-file").addClass("hide");
            if ($("#modal-create-edit-offer .mattach-template").length == 2) {
                $($("#modal-create-edit-offer .mattach-template")[0]).remove();
            }
            uploadFileS3Offer(file, file_dom);
        });

        $('#modal-create-offer').on('hidden.bs.modal', function () {
            myDropzone.removeAllFiles();
        });

        $('#modal-edit-offer').on('hidden.bs.modal', function () {
            myDropzone.removeAllFiles();
        });

        $(".mattach-info-file .icon--sicon-close").on("click", function() {
            $(".mattach-info-file").addClass("hide");
            is_delete_file = true;
        })
    }
};
