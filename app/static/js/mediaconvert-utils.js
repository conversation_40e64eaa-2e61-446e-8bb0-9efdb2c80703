/**
 * MediaConvert Utilities for HLS Video Support
 * Handles video conversion and HLS playback across the application
 */

// Initialize HLS video player for converted media files
function initializeHLSVideo(video) {
    if (!video) return;

    const videoSrc = video.getAttribute('data-video-src');
    const isHLS = video.getAttribute('data-is-hls') === 'true';
    const fallbackSrc = video.getAttribute('data-fallback-src');

    if (isHLS && videoSrc && videoSrc !== 'undefined') {
        if (typeof Hls !== 'undefined' && Hls.isSupported()) {
            const hls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90
            });

            hls.loadSource(videoSrc);
            hls.attachMedia(video);

            // Store HLS instance for cleanup
            video._hlsInstance = hls;

            // Error handling
            hls.on(Hls.Events.ERROR, function(event, data) {
                console.warn('HLS Error:', data);
                if (data.fatal) {
                    if (fallbackSrc && fallbackSrc !== 'undefined' && fallbackSrc !== 'null') {
                        // Fallback to original video if <PERSON><PERSON> fails and fallback is available
                        console.log('Falling back to original video:', fallbackSrc);
                        video.src = fallbackSrc;
                        if (video._hlsInstance) {
                            video._hlsInstance.destroy();
                            video._hlsInstance = null;
                        }
                    } else {
                        // No fallback available, try to reload HLS or show error
                        console.error('HLS failed and no fallback available');
                        // Optionally reload HLS after a delay
                        setTimeout(() => {
                            if (video._hlsInstance) {
                                video._hlsInstance.loadSource(videoSrc);
                            }
                        }, 2000);
                    }
                }
            });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari native HLS support
            video.src = videoSrc;
        }
    }
}

// Initialize all HLS videos on the page
function initializeAllHLSVideos() {
    const videos = document.querySelectorAll('video[data-is-hls="true"]');
    videos.forEach(function(video) {
        if (!video._hlsInstance) {
            initializeHLSVideo(video);
        }
    });
}

// Initialize HLS videos in carousel/slider contexts
function initializeCarouselHLSVideos(container) {
    if (!container) return;

    const videos = container.querySelectorAll('video[data-is-hls="true"]');
    videos.forEach(function(video) {
        // Only initialize if not already initialized and video is visible
        if (!video._hlsInstance && isVideoVisible(video)) {
            initializeHLSVideo(video);
        }
    });
}

// Check if video element is visible (for carousel optimization)
function isVideoVisible(video) {
    const rect = video.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0 &&
           rect.top >= 0 && rect.left >= 0 &&
           rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
           rect.right <= (window.innerWidth || document.documentElement.clientWidth);
}

// Auto-initialize HLS videos when DOM changes
function setupHLSVideoObserver() {
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Check if the added node is a video or contains videos
                            const videos = node.tagName === 'VIDEO' ? [node] : node.querySelectorAll ? node.querySelectorAll('video[data-is-hls="true"]') : [];
                            videos.forEach(initializeHLSVideo);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// Initialize HLS videos when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeAllHLSVideos();
    setupHLSVideoObserver();
    initializeSlickHLSSupport();
});

// Cleanup HLS instances (useful for carousel/slider cleanup)
function cleanupHLSVideo(video) {
    if (video && video._hlsInstance) {
        video._hlsInstance.destroy();
        video._hlsInstance = null;
    }
}

// Initialize HLS for Slick carousel events
function initializeSlickHLSSupport() {
    // Listen for Slick carousel events
    $(document).on('afterChange', '.slick-slider', function(event, slick, currentSlide) {
        setTimeout(function() {
            initializeAllHLSVideos();
            // Also initialize videos in the current slide specifically
            const currentSlideElement = $(slick.$slides[currentSlide]);
            if (currentSlideElement.length) {
                initializeCarouselHLSVideos(currentSlideElement[0]);
            }
        }, 100);
    });

    // Listen for before slide change to cleanup if needed
    $(document).on('beforeChange', '.slick-slider', function(event, slick, currentSlide, nextSlide) {
        // Optional: cleanup videos that are going out of view
        if (currentSlide !== nextSlide) {
            const currentSlideElement = $(slick.$slides[currentSlide]);
            if (currentSlideElement.length) {
                currentSlideElement.find('video').each(function() {
                    if (this._hlsInstance) {
                        this.pause();
                    }
                });
            }
        }
    });
}

// Export functions for use in other modules
window.initializeHLSVideo = initializeHLSVideo;
window.initializeAllHLSVideos = initializeAllHLSVideos;
window.initializeCarouselHLSVideos = initializeCarouselHLSVideos;
window.cleanupHLSVideo = cleanupHLSVideo;
window.initializeSlickHLSSupport = initializeSlickHLSSupport;
window.setupHLSVideoObserver = setupHLSVideoObserver;