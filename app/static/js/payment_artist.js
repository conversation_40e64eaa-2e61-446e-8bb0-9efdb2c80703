//unused file?
var current_page = 1;
var call_ajax = false;
var total_page = 1;
var ajax_order = false;
function loaMorePayment() {
    if (current_page < total_page && !call_ajax && !ajax_order) {
        let payment_status = 'undone';
        if ($('#filter-payment').is(':checked')) {
            payment_status = 'done';
        }

        current_page++;
        call_ajax = true;
        let current_page_tostr = current_page.toString();
        $.ajax({
            type: 'GET',
            data: {
                'page': current_page,
                'payment_status': payment_status
            },
            url: '',
            beforeSend: function() {
                $('.payment__content-list').append(`<div class="load-more-loading" data-load='${current_page_tostr}'></div>`)
            },
            success: function (data) {
                $(data).insertAfter($('.payment__content-item').last());
                if (payment_status === 'done') {
                }
                $('.load-more-loading[data-load^='+ data.page +']').removeClass();
            },
            error: function () {
                current_page--;
            },
            complete: function () {
                call_ajax = false
            }
        });
    }
}

function switchPayment() {
    if (!$('#filter-payment').is(':checked')) {
        let payment_id = $(this).data('payment');
        let payment_dones = $('.payment__content-item[data-payment!=' + payment_id + ']');
        payment_dones.each(function () {

        })
    }

    $(document).on('change', '#filter-payment', function () {
        loaMorePayment();
    });
}

function previewFilePDF(data) {
    $.ajax({
        type: 'POST',
        datatype: 'json',
        contentType: false,
        processData: false,
        url: '',
        data: data,
        success: function (data) {
            $('#modal-preview-pdf').find('iframe').attr('src', 'about:blank');
            $('#modal-preview-pdf').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.url) + '#zoom=page-width');
        }
    })
}

function previewPDF() {
    $(document).off('click', '.btn-preview-pdf').on('click', '.btn-preview-pdf', function () {
        let name = $(this).attr('data-name');
        let pdf_id = $(this).attr('data-pdf-id');
        let data = new FormData();
        data.append('pdf_id', pdf_id);
        previewFilePDF(data);
        $('#modal-preview-pdf').find('.btn-preview-pdf').html(name);
        $('#modal-preview-pdf').attr('data-pdf-id', pdf_id);
    });
    
    $(document).on('hidden.bs.modal', '#modal-preview-pdf', function (e) {
        $('#modal-preview-pdf').find('iframe').attr('src', 'about:blank');
    })
}

$(document).ready(function () {
    switchPayment();
    loaMorePayment();
    previewPDF();
});
