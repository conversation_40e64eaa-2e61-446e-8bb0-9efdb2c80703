const MAX_LENGTH_NAME = 64

function pageCollection() {
    $('.pd-chapter').each(function (i) {
        if (i == 0) {
            $(this).find('.pd-chapter__content').show();
        }
    });

    createAddItem();
    editDeleteItem();
    createNewList();
    editDeleteList();
}

function activeTab() {
  // Show the first tab and hide the rest
  $('#tabs-tab li:first-child').addClass('active');
  $('.tab-pane').hide();
  $('.tab-pane:first').show();

  // Click function
  $('#tabs-tab li').click(function () {
      $('#tabs-tab li').removeClass('active');
      $(this).addClass('active');
      $('.tab-pane').hide();

      var activeTab = $(this).find('a').attr('href');
      $(activeTab).fadeIn();
      return false;
  });
};

function createAddItem() {
    // Checked bookmark
    $(document).on('click', '.cvideo__bookmark', function () {
      let action_element = $(this).find(".icon--bookmark");
      if(action_element.hasClass("checked")) {
          action_element.removeClass("checked");
      }else{
          action_element.addClass("checked");
      }
      let $newItems = $("#add-item-modal").find(".icon--bookmark.checked");
      let button_accept = $("#add-item-modal .btn-new-add");
      button_accept.toggleClass('disabled', !$newItems.length);
    });

    // Event btn add item
    $('.add-modal__action').find('.btn-new-add').off().on('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      let $newItems = $("#add-item-modal").find(".icon--bookmark.checked");
      let new_bookmark_ids_scene = "";
      let new_bookmark_ids_sale = ""
      let list_id = $('#add-item-modal').attr("data-list-id");
      $newItems.each(function(i, item){
        let id = $(item).parents(".project-delivery-item-content").attr("data-scene-title-id");
        
        if(!id) {
            id = $(item).parents(".list-new-works__item-container").attr("data-sale-id");
            new_bookmark_ids_sale += id + ",";
        } else {
            new_bookmark_ids_scene += id + ",";
        }
      })
      if(!new_bookmark_ids_scene && !new_bookmark_ids_sale){
        $('#add-item-modal').modal('hide');
        return;
      }
      $.ajax({
        method: 'POST',
        url: '/collection/add_new_bookmark_items',
        data: {
            'new_bookmark_ids_scene': new_bookmark_ids_scene,
            'new_bookmark_ids_sale': new_bookmark_ids_sale,
            'list_id': list_id
        },
        success: function (data) {
            let list_id = $('#add-item-modal').attr("data-list-id");
            let list_bookmark = $(`.tab-section__item[data-item-id='${list_id}']`)
            $(data.html).insertAfter(list_bookmark.find(".pd-chapter__add"));
            let button_accept = $("#add-item-modal .btn-new-add");
            button_accept.addClass('disabled');
            dragItem();
            // toastr.success('アイテムをテーマに追加しました。');
        },
        error: function () {
            toastr.error('エラーが発生しました');
        }
      })
      $('#add-item-modal').modal('hide');
    });
  }

function deleteSceneItem(title_id, list_id, list_items, title, bookmark_type) {
    $('.btn-delete-scene').off('click').on('click', function (e) {
        $.ajax({
            type: "POST",
            url: "/collection/delete_bookmark_item",
            data: {
                'title_id': title_id,
                'list_id': list_id,
                'bookmark_type': bookmark_type
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                if (response.code == 200) {
                    let target = list_items.find('.project-delivery-item-content[data-scene-title-id^='+title_id+']');
                    if (!target.length) {
                        target = list_items.find('.list-new-works__item-container[data-sale-id^='+title_id+']').find('.list-new-works__media.gallery__item');
                    }
                    target.parent().remove();
                    // toastr.success('テーマからシーンを削除しました。');
                } else {
                    toastr.error('エラーが発生しました');
                }
            },
            error: function () {
                toastr.error('エラーが発生しました');
            }
        })
        $('#delete-scene-bookmark').modal('hide');
    })

    $("#delete-scene-bookmark").on("hidden.bs.modal", function () {
        title.next().removeClass("active");
    });
}

function editDeleteItem() {
    // Event btn delete item
    $(document).on('click', '.item_bookmark_delete', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let bookmark_type = 'scene'
        var title_id = $(this).parents('.project-delivery-item-content').attr('data-scene-title-id');
        if(!title_id) {
            bookmark_type = 'sale'
            title_id = $(this).parents('.list-new-works__item-container').attr('data-sale-id');
        }
        var list_id = $(this).parents(".tab-section__item").attr("data-item-id");
        var list_items = $(this).parents(".pd-chapter__list");
        var title = $(e.target).parents(".cvideo__heading").find('.cvideo__title');
        deleteSceneItem(title_id, list_id, list_items, title, bookmark_type);
    });

    // Event unbookmark
    $(document).on('click', '.unbookmark_item', function (e) {
        e.preventDefault();
        e.stopPropagation();
        var title_id = $(this).parents('.project-delivery-item-content').attr('data-scene-title-id');
        var title = $(e.target).parents(".cvideo__heading").find('.cvideo__title');
        unbookmarkScene(title_id, title);
    });
}

function createNewList() {
    // Event create new list
    $('.btn-new-create').off('click').on('click', function (e) {
        e.preventDefault();
        let input_text = $('#create-new-modal').find('.input-title-name').val();
        input_text = input_text.trim();
        if(input_text.length > MAX_LENGTH_NAME || !input_text.length){
            toastr.error('エラーが発生しました', 'シーン並び替え');
        }else{
            if (!$(this).hasClass('active')) {
                $(this).addClass('active');
                $.ajax({
                    type: "POST",
                    url: '/collection/create_list_bookmarks',
                    data:
                        {
                          'name': input_text,
                        },
                    success: function (data) {
                        let html = data.html;
                        $(".tab-section__list:not(.tab-section__list__root)").append(html);
                        $(".input-title-name").val("");
                        $("#create-new-modal .btn-new-create").addClass('disabled');
                        // toastr.success('テーマを作成しました。');
                        openModalAddItem();
                        collapseListBookmark();
                        $('#create-new-modal').modal('hide');
                    },
                    error: function() {
                        toastr.error('エラーが発生しました', 'シーン並び替え');
                    },
                    complete: function () {
                      $('.btn-new-create').removeClass('active');
                    }
                })
            }
        }
    });

    // Event action video
    $(document).on('mouseenter mouseleave', '.tab-section .cvideo__heading, .tab-section .list-new-works__title', function (e) {
      e.stopPropagation();
      if (e.type === 'mouseenter') {
        $(this).find('.scene-title__action').addClass("active")
      } else if (e.type === 'mouseleave') {
        $(this).find('.scene-title__action').removeClass("active");
      }
    });
    
    onchangeInputCreateList();
    dragItem();
}

function dragItem(){
    // Drag item
    $(".pd-chapter__list").sortable({
        containment: "document",
        items: "> div",
        connectWith: '.pd-chapter__list',
        handle: 'a.scene-title__move',
        cursor: "move",
        scroll: true,
        tolerance: "pointer",
        helper: 'clone',
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
            let index = ui.item.index();
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
            let list_id = $(event.target).parents(".tab-section__item").attr("data-item-id");
            let new_list_id = "";
            let target_id = $(ui.item).find(".project-delivery-item-content").attr("data-scene-title-id");
            let target_type = 'scene'
            if(!target_id) {
                target_id = $(ui.item).attr("data-sale-id");
                target_type = 'sale'
            }
            var title = $(ui.item).find(".cvideo__title");
            let direction = "left";
            let id_replace = "";
            let replace_type = 'scene'
            // drag drop trong cung 1 list
            if (ui.offset.left > ui.originalPosition.left){
                direction = "right";
            }
            if(direction == 'left'){
                let after_element = $(ui.item).next();
                if(after_element.hasClass("cvideo")){
                    id_replace = after_element.find(".project-delivery-item-content").attr("data-scene-title-id");
                } else {
                    id_replace = after_element.attr("data-sale-id");
                    replace_type = 'sale'
                }
            }else if(direction == "right"){
                let prev_element = $(ui.item).prev();
                if(prev_element.hasClass("cvideo")){
                    id_replace = prev_element.find(".project-delivery-item-content").attr("data-scene-title-id");
                } else {
                    id_replace = prev_element.attr("data-sale-id");
                    replace_type = 'sale'
                }
            }
            // drag drop item sang list khac
            item_right = ""
            item_left = ""
            item_left_type = 'scene'
            item_right_type = 'scene'
            if (Math.abs(ui.offset.top - ui.originalPosition.top) > 100){
                let prev_element = $(ui.item).prev();
                let after_element = $(ui.item).next();
                if(prev_element.hasClass("cvideo")){
                    item_left = prev_element.find(".project-delivery-item-content").attr("data-scene-title-id");
                } else {
                    item_left = prev_element.attr("data-sale-id");
                    item_left_type = 'sale'
                }
                if(after_element.hasClass("cvideo")){
                    item_right = after_element.find(".project-delivery-item-content").attr("data-scene-title-id");
                } else {
                    item_right = after_element.attr("data-sale-id");
                    item_right_type = 'sale'
                }
                new_list_id = $(ui.item).parents(".tab-section__item").attr("data-item-id");
                if(new_list_id == list_id){
                    return;
                }
            }
            $.ajax({
                type: "POST",
                url: "/collection/order_book_mark_item",
                data: {
                    'list_id': list_id,
                    'target_id': target_id,
                    'target_type': target_type,
                    'direction': direction,
                    'id_replace': id_replace,
                    'replace_type': replace_type,
                    'new_list_id': new_list_id,
                    'item_left': item_left,
                    'item_left_type': item_left_type,
                    'item_right': item_right,
                    'item_right_type': item_right_type,
                },
                success: function(data){
                    if (data.code == 200){
                        title.trigger("click");
                        // toastr.success('シーンを移動しました');
                    }
                },
                fail: function (data) {
                   toastr.error('エラーが発生しました', 'シーン並び替え');
                }
            })
        },
        stop: function(event, ui){
            let list_scenes = $(ui.item).parents(".tab-section__item").find(".project-delivery-item-content");
            let target_id = $(ui.item).find(".project-delivery-item-content").attr("data-scene-title-id");
            var title = $(ui.item).find(".cvideo__title");
            let number = 0;
            list_scenes.each(function(j, scene){
                if($(scene).attr("data-scene-title-id") == target_id){
                    number++;
                }
            })
            if(number == 2){
                $(this).sortable('cancel');
                title.trigger("click");
            }
        }
    }).disableSelection();
}
function dragList(){
    // Drag list
    $(".tab-section__list").sortable({
        containment: "document",
        items: "> div",
        connectWith: ".tab-section__list",
        handle: '.tab-section__header:not(.can-not-drag)',
        cursor: "move",
        scroll: true,
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
            let index = ui.item.index();
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
            let list_id = $(ui.item).attr('data-item-id');
            let prev_id = $(ui.item).prev().attr('data-item-id');
            let after_id = $(ui.item).next().attr('data-item-id');
            $.ajax({
                type: "POST",
                url: "/collection/order_list_bookmark",
                data: {
                    'list_id': list_id,
                    'prev_id': prev_id,
                    'after_id': after_id
                },
                success: function(data){
                    if(data.code == 200){
                        // toastr.success('テーマを並び替えました');
                    }else{
                        toastr.error('エラーが発生しました', 'シーン並び替え');
                    }
                },
                fail: function (data) {
                   toastr.error('エラーが発生しました', 'シーン並び替え');
                }
            })
        },
    }).disableSelection();
}

function onchangeInputCreateList() {
    $("#create-new-modal input").on("input", function(e){
        let value = e.target.value.trim();
        let btn = $("#create-new-modal .btn-new-create");
        btn.toggleClass('disabled', !Boolean(value));
    })
}

function collapseListBookmark(){
    $(".pd-chapter__toggle").off("click").on("click", function(e){
        e.preventDefault();
        e.stopPropagation();
        let target = $(e.target);
        target.toggleClass('active');
        let list_bookmark = $(".tab-section__item:not(.tab-section__item-root)").find(".tab-section__main");
        list_bookmark.each((index, list) => {
            if (target.hasClass('active')){
                $(list).slideDown(300);
                $(".tab-section__header").addClass("can-not-drag");
                $(".pd-chapter__line").hide();
            }else{
                $(list).slideUp(300);
                $(".tab-section__header").removeClass("can-not-drag");;
                $(".pd-chapter__line").show();
            }
        })
    })
    dragList();
}

function editListName(old_name, item_id, title) {
    $('.btn-edit-change').off('click').on('click', function (e) {
        let input_text = $('#edit-list-name-modal').find('.input-title-name').val();
        input_text = input_text.trim();
        if (input_text == old_name){
            $('#edit-list-name-modal').modal('hide');
            return
        }
        if(input_text.length > MAX_LENGTH_NAME || !input_text.length){
            toastr.error('エラーが発生しました', 'シーン並び替え');
        }else{
            $.ajax({
                type: "POST",
                url: "/collection/edit_list_name",
                data: {
                    'name': input_text,
                    'list_id': item_id
                },
                beforeSend: function (xhr, settings) {
                    xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                },
                success: function (response) {
                    if (response.code == 200) {
                        let product_scene = $('.tab-section__item[data-item-id^=' + item_id + ']');
                        product_scene.find('.tab-section__title').text(input_text);
                        if (!response.mes){
                            // toastr.success('テーマ名を更新しました');
                        }
                        $('#edit-list-name-modal').modal('hide');
                    } else {
                        toastr.error('エラーが発生しました', 'チャプター名を編集');
                    }
                },
                error: function () {
                    toastr.error('エラーが発生しました', 'チャプター名を編集');
                }
            })
        }
    });

    $('.btn--edit').off("click").on("click", function(){
        $(".input-title-name").val("");
    });

    $("#edit-list-name-modal").on("hidden.bs.modal", function () {
        title.next().removeClass("active");
    });

}

function deleteListBookmark(list_id, title) {
    $('.btn-delete-list').off('click').on('click', function (e) {
        $.ajax({
            type: "POST",
            url: "/collection/delete_list_bookmarks",
            data: {
                'list_id': list_id
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                if (response.code == 200) {
                    $('.tab-section__item[data-item-id^='+list_id+']').remove();
                    // toastr.success('テーマを削除しました。');
                } else {
                    toastr.error('エラーが発生しました', 'チャプター削除');
                }
            },
            error: function () {
                toastr.error('エラーが発生しました', 'チャプター削除');
            }
        })
        $('#delete-list-bookmark').modal('hide');
    })
    $("#delete-list-bookmark").on("hidden.bs.modal", function () {
        title.next().removeClass("active");
    });
}

function unbookmarkScene(title_id, title) {
    $('.btn-unbookmark-scene').off('click').on('click', function (e) {
        $.ajax({
            type: "POST",
            url: "/collection/unbookmark",
            data: {
                'title_id': title_id
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                if (response.code == 200) {
                    let scenes = $(`.project-delivery-item-content[data-scene-title-id='${title_id}']`);
                    scenes.each(function(i, item){
                       $(item).parent(".cvideo").remove();
                    });
                    // toastr.success("ブックマークのリストから削除しました。");
                } else {
                    toastr.error('エラーが発生しました', 'チャプター削除');
                }
            },
            error: function () {
                toastr.error('エラーが発生しました', 'チャプター削除');
            }
        })
        $('#unbookmark-scene').modal('hide');
    });

    $("#unbookmark-scene").on("hidden.bs.modal", function () {
        title.next().removeClass("active");
    });
}

function editDeleteList() {
    // Click header show action btn edit & btn delete
    $(document).on('click', '.tab-section__header', function () {
        let action_element = $(this).find(".tab-section__action");
        let title = $(this).find(".tab-section__title");
        action_element.toggleClass("active");
        title.toggleClass("tab-section__title-active");
    });
    //Hover header show action btn edit & btn delete
    $(document).on('mouseover mouseleave', '.tab-section__header', function (e) {
        let action_element = $(this).find(".tab-section__action");
        let title = $(this).find(".tab-section__title");
        if ($('.tab-section__header').length < 2) {
            action_element.find('.btn--delete').addClass('hide')
        } else {
            action_element.find('.btn--delete').removeClass('hide')
        }
        if(action_element.hasClass('active') && e.type === 'mouseleave'){
            action_element.removeClass("active");
        }
        action_element.toggleClass("active-hover", e.type === 'mouseover');
        title.toggleClass("tab-section__title-active", e.type === 'mouseover');
    });
    // Event btn edit
    $(document).on('click', '.tab-section__header .btn--edit', function (e) {
        e.preventDefault();
        e.stopPropagation();
        var title = $(e.target).parents(".tab-section__header").find('.tab-section__title');
        let value = title.text();
        let item_id = $(this).parents('.tab-section__item').attr('data-item-id');
        $("#edit-list-name-modal").find("input").val(value);
        editListName(value, item_id, title);
    });

    collapseListBookmark();

    // Event btn delete
    $(document).on('click', '.tab-section__header .btn--delete', function (e) {
        e.preventDefault();
        e.stopPropagation();
        var title = $(e.target).parents(".tab-section__header").find('.tab-section__title');
        let list_id = $(this).parents('.tab-section__item').attr('data-item-id');
        deleteListBookmark(list_id, title);
    });
}

function openModalAddItem(){
    $(".pd-chapter__add").off("click").on("click", function(e){
        let add_button = e.target;
        let list_bookmark = $(add_button).parents(".tab-section__item");
        let list_id = list_bookmark.attr("data-item-id");
        $("#add-item-modal").attr("data-list-id", list_id);
        $("#add-item-modal .pd-chapter__list").html("");
        $("#search-input").val("");
        $.ajax({
            type: "POST",
            url: "/collection/get_modal_add_item",
            data: {
                'list_id': list_id
            },
            success: function (data) {
                if (data.code == 200) {
                    let html = data.html;
                    $("#add-item-modal .pd-chapter__list").append(html);
                    $("#add-item-modal .stars").each(function(i, item){
                        let rating = parseInt($(item).attr("data-rating"));
                        $(item).find(".star-" + rating ).addClass("active");
                    })
                } else {
                    toastr.error('エラーが発生しました');
                }
            },
            error: function () {
                toastr.error('エラーが発生しました');
            }
        })
        
        $("#search-input").on("keyup", function(e){
            let list_items = $(".modal-content .cvideo");
            let input_text = $(this).val().trim().toLowerCase();
            list_items.each(function(i, item){
                if(!input_text){
                    $(item).show();
                    return;
                }
                let title = $(item).find(".cvideo__heading").text().trim().toLowerCase();
                if(title.includes(input_text)){
                    $(item).show();
                }else{
                    $(item).hide();
                }
            })
        })
    });
}


function previousURL() {
    $('.sheader-link.current').click(function (e) {
        e.preventDefault();
        let url_string = window.location.href;
        if (url_string.includes('/collection')) {
            var ref = document.referrer;
            if (ref) {
                location.href = document.referrer
            } else {
                location.href ='/';
            }
        }
    });
}

$('#create-new-modal').on('hidden.bs.modal', function (e) {
    $(this).find("input").val('').end();
    $("#create-new-modal .btn-new-create").addClass('disabled');
})

    
function actionAlbum() {
    $(document).on('mouseenter mouseleave', '.list-new-works__media.gallery__item', function(e) {
        if($(this).parents('.modal-dialog').length) {
            return
        }
        if (e.type === 'mouseenter') {
            let target = $(this)
            let timeout = 200;
            if(hover_zooming) {
                timeout = 400;
            }
            let current_hover_id = hovering_id;
            hovering = true;
            setTimeout(() => {
                if(hovering && current_hover_id == hovering_id) {
                    let audio = target.find('audio')[0];
                    $('video, audio').each(function(i, e) {
                        e.pause();
                    })
                    audio.loop = true
                    audio.play()
                }
            }, timeout)
        } else if (e.type === 'mouseleave') {
            $(this).find('audio')[0].pause()
            hovering = false;
            hovering_id++;
        }
    })

    $(document).on('click', '.list-new-works__media.gallery__item', function(e) {
        let id = $(this).parents('.list-new-works__item-container').attr('data-sale-id')
        $(this).find('audio, video').each(function(i, e) {
            e.pause();
        })
        window.open('/accounts/creator/' + $(this).parents('.list-new-works__item-container').attr('data-artist') + '?playing_id=' + id + '&autoplay=true', "_blank" )
        hovering = false;
        hovering_id++;
    })
}

$(document).ready(function () {
    pageCollection();
    previousURL();
    activeTab();
    projectRating();
    show_modal();
    hover_play();
    openModalAddItem();
    actionAlbum()
    $('#tabs-1').on('click', '.project-delivery-item-content .cvideo__thumb', function (e) {
        stop_video_audio();
        let url_string = $(this).attr('data-link');
        window.open(url_string);
    });
});
