# 01EG89H6SS2141VNGDDEHBMV4Q
# -*- coding: utf-8 -*-
from django.contrib.auth.decorators import login_required
from django.urls import re_path, path
from app import views
from app import apis
from app.app_views import upload_contract_and_plan_views, create_update_staff_credit_views, offer_project_in_payment
from app.util import login_staff_required, login_messenger_required, login_master_admin_required, login_curator_required
from app.views import check_offer_creator, charge_for_order, taken_scenes

from .apis import MessageFolderItemsApiView, ProjectFinishedVideo, ProjectListView, ProjectDetailView, ProductSceneApiView, ProjectCommentView, MessengerArtistView, OfferMessageApiView, ProjectMembersApiView, ProjectMessengerArtistApiView, ProjectProcessVideo, ProjectStaffCredit, ProjectUpdateVideo, ProjectUpdatedCountApiView, ActiveProjectsApiView, CompletedProjectsApiView
from accounts.apis import <PERSON>gin<PERSON>pi<PERSON>ie<PERSON>, AuthUserApiView, MeApiView
from mileages.apis import MyMileageRankView

from .views import upgrate_director_to_producer_project, swap_order_user


urlpatterns = [
    path("", views.Index.as_view(), name="index"),
    re_path(r'^index.html$', views.Index.as_view(), name='index'),
    re_path(r'^$', views.Index.as_view(), name='index'),
    re_path(r'^about.html$', views.About.as_view(), name='about'),
    re_path(r'^about$', views.About.as_view(), name='about'),
    re_path(r'^service.html$', views.Service.as_view(), name='service'),
    re_path(r'^service$', views.Service.as_view(), name='service'),
    re_path(r'^ownershelp.html$', views.OwnerShelp.as_view(), name='ownershelp'),
    re_path(r'^ownershelp$', views.OwnerShelp.as_view(), name='ownershelp'),
    re_path(r'^ownershelp_200810_sendfile.html$', views.OwnerShelpSendFile.as_view(), name='ownershelp_sendfile'),
    re_path(r'^ownershelp_200810_sendfile$', views.OwnerShelpSendFile.as_view(), name='ownershelp_sendfile'),
    re_path(r'^partnerhelp.html$', views.PartnerHelp.as_view(), name='partnerhelp'),
    re_path(r'^partnerhelp$', views.PartnerHelp.as_view(), name='partnerhelp'),
    re_path(r'^privacypolicy.html$', views.PrivacyPolicy.as_view(), name='privacypolicy'),
    re_path(r'^privacypolicy$', views.PrivacyPolicy.as_view(), name='privacypolicy'),
    re_path(r'^termsofservice.html$', views.TermsofService.as_view(), name='termsofservice'),
    re_path(r'^termsofservice$', views.TermsofService.as_view(), name='termsofservice'),
    re_path(r'^updateinfo.html$', views.UpdateInfo.as_view(), name='updateinfo'),
    re_path(r'^updateinfo$', views.UpdateInfo.as_view(), name='updateinfo'),
    # re_path(r'^updateinfonew$', views.UpdateInfoNew.as_view(), name='updateinfonew'),
    re_path(r'^post/delete_post$', views.delete_post, name='delete_post'),
    re_path(r'^post/add_update_post_info$', views.add_update_post_info, name='add_update_post_info'),
    re_path(r'^post/get_post_items$', views.get_post_items, name='get_post_items'),
    re_path(r'^tokushoho.html$', views.Tokushoho.as_view(), name='tokushoho'),
    re_path(r'^tokushoho$', views.Tokushoho.as_view(), name='tokushoho'),
    re_path(r'^news/', views.News.as_view(), name='news'),
    re_path(r'^mgk$', views.MgkView.as_view(), name='mgk'),
    re_path(r'^os$', views.OsView.as_view(), name='os'),
    re_path(r'^test$', views.TestView.as_view(template_name="creator/___test.html"), name='test'),
    re_path(r'^test1$', views.TestView.as_view(template_name="creator/___test1.html"), name='test1'),
    re_path(r'^test2$', views.TestView.as_view(template_name="creator/___test2.html"), name='test2'),
    # re_path(r'^process_review/$', views.process_review, name='process_review'),
    re_path(r'^test3$', views.TestView.as_view(template_name="creator/___test3.html"), name='test3'),
    re_path(r'^test4$', views.TestView.as_view(template_name="creator/___test4.html"), name='test4'),
    re_path(r'^test5$', views.TestView.as_view(template_name="creator/___test5.html"), name='test5'),
    re_path(r'^test6$', views.TestView.as_view(template_name="creator/___test6.html"), name='test6'),
    re_path(r'^test7$', views.TestView.as_view(template_name="creator/___test7.html"), name='test7'),
    re_path(r'^test8$', views.TestView.as_view(template_name="creator/___test8.html"), name='test8'),
    re_path(r'^test9$', views.TestView.as_view(template_name="creator/___test9.html"), name='test9'),
    re_path(r'^product/list$', login_required(views.ProductList.as_view()), name='product_list'),
    # re_path(r'^product/create$', login_master_admin_required(views.ProductCreate.as_view()), name='product_create'),
    re_path(r'^product/active/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_staff_required(views.product_active),
        name='product_active'),
    re_path(r'^product/order/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_staff_required(views.ProductOrder.as_view()),
        name='product_order'),
    re_path(r'^product/order/change/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.OrderCommentChange.as_view()),
        name='order_comment_change'),
    re_path(r'^product/order/delete/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.OrderCommentDelete.as_view()),
        name='order_comment_delete'),
    re_path(r'^product/order/upload/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.ProductOrderUpload.as_view()),
        name='product_order_upload'),
    re_path(r'^product/order/file/delete/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.ProductOrderFileDelete.as_view()),
        name='product_order_file_delete'),
    re_path(r'^product/delete/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_master_admin_required(views.ProductDelete.as_view()),
        name='product_delete'),
    re_path(r'^product/scene/add$', login_required(views.ProductSceneAdd.as_view()),
        name='product_scene_add'),
    re_path(r'^product/scene/create$', login_required(views.ProductSceneCreate.as_view()),
        name='product_scene_create'),
    re_path(r'^product/export_csv/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.ExportCSV), name='export_csv'),
    re_path(r'^product/account/list$', login_required(views.ProductAccountList.as_view()), name='product_account_list'),
    re_path(r'^product/scene/delete/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.ProductSceneDelete.as_view()),
        name='product_scene_delete'),
    re_path(r'^product/order/tag/update/(?P<product_order_upload_id>[a-zA-Z0-9¥-]+)/$', login_required(views.TagProductOrderChange), {},
        name='product_order_tag_update'),
    re_path(r'^product_user/(?P<product_user_id>[a-zA-Z0-9¥-]+)/$', login_required(views.ProductUserChange), {},
        name='product_user_change'),
    re_path(r'^product/information/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.ProductInformation.as_view()),
        name='product_information'),
    re_path(r'^scene$', login_required(views.Scene.as_view()), name='scene_index'),
    re_path(r'^scene/filter$', login_required(views.SceneFilter.as_view()), name='scene_filter'),
    re_path(r'^scene/list$', login_required(views.SceneList.as_view()), name='scene_list'),
    re_path(r'^scene/(?P<strslug>[a-zA-Z0-9+-\_]+)/show$', views.scene_show, name='scene_show'),
    re_path(r'^scene/get_link_back_to_chapter$', views.get_link_back_to_chapter, name='chapter_show'),
    re_path(r'^scene/update_status/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneUpdateStatus.as_view()),
        name='scene_update_status'),
    re_path(r'^scene/edit/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneEdit),
        name='scene_edit'),
    re_path(r'^scene/delete/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneDelete.as_view()),
        name='scene_delete'),
    re_path(r'^scene/title_delete$', login_required(views.SceneTitleDelete.as_view()),
        name='scene_title_delete'),
    re_path(r'^scene/title_move$', login_required(views.SceneTitleMove.as_view()),
        name='scene_title_move'),
    re_path(r'^scene/comment/(?P<scene_id>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneComment.as_view()),
        name='scene_comment'),
    re_path(r'^scene/order/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.order_scene),
        name='scene_order'),
    re_path(r'^scene/comment/change/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneCommentChange.as_view()),
        name='scene_comment_change'),
    re_path(r'^scene/comment/delete/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneCommentDelete.as_view()),
        name='scene_comment_delete'),
    re_path(r'^scene/tag/change/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneTagChange.as_view()),
        name='scene_tag_change'),
    re_path(r'^scene/upload$', login_required(views.SceneUpload.as_view()), name='scene_upload'),
    re_path(r'^scene/change/(?P<pk>[a-zA-Z0-9¥-]+)/$', login_required(views.SceneChange.as_view()), name='scene_change'),
    re_path(r'^scene/tag/update/(?P<scene_id>[a-zA-Z0-9¥-]+)/$', login_required(views.TagChange), {},
        name='scene_tag_update'),
    re_path(r'^scene/rating_scene$', login_required(views.rating_scene_title), {}, name='rating_scene'),
    re_path(r'^scene/undone$', login_required(views.undone_scene_title), {}, name='undone_scene_title'),
    re_path(r'^scene/upload_production$', login_required(views.upload_production), {}, name='upload_production'),
    re_path(r'^scene/bookmark_scene_title$', login_required(views.bookmark_scene_title), {}, name='bookmark_scene_title'),
    re_path(r'^scene/update_can_share$', login_required(views.update_can_share), {}, name='update_can_share'),
    re_path(r'^scene/taken/(?P<pk>[a-zA-Z0-9¥-]+)/$', taken_scenes, name='scene_taken'),
    re_path(r'^ajax/edit_product_scene_name/$', views.edit_product_scene_name, name='edit_product_scene_name'),
    re_path(r'^ajax/delete_product_scene/$', views.delete_product_scene, name='delete_product_scene'),
    re_path(r'^ajax/undelete_product_scene/$', views.undelete_product_scene, name='undelete_product_scene'),
    re_path(r'^ajax/edit_scene_video_name/$', views.edit_scene_video_name, name='edit_scene_video_name'),
    re_path(r'^ajax/delete_scene_video/$', views.delete_scene_video, name='delete_scene_video'),
    re_path(r'^download/video/$', views.download_video, name='download_video'),
    re_path(r'^ajax/change_permission/$', views.change_permission, name='change_permission'),
    re_path(r'^ajax/add-user-project/$', views.add_user_to_project, name='add_user_project'),
    re_path(r'^ajax/delete_permission/$', views.delete_permission, name='delete_permission'),
    re_path(r'^ajax/product_scene_filter/$', views.product_scene_filter, name='product_scene_filter'),
    re_path(r'^ajax/product_filter/$', views.product_filter, name='product_filter'),
    re_path(r'^ajax/get_button/$', views.get_button_next_prev, name='get_button_next_prev'),
    re_path(r'^ajax/preview_video/$', views.update_preview_video, name='update_preview_video'),
    re_path(r'^payment/$', views.payment, name='payment'),
    re_path(r'^charge/$', views.charge, name='charge'),
    re_path(r'^order/new$', views.new_order, name='new_order'),
    re_path('^warning/404$', views.warning, name='warning'),
    re_path(r'^offer_message/get_room_message$', views.get_room_messages, name='get_room_message'),
    re_path(r'^offer_message/create$', views.create_offer_message, name='create_offer_message'),
    re_path(r'^upload/create_file$', views.create_files, name='create_files'),
    re_path(r'^top/create_file_comment$', views.create_file_comment, name='create_file_comment'),
    re_path(r'^top/create_or_update_scene_link$', views.create_or_update_scene_link, name='create_or_update_scene_link'),
    re_path(r'^upload/create_folder$', views.create_folder, name='create_folder'),
    re_path(r'^upload/delete_folder$', views.delete_folder, name='delete_folder'),
    re_path(r'^top/get_item_in_message$', views.get_item_in_message, name='get_item_in_message'),
    re_path(r'^get_presigned_url$', views.get_presigned_url, name='get_presigned_url'),
    re_path(r'^top/update_scene_comment$', views.update_scene_comment, name='update_scene_comment'),
    re_path(r'^upload/remove_file$', views.remove_files, name='remove_files'),
    re_path(r'^offer_message/update_seen$', views.update_seen_offer_message, name='update_seen_offer_message'),
    re_path(r'^audio/create/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.AudioCreate.as_view()), name='audio_add'),
    re_path(r'^home$', views.HomePageView.as_view(), name='home_page'),
    re_path(r'^content_sale/create/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.ContentSaleCreate.as_view()), name='content_sale_create'),
    re_path(r'^content_sale/update/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.ContentSaleUpdate.as_view()), name='content_sale_update'),
    re_path(r'^content_sale/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.ContentSaleList.as_view()), name='content_sale_list'),
    re_path(r'^admin_content_sale/$', login_required(views.AdminContentSaleList.as_view()), name='admin_content_sale_list'),
    re_path(r'^introduction$', views.HomePageView.as_view(), name='home_page'),
    re_path(r'^admin_review$', views.admin_review_sale_content, name='admin_review_sale_content'),
    re_path(r'^change_status_sale_content', views.change_status_sale_content, name='change_status_sale_content'),
    re_path(r'^creators$', views.CreatorIndex.as_view(), name='creator_index'),
    re_path(r'^gallery.html$', views.CreatorIndex.as_view(), name='creator_index'),
    re_path(r'^gallery$', views.CreatorIndex.as_view(), name='creator_index'),
    re_path(r'^offer_creator/search$', login_required(views.CreatorScheduleSearchView.as_view()), name='creator_search'),
    re_path(r'^offer_creator$', login_required(views.OfferCreatorView.as_view()), name='creator_offer'),
    re_path(r'^check_offer_creator$', login_required(check_offer_creator), name='check_creator_offer'),
    re_path(r'^get_thumbnail_video$', login_required(views.get_thumbnail_video), name='get_thumbnail_video'),
    re_path(r'^top$', views.TopPage.as_view(), name='top_page'),
    re_path(r'^top/load_project_list_with_order$', views.load_project_list_with_order, name='load_project_list_with_order'),
    re_path(r'^top/load_more_project$', views.load_more_project, name='load_moretop_page'),
    re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)/scene/(?P<pk_scene>[a-zA-Z0-9¥-]+)$', views.SceneDetail.as_view(),
        name='scene_title_detail'),
    re_path(r'^top/load_more_scene_comment', views.get_load_more_comments_scene_title, name='load_more_scene_comment'),
    # re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)$', views.TopProjectDetailView.as_view(), name='top_project_detail'),
    re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)$', views.TopProjectDetailViewNew.as_view(), name='top_project_detail'),
    re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)/search$', views.search_project_scene_title, name='top_project_search'),
    re_path(r'^top/get_project_video$', views.get_project_video, name='get_project_video'),
    re_path(r'^top/get_comment', views.get_comment, name='get_comment'),
    re_path(r'^top/refactor_get_comment', views.get_comment_refactor, name='get_comment_refactor'),
    re_path(r'^top/get_load_more_comment', views.get_load_more_comment, name='get_load_more_comment'),
    re_path(r'^top/get_messenger_artist', views.get_messenger_artist, name='get_messenger_artist'),
    re_path(r'^top/get_refactor_messenger_artist', views.get_messenger_artist_refactor, name='get_messenger_artist_refactor'),
    re_path(r'^form_upload_and_plans/get_current_form_upload_and_plan', upload_contract_and_plan_views.get_current_form_upload_and_plan,
        name='get_current_form_upload_and_plan'),
    re_path(r'^top/create_comment$', views.create_comment, name='create_comment'),
    re_path(r'^top/seen_comment$', views.seen_comment, name='seen_comment'),
    re_path(r'^top/delete_comment$', views.delete_comment, name='delete_comment'),
    re_path(r'^top/update_comment$', views.update_comment, name='update_comment'),
    re_path(r'^top/update_size_scene$', views.update_size_scene, name='update_size_scene'),
    re_path(r'^top/mark_as_read_comment$', views.mark_as_read_comment, name='mark_as_read_comment'),
    re_path(r'^top/resolve_comment$', views.resolve_comment, name='resolve_comment'),
    re_path(r'^top/get_project_process_video$', views.get_project_process_video, name='get_project_process_video'),
    re_path(r'^top/get_project_process_video_load_more$', views.get_project_process_video_load_more,
        name='get_project_process_video_load_more'),
    re_path(r'^top/get_project_update_video$', views.get_project_update_video, name='get_project_update_video'),
    re_path(r'^top/get_project_wattings_feedback$', views.get_project_wattings_feedback, name='get_project_wattings_feedback'),
    re_path(r'^top/get_scenes_of_title_by_scene$', views.get_scenes_of_title_by_scene, name='get_scenes_of_title_by_scene'),
    re_path(r'^top/get_message_scene_title$', views.get_message_scene_title, name='get_message_scene_title'),
    re_path(r'^top/get_all_file_scene_title$', views.get_all_file_scene_title, name='get_all_file_scene_title'),
    re_path(r'^top/get_status_scene_title$', views.get_status_scene_title, name='get_status_scene_title'),
    re_path(r'^top/mass_download$', views.mass_download, name='mass_download'),
    re_path(r'^top/admin_setting_project', views.admin_setting_project, name='admin_setting_project'),
    re_path(r'^top/project_member_list', login_required(views.project_member_list), name='project_member_list'),
    re_path(r'^top/project_admin_list', login_required(views.project_admin_list), name='project_admin_list'),
    re_path(r'^top/project_members', views.project_members, name='project_members'),
    re_path(r'^top/member_list_ip', login_required(views.member_list_ip), name='member_list_ip'),
    re_path(r'^top/update_ip_user', login_required(views.update_ip_user), name='update_ip_user'),
    re_path(r'^top/delete_ip_user', login_required(views.delete_ip_user), name='delete_ip_user'),
    re_path(r'^top/update_access_view_user', login_required(views.update_access_view_user), name='update_access_view_user'),
    re_path(r'^top/update_project_member', views.update_project_member, name='update_project_member'),
    re_path(r'^top/search_project_list', views.search_project_list, name='search_project_list'),
    re_path(r'^top/invite_user', views.InviteUser.as_view(), name='invite_user'),
    re_path(r'^top/update_view_only_product', views.update_view_only_status, name='update_view_only_status'),
    re_path(r'^top/mark_as_ok', views.mark_as_ok, name='mark_as_ok'),
    re_path(r'^top/mark_as_done', views.mark_as_done, name='mark_as_done'),
    re_path(r'^top/upload_video', views.upload_video_top, name='upload_video_top'),
    re_path(r'^top/create_product_scene_by_name', views.create_product_scene_by_name, name='create_product_scene_by_name'),
    re_path(r'^top/check_email_exist_invite_user', views.check_email_exist_invite_user,
        name='check_email_exist_invite_user'),
    re_path(r'^top/delete_scene', views.delete_scene, name='delete_scene'),
    re_path(r'^top/edit_scene', views.edit_scene, name='edit_scene'),
    re_path(r'^top/get_file_download_link$', views.get_file_download_link, name='get_file_download_link'),
    re_path(r'^top/update_product_scene_index$', views.update_product_scene_index, name='update_product_scene_index'),
    re_path(r'^top/update_scene_title_position$', views.update_scene_title_position, name='update_scene_title_position'),
    re_path(r'^top/update_max_scene$', login_required(views.update_max_scene), name='update_max_scene'),
    re_path(r'^messenger/project', login_messenger_required(views.MessengerProject.as_view()), name='messenger_project'),
    re_path(r'^messenger/search_creator', views.search_creator_with_skill,
        name='search_creator'),
    re_path(r'^messenger/get_infor_artist', login_messenger_required(views.get_infor_artist),
        name='get_infor_artist'),
    re_path(r'^messenger/search', login_messenger_required(views.DirectorMessengerSearch.as_view()),
        name='director_messenger_search'),
    re_path(r'^messenger/waiting', login_messenger_required(views.MessengerWaiting.as_view()), name='messenger_waiting'),
    re_path(r'^messenger/processing', login_messenger_required(views.MessengerProcessing.as_view()),
        name='messenger_processing'),
    re_path(r'^messenger/load_offer', login_messenger_required(views.load_offer_message), name='load_offer_message'),
    re_path(r'^messenger/refactor_load_offer', login_messenger_required(views.load_offer_message_refactor), name='load_offer_message_refactor'),
    re_path(r'^messenger/load_more_offer_message', login_messenger_required(views.get_load_more_offer_message),
        name='load_more_offer_message'),
    re_path(r'^messenger/update_status_offer', login_messenger_required(views.update_offer_status),
        name='update_offer_status'),
    re_path(r'^messenger/update_review_offer', login_messenger_required(views.update_offer_review),
        name='update_offer_review'),
    re_path(r'^messenger/ajax/update_reject_offer', login_messenger_required(views.update_offer_reject),
        name='update_offer_reject'),
    re_path(r'^messenger/ajax/update_offer_closed', login_messenger_required(views.update_offer_closed),
        name='update_offer_closed'),
    re_path(r'^messenger/offer_accept', login_messenger_required(views.update_offer_accept), name='update_offer_accept'),
    re_path(r'^messenger/delete_message', login_messenger_required(views.delete_message), name='delete_message'),
    re_path(r'^messenger/update_offer_message', login_messenger_required(views.update_offer_message),
        name='update_offer_message'),
    re_path(r'^messenger/update_peaks_file', login_messenger_required(views.update_peaks_file),
        name='update_peaks_file'),
    re_path(r'^messenger/get_information_offer', login_messenger_required(views.get_information_offer),
        name='get_information_offer'),
    re_path(r'^messenger/edit_information_offer', login_messenger_required(views.edit_information_offer),
        name='edit_information_offer'),
    re_path(r'^messenger/offer_creator_edit_confirm', login_messenger_required(views.offer_creator_edit_confirm),
        name='offer_creator_edit_confirm'),
    re_path(r'^messenger/offer_creator_edit_reject', login_messenger_required(views.offer_creator_edit_reject),
        name='offer_creator_edit_reject'),
    re_path(r'^messenger/check_edit_offer', login_messenger_required(views.check_edit_offer),
        name='check_edit_offer'),
    re_path(r'^messenger/get_task_deadline', login_messenger_required(views.get_task_deadline), name='get_task_deadline'),
    re_path(r'^messenger/get_offer', login_messenger_required(views.get_offer), name='get_offer'),
    re_path(r'^ajax/search_offer_creator', login_messenger_required(views.search_offer_creator),
        name='search_offer_creator'),
    re_path(r'^acr/test', views.AcrTestView.as_view(), name='acr_test'),
    re_path(r'^product/(?P<pk>[a-zA-Z0-9¥-]+)/acr_export$', login_required(views.AcrCloudProjectExportView.as_view()),
        name='acr_export_csv'),
    re_path(r'^product/acr_export/check_download_acr$', login_required(views.check_download_acr),
        name='check_download_acr'),
    re_path(r'^direct/new', login_required(views.DirectInbox.as_view()), name='direct_inbox'),
    re_path(r'^top/load_message_product$', views.load_product_message, name='load_product_message'),
    re_path(r'^product_message/create$', views.create_product_message, name='create_product_message'),
    re_path(r'^product_message/update_seen$', views.update_seen_product_message, name='update_seen_product_message'),
    re_path(r'^direct/upload_plan$', views.upload_plan, name='upload_plan'),
    re_path(r'^direct/update_plan_status$', views.update_plan_status, name='update_plan_status'),
    re_path(r'^direct/create_product$', views.create_product, name='create_product'),
    re_path(r'^direct/upload_contract$', views.upload_contract, name='upload_contract'),
    re_path(r'^direct/upload_contract_confirm$', views.upload_contract_confirm, name='upload_contract_confirm'),
    re_path(r'^direct/upload_contract_reject$', views.upload_contract_reject, name='upload_contract_reject'),
    re_path(r'^direct/accept_contract$', views.accept_contract, name='accept_contract'),
    re_path(r'^direct/check_offer_status$', views.check_offer_status, name='check_offer_status'),
    re_path(r'^direct/upload_bill$', views.upload_bill, name='upload_bill'),
    re_path(r'^direct/check_payment$', views.check_payment, name='check_payment'),
    re_path(r'^direct/payment/charge$', login_required(charge_for_order), name='payment_charge'),
    # re_path(r'^project/message_owner/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.MessageOwner.as_view()), name='message_owner'),
    re_path(r'^project/message_owner/(?P<pk>[a-zA-Z0-9¥-]+)$', login_required(views.MessageOwnerRefactor.as_view()), name='message_owner'),
    re_path(r'^direct/create_offer', views.OfferProductCreateView.as_view(),
        name='direct_create_offer'),
    re_path(r'^direct/delete_unpaid_charge_product_user', login_required(views.delete_unpaid_charge_product_user),
        name='delete_unpaid_charge_product_user'),
    re_path(r'^charge_for_product', login_required(views.charge_for_product),
        name='charge_for_product'),
    re_path(r'^close_charged_product', login_required(views.close_charged_product),
        name='close_charged_product'),
    re_path(r'^direct/delete_offer_product', login_messenger_required(views.delete_offer_product),
        name='delete_offer_product'),
    re_path(r'^top/update_product_index$', views.update_product_index, name='update_product_index'),
    re_path(r'^top/setting_notification_product_user$', views.setting_notification_product_user,
        name='setting_notification_product_user'),
    re_path(r'^direct/load_file_offer$', views.load_file_offer, name='load_file_offer'),
    re_path(r'^direct/offer_creator_confirm$', views.offer_creator_confirm, name='offer_creator_confirm'),
    re_path(r'^direct/offer_creator_reject$', views.offer_creator_reject, name='offer_creator_reject'),
    re_path(r'^direct/get_link_pdf_contract_artist$', views.get_link_pdf_contract_artist, name='get_link_pdf_contract_artist'),
    re_path(r'^direct/get_artist_contract_download_link$', views.get_artist_contract_download_link, name='get_artist_contract_download_link'),
    re_path(r'^top/get_video_modal$', views.get_video_modal, name='get_video_modal'),
    re_path(r'^top/update_variation$', views.update_variation, name='update_variation'),
    re_path(r'^top/update_order_version$', views.update_order_version, name='update_order_version'),
    re_path(r'^top/update_detail_scenes$', views.update_detail_scenes, name='update_detail_scenes'),
    re_path(r'^top/upload_scene_for_scenetitle$', views.upload_scene_for_scenetitle, name='upload_scene_for_scenetitle'),
    re_path(r'^top/get_scene_detail_edit$', views.get_scene_detail_edit, name='get_scene_detail_edit'),
    re_path(r'^top/edit_version_detail_scene$', views.edit_version_detail_scene, name='edit_version_detail_scene'),
    re_path(r'^top/re_upload_scene$', views.re_upload_scene, name='re_upload_scene'),
    re_path(r'^top/update_title_for_scenetitle$', views.update_title_for_scenetitle, name='update_title_for_scenetitle'),
    re_path(r'^top/update_name_for_productscene$', views.update_name_for_productscene, name='update_name_for_productscene'),
    re_path(r'^top/update_name_for_variation$', views.update_name_for_variation, name='update_name_for_variation'),
    re_path(r'^top/get_scenetitle_videos$', views.get_scenetitle_videos, name='get_scenetitle_videos'),
    re_path(r'^top/update_product_owner$', views.update_product_owner, name='update_product_owner'),
    re_path(r'^top/get_scenetitle_process_videos$', views.get_scenetitle_process_videos,
        name='get_scenetitle_process_videos'),
    re_path(r'^messenger/update_offer/$', views.update_offer, name='update_offer'),
    re_path(r'^creator/upload_audio_creator$', views.upload_audio_creator, name='upload_audio_creator'),
    re_path(r'^get_sale_content_download_link$', views.get_sale_content_download_link,
        name='get_sale_content_download_link'),
    re_path(r'^get_video_url_with_fallback$', views.get_video_url_with_fallback,
        name='get_video_url_with_fallback'),
    re_path(r'^get_file_header_download_link$', views.get_file_header_download_link,
        name='get_file_header_download_link'),
    re_path(r'^get_product_file_download_link$', views.get_product_file_download_link,
        name='get_product_file_download_link'),
    re_path(r'^get_link_download_folder', views.get_link_download_folder,
        name='get_link_download_folder'),
    re_path(r'^get_link_pdf_to_preview', views.get_link_pdf_to_preview,
        name='get_link_pdf_to_preview'),
    re_path(r'^get_link_pdf_contract_plan', login_required(views.get_link_pdf_contract_plan),
        name='get_link_pdf_contract_plan'),
    re_path(r'^product/export_creator_offer_info/(?P<pk>[a-zA-Z0-9¥-]+)$',
        login_master_admin_required(views.export_creator_offer_info), name='export_creator_offer_info'),
    re_path(r'^get_list_admin$', login_required(views.get_list_admin),
        name='get_list_admin'),
    re_path(r'^add_edit_admin_product$', login_required(views.add_edit_admin_product),
        name='add_edit_admin_product'),
    re_path(r'^update_sale_content_index$', login_required(views.update_sale_content_index),
        name='update_sale_content_index'),
    re_path(r'^get_list_random_samples$', views.get_html_sound_samples,
        name='get_html_sound_samples'),
    re_path(r'^get_content_contract_modal$', login_required(views.get_content_contract_modal),
        name='get_content_contract_modal'),
    re_path(r'^creators/create_list_creators$', login_curator_required(views.create_list_creators),
        name="create_list_creators"),
    re_path(r'^creators/delete_creator_from_list', login_curator_required(views.delete_creator_from_list),
        name="delete_creator_from_list"),
    re_path(r'^(?P<slug>[a-zA-Z0-9-_.]+)$', views.CreatorView.as_view(), name='creator_info'),
    re_path(r'^gallery/get_creator_lists$', views.load_creator_lists, name='get_creator_lists'),
    re_path(r'^collection/get_bookmarked$', login_required(views.Collection.as_view()), name='get_bookmarked'),
    re_path(r'^collection/edit_list_name$', login_required(views.edit_list_bookmark_name), name='create_list_bookmark'),
    re_path(r'^gallery/load_more_creator_lists$', views.load_more_creator_lists, name='load_more_creator_lists'),
    re_path(r'^gallery/load_more_list_topics$', views.load_more_list_topics, name='load_more_list_topics'),
    re_path(r'^gallery/load_more_list_new_work$', views.load_more_list_new_work, name='load_more_list_new_work'),
    re_path(r'^gallery/get_artist_name_with_sale$', views.get_artist_name_with_sale, name='get_artist_name_with_sale'),
    re_path(r'^gallery/create_list_work$', views.create_list_work, name='create_list_work'),
    re_path(r'^gallery/update_list_work_index$', views.update_list_work_index, name='update_list_work_index'),
    re_path(r'^gallery/get_sale_content_to_add_list_work$', views.get_sale_content_to_add_list_work,
        name='get_sale_content_to_add_list_work'),
    re_path(r'^gallery/add_sale_content_into_list_work$', views.add_sale_content_into_list_work,
        name='add_sale_content_into_list_work'),
    re_path(r'^gallery/update_sale_content_index$', views.update_sale_content_index_gallery,
        name='update_sale_content_index_gallery'),
    re_path(r'^gallery/delete_list_work$', views.delete_list_work,
        name='delete_list_work'),
    re_path(r'^gallery/delete_sale_content_in_list_work$', views.delete_sale_content_in_list_work,
        name='delete_sale_content_in_list_work'),
    re_path(r'^gallery/edit_list_work_name$', views.edit_list_work_name,
        name='edit_list_work_name'),
    re_path(r'^gallery/create_topic_form$', login_required(views.TopicGalleryView.as_view()),
        name='topic_create'),

    re_path(r'^gallery/create_topic_form_create$', login_required(views.ajax_create_gallery),
        name='topic_create_ajax'),
    re_path(r'^gallery/ajax_update_topic_gallery$', login_required(views.ajax_update_topic_gallery),
        name='ajax_update_topic_gallery'),
    re_path(r'^gallery/delete_topic$', login_required(views.delete_topic),
        name='topic_delete'),
    re_path(r'^gallery/update_topic_index$', login_required(views.update_topic_index),
        name='update_topic_index'),
    re_path(r'^gallery/topic/(?P<pk>[a-zA-Z0-9¥-]+)$', views.TopicDetailView.as_view(), name='topic_detail'),
    re_path(r'^gallery/get_sale_content_to_add_selection$', login_required(views.get_sale_content_to_add_selection),
        name='selection_search_sale'),
    re_path(r'^gallery/add_sale_content_into_selection$', login_required(views.add_sale_content_into_selection),
        name='add_sale_content_into_selection'),
    re_path(r'^gallery/get_topic_detail$', views.get_topic_detail,
        name='get_topic_detail'),
    re_path(r'^gallery/topic_file_download_link$', views.topic_file_download_link,
        name='topic_file_download_link'),
    re_path(r'^collection/create_list_bookmarks$', login_required(views.create_list_bookmark), name='create_list_bookmark'),
    re_path(r'^collection/delete_list_bookmarks$', login_required(views.delete_list_bookmark), name='delete_list_bookmark'),
    re_path(r'^collection/unbookmark$', login_required(views.scene_unbookmark), name='scene_unbookmark'),
    re_path(r'^collection/get_modal_add_item$', login_required(views.get_modal_add_item), name='get_modal_add_item'),
    re_path(r'^collection/add_new_bookmark_items$', login_required(views.add_new_bookmark_items), name='add_new_bookmark_items'),
    re_path(r'^collection/delete_bookmark_item$', login_required(views.delete_bookmark_item), name='delete_bookmark_item'),
    re_path(r'^collection/order_book_mark_item$', login_required(views.order_book_mark_item), name='order_book_mark_item'),
    re_path(r'^collection/order_list_bookmark$', login_required(views.order_list_bookmark), name='order_list_bookmark'),
    re_path(r'^creator/update_creator_schedule$', login_required(views.update_creator_schedule),
        name='update_creator_schedule'),
    re_path(r'^top/get_project_detail_info$', login_required(views.get_project_detail_info),
        name='get_project_detail_info'),
    re_path(r'^top/update_project_setting$', login_required(views.update_project_setting),
        name='update_project_setting'),
    re_path(r'^top/get_project_setting_detail$', login_required(views.get_project_setting_detail),
        name='get_project_setting_detail'),
    re_path(r'^top/get_form_project_setting_modal/(?P<pk>[a-zA-Z0-9¥-]+)/',
        login_required(views.ProductUpdateModalView.as_view()),
        name='get_form_project_setting'),
    re_path(r'^top/search_artist_to_add_block_list$', views.search_artist_to_add_block_list,
        name='search_block_artist'),
    re_path(r'^top/add_artist_into_block_list$', views.add_artist_into_block_list,
        name='add_artist_into_block_list'),
    re_path(r'^top/update_member_to_owner$', views.update_member_to_owner,
        name='update_member_to_owner'),
    re_path(r'^top/project/top/change_userposition/$', views.change_userposition,
        name='change_userposition'),
    re_path(r'^top/project/top/swap_order_user/$', views.swap_order_user,
        name='swap_order_user'),
    re_path(r'^top/remove_director_project$', views.remove_director_project,
        name='remove_director_project'),
    re_path(r'^top/get_artist_to_add_project$', views.get_artist_to_add_project,
        name='get_artist_to_add_project'),
    re_path(r'^top/get_admin_to_add_project$', views.get_admin_to_add_project,
        name='get_admin_to_add_project'),
    re_path(r'^top/add_this_admin_to_project$', views.add_this_admin_to_project,
        name='add_this_admin_to_project'),
    re_path(r'^top/add_artist_to_director_project$', views.add_artist_to_director_project,
        name='add_artist_to_director_project'),
    re_path(r'^top/upgrate_director_to_producer_project$', views.upgrate_director_to_producer_project,
        name='upgrate_director_to_producer_project'),    
    re_path(r'^top/check_can_add_artist_to_master_producer$', views.check_can_add_artist_to_master_producer,
        name='check_can_add_artist_to_master_producer'),

    re_path(r'^top/get_acr_result_by_file$', views.get_acr_result_by_file,
        name='get_acr_result_by_file'),
    re_path(r'^gallery/search_sale_content_in_gallery$', views.search_sale_content_in_gallery,
        name='search_sale_content_in_gallery'),
    re_path(r'^direct/save_order_data$', views.save_order_data,
        name='save_order_data'),
    re_path(r'^credit/download_staff_credit_pdf$', create_update_staff_credit_views.download_staff_credit_pdf,
        name='download_staff_credit_pdf'),
    re_path(r'^credit/get_staff_credit_for_project$', create_update_staff_credit_views.get_staff_credit_for_project,
        name='get_staff_credit_for_project'),
    re_path(r'^credit/add_update_section_credit$', create_update_staff_credit_views.add_update_section_credit,
        name='add_update_section_credit'),
    re_path(r'^credit/delete_section_item_artist$', create_update_staff_credit_views.delete_section_item_artist,
        name='delete_section_item_artist'),
    re_path(r'^credit/update_index_section_staff_credit$',
        create_update_staff_credit_views.update_index_section_staff_credit,
        name='update_index_section_staff_credit'),
    re_path(r'^credit/add_update_item_artist_section_credit$',
        create_update_staff_credit_views.add_update_item_artist_section_credit,
        name='add_update_item_artist_section_credit'),
    re_path(r'^credit/get_all_artist_in_project$',
        create_update_staff_credit_views.get_all_artist_in_project,
        name='get_all_artist_in_project'),
    # master admin in screen payment
    re_path(r'^master_admin/delete_project_view$', login_required(offer_project_in_payment.delete_project_view),
        name='delete_project_view'),
    re_path(r'^master_admin/delete_offer_creator_view$', login_required(offer_project_in_payment.delete_offer_creator_view),
        name='delete_offer_creator_view'),
    re_path(r'^master_admin/get_information_offer_creator_view$',
        login_required(offer_project_in_payment.get_information_offer_creator_view),
        name='get_information_offer_creator_view'),
    re_path(r'^master_admin/update_info_offer_creator_view$',
        login_required(offer_project_in_payment.update_info_offer_creator_view),
        name='update_info_offer_creator_view'),
    re_path(r'^message/get_draft_message', login_messenger_required(views.get_draft_message),
        name='get_draft_message'),
    re_path(r'^message/save_draft_message', login_messenger_required(views.save_draft_message),
        name='save_draft_message'),
    re_path(r'^message/get_online_status_DM', login_messenger_required(views.check_online_user_DM),
        name='get_online_status_DM'),
    re_path(r'^message/check_AFK_user', login_required(views.checking_AFK_user),
        name='get_online_status_DM'),
    re_path(r'^direct/artist_create_product', login_required(views.ArtistChargeProductCreateView.as_view()),
        name='artist_create_product'),
    re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)/budget_information', views.ProjectBudgetInfo.as_view(),
        name='project_budget_information'),
    re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)/group_offer_product', views.group_offer_product,
        name='project_group_offer_product'),
    re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)/project_setting', views.ProjectSetting.as_view(),
        name='project_setting'),
    re_path(r'^top/add_block_artist_into_project_setting$', views.add_block_artist_into_project_setting,
        name='add_block_artist_into_project_setting'),
    re_path(r'^top/project/(?P<pk>[a-zA-Z0-9¥-]+)/project_schedule', views.get_project_schedule,
        name='project_schedule'),

    # new api
    re_path(r'^api/get-list-comment-data', apis.get_list_comment, name='`get_list_comment`'),
    re_path(r'^api/messager/load-more-offer-message', apis.get_load_more_offer_message, name='get_load_more_offer_message'),
    re_path(r'^api/messager/load-offer-message', apis.get_load_offer_message, name='get_load_offer_message'),
    re_path(r'^api/top/get-messenger-artist', apis.get_messenger_artist, name='get_messenger_artist'),
    re_path(r'^api/load-more-comments', apis.load_more_comments, name='load_more_comments'),
    re_path(r'^api/create-and-get-data-comment', apis.create_and_update_comment, name='create_and_update_comment'),
    re_path(r'^api/seen_comment', apis.seen_comment, name='seen_comment'),
    re_path(r'^api/change-project-scene-order', apis.change_project_scene_order, name='change_project_scene_order'),
    re_path(r'^api/video/get-project-update-video', apis.get_project_update_video, name='get_project_update_video'),
    re_path(r'^api/video/get-project-process-video', apis.get_project_process_video, name='get_project_process_video'),
    re_path(r'^api/video/get-project-finished-video', apis.get_project_finished_video, name='get_project_finished_video'),
    re_path(r'^api/scene/get-scene-by-id', apis.get_scene_by_id, name='get_scene_by_id'),
    re_path(r'^api/update-content-comment', apis.update_content_comment, name='update_content_comment'),
    re_path(r'^api/download-file', apis.download_file_link, name='download_file_link'),
    re_path(r'^api/update-offer-review', apis.update_offer_review, name='update_offer_review'),
    path('api/get-profile', apis.ProfileApiView.as_view(), name='get_profile'),
    path('api/get-list-project', ProjectListView.as_view(), name='project-list'),
    path('api/login', LoginApiView.as_view(), name='accounts_login_api'),
    path('api/get-project', ProjectDetailView.as_view(), name='get_project'),
    path('api/scene/create-scene', ProductSceneApiView.as_view(), name='create_scene'),
    path('api/get-list-comment-new', ProjectCommentView.as_view(), name='get_list_project_comment'),
    path('api/load-offer-detail-message', OfferMessageApiView.as_view(), name='load_offer_detail_message'),
    path('api/get-messenger-list-artist', MessengerArtistView.as_view(), name='messenger_list_artist'),
    path('my-page/', views.MyPage.as_view(), name='my_page_test'),
    path('api/auth-user/', AuthUserApiView.as_view(), name='auth_user'),
    path('api/auth-user/<int:id>/', AuthUserApiView.as_view(), name='auth_user_detail'),
    path('mileage/my/', MyMileageRankView.as_view(), name='my_mileage_ranks'),
    path('api/project/updated-count', ProjectUpdatedCountApiView.as_view(), name='project_updated_count'),
    path('api/project/members', ProjectMembersApiView.as_view(), name='get_project_members'),
    path('api/project/messenger-artist', ProjectMessengerArtistApiView.as_view(), name='get_project_messenger_artist'),
    path('api/project/update-video', ProjectUpdateVideo.as_view(), name='get_project_update_video'),
    path('api/project/process-video', ProjectProcessVideo.as_view(), name='get_project_process_video'),
    path('api/project/finished-video', ProjectFinishedVideo.as_view(), name='get_project_finished_video'),
    path('api/project/staff-credit', ProjectStaffCredit.as_view(), name='get_project_staff_credit'),
    path('api/message/folder-items', MessageFolderItemsApiView.as_view(), name='get_folder_items_in_message'),
    path('api/me/active-projects', ActiveProjectsApiView.as_view(), name='get_active_projects'),
    path('api/me/completed-projects', CompletedProjectsApiView.as_view(), name='get_completed_projects'),
    path('api/me', MeApiView.as_view(), name='me'),
]
