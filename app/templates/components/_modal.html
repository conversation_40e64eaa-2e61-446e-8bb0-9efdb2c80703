<div class="modal popup-container {% if position == "center" %} modal-centered{% endif %}" id="{{ modal_id }}" role="dialog" style="z-index: {{ modal_zindex }}">
    <div class="modal-dialog popup-dialog">
        <div class="modal-content popup-content">
            <div class="popup-body">
                {% include body_html with default=True %}
            </div>
            {% if footer == 'True' %}
                <div class="popup-footer" style="text-align: right;">
                    <button type="button"
                            class="btn btn--tertiary {{ btn_tertiary }}">キャンセル</button>
                    <button type="button"
                            class="btn btn--primary {{ btn_primary }}">OK</button>
                </div>
            {% endif %}
        </div>
    </div>
</div>
