{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Offer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
  <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <style type="text/css">
    @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-sub-title {
        font-size: 14px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 12px;
      }

      .email-info {
        font-size: 12px;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .email-info-detail {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }

      img.fullwidth,
      img.fullwidthOnMobile {
        max-width: 100% !important;
      }
    }

  </style>
</head>

<body style="margin: 0; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo"
                                    height="45px" width="45px" style="border-radius: 50%"></td>
        <td style="text-align: right">
          <table class="user-info" style="width:100%; table-layout: fixed">
            <tbody>
            <tr>
              <td class="user-name">{{ user_name }} 様</td>
              <td style="width: 40px; padding-left: 10px; background-color: white; border-radius: 50%;">
                <img src="{{ user_avt }}" alt="User Avatar"
                     border="0"
                     style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>

    <p class="email-text" style="margin: 0 0 8px">お世話になっております。</p>

    {% if product_created and product_created.exists %}
      <p class="email-text" style="margin: 0 0 8px">下記プロジェクト、新しいメッセージが届いております。</p>
      {% for project in product_created %}
        {# banner #}
        {% include 'emails/_banner_product.html' with url=project.product_offers.first|get_offer_url:host project=project host=host %}
        {% for offer in project.product_offers.all %}
          {# content #}
          {% include 'emails/_new_offer_creator.html' with offer=offer sender=offer.admin %}
        {% endfor %}
      {% endfor %}
    {% endif %}
    <br>
    {% if product_accepted and product_accepted.exists %}
      <p class="email-text" style="margin: 0 0 8px">下記プロジェクト、{{ list_name_artist }} へのオファーが成立しました。</p>
      {% for project in product_accepted %}
        {# banner #}
        {% include 'emails/_banner_product.html' with url=project.product_offers.first|get_offer_url:host project=project host=host %}
        {% with project.product_offers.count as count %}
          <div style="margin-left: 80%">({{ count }}つのファー）</div>
        {% endwith %}
      {% endfor %}
      <br>
    {% endif %}

    {% if product_uploaded and product_uploaded.exists %}
      <p class="email-text" style="margin: 0 0 8px">下記プロジェクト、納品がありました。</p>
      {% for project in product_uploaded %}
        {# banner #}
        {% include 'emails/_banner_product.html' with url=project.product_offers.first|get_offer_url:host project=project host=host %}
        {% with project.product_offers.count as count %}
          <div style="margin-left: 80%">({{ count }}つのファー）</div>
        {% endwith %}
      {% endfor %}
      <br>
    {% endif %}

    {% if product_new_message and product_new_message.exists %}
      {% for project in product_new_message %}
        <p class="email-text" style="margin: 0 0 8px">下記プロジェクト、新しいメッセージが届いております。</p>
        {# banner #}
        {% include 'emails/_banner_product.html' with url=project.product_offers.first|get_offer_url:host project=project host=host %}
        {% for offer in project.product_offers.all %}
          {% if offer.message_offer.exists %}
            {% if offer.message_offer.count > 1 %}
              <div style="text-align: center">
                <p style="font-size: .5em; color: #a5a6a7;">●</p>
                <p style="font-size: .5em; color: #a5a6a7;">●</p>
                <p style="font-size: 2em; color: #a5a6a7; margin: 0;">+{{ offer.message_offer.count|minus:1 }}</p>
                <p style="font-size: .5em; color: #a5a6a7;">●</p>
                <p style="font-size: .5em; color: #a5a6a7;">●</p>
              </div>
            {% endif %}
            {# content #}
            {% include 'emails/_new_message.html' with message=offer.message_offer.last  type_message='messenger_artist' %}
          {% endif %}
        {% endfor %}
      {% endfor %}
    {% endif %}

    {% if product_done and product_done.exists %}
      {% for project in product_done %}
        <p class="email-text" style="margin: 0 0 8px">下記プロジェクト、オファーの納品データの検収が完了しました。</p>
        {# banner #}
        {% include 'emails/_banner_product.html' with url=project.product_offers.first|get_offer_url:host project=project host=host %}
        {% with project.product_offers.count as count %}
            <div style="margin-left: 80%">({{ count }}つのファー）</div>
        {% endwith %}
      {% endfor %}
    {% endif %}
    <br>

    {# text2 #}
    <p class="email-text" style="margin: 0 0 8px">ご確認、ご検討を頂き、下記よりお気軽にご意向をお知らせください。</p>
    <p class="email-text" style="margin: 0 0 8px">下記プロジェクト、オファーの納品データの検収が完了しました。</p>

    {# button #}
    {% include 'emails/_button_go_to_web.html' with url=host %}

    {# footer #}
    {% include 'emails/_footer_email.html' %}
  </div>
</main>
</body>
</html>
