{% load static %}
{% load util %}
<div style="background-color:transparent;">
  <div class="block-grid"
       style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
    <div class="email-box" style="border: 1px solid #eee; padding: 10px; margin-bottom: 30px; border-radius: 10px">
      <table class="user-info" style="width:100%; table-layout: fixed; border-spacing: 0;">
        <tbody>
        <tr>
          <td style="width: 40px; padding-left: 10px; vertical-align: top; background-color: white; border-radius: 50%;">
            <img class="" src="{{ message.user|get_user_url:'medium' }}"
                 alt="User Avatar" border="0"
                 style="width: 40px; height: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
            <div class="email-time"
                 style="text-align: left; color: #ababab; font-size: 10px; text-align: center">{{ message.created|get_weekday }}
            </div>
          </td>
          <td style="padding-left: 10%">
            <div class="email-message" style="white-space: pre-line;">{{ message.comment | linebreaks }}</div>

            {% if message.files.exists %}
              <div class="email-link"
                   style="margin-top: 5px;align-content: center;">
              {% for file in message|get_sorted_message_files %}
                {% if not file.folder %}
                  <div style="display: flex; margin-bottom: 5px;">
                    <img src="{{ host }}/static/images/download-gray.png" alt="Soremo"
                         style="height: 20px; width: 20px; padding-right: 5px">
                    <a class="email-link" href="{{ file.file.url }}" target="_blank"
                       style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;">
                      <span style="margin: 10px;">{{ file.real_name }}</span>
                    </a>
                  </div>
                {% endif %}
              {% endfor %}

                {% for folder in message.folders.all %}
                  {% if not folder.parent %}
                    <div style="display: flex; margin-bottom: 5px;">
                      <img src="{{ host }}/static/images/icon_folder.png" alt="Soremo"
                           style="height: 18px; width: 18px; padding-right: 5px">
                      <a class="email-link" href="#"
                         style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px;">
                        <span class="email-link" style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px;">{{ folder.name }}</span>
                      </a>
                    </div>
                  {% endif %}
                {% endfor %}
            </div>
            {% endif %}
            </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
