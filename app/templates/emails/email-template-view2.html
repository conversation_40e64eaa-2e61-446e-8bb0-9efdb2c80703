{% load util %}

<body style="background: #f5f5f4;">
<div class="email-template"
     style="max-width: 600px; margin: 20px auto; height: 100%; position: relative; background-color: white; padding: 10px;">
    <div class="hr-space" style="clear: both; height:2px; background-color: #999; margin: 25px 0 10px;"></div>
    <div class="email-template__block">
        <div class="email-template-left" style="float: left">
            <img src="{{ host }}/static/images/soremo-favi2_01.png" width="120px">
        </div>
        <div class="email-template-right" style="float: right">
            <div class="user" style="text-align: right">
                <span style="font-size: 12px; margin-right: 5px; color: #000; position: relative;
      top: 3px;">{{ user }}</span>
                {% if user.avatar %}
                    <img src="{{ user|get_avatar:'small' }}" height="24px" width="24px"
                         style="float: right; border-radius: 50%">
                {% else %}
                    <img src="{{ host }}/static/images/nav_login.png" height="24px" width="24px"
                         style="float: right; border-radius: 50%">
                {% endif %}
            </div>
            <p style="margin: 5px 0; color: #999; font-size: 12px;">新しい演出が届いています。</p>
        </div>
    </div>
    <div class="clearfix" style="clear: both"></div>
    <div class="email-content-block" style="padding: 0 30px">
        <a href="{{ host }}{{ url }}"
           style="border-radius: 20px; width: 100%; background-color: #0099cc; color: #fff; font-size: 14px; text-decoration: none; display: block;text-align: center;padding: 3px; margin: 10px auto 0">WEBで見る</a>
    </div>
    {% if product.image %}
        <div class="email-content-block" style="margin: 10px 0">
            <img src="{{ product.image.url }}" alt="" style="width: 100%">
        </div>
    {% endif %}
    <div class="email-content-block">
        <h4 style="margin: 0; text-transform: uppercase; font-weight: normal;">
            {{ product_scene.name }}</h4>
    </div>
    <div class="hr-space" style="clear: both; height:1px; background-color: #999; margin: 0;"></div>
    <div class="email-content-block" style="margin-right: -1.33%; margin-top: 5px">
        <a href="{{ host }}/download/video/?scene_id={{ scene.scene_id }}">
             <div class="email-content-item" style="width: 32%; margin-right: 1.33%; float: left">
                {% if scene.thumbnail %}
                    <img src="{{ scene.thumbnail.url }}" alt="" style="width: 100%">
                {% endif %}
                <span style="margin: 0; color: #0099cc; font-size: 12px;">{{ scene_created }}</span>
                <p style="color: #666; margin: 0; font-size: 15px;">
                    {{ scene_title.title }} </p>
            </div>
        </a>
    </div>
    <div class="clearfix" style="clear: both"></div>
    <div class="email-template__block"
         style="background-color: #FAFAFA; padding: 10px 0; text-align: center; display: block">
        <p style="margin: 5px 0; color: #999; font-size: 12px;">
            通知を希望されない方は、<a href="{{ host }}/accounts/update/{{ user.id }}">こちら</a>から通知をOFFにしてください。</p>
        <p style="margin: 5px 0; color: #999; font-size: 12px;">
            Copyright© SOREMO Co.,Ltd. All Right Reserved.</p>
    </div>
</div>
</body>
