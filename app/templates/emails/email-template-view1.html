{% load util %}
<body style="background: #f5f5f4;">
  <div class="email-template" style="max-width: 600px; margin: 20px auto; height: 100%; position: relative; background-color: white; padding: 10px;">
    <div class="hr-space" style="clear: both; height:2px; background-color: #999; margin: 25px 0 10px;"></div>
    <div class="email-template__block">
      <div class="email-template-left" style="float: left">
        <img src="{{host}}/static/images/soremo-favi2_01.png" width="120px" style="border-radius: 50%">
      </div>
      <div class="email-template-right" style="float: right">
        <div class="user" style="text-align: right">
          <span style="font-size: 12px; margin-right: 5px; color: #000; position: relative;
      top: 3px;">{{ user }}</span>
          <img src="{{ user|get_user_url:'small' }}" height="24px" width="24px" style="float: right; border-radius: 50%">
        </div>
        <p style="margin: 5px 0; color: #999; font-size: 12px;">新しいメッセージが届いています。</p>
      </div>
    </div>
    <div class="clearfix" style="clear: both"></div>
    <div class="email-content-block" style="padding: 0 30px">
      <a href="{{host}}{{url}}" style="border-radius: 20px; width: 100%; background-color: #0099cc; color: #fff; font-size: 14px; text-decoration: none; display: block;text-align: center;padding: 3px; margin: 10px auto 0">WEBで見る</a>
    </div>
      {% if product.image %}
    <div class="email-content-block" style="margin: 10px 0">
      <img src="{{product.image.url}}" alt="" style="width: 100%">
    </div>
      {% endif %}
    <div class="email-template-block">
      <div class="email-template-list">
        {% if product_scene %}
          <span style="color: #000; text-transform: uppercase; font-size: 14px; font-weight: bold;">{{ product_scene.name }}</span>
          {% else %}
          <span style="color: #000; text-transform: uppercase; font-size: 14px; font-weight: bold;">ALL</span>
          {% endif %}
        <span style="color: #000; margin: 0 5px;">
          ></span>
        <span style="color: #000; font-size: 12px;">{{ scene_title.title }} </span>
      </div>
    </div>
    <div class="hr-space" style="clear: both; height:1px; background-color: #999; margin: 0 0 10px"></div>
    <div class="email-template-block">
      <div class="email-template-item" style="float: right; width: 24px">
        <img src="{{ user_comment|get_user_url:'small' }}" alt="" width="24px" height="24px" style="border-radius: 50%">
      </div>
      <div class="email-template-item" style="float: right; width: 80%; margin-right: 10px;">
        {% if comment.stamp %}
          <img src="{{host}}{{comment.comment}}" style="float: right; max-width: 200px;">
        {% else %}
        <div class="email-template-box" style="float: right; max-width: 200px; border: 1px solid #666; border-radius: 15px; padding: 5px 20px;">
          <p style="margin: 5px 0; color: #666; margin: 0; font-size: 12px;">
            {{ comment.comment | pin_time_comment:comment.pin_time | linebreaks }}</p>
        </div>
        {% endif%}
        <div class="clearfix" style="clear: both"></div>
        {% if comment.file %}
        <div class="email-template-box" style="float: right; margin-top: 5px; display: flex; align-items: center;">
          <img src="{{host}}/static/images/download-solid.svg" alt="" width="10px" height="10px">
          <a href="" style="border-radius: 20px; background-color: #0099cc; color: #fff; font-size: 10px; text-decoration: none; text-align: center; padding: 3px 20px; margin-left: 2px">
            {{comment.get_file_name}}</a>
        </div>
        {% endif %}
      </div>
    </div>
    <div class="clearfix" style="clear: both"></div>
    <div class="email-template__block" style="background-color: #FAFAFA; padding: 10px 0; text-align: center; display: block">
      <p style="margin: 5px 0; color: #999; font-size: 12px;">
        通知を希望されない方は、<a href="{{ host }}/accounts/update/{{ user.id }}">こちら</a>から通知をOFFにしてください。</p>
      <p style="margin: 5px 0; color: #999; font-size: 12px;">
        Copyright© SOREMO Co.,Ltd. All Right Reserved.</p>
    </div>
  </div>
</body>
