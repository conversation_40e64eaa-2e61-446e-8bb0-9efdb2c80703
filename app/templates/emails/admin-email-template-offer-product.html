{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Offer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
    <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <style type="text/css">
    @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-sub-title {
        font-size: 14px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 12px;
      }

      .email-info {
        font-size: 12px;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .email-info-detail {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }
    }

    .master_admin {
      filter: invert(61%) sepia(53%) saturate(690%) hue-rotate(81deg) brightness(95%) contrast(85%);
    }

    .master_client {
      filter: invert(48%) sepia(36%) saturate(7500%) hue-rotate(168deg) brightness(98%) contrast(103%);
    }
  </style>
</head>

<body style="margin: 0; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo" height="45px" width="45px" style="border-radius: 50%"></td>
        <td style="text-align: right">
          <table class="user-info" style="width:100%; table-layout: fixed">
            <tbody>
            <tr>
              <td class="user-name">{{ user.get_display_name }} 様</td>
              <td style="width: 40px; padding-left: 10px"><img src="{{ recipient_avt }}" alt="User Avatar" border="0"
                                   style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
    <p class="email-text" style="margin: 0 0 8px">新しいオーダーが届いています。</p>
    <div class="email-box" style="border: 1px solid #eee; padding: 10px; margin-bottom: 30px">
      <table class="user-info" style="width:100%; table-layout: fixed; border-spacing: 0;">
        <tbody>
        <tr>
          <td>
            <div class="email-message" style="white-space: pre-line;">{{ offer.description | linebreaks }}</div>
          </td>
          <td style="width: 40px; padding-left: 10px; vertical-align: top; background-color: white; border-radius: 50%;"><img src="{{ master_client_avt }}"
                               alt="User Avatar" border="0"
                               style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
            <div class="email-time"
                 style="text-align: left; color: #ababab; font-size: 10px; text-align: center">{{ offer.created|get_weekday }}
            </div>
          </td>
        </tr>
        </tbody>
      </table>
      {% if offer.file %}
        <a class="email-link" href="{{ offer.file.url }}"
           style="color: #009cc6; font-weight: 500; text-decoration: none; text-align: center; display: block; margin-top: 5px;">{{ offer.get_file_name }}</a>
      {% endif %}
      <table class="user-form" style="border-spacing: 0; margin: 25px auto">
        <tbody>
        <tr>
          <td><label for="date"
                     style="margin-right: 5px; margin-bottom: 3px;; display: block; color: #333; font-weight: 500">希望納期</label><input
                  class="email-input" readonly type="text" id="date" value="{{ offer.get_deadline_date }}"
                  style=" margin-right: 5px; text-align: right; background-color: #eee; border: 1px solid #333; padding: 6px 10px;
                  max-width: 200px; pointer-events: none">
          </td>
          {% if offer.budget %}
            <td><label for="budget"
                       style="margin-left: 5px; margin-bottom: 3px; display: block; color: #333; font-weight: 500">
              予算目安（税抜）</label><input
                    class="email-input" readonly type="text" id="budget" value="{{ offer.budget }} 円"
                    style="margin-left: 5px; text-align: right; background-color: #eee; border: 1px solid #333; padding: 6px 10px; max-width: 200px; pointer-events: none">
            </td>
          {% endif %}
        </tr>

        </tbody>
      </table>
    </div>
      <p class="email-text" style="margin: 0 0 8px">内容を確認の上、お見積りプランの提案をお願いします。</p>
    {% include 'emails/_button_go_to_web.html' with url=url %}
    {% include 'emails/_footer_email.html' %}
  </div>
</main>
</body>
</html>
