{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Offer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
    <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <style type="text/css">
    @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-sub-title {
        font-size: 14px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 12px;
      }

      .email-info {
        font-size: 12px;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .email-info-detail {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }

      img.fullwidth,
			img.fullwidthOnMobile {
			max-width: 100% !important;
			}
    }

  </style>
</head>

<body style="margin: 0; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo" height="45px" width="45px" style="border-radius: 50%"></td>
        <td style="text-align: right">
          <table class="user-info" style="width:100%; table-layout: fixed">
            <tbody>
            <tr>
              <td class="user-name">{{user.get_display_name}} 様</td>
              <td style="width: 40px; padding-left: 10px; background-color: white; border-radius: 50%;">
                <img src="{{ master_client_avt }}" alt="User Avatar"
                     border="0"
                     style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>

    <p class="email-text" style="margin: 0 0 8px">プロジェクトの招待が届いています。</p>

    <div style="background-color:transparent;">
					<div class="block-grid" style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
						<div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
							<div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
								<div style="width:100% !important;">
									<div style="margin-bottom: 5px;border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
										<div align="center" class="img-container center autowidth fullwidth" style="padding-right: 0px;padding-left: 0px; margin-top: 10px;">
											<a href="{{url}}">
												<img align="center" alt="Image" border="0" class="center autowidth fullwidth" src="{{ product_image }}" style="outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; clear: both; border: 0; height: auto; float: none; width: 100%; max-width: 600px; display: block;" title="Image" width="600"/>
											</a>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>


    {% include 'emails/_button_go_to_web.html' with url=url %}
    {% include 'emails/_footer_email.html' %}
  </div>
</main>
</body>
</html>
