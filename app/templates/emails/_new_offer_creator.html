{% load static %}
{% load util %}
<style>
  .offer__content {
    margin-left: 10px;
  }
</style>

<div style="background-color:transparent;">
  <div class="block-grid"
       style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
  <div class="email-box" style="border: 1px solid #eee; padding: 10px; margin-bottom: 30px; border-radius: 10px">
  <table class="user-info" style="width:100%; table-layout: fixed; border-spacing: 0;">
    <tbody>
    <tr>
      <td style="width: 40px; padding-left: 10px; vertical-align: top; background-color: white; border-radius: 50%;">
        <img class="" src="{{ sender|get_user_url:'medium' }}"
             alt="User Avatar" border="0"
             style="width: 50px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
        <div class="email-time"
             style="text-align: left; color: #ababab; font-size: 10px; text-align: center">{{ offer.created|get_weekday }}
        </div>
      </td>
      <td style="padding-left: 10%">
        <div class="email-message" style="white-space: pre-line;">{{ offer.message | linebreaks }}</div>
      </td>
    </tr>
    </tbody>
  </table>
  <div class="offer-info" style="margin-left: 20%;">
    {% if offer.file %}
      <div class="email-link"
           style="margin-top: 5px;align-content: center;display: flex;">
        <div class="messenger__icon icon-download offer__item" style="display: flex;">
          <img src="{{ host }}/static/images/download.png" alt="Soremo"
               style="height: 20px; padding-right: 5px">
        </div>
        <a class="email-link" href="{{ offer.file.url }}"
           style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;">
          <span style="margin: 10px;">{{ offer.get_file_name }}</span>
        </a>
      </div>
    {% endif %}
    <div class="email-link"
         style="margin-top: 5px;">
      <span class="offer__item">納期</span><span style="margin-left: 10px;">{{ offer.deadline }}</span></div>
    <div class="email-link"
         style="margin-top: 5px;">
      <span class="offer__item">対価</span><span style="margin-left: 10px;">{{ offer.reward|display_currency }}円（税込）</span></div>
  </div>
</div>
  </div>
</div>
