{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Offer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
    <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">

  <style>
    .button--goto-web {
      font-weight: 300;
      line-height: 21px;
      min-width: 100px;
      padding: 12px 39px;
      border-radius: 4px;
      border: none;
      color: #fff;
      background-color: #009ace;
      cursor: pointer
    }

    .button--goto-web:hover {
      background-color: #0076A5;
    }
  </style>
  <style type="text/css">
    @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-sub-title {
        font-size: 14px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 12px;
      }

      .email-info {
        font-size: 12px;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .email-info-detail {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }
    }

    .master_admin {
      filter: invert(61%) sepia(53%) saturate(690%) hue-rotate(81deg) brightness(95%) contrast(85%);
    }

    .master_client {
      filter: invert(48%) sepia(36%) saturate(7500%) hue-rotate(168deg) brightness(98%) contrast(103%);
    }
  </style>
</head>

<body style="margin: 0; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo" height="45px" width="45px" style="border-radius: 50%"></td>
      </tr>
      </tbody>
    </table>
    <div style="text-align: center;"><img src="{{ sender_avt }}" alt="User Avatar"
                     border="0"
                     style="width: 150px; max-width: 150px; border: 1px solid #eee; border-radius: 50%;"></div>
    <div style="margin-left: 20%; margin-top: 20px">
      <p class="email-text" style="margin: 0 0 8px">{{sender_name}}よりプロジェクトの招待が届いています。</p>
    </div>

    {# banner #}
    {% include 'emails/_banner_product.html' with url=path project=product host=host %}

    <div class="email-button" style="text-align: center; margin: 40px 0;">
      <a class="button button--goto-web" href="{{ path }}" target="_blank"
         style="font-size: 16px; text-decoration: none;font-weight: 300;line-height: 21px;min-width: 100px;
            padding: 12px 39px;border-radius: 4px;border: none;color: #fff;background-color: #009ace;cursor: pointer">
        プロジェクトに参加
      </a>
    </div>

    {% include 'emails/_footer_email.html' %}
  </div>
</main>
</body>
</html>

