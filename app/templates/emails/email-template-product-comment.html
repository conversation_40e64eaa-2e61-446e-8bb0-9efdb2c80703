{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Offer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
  <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <style type="text/css">
    @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-sub-title {
        font-size: 14px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 12px;
      }

      .email-info {
        font-size: 12px;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .email-info-detail {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }

      .video-comment-item-reply-user {
        text-align: center;
      }

      .video-comment-item-reply {
        border: none;
      }

      .video-comment-item-reply {
        padding: 0;
      }

      .video-comment-item-reply-content {
        padding: 10px;
        position: relative;
        border: 1px solid #d9d9d9;
        border-radius: 5px;
        width: 100%;
      }

      .video-comment-item-reply-user {
        padding-right: 20px;
        width: 60px;
      }

      .video-comment-item-reply.right {
        flex-direction: row-reverse;
      }

      .video-comment-item-reply.right .video-comment-item-reply-user {
        padding-right: 0;
        padding-left: 20px;
        width: 60px;
      }

      .video-comment-user-img {
        max-width: 35px;
        border-radius: 50%;
      }

      img.fullwidth,
      img.fullwidthOnMobile {
        max-width: 100% !important;
      }
    }

    .admin {
      filter: invert(34%) sepia(6%) saturate(372%) hue-rotate(174deg) brightness(91%) contrast(87%);
    }

    .client, .master_client {
      filter: invert(40%) sepia(37%) saturate(2330%) hue-rotate(163deg) brightness(100%) contrast(105%);
    }

    .owner-project {
      filter: invert(28%) sepia(98%) saturate(1535%) hue-rotate(174deg) brightness(88%) contrast(102%);
    }
    .button--goto-web:hover {
      background-color: #0f81b7;
    }

    .class-folder-name {

    }

  </style>
</head>

<body style="margin: 0; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo"
                                    height="45px" width="45px" style="border-radius: 50%"></td>
        <td style="text-align: right">
          <table class="user-info" style="width:100%; table-layout: fixed">
            <tbody>
            <tr>
              <td class="user-name">{{ recipient.get_display_name }} 様</td>
              <td style="width: 40px; padding-left: 10px; background-color: white; border-radius: 50%;">
                <img {% if recipient in comment.project|get_owners_project and not recipient.avatar %}
                  src="{{ default_owner_avt }}" {% else %} src="{{ recipient_avt }}"{% endif %} alt="User Avatar"
                  border="0"
                  style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
    <h1 class="email-title"
        style="font-size: 14px; font-weight: 400; line-height: 1.1; color: #333; margin: 0 0 25px; padding-top: 25px;">
      {{ sender.get_display_name }}より新しい{% if comment.files.exists %}ファイル{% else %}メッセージ{% endif %}が届いています
    </h1>

    <div style="background-color:transparent;">
      <div class="block-grid"
           style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
          <div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
            <div style="width:100% !important;">
              <div style="margin-bottom: 5px;border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                <div align="center" class="img-container center autowidth fullwidth"
                     style="padding-right: 0px;padding-left: 0px; margin-top: 10px;">
                  <a href="{{ url }}">
                    <img align="center" alt="Image" border="0" class="center autowidth fullwidth"
                         src="{{ product_image }}"
                         style="outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; clear: both; border: 0; height: auto; float: none; width: 100%; max-width: 600px; display: block;"
                         title="Image" width="600"/>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="email-box" style="max-width: 600px; padding: 10px; margin: auto auto 30px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word">
      <table class="user-info"
             style="min-width:30%; table-layout: fixed; border-spacing: 0; height: auto;">
        <tbody>
        <tr>
          <td style="width: 40px; padding-left: 10px; vertical-align: top; padding-right: 20px;">
            <img {% if sender in comment.project|get_owners_project and not sender.avatar %}src="{{ default_owner_avt }}"{% else %}
                 src="{{ sender_avt }}"{% endif %} alt="User Avatar" border="0"
                 style="width: 40px; height: 40px; max-width: 40px; border-radius: 50%">
            <div style="color:#ababab;font-size:10px;text-align:center">{{ comment.created|get_weekday }}
            </div>
          </td>
          <td style="border:1px solid #eee;padding:6px 10px;right: 5px;border-radius: 10px;
                     "><div style="white-space: pre-line;">{{ comment.comment | linebreaks }}</div>

            {% if comment.files.exists %}
              <div class="email-link"
                   style="margin-top: 5px;align-content: center;">
                {% for file in  comment.files.all %}
                  {% if not file.folder %}
                  <div style="display: flex; margin-bottom: 5px;">
                    <img src="{{ host }}/static/images/download-gray.png" alt="Soremo"
                         style="height: 20px; width: 20px; padding-right: 5px">
                    <a class="email-link" href="{{ file.file.url }}" target="_blank"
                       style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;">
                      <span style="margin: 10px;">{{ file.real_name }}</span>
                    </a>
                  </div>
                  {% endif %}
                {% endfor %}

                {% for folder in comment.folders.all %}
                  {% if not folder.parent %}
                    <div style="display: flex; margin-bottom: 5px;">
                    <img src="{{ host }}/static/images/icon_folder.png" alt="Soremo"
                         style="height: 18px; width: 18px; padding-right: 5px">
                    <a class="email-link class-folder-name" href="#"
                       style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px;">
                      <span class="email-link" style="color: #FFFFFF;text-decoration:none;text-align:center;background-color: #C4C4C4;width: fit-content;border-radius: 10px;margin-left: 2px;">{{ folder.name }}</span>
                    </a>
                  </div>
                  {% endif %}
                {% endfor %}
              </div>
            {% endif %}

          </td>
        </tr>

        </tbody>
      </table>

      {% include 'emails/_button_go_to_web.html' with url=url %}

      {% include 'emails/_footer_email.html' %}
    </div>
  </div>
</main>
</body>
</html>
