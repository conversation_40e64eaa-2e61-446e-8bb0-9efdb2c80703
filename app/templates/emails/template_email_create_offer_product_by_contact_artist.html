{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Contact info master admin</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
  <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <style type="text/css">
    @font-face {
      font-family: AxisRound;
      font-weight: 700;
      src: url('../fonts/AxisRound100StdN-B.otf');
    }

    @font-face {
      font-family: AxisRound;
      font-weight: 400;
      src: url('../fonts/AxisRound100StdN-R.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 12px !important;
      }

      .email-sub-title {
        font-size: 14px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 12px;
      }

      .email-info {
        font-size: 12px;
      }

      .email-message {
        font-size: 12px;
      }

      .email-link {
        font-size: 12px;
      }

      .file-link {
        color: #707070;
        text-decoration: none;
        text-align: center;
      }

      .not-link {
        cursor: default;
      }

      .email-info-detail {
        font-size: 12px;
      }

      .email-footer {
        font-size: 12px;
      }

      img.fullwidth,
      img.fullwidthOnMobile {
        max-width: 100% !important;
      }
    }

    .account__file {
      position: relative;
      max-width: 170px;
      display: flex;
      align-items: center;
      padding: 8px 25px 8px 16px;
      background-color: #f0f0f0;
      border-radius: 6px;
      margin: 8px 0;
      color: #707070;
      text-decoration: none;
      text-align: center;
    }

    .account__file-name {
      font-size: 11px;
      line-height: 17px;
      display: block;
      margin: 0 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100px;
      max-height: 20px
    }

    .email-message {
      word-break: break-word;
      white-space: pre-line;
    }

    .custom-switch {
      padding-left: 0;
    }

    .custom-switch .form-check-label {
      display: flex;
      align-items: center;
    }

    .custom-switch .switch-label {
      font-size: 13px;
      font-weight: 400;
    }

    .switch-slider, .form-check-label, .disabledbutton {
      pointer-events: none;
    }

  </style>
</head>

<body style="margin: 0; font-family: 'Hiragino Sans', 'Noto Sans Japanese', 'Yu Gothic', 'Meiryo', 'Hiragino Kaku Gothic Pro', '游ゴシック', 'メイリオ', 'MS ゴシック', 'sans-serif'; font-size: 13px; line-height: 1.4; -webkit-font-smoothing: antialiased; color: #707070;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0" style="margin-bottom: 30px">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo"
                                    height="45px" width="45px" style="border-radius: 50%"></td>
        {#        {% if recipient %}#}
        <td style="text-align: right">
          <table class="user-info" style="width:100%; table-layout: fixed">
            <tbody>
            <tr>
              <td class="user-name">
                {% if recipient %}{{ recipient.get_display_name }} 様
                  {% elif offer.contract_info.first %}{{ offer.contract_info.first.fullname }} 様{% endif %}
              </td>
              <td style="width: 50px; padding-left: 10px; background-color: white; border-radius: 50%;">
                <img src="{{ recipient_avt }}" alt="User Avatar"
                     border="0"
                     style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
              </td>
            </tr>
            </tbody>
          </table>
        </td>
        {#        {% endif %}#}
      </tr>
      </tbody>
    </table>

    {% if type_mail == 'master_client' %}
      <p class="email-text" style="margin: 0 0 8px">お世話になっております。</p>
      <p class="email-text" style="margin: 0 0 30px">このたびは、SOREMOにご相談を頂き、ありがとうございます。下記内容を承りました。</p>
    {% elif type_mail == 'producer' %}
      <p class="email-text" style="margin: 0 0 8px">お世話になっております。</p>
      <p class="email-text" style="margin: 0 0 30px">{{ master_client.get_display_name }}より、プロフィールサイトでオーダーが届いています。</p>
    {% else %}
      <p class="email-text" style="margin: 0 0 30px">{{ master_client.get_display_name }}より、{{ artist_name }}へのオーダーが届いています。</p>
    {% endif %}


    {# content #}

    {#    Album name/artist name#}
    <p class="email-text strong-text" style="font-weight: bold;"><span class="email-text">アーティスト名 </span></p>
    <p class="email-text"><span class="email-text">{{ artist_name }} </span></p>

    <p class="email-text strong-text" style="font-weight: bold;"><span class="email-text">メッセージ </span></p>
    <p class="email-text">{{ offer.description | linebreaks }}</p>

    {% if offer.files.exists %}
      <p class="email-text strong-text" style="font-weight: bold;"><span class="email-text">資料 </span></p>
      <div class="email-link"
           style="margin-top: 5px;align-content: center;">
        {% for file in  offer.files.all %}
          {% if not file.folder %}
            <a class="account__file" href="{{ file.file.url }}" target="_blank"
               style="position: relative;max-width: 170px;display: flex;align-items: center;padding: 8px 25px 8px 16px;background-color: #f0f0f0;border-radius: 6px;margin: 8px 0;color: #707070;text-decoration:none;text-align:center;">
              <i class="icon icon--sicon-clip"><img src="{{ host }}/static/images/icon_file.png" alt="Soremo"
                                                    style="height: 15px; width: 14px; padding-right: 5px"></i>
              <p class="account__file-name">
                <span class="email-link file-link">{{ file.real_name }}</span>
              </p>
            </a>
          {% endif %}
        {% endfor %}

        {% for folder in offer.folders.all %}
          {% if not folder.parent %}
            <div class="account__file"
                 style="position: relative;max-width: 170px;display: flex;align-items: center;padding: 8px 25px 8px 16px;background-color: #f0f0f0;border-radius: 6px;margin: 8px 0;color: #707070;text-decoration:none;text-align:center;">
              <i class="icon icon--sicon-clip"><img src="{{ host }}/static/images/icon_folder.png" alt="Soremo"
                                                    style="height: 15px; width: 14px; padding-right: 5px"></i>
              <p class="account__file-name">
                <a href="#" target="_blank" class="email-link file-link not-link"
                   style="color: #707070;text-decoration:none; cursor: default !important">{{ folder.name }}</a>
              </p>
            </div>
          {% endif %}
        {% endfor %}

      </div>
    {% endif %}


    {#  Step 3#}
    <h2>条件</h2>

    <p class="email-text strong-text" style="font-weight: bold;">
      <span class="email-text">制作期間 </span>
    </p>
    <p class="email-text">
      <span class="email-text"><a href="#" target="_blank" class="email-link file-link not-link"
                                  style="color: #707070;text-decoration:none;cursor: default !important">{{ offer.start_time|get_datetime }} ~ {{ offer.end_time|get_datetime }} </a></span>
    </p>

    <p class="email-text strong-text" style="font-weight: bold;">
      <span class="email-text">最終期限 </span>
    </p>
    <p class="email-text">
      <span class="email-text"><a href="#" target="_blank" class="email-link file-link not-link"
                                  style="color: #707070;text-decoration:none;cursor: default !important">{{ offer.deadline|get_deadline_full_time }}</a></span>
    </p>

    <p class="email-text strong-text" style="font-weight: bold;">
      <span class="email-text">権利の取り扱い </span>
    </p>
    <p class="email-text">
      <span class="email-text">{{ offer.get_contract_type_display }}</span>
    </p>

    <p class="email-text strong-text" style="font-weight: bold;">
      <span class="email-text">契約書 </span>
    </p>
    <p class="email-text">
      <span class="email-text">{{ offer.get_ownership_type_display }}</span>
    </p>

    <p class="email-text strong-text" style="font-weight: bold;">
      <span class="email-text">実績公開 </span>
    </p>
    <p class="email-text">
      <span class="email-text">{{ offer.get_disclosure_rule_display }}</span>
    </p>


    {#  contract #}
    {% with offer.contract_info.first as contact_info %}
      {% if contact_info %}
        <h2>ご連絡先</h2>
        <p class="email-text strong-text" style="font-weight: bold;">
          <span class="email-text">氏名</span>
        </p>
        <p class="email-text">
          <span class="email-text">{{ contact_info.fullname }}</span>
        </p>

        <p class="email-text strong-text" style="font-weight: bold;">
          <span class="email-text">メールアドレス</span>
        </p>
        <p class="email-text">
          <span class="email-text"><a href="mailto:{{ contact_info.email }}"
                                      target="_blank">{{ contact_info.email }}</a></span>
        </p>

        <p class="email-text strong-text" style="font-weight: bold;">
          <span class="email-text">役職・ポジション</span>
        </p>
        <p class="email-text">
          <span class="email-text">{{ contact_info.job_type }}</span>
        </p>

        <p class="email-text strong-text" style="font-weight: bold;">
          <span class="email-text">会社名</span>
        </p>
        <p class="email-text">
          <span class="email-text">{{ contact_info.enterprise }}</span>
        </p>

        <p class="email-text strong-text" style="font-weight: bold;">
          <span class="email-text">WEBサイト</span>
        </p>
        <p class="email-text">
          <span class="email-text"><a href="{{ contact_info.company_url }}"
                                      target="_blank">{{ contact_info.company_url }}</a></span>
        </p>

        <p class="email-text strong-text" style="font-weight: bold;">
          <span class="email-text">電話番号（日中連絡がとれる番号）</span>
        </p>
        <p class="email-text">
          <span class="email-text"><a href="tel:{{ contact_info.phone }}"
                                      target="_blank">{{ contact_info.phone }}</a></span>
        </p>

        <p class="email-text strong-text" style="font-weight: bold;">
          <span class="email-text">ご希望の連絡方法</span>
        </p>
        <p class="email-text">
          <span class="email-text">{{ contact_info.get_contact_channel_display }}</span>
        </p>

      {% endif %}
    {% endwith %}

    {% if producer.user_creator.last.is_direct %}
      {% if type_mail == 'master_client' %}
        <p class="email-text" style="margin: 30px 0 8px">内容を確認の上、{{ producer.get_display_name }}よりご連絡致します。</p>
        <p class="email-text" style="margin: 0 0 8px">今しばらくお待ちください。</p><br>
        <p class="email-text" style="margin: 0 0 8px">内容の更新、ご不明な点などございましたら、下記よりお気軽にお問い合わせください。</p>
      {% elif type_mail == 'producer' %}
        <p class="email-text" style="margin: 30px 0 8px">内容を確認の上、お見積りプランの提案をお願いします。</p>
      {% else %}
        <p class="email-text" style="margin: 30px 0 8px">必要に応じて、アーティストへのサポートをお願いします。</p>
      {% endif %}
    {% else %}
      {% if type_mail == 'master_client' %}
        <p class="email-text" style="margin: 30px 0 8px">内容を確認の上、改めてご連絡致します。</p>
        <p class="email-text" style="margin: 0 0 8px">今しばらくお待ちください。</p><br>
        <p class="email-text" style="margin: 0 0 8px">内容の更新、ご不明な点などございましたら、下記よりお気軽にお問い合わせください。</p>
      {% else %}
        <p class="email-text" style="margin: 30px 0 8px">必要に応じて、アーティストへのサポートをお願いします。</p>
      {% endif %}
    {% endif %}


    {#    Show button go to web #}
    {% if offer.project %}
      {% if type_mail != 'master_admin' or type_mail == 'master_admin' and not producer.user_creator.last.is_direct %}
        {% include 'emails/_button_go_to_web.html' with url=url %}
      {% endif %}
    {% endif %}

    {# footer #}
    {% include 'emails/_footer_email.html' %}
  </div>
</main>
</body>
</html>
