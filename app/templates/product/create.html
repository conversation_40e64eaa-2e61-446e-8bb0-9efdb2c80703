{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load widget_tweaks %}
{% load compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css" integrity="sha512-aOG0c6nPNzGk+5zjwyJaoRUgCdOrfSDhmMID2u4+OIslr0GjpLKo7Xm0Ao3xmpM4T8AmIouRkqwj1nrdVsLKEQ==" crossorigin="anonymous" />
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
  <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
  {% endcompress %}
  {% compress js inline %}
  <script src="{% static 'js/cropper.min.js' %}"></script>
  <script src="{% static 'js/main_cropping.js' %}"></script>
  {% endcompress %}
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top_admin.css' %}"/>
  <style>
    .form_create {
      padding-top: 100px;
    }

    .col-xs-12 input[type=file] {
      position: absolute;
      bottom: 0;
      margin: 0;
      padding: 0;
      font-size: 20px;
      cursor: pointer;
      opacity: 0;
    }

    .no-file {
      font-size: 13px;
    }

    .button-small {
      line-height: unset;
      padding: 5px 15px;
      margin: 5px 10px;
    }

    .button--secondary {
      border: none;
    }

    #id_description {
      height: 250px;
    }
  </style>
  {% endcompress %}
  {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
  {% endcompress %}
{% endblock %}

{% block content %}

  <main class="container" style="min-height: 55vh; margin-top: 80px;">
    <div class="m10 row">
        <div class="col-sm-offset-3 col-sm-6">
            <h3>新規プロジェクト追加</h3>
            <br>
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                {% bootstrap_form form exclude='auto_use_last,allow_url_share,information,acr_host,acr_access_key,acr_access_secret, share_link, client_name' %}
                <hr>
              <div class="form-group select-container select_role sumo-select">
                <label>オーナー会社名＊</label>
                {{ form.client_name }}
                <datalist id="client_name">
                  {% for end_client in list_end_client %}
                    <option>{{ end_client }}</option>
                  {% endfor %}
                </datalist>
                <div class="error-message">このフィールドは必須項目です。</div>
              </div>
              <hr>
                <h4>納品形式</h4>
                <div class="form-group">
                    <div class="checkbox">
                        <label for="id_auto_use_last">
                            {{ form.auto_use_last }} 最終動画ファイルの納品データにする</label>
                    </div>
                </div>
                <hr>
                <h4>利用設定</h4>
                <div class="form-group">
                    <div class="checkbox">
                        <label for="id_auto_use_last">
                        {{ form.share_link }} URLリンク共有を許可</label>
                    </div>
                </div>
                <hr>
                <h4>ACR</h4>
                <div class="form-group">
                    {{ form.acr_host.label_tag }}
                    {{ form.acr_host }}
                </div>
                <div class="form-group">
                    {{ form.acr_access_key.label_tag }}
                    {{ form.acr_access_key }}
                </div>
                <div class="form-group">
                    {{ form.acr_access_secret.label_tag }}
                    {{ form.acr_access_secret }}
                </div>

                <hr>
                <h4>スタッフロール</h4>
                <div class="form-group row">
                    <div class="button button--gradient button--gradient-primary button--round button-small">
                        <label for="id_information" style="color: #fff; cursor: pointer;">スタッフロールを更新</label>
                    </div>
                </div>
                {{ form.information|add_class:'hide' }}
                <hr>
                {% buttons %}
                    <input type="submit" value="OK"
                           class="col-sm-offset-3 col-sm-6 button button--gradient button--gradient-primary button--round"/>
                {% endbuttons %}
            </form>
        </div>
    </div>

    <div class="modal fade" id="modalCrop">
      <div class="modal-dialog" style="transform: translate(0,10%);">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
            <h4 class="modal-title">画像登録</h4>
          </div>
          <div class="modal-body">
            <img src="" id="image" style="max-width: 100%;">
          </div>
          <div class="modal-footer">
            <div class="btn-group pull-left" role="group">
              <button type="button" class="btn btn-default js-zoom-in">
                <span class="glyphicon glyphicon-zoom-in"></span>
              </button>
              <button type="button" class="btn btn-default js-zoom-out">
                <span class="glyphicon glyphicon-zoom-out"></span>
              </button>
            </div>
            <button type="button" class="btn btn-primary js-crop-and-upload">登録する</button>
          </div>
        </div>
      </div>
    </div>
</main>
    {% compress js inline %}
  <script>
      let default_thumb = '{% static 'images/messenger-thumb.png' %}';
      let user = {{ user.id }};
  </script>
  <script src="{% static 'js/main.js' %}"></script>
    {% endcompress %}
  <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.2.5/moment-timezone-with-data.min.js" integrity="sha512-OvYMp/zgYHU6ojgDxmtCLsgHBtSrX34Cx/01Tv2bFfbJnlVMbIv5TVVTQ+0wU4GHVDIlHAMMsjFBpTzDnSqIiA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
    {% compress js inline %}
  <script src="{% static 'js/isInViewport.min.js' %}"></script>
  <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
  <script src="{% static 'js/top_page_admin.js' %}"></script>
  <script src="{% static 'js/top_page_member.js' %}"></script>
  <script src="{% static 'js/product_admin.js' %}"></script>
    {% endcompress %}
{% endblock content %}
