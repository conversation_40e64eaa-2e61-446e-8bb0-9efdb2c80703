{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load widget_tweaks %}
{% load compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css"
        integrity="sha512-aOG0c6nPNzGk+5zjwyJaoRUgCdOrfSDhmMID2u4+OIslr0GjpLKo7Xm0Ao3xmpM4T8AmIouRkqwj1nrdVsLKEQ=="
        crossorigin="anonymous"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/project_list.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top_admin.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/project_offer.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_contract.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
  {% compress css %}
  <style>
    .form_create {
      padding-top: 100px;
    }

    .col-xs-12 input[type=file] {
      position: absolute;
      bottom: 0;
      margin: 0;
      padding: 0;
      font-size: 20px;
      cursor: pointer;
      opacity: 0;
    }

    .no-file {
      font-size: 13px;
    }

    .button-small {
      line-height: unset;
      padding: 5px 15px;
      margin: 5px 10px;
    }

    .button--secondary {
      border: none;
    }

    #id_description {
      height: 250px;
    }

    @media (max-width: 992px) {
    .project-item__filter {
      flex-direction: column;
    }

    .project-item-right-action {
      margin: 10px 0 0 auto;
      max-width: 40px;
  }
}

  </style>
  {% endcompress %}
{% endblock %}

{% block content %}

  <main class="owner-top {% if not request|is_pc %} on-mobile{% endif %}">
    <div class="container">
      <div class="new-video-menu">
        <div class="project-list">
          <div class="project-item {% if not request|is_pc %} on-mobile{% endif %}"
               data-project-id="{{ product.pk }}" data-user-role="{{ user.role }}">
            <a href="{% url 'app:top_page' %}?force=true">
               {% include 'top/_product_banner.html' with project=product user=user type_page='top_page_admin' project_list=False is_pc=request|is_pc show_staff=True %}
            </a>

            <div class="project-item_content">
              <div class="project-item__filter">
                <div class="project-item-left-action" style="display: flex;">
                  <a href="{% url 'app:product_update' product.pk %}">
                    <div class="project-item__filter-item ">設定
                    </div>
                  </a>
                  <a href="{% url 'app:message_owner'  product.pk %}">
                    <div class="project-item__filter-item">DM</div>
                  </a>
                </div>
                <div class="project-item-right-action ">
                  <div class="button button--text button--text-primary project-item__export_menu
                                                    dropdown-toggle project-item__filter-item deepgray"
                       title="取引情報"
                       data-show="export-csv" role="button">
                    <a href="{% url 'app:export_creator_offer_info' product.pk %}">
                      <svg width="64" height="36" viewBox="0 0 64 36" fill="none"
                           xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M40 4H24C22.9 4 22 4.9 22 6V20C22 21.1 22.9 22 24 22H40C41.1 22 42 21.1 42 20V6C42 4.9 41.1 4 40 4ZM29 18H26C25.45 18 25 17.55 25 17C25 16.45 25.45 16 26 16H29C29.55 16 30 16.45 30 17C30 17.55 29.55 18 29 18ZM29 14H26C25.45 14 25 13.55 25 13C25 12.45 25.45 12 26 12H29C29.55 12 30 12.45 30 13C30 13.55 29.55 14 29 14ZM29 10H26C25.45 10 25 9.55 25 9C25 8.45 25.45 8 26 8H29C29.55 8 30 8.45 30 9C30 9.55 29.55 10 29 10ZM38.7 12.12L35.53 15.29C35.14 15.68 34.5 15.68 34.11 15.29L32.7 13.87C32.31 13.48 32.31 12.85 32.7 12.46C33.09 12.07 33.72 12.07 34.11 12.46L34.82 13.17L37.29 10.7C37.68 10.31 38.31 10.31 38.7 10.7L38.71 10.71C39.09 11.1 39.09 11.74 38.7 12.12Z"
                              fill="#53565A"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
              <div class="m10 row">
                <div class="project-offer">
                  <div class="project-offer__list">
                    {% for user in users %}
                      {% include  'product/_creator_item.html' with project=product user=user is_pc=request|is_pc show_staff=True %}
                    {% endfor %}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="project-member-setting-modal modal fade" id="project-member-setting"
               role="dialog" style="display: none;">
            <!-- ajax will append data here -->
          </div>
        </div>
      </div>


      {% include 'top/_modal_setting_setting.html' with project_list=False %}
    </div>
  </main>
    {% compress js inline %}
  <script src="{% static 'js/main.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.2.5/moment-timezone-with-data.min.js" integrity="sha512-OvYMp/zgYHU6ojgDxmtCLsgHBtSrX34Cx/01Tv2bFfbJnlVMbIv5TVVTQ+0wU4GHVDIlHAMMsjFBpTzDnSqIiA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
{#  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>#}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
  {% compress js inline %}
    <script src="{% static 'js/isInViewport.min.js' %}"></script>
  <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
  <script src="{% static 'js/top_page_admin.js' %}"></script>
  <script src="{% static 'js/top_page_member.js' %}"></script>
  <script src="{% static 'js/product_admin.js' %}"></script>
  <script src="{% static 'js/action_banner.js' %}"></script>
    {% endcompress %}
{% endblock content %}
