{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}

{% block extrahead %}
    <style>
        .delete_comment{
            padding-top: 50px;
        }
    </style>
{% endblock %}

{% block content %}
    <main class="container delete_comment">
        <div class="m10 row">
            <div class="col-xs-6">
                <form action="" method="post">
                    {% csrf_token %}
                    <div class="comment__reply-content ">
                        <div class="comment__reply-desc">
                            <p>{{ object.comment | linebreaks }}</p>
                        </div>
                        {% if object.file %}
                            <div class="comment__reply-donwload">
                                <i class="fas fa-download"></i>
                                <a href="{{ object.file.url }}">{{ object.file }}</a>
                            </div>
                        {% endif %}
                    </div>
                    {% buttons %}
                        <div class="delete_comment"><input type="submit" value="削除する" class="btn btn-danger"></div>
                    {% endbuttons %}
                </form>
            </div>
        </div>
    </main>
{% endblock content %}
