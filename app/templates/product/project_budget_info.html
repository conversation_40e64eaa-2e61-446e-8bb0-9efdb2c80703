{% load static %}
<div id="budgetLogSidebar" class="sidebar refactor d-none-sb">
     <span class="material-symbols-rounded u-pr8 balance-wallet-icon open-close-nav nav-close open-close-wallet-sp show"
           onclick="openCloseNav(this)">
            account_balance_wallet
        </span>
    <div class="sidebar-relative">
        <div class="custom-loader loader-bg"></div>
        <a href="javascript:void(0)" style="visibility: hidden" class="closebtn" onclick="closeNav()">
            <span class="material-symbols-rounded icon-balance-wallet">account_balance_wallet</span>
        </a>
        <div class="total-budget-block u-col u-gap8">
            <div class="heading-13 u-text-white u-line-height-200">あなたのプロジェクト収支</div>
            <div class="total-budget">
                <span class="budget-number">0</span>
                <span class="budget-unit">円（税込）</span>
            </div>
        </div>
        <div class="budget-progress">
            <div class="line-progress">
                <div class="progress-item line-blue"></div>
                <div class="progress-item line-white"></div>
                <div class="progress-item line-grey"></div>
                <div class="progress-item line-dark-grey"></div>
            </div>
            <p class="percent-progress">0%</p>
        </div>
        <div class="tab-filter-budget">
            <div class="switch-btn-group">
                <div class="switch-btn-item" data-option="1">カテゴリーごと</div>
                <div class="switch-btn-item active" data-option="2">ユーザーごと</div>
            </div>
        </div>
        <div class="title-list-item">
            <span class="title-item title-item-1">相手先</span>
            <span class="title-item title-item-2">対価（税込）</span>
        </div>
        <div class="budget-list-item tab-1 d-none-sb">

        </div>
        <div class="budget-list-item tab-2 refactor">
        </div>
        {#        <div id="overlayBg" class="modal-none"></div>#}
        <div class="modal-budget-detail modal-none" id="modalBudgetDetail">
            <div class="modal-bd-header">
                <span class="material-symbols-rounded btn-close-bgdt">arrow_back_ios_new</span>
            </div>
            <div class="modal-bd-body">
                <div class="bg-detail">

                    <div class="title-item-detail">
                        <span class="title-dt title-1">依頼日</span>
                        <span class="title-dt title-2">対価（税込）</span>
                        <span class="title-dt title-3">ロール</span>
                    </div>
                    <div class="list-item-detail">
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
