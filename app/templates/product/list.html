{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load compress %}
{% block title %}{% endblock title %}

{% block extrahead %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/project.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
<main class="project-list">
    <div class="container">
        <div class="project-list__filter">
            <div class="project-list__filter-left">
                <a class="filter-fix" href="javascript:void(0)">FIX</a>
                <a class="active filter-all" href="javascript:void(0)">ALL</a>
                <a class="icon-new filter-new" href="javascript:void(0)">NEW</a>
                <a class="filter-order icon-order" href="javascript:void(0)">ORDER</a>
            </div>
            <div class="project-list__filter-right" data-filter="date" data-sort="sort-up">
                <div class="sort-menu">
                    <a class="sort-menu__toggle" data-toggle="dropdown" href="#">...</a>
                    <ul class="sort-menu__dropdown dropdown-menu">
                        <li class="dropdown-item">
                            <label for="radio-inline">
                                <input class="btn-radio" type="radio" name="optradio" data-value="date" checked>更新日
                            </label>
                        </li>
                        <li class="dropdown-item">
                            <label for="radio-inline">
                                <input class="btn-radio" type="radio" name="optradio" data-value="progress">％
                            </label>
                        </li>
                    </ul>
                </div>
                <div class="project-list__sort text-right">
                    <div class="sort-down">
                        <a class="icon" href="javascript:void(0)">▼</a>
                    </div>
                    <div class="sort-up">
                        <a class="icon" href="javascript:void(0)">▲</a>
                    </div>
                </div>
            </div>
        </div>

        {% include 'product/_project_content.html' with products=products filter=filter %}


        {% if user.role == 'master_client' %}
            {% if user.is_stripe_validated %}
                <a href="{% url 'app:new_order' %}">
                    <div class="project-list__item text-center project_filter_order">
                         <button class="btn button button--submit project-list__button">NEW ORDER</button>
                    </div>
                </a>
            {% else %}
                <div class="project-list__item text-center project_filter_order">
                    <form action="{% url 'app:payment' %}" method="POST">
                        {% csrf_token %}
                        <script
                            src="https://checkout.stripe.com/checkout.js" class="stripe-button"
                            data-key="{{ customer_pk }}"
                            data-amount="0"
                            data-currency="USD"
                            data-name="SOREMO"
                            data-description="まず支払い情報を入力してください。"
                            data-image="{% static "images/avatar-user.jpg" %}"
                            data-locale="auto"
                            data-label="NEW ORDER"
                            data-panel-label="保存">
                        </script>
                    </form>
                </div>
            {% endif %}
        {% endif %}

    </div>
</main>
    {% compress js inline %}
<script>
    $('.icon-new').on('click', function() {
        if(!$(this).hasClass('disable-click')) {
            $(this).siblings('.active').removeClass('active');
            $(this).addClass('active')
            $('.project-list__content').data('filter', 'new');
            $('.project_filter_fix, .project_filter_order, .project_filter_all').addClass('hide');
            $('.project_filter_new').removeClass('hide');
        }
    });

    $('.filter-all').on('click', function() {
        $(this).siblings('.active').removeClass('active');
        $(this).addClass('active');
        $('.project-list__content').data('filter', 'all');
        $('.project_filter_fix, .project_filter_order').addClass('hide');
        $('.project_filter_new, .project_filter_all').removeClass('hide');
    });

    $('.filter-fix').on('click', function() {
        if(!$(this).hasClass('disable-click')) {
            $(this).siblings('.active').removeClass('active');
            $(this).addClass('active');
            $('.project-list__content').data('filter', 'fix');
            $('.project_filter_new, .project_filter_order, .project_filter_all').addClass('hide');
            $('.project_filter_fix').removeClass('hide');
        }
    });

    $('.filter-order').on('click', function() {
        if(!$(this).hasClass('disable-click')) {
            $(this).siblings('.active').removeClass('active');
            $(this).addClass('active');
            $('.project-list__content').data('filter', 'order');
            $('.project_filter_fix, .project_filter_new, .project_filter_all').addClass('hide');
            $('.project_filter_order').removeClass('hide');
        }
    });

    $('.sort-down, .sort-up').on('click', function() {
        $(this).parent().toggleClass('show-item');
        let sort_data = this.className;
        let project_filter = $('.project-list__filter-right')
        project_filter.data('sort', sort_data);
        let filter_data = project_filter.data('filter');
        let ajax_target = $('.project-list__content');
        let current_filter = ajax_target.data('filter');

        ajaxLoadProject(filter_data, sort_data, ajax_target, current_filter)
    });

    $('.btn-radio').on('click', function() {
        let filter_data = $(this).data('value');
        let project_filter = $('.project-list__filter-right');
        project_filter.data('filter', filter_data);
        let sort_data = project_filter.data('sort');
        let ajax_target = $('.project-list__content');
        let current_filter = ajax_target.data('filter');

        ajaxLoadProject(filter_data, sort_data, ajax_target, current_filter);
    });

    function ajaxLoadProject(filter, sort, target, current_filter) {
        $.ajax({
            url: "/ajax/product_filter/",
            type: "POST",
            data: {
                'sort': sort,
                'user': {{ user.id }},
                'csrfmiddlewaretoken': '{{ csrf_token }}',
                'filter': filter,
                'current_filter': current_filter
            },
            success: function (result) {
                target.replaceWith(result);
                $('.progress').each(function () {
                    let width = $(this).find('.progress-bar').width();
                    if (width === 0) {
                      $(this).find('.bg-warning').css('border-radius', '20px');
                    } else {
                      $(this).find('.bg-warning').addClass('warning-custom');
                    }
                });
            },
            fail: function () {
                alert("Something wrong. Please reload page");
            }
        });
    }

    function updateFilter(filter) {
        $('.project-list__filter-left a').removeClass('active');
        $('.project-list__filter-left .filter-' + filter).addClass('active');
        if (filter === 'all') {
            $('.project-list__item').removeClass('hide');
        } else {
            $('.project_filter_fix, .project_filter_new, .project_filter_all, .project_filter_order').addClass('hide');
            $('.project_filter_' + filter).removeClass('hide');
        }
    }

    $(document).ready(function() {
        if($('.project_filter_new').length === 0) {
            $('.filter-new').removeClass('active').addClass('disable-click')
        }
        if($('.project_filter_fix').length === 0) {
            $('.filter-fix').removeClass('active').addClass('disable-click')
        }
        if($('.project_filter_order').length === 0) {
            $('.filter-order').removeClass('active').addClass('disable-click')
        }
    })
</script>
    {% endcompress %}
{% endblock content %}
