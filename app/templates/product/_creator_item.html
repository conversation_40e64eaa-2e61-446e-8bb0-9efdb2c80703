{% load static %}
{% load util %}
{% with user.user_creator.first as creator %}
  {% if user.offer_to_creator.all.exists %}
    <div class="project-offer__item">
      <div class="offer-user">
        <div class="offer-user__avatar background-avt">
          <img class="offer-user__avatar-img" src="{{ user|get_avatar:'medium' }}" alt="">
        </div>
        <div class="offer-user__info">
          <div class="offer-user__name" data-toggle="tooltip" data-placement="top"
               title="{{ user.get_display_name }}">{{ user.get_display_name }}
          </div>
          <div class="offer-user__work">{% if creator.user.type %}{{ creator.user.type }}{% endif %}</div>
        </div>
        <div class="offer-user__money">
          <div class="offer-user__price">{{ user|get_done_budget_creator:product|display_currency }} 円</div>
          <div class="offer-user__price-sub">{{ user|get_undone_budget_creator:product|display_currency }} 円</div>
          <div class="offer-user__price-total">{{ user|get_current_budget_creator:product|display_currency }} 円</div>
        </div>
      </div>
      <div class="offer-detail">
        <table class="table table-offer offer-detail__info">
          <thead>
          <tr>
            <th class="offer-detail__name">姓名</th>
            <th class="offer-detail__bank">銀行名</th>
            <th class="offer-detail__branch">支店</th>
            <th class="offer-detail__branch-number">支店番号</th>
            <th class="offer-detail__account-type">口座種類</th>
            <th class="offer-detail__account-number">口座番号</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>{{ user.get_display_name }}</td>
            <td>{% if user.bank %}{{ creator.user.bank }}{% endif %}</td>
            <td>{% if user.bank_branch %}{{ creator.user.bank_branch }}{% endif %}</td>
            <td>{% if user.bank_branch_number %}{{ creator.user.bank_branch_number }}{% endif %}</td>
            <td>
              {% if user.get_account_type_display %}{{ user.get_account_type_display }}{% endif %}</td>
            <td>{% if user.account_number %}{{ user.account_number }}{% endif %}</td>
          </tr>
          </tbody>
        </table>
        <table class="table table-offer offer-detail__transaction">
          <thead>
          <tr>
            <th class="offer-detail__price">ディレクター</th>
            <th class="offer-detail__price">金額</th>
            <th class="offer-detail__close-date">取引成立日</th>
            <th class="offer-detail__accept-date">検収日</th>
            <th class="offer-detail__detail">業務内容</th>
            <th class="offer-detail__detail">シーン</th>
          </tr>
          </thead>
          <tbody>
          {% for offer in user.offer_to_creator.all %}
            <tr>
              <td>{{ offer.admin.get_display_name }}</td>
              <td>{{ offer.reward|display_currency }} 円</td>
              <td>{{ offer.accept_time|get_datetime }}</td>
              <td>{{ offer.check_time|get_datetime }}</td>
              <td>{{ offer.get_contract_display }}</td>
              <td>{{ offer.scenes }}</td>
            </tr>
          {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  {% endif %}
{% endwith %}
