{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load static %}
{% load util %}

{% block extrahead %}
    <style>
        .update_comment {
            padding-top: 20px;
        }
    </style>
{% endblock %}

{% block content %}
    <main class="container update_comment">
        <div class="m10 row">
            <div class="col-xs-6">
                <form method="post" action="{% url 'app:order_comment_change' order_id %}"
                      enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group">
                        <label class="control-label" for="id_comment">コメント</label>
                        <textarea
                                name="comment" cols="40" rows="10" class="form-control" placeholder="コメント" title=""
                                required="" id="id_comment">{{ object.comment }}</textarea>
                    </div>
                    {% if form.errors %}
                        {% for error in form.errors %}
                            {{ error.value }}
                        {% endfor %}
                    {% endif %}
                    <div class="form-group"><label class="control-label" for="id_file">File</label>
                        <div class="row bootstrap3-multi-input">
                            <div class="col-xs-12">
                                {% if object.file %}
                                    現在: <a href="{{ object.file.url }}">{{ object.file }}</a>
                                {% endif %}
                                <input type="file" name="file" class="" title="" id="id_file">
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="owner_id" value="{{ object.owner_id }}" id="id_owner_id">
                    <input type="hidden" name="product" value="{{ object.product_id }}" id="id_product_id">
                    <input type="hidden" name="product_order_upload" value="{{ object.product_order_upload }}" id="product_order_upload_id">
                    {% buttons %}
                        <input type="submit" value="更新" class="btn btn-primary"/>
                    {% endbuttons %}
                </form>
            </div>
        </div>
    </main>
{% endblock content %}
