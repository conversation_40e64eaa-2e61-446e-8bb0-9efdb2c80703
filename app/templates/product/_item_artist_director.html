{% load util %}
{% load static %}
{% load i18n %}

{% for user in users %}
  {% with user.user_creator.last as creator %}
  <div class="block-list__results-search-detail add-director__results-search-detail" data-user="{{ user.pk }}">
    <div class="project-setting__block-list" style="display: block;">
      <div class="block-list__user-info-left">
        <div class="user-info__avatar">
          <img src="{{ user|get_avatar:'medium' }}" alt="">
          {% with creator|get_task_in_progress as task_progress %}
            <div class="user-info__avatar-bandage {% if task_progress < 1 %}hide{% endif %}">{{ task_progress }}</div>
          {% endwith %}
        </div>
        <div class="user-info__artist-infor">
          <div class="artist-infor__name">{{ user.get_display_name }}</div>
          <div class="artist-infor__detail">
            <div class="artist-infor__title">{% if user.position %}{{ user.position }}{% endif %}</div>
          </div>
        </div>
      </div>
      <div class="block-list__user-info-right">
{#        <div class="block-list__identity-confirmation">#}
{#          <span class="identity-confirmation__identification">本人確認</span>#}
{#          <span class="identity-confirmation__NDA">NDA</span>#}
{#        </div>#}
        <div class="block-list__value_tradeoff_slider">
          <div class="account__tradeoff">
            <div class="account__trade-slider">
              {% for i in '12345' %}
                <div class="account__trade-item {% if i == creator.get_trading_display|stringformat:"i" %}active{% endif %}"
                     data-option="{{ i }}"></div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="block-list__register-action">
      {% if add_producer %}
        <input type="button"
               class="add-artist-into-list add-producer-into-list"
               value="{% trans 'Invite this producer' %}" name="add-this-director" style="margin-top: 12px;">
      {% else %}
        <input type="button"
               class="add-artist-into-list add-director-into-list"
               value="このディレクターを招待" name="add-this-director" style="margin-top: 12px;">
      {% endif %}
    </div>
  </div>
  {% endwith %}
{% endfor %}
