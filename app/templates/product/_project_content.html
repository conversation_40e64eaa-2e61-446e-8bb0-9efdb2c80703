{% load static %}
{% load util %}

{% block content %}
    <main class="project-list__content" data-filter="{{ filter }}" data-ajax="{{ ajax }}">
        {% for project in products %}
            {% with project|check_project_status:user as project_status %}
                <div class="project-list__item project_filter_{{ project_status }}">
                    <div class="overlay"></div>
                    {% if project_status != 'order' %}
                        <a href="{% url 'app:scene_index' %}?product_id={{ project.pk }}">
                            <div class="project-list__image">
                                <span class="pc-img">
                                    <img src="{{ project|get_image }}" alt="" >
                                </span>
                                <span class="sp-img">
                                    <img src="{{ project|get_image }}" alt="" >
                                </span>
                            </div>

                            <div class="progressbar">
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: {{ project.get_current_heart_rate }}%"></div>
                                    <div class="progress-bar bg-warning" style="width: {{ project.get_current_scene_rate }}%"></div>
                                </div>
                                <span class="progressbar__title">{{ project.get_current_heart_rate }}%</span>
                            </div>
                    {% else %}
                        <a href="{% url 'app:product_order' project.pk %}">
                            <div class="project-list__image">
                                <span class="pc-img"><img src="{% static 'images/ProjectBanner_b.png'%}" alt=""></span>
                                <span class="sp-img"><img src="{% static 'images/ProjectBanner_b.png'%}" alt=""></span>
                            </div>
                    {% endif %}

                        <div class="project-list__info">
                            <div class="project-list__tag">
                                {% if not project_status == 'all' %}
                                    <span class="project-list__tag-{% if project_status == 'new' %}new{% else %}fix{% endif %}">
                                        {{ project_status.upper }}
                                    </span>
                                {% endif %}
                            </div>
                            <div class="project-list__time">
                                {{ project.last_update_str }}
                            </div>
                        </div>
                    </a>
                </div>
            {% endwith%}
        {% endfor %}
    </main>
    <script>
        $(document).ready(function() {
            $('.overlay').on('click', function () {
               $(this).next()[0].click();
            });

            let content = $('.project-list__content');
            if (content.data('ajax') === 'False') {
                if ($('.project_filter_new')[0]) {
                    content.data('filter', 'new');
                    updateFilter('new');
                } else if ($('.project_filter_fix')[0]) {
                    updateFilter('fix');
                } else {
                    updateFilter('all')
                }
            } else if (content.data('ajax') === 'True'){
                updateFilter(content.data('filter'));
            }
        })
    </script>
{% endblock %}
