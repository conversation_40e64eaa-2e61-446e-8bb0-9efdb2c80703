{% load util %}
{% load static %}
{% load i18n %}

<div class="block-list__results-search-detail" data-artist="{{ artist.pk }}">
  <div class="project-setting__block-list">
    <div class="block-list__user-info-left">
      <div class="user-info__avatar">
        <img src="{{ artist.user|get_avatar:'medium' }}" alt="">
        {% with artist|get_task_in_progress as task_progress %}
          <div class="user-info__avatar-bandage {% if task_progress < 1 %}hide{% endif %}">{{ task_progress }}</div>
        {% endwith %}
      </div>
      <div class="user-info__artist-infor">
        <div class="artist-infor__name">{{ artist.user.get_display_name }}</div>
        <div class="artist-infor__detail">
          <div class="artist-infor__title">{% if artist.user.position %}{{ artist.user.position }}{% endif %}</div>
          <div class="artist-infor__organization-name">{% if artist.user.enterprise  %}{{ artist.user.enterprise }}{% endif %}</div>
        </div>
      </div>
    </div>
    <div class="block-list__user-info-right">
      {#      <div class="block-list__identity-confirmation">#}
      {#        <span class="identity-confirmation__identification">本人確認</span>#}
      {#        <span class="identity-confirmation__NDA">NDA</span>#}
      {#      </div>#}
      <div class="block-list__value_tradeoff_slider">
        <div class="account__tradeoff">
          <div class="account__trade-slider">
            {% for i in '12345' %}
              <div class="account__trade-item {% if i == artist.get_trading_display|stringformat:"i" %}active{% endif %}"
                   data-option="{{ i }}"></div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="artist-block-list mscrollbar">
    {% for company in artist.company_banned_creator.all %}
      <div class="artist-block-list__blocked-company">
        <div class="blocked-company__company-name">{{ company.company_name }}</div>
        <div class="blocked-company__reason-text">{{ company.reason }}</div>
      </div>
    {% endfor %}
  </div>
  <div class="block-list__register-action">
    <input type="button" class="btn btn--primary add-artist-into-block-list" value="このアーティストを登録" name="register-this-artist">
  </div>
</div>
