{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load compress %}
{% block title %}{% endblock title %}

{% block extrahead %}
    {% compress css %}
    <style>
        textarea.form-control {
            height: 60px;
        }

        .comments {
            width: 100%;
        }

        .button_summit {
            border: 0;
            background: 0;
            outline: none;
        }

        video {
            width: 100%;
        }

        .file_pdf {
            width: 100%;
            height: 250px;
        }

        .massenger__config-number {
            bottom: 12px;
        }

        .carousel_scroll_sp {
            overflow:auto;
            -webkit-overflow-scrolling:touch;
            border: 0.5px solid #cecece;
            max-height: 250px;
        }
        .show-item {
            display: block;
        }
    </style>
    {% endcompress %}
{% endblock %}

{% block content %}
    <main class="massenger order">
        {% if user.role == 'master_admin' and estimate_liked and not product.is_active %}
            <a href="{% url 'app:product_update' product.product_id %}">
                <div class="order__btnnew">
                    <button class="order__newpj" type="button" aria-hidden="true">Update Project Info</button>
                </div>
            </a>

            <a href="{% url 'app:product_active' product.product_id %}">
                <div class="order__btnnew">
                    <button class="order__newpj" type="button" aria-hidden="true">New project</button>
                </div>
            </a>
        {% endif %}
    </main>
    <div class="massenger__content">
        <div class="container">
            <div class="massenger__header">
                <div class="massenger__nav">
                    <div class="massenger__breadcrumbs">
                        <span>ORDER</span>
                    </div>

                    {% if user.role == "master_admin" %}
                        <div class="setting-config">
                            <i class="fas fa-cog"></i>
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="massenger__main">
                <div style="margin-bottom: 10px">クリップアイコンをクリックし、音をつけたい動画や資料をお送りください。</div>
                <div class="row massenger__column">
                    <div class="firt_comment" data-upload='{{ comment_without_file }}'>
                        <div class="col-md-6 col-sm-6">
                        </div>
                        <div class="col-md-6 col-sm-6">
                            <div class="comment">
                                <div class="comment__form">
                                    <div class="comment__textarea">
                                        <form method="post" action="{% url 'app:product_order' product.product_id %}"
                                              enctype="multipart/form-data" class="comments">
                                            {% csrf_token %}
                                            <div class="comment__textarea-item">
                                                <div class="comment__textarea-link">
                                                    <label class="comment__textarea-pin"
                                                           style="cursor: pointer; left: 10px">
                                                        <i class="fas fa-paperclip"></i>
                                                        <input type="file" name="file" class="hidden file-{{product.product_id}}" name="file" >
                                                    </label>
                                                </div>
                                            </div>
                                            <textarea name="comment" cols="40" rows="1"
                                                      class="textarea movie-{{ product.product_id }} comment__textarea-box"
                                                      required maxlength="255"></textarea>
                                            <input type="hidden" name="owner_id" value="{{ owner_id }}"
                                                   id="id_owner_id">
                                            <input type="hidden" name="product" value="{{ product.product_id }}"
                                                   id="id_product_id">
                                            <div class="comment__textarea-submit">
                                                <button type="submit" class="submit-link button_summit" style="padding: 0"><i
                                                        class="fas fa-paper-plane"></i></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if comment_without_file %}
                        <div class="col-md-6 col-sm-6">
                        </div>
                        <div class="col-md-6 col-sm-6">
                            {% with comment_without_file.first.created_en as comment_time %}
                                <div class="comment">
                                    <div class="comment__form">
                                        {% for obj in comment_without_file %}
                                            {% if forloop.counter == 1 %}
                                                <div class="comment__date">
                                                    <span>{{ obj.created_en }}</span>
                                                </div>
                                            {% elif comment_time != obj.created_en %}
                                                <div class="comment__date">
                                                    <span>{{ obj.created_en }}</span>
                                                </div>
                                                {% define obj.created_en as comment_time %}
                                            {% endif %}
                                            {% if obj.user.role == user.role %}
                                                <div class="comment__reply">
                                                    <div class="comment__reply-bgr">
                                                        <div class="comment__reply-right">
                                                            <div class="comment__reply-view">
                                                                <ul class="comment__reply-list unstyled">
                                                                    {% if obj.preview_comment_order.count > 3 %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            {% if forloop.counter <= 2 %}
                                                                                <li class="comment__view-item">
                                                                                    <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                         alt="" title="{{ pre.owner }}">
                                                                                </li>
                                                                            {% endif %}
                                                                        {% endfor %}

                                                                        <li class="comment__view-item comment__view-item--add">
                                                                            <a class="dropdown-link"
                                                                               data-toggle="dropdown">
                                                                                <span>+{{ obj.preview_comment_order.count|add:"-2" }}</span>
                                                                            </a>
                                                                            <ul class="comment__view-dropdown dropdown-menu unstyled">
                                                                                {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                                    {% if forloop.counter > 2 %}
                                                                                        <li class="dropdown-item">
                                                                                            <a href="#">
                                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                                     alt="" title="{{ pre.owner }}">
                                                                                                <p class="comment__view-name">{{ pre.owner }}</p>
                                                                                            </a>
                                                                                        </li>
                                                                                    {% endif %}
                                                                                {% endfor %}
                                                                            </ul>
                                                                        </li>
                                                                    {% else %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            <li class="comment__view-item">
                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                     alt="" title="{{ pre.owner }}">
                                                                            </li>
                                                                        {% endfor %}
                                                                    {% endif %}
                                                                </ul>
                                                            </div>
                                                            <div class="comment__reply-content">
                                                                <div class="comment__reply-setting">
                                                                    {% if obj.owner_id == user.id %}
                                                                        <div class="comment__reply-link">
                                                                            <span>...</span>
                                                                        </div>
                                                                    {% endif %}
                                                                    <div class="comment__reply-edit-box">
                                                                        <div class="comment__reply-edit">
                                                                            <a href="{% url 'app:order_comment_change' obj.pk %}">
                                                                                <i class="fas fa-pencil-alt"> </i>
                                                                            </a>
                                                                        </div>
                                                                        <div class="comment__reply-remove">
                                                                            <a href="{% url 'app:order_comment_delete' obj.pk %}">
                                                                                <i class="far fa-times-circle"></i>
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="comment__reply-desc">
                                                                    <p>{{ obj.comment | linebreaks }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="comment__reply-avatar">
                                                            <img src="{% if obj.user.avatar %}{{ obj.user|get_avatar:'small' }}{% else %} {% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                 alt="" title="{{ obj.user }}">
                                                        </div>
                                                    </div>

                                                    {% if obj.file %}
                                                        <div class="comment__download comment__download--reply">
                                                            <i class="fas fa-download"></i>
                                                            <a class="comment__download-icon-down" href="{{ obj.file.url }}">{{ obj.get_file_name }}</a>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            {% else %}
                                                <div class="comment__item">
                                                    <div class="comment__item-bgr">
                                                        <div class="comment__avatar">
                                                            <img src="{% if obj.user.avatar %}{{ obj.user|get_avatar:'small' }}{% else %} {% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                alt="" title="{{ obj.user }}">
                                                        </div>
                                                        <div class="comment__item-right">
                                                            <div class="comment__content comment__content-desc">
                                                                <p>{{ obj.comment | linebreaks }}</p>
                                                            </div>
                                                            <div class="comment__view">
                                                                <ul class="comment__view-list unstyled">
                                                                    {% if obj.preview_comment_order.count > 3 %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            {% if forloop.counter <= 2 %}
                                                                                <li class="comment__view-item">
                                                                                    <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                         alt="" title="{{ pre.owner }}">
                                                                                </li>
                                                                            {% endif %}
                                                                        {% endfor %}

                                                                        <li class="comment__view-item comment__view-item--add">
                                                                            <a class="dropdown-link"
                                                                               data-toggle="dropdown">
                                                                                <span>+{{ obj.preview_comment_order.count|add:"-2" }}</span>
                                                                            </a>
                                                                            <ul class="comment__view-dropdown dropdown-menu unstyled">
                                                                                {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                                    {% if forloop.counter > 2 %}
                                                                                        <li class="dropdown-item">
                                                                                            <a href="#">
                                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                                     alt="" title="{{ pre.owner }}">
                                                                                                <p class="comment__view-name">{{ pre.owner }}</p>
                                                                                            </a>
                                                                                        </li>
                                                                                    {% endif %}
                                                                                {% endfor %}
                                                                            </ul>
                                                                        </li>
                                                                    {% else %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            <li class="comment__view-item">
                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                     alt="" title="{{ pre.owner }}">
                                                                            </li>
                                                                        {% endfor %}
                                                                    {% endif %}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>


                                                    {% if obj.file %}
                                                        <div class="comment__download comment__download--bottom">
                                                            <i class="fas fa-download"></i>
                                                            <a class="comment__download-icon-down" href="{{ obj.file.url }}">{{ obj.get_file_name }}</a>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endwith %}
                            <div class="comment__textarea">
                                <form method="post" action="{% url 'app:product_order' product.product_id %}"
                                      enctype="multipart/form-data" class="comments">
                                    {% csrf_token %}
                                    <div class="comment__textarea-item">
                                        <div class="comment__textarea-link">
                                            <label class="comment__textarea-pin"
                                                   style="cursor: pointer; left: 10px">
                                                <i class="fas fa-paperclip"></i>
                                                <input type="file" name="file" class="hidden file-{{product.product_id}}" name="file" >
                                            </label>
                                        </div>
                                    </div>
                                    <textarea name="comment" cols="40" rows="1"
                                              class="textarea movie-{{ product.product_id }} comment__textarea-box"
                                              required maxlength="255"></textarea>
                                    <input type="hidden" name="owner_id" value="{{ owner_id }}" id="id_owner_id">
                                    <input type="hidden" name="product" value="{{ product.product_id }}" id="id_product_id">
                                    <input type="hidden" name="product_order_upload" value="{{ order_upload.pk }}"
                                           id="id_product_order_upload_id">
                                    <div class="comment__textarea-submit">
                                        <button type="submit" class="submit-link button_summit" style="padding: 0"><i
                                                class="fas fa-paper-plane"></i></button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    {% endif %}
                </div>
                {% for order_upload in list_order_upload %}
                    <div class="row massenger__column">
                        <div class="col-md-6 col-sm-6">
                            <div class="massenger__left">
                                {% if user.role == 'master_admin' and not order_upload.check_favorite %}
                                    {% if order_upload.type_file_upload == 1 %}
                                        <span class="massenger__video-addvideo">
                                            <a href="{% url 'app:product_order_upload' product.product_id %}?upload=video&upload_id={{ order_upload.pk }}">+</a>
                                        </span>
                                    {% elif order_upload.type_file_upload == 0 and order_upload.type_file_pdf != 2 %}
                                        <span class="massenger__video-addvideo">
                                            <a href="{% url 'app:product_order_upload' product.product_id %}?upload=pdf&upload_id={{ order_upload.pk }}&upload_carousel={{ order_upload.type_file_pdf }}">+</a>
                                        </span>
                                    {% endif %}
                                {% endif %}
                                {% if order_upload.check_favorite %}
                                    <div class="carousel slide" id="videoCarousel-{{ order_upload.pk }}"
                                        data-ride="carousel"  data-interval="false">

                                        {% if order_upload.carousel_product_order_upload_list_index %}
                                            <ol class="carousel-indicators {% if order_upload.file.name == '' and order_upload.carousel_product_order_upload_list_index|length == 1 %} hide{% endif %}">
                                                {% if order_upload.file %}
                                                    <li class="{% if order_upload.tag == 3 %}heart-active{% else %}disable-click{% endif %}"
                                                        data-target="#videoCarousel-{{order_upload.product_order_upload_id}}"
                                                        data-slide-to="0" data-scene='{{order_upload.pk}}'>a</li>
                                                {% endif %}
                                                {% for o in order_upload.carousel_product_order_upload_list_index %}
                                                    <li data-target="#videoCarousel-{{ order_upload.product_order_upload_id }}"
                                                        data-slide-to="{% if order_upload.file %}{{forloop.counter}}{%else%}{{forloop.counter0}}{%endif%}"
                                                        class="{% if o.tag == 3 %}heart-active{% else %}disable-click{% endif %}"
                                                        data-scene='{{ o.pk }}'>
                                                        {{ o.index }}
                                                    </li>
                                                {% endfor %}
                                            </ol>
                                        {% else %}
                                            <ol class="carousel-indicators hide">
                                                <li class="active" data-scene="{{ order_upload.pk }}"></li>
                                            </ol>
                                        {% endif %}
                                        <div class="carousel-inner">
                                            {% if order_upload.type_file_upload == 1 %}
                                                {% if order_upload.file %}
                                                    <div class="item {% if order_upload.tag == 3 %}active{% endif %}">
                                                        {% with video_info=order_upload.file|get_video_url_with_fallback %}
                                                        <video id="movie-{{ order_upload.pk }}" class="video-js" controls
                                                               preload="metadata"
                                                               poster="" data-setup="{}"
                                                               data-video-src="{{ video_info.url }}"
                                                               data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                                                               data-fallback-src="{% if order_upload.file %}{{ order_upload.file.url }}{% endif %}">
                                                            <source src="{{ video_info.url }}" type='video/mp4'>
                                                        {% endwith %}
                                                            <p class="vjs-no-js">
                                                                To view this video please enable JavaScript, and consider
                                                                upgrading to a
                                                                web browser that
                                                                <a href="http://videojs.com/html5-video-support/"
                                                                   target="_blank">
                                                                    supports HTML5 video
                                                                </a>
                                                            </p>
                                                        </video>
                                                    </div>
                                                {% endif %}

                                                {% for o in order_upload.carousel_product_order_upload_list %}
                                                    <div class="item {% if o.tag == 3 %}active{%endif%}">
                                                        {% with video_info=o.file|get_video_url_with_fallback %}
                                                        <video id="movie-{{ o.pk }}" class="video-js" controls
                                                               preload="metadata"
                                                               poster="" data-setup="{}"
                                                               data-video-src="{{ video_info.url }}"
                                                               data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                                                               data-fallback-src="{% if o.file %}{{ o.file.url }}{% endif %}">
                                                            <source src="{{ video_info.url }}" type='video/mp4'>
                                                        {% endwith %}
                                                            <p class="vjs-no-js">
                                                                To view this video please enable JavaScript, and consider
                                                                upgrading to a
                                                                web browser that
                                                                <a href="http://videojs.com/html5-video-support/"
                                                                   target="_blank">
                                                                    supports HTML5 video
                                                                </a>
                                                            </p>
                                                        </video>
                                                    </div>
                                                {% endfor %}
                                            {% elif order_upload.type_file_upload == 0 %}
                                                {%  if order_upload.file %}
                                                    <div class="item {% if order_upload.tag == 3 %}active{% endif %} {% if not request.user_agent.is_pc %}carousel_scroll_sp{% endif %}">
                                                        <iframe src="{{ order_upload.file.url }}#page=1&view=FitH" width="100%" height="250px" frameborder="0"></iframe>
                                                        <a href="{{ order_upload.file.url }}">View on new tab</a>
                                                    </div>
                                                {% endif %}
                                                {% for o in order_upload.carousel_product_order_upload_list %}
                                                    <div class="item {% if o.tag == 3 %} active{% endif %} {% if not request.user_agent.is_pc %} carousel_scroll_sp{% endif %}">
                                                        <iframe src="{{ o.file.url }}#page=1&view=FitH" width="100%" height="250px" frameborder="0"></iframe>
                                                        <a href="{{ o.file.url }}">View on new tab</a>
                                                    </div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="massenger__videodone">
                                        {% if user.role == 'master_admin' %}
                                            <span class="videoicon link-icon ic-done">
                                                <i class="fas fa-heart icon done"></i>
                                                <i class="far fa-heart icon null"></i>
                                            </span>
                                        {% else %}
                                            <a class="link-icon videoicon ic-done heart-active"
                                               href="javascript:void(0)" {% if user.role == 'master_client' %}onclick="updateTag(this)"{% endif %}>
                                                <i class="fas fa-heart icon done"></i>
                                                <i class="far fa-heart icon null"></i>
                                            </a>
                                            {% if order_upload.type_file_pdf == 2 and user.role == 'master_client' %}
                                                <span style="padding: 5px 10px; font-size: 0.4em">(ハート付けたら自動的に{{ order_upload.price_bill_pdf }}円を支払いますので、ご注意ください。)</span>
                                            {% endif %}
                                        {%  endif %}
                                    </div>
                                {% else %}
                                    <div class="carousel slide" id="videoCarousel-{{ order_upload.pk }}"
                                        data-ride="carousel"  data-interval="false">

                                        {% if order_upload.carousel_product_order_upload_list_index %}
                                            <ol class="carousel-indicators {% if order_upload.file.name == '' and order_upload.carousel_product_order_upload_list_index|length == 1 %} hide{% endif %}">
                                                {% if order_upload.file %}
                                                <li class="{% if not order_upload.carousel_product_order_upload_list.exists %}active{% endif %}"
                                                    data-target="#videoCarousel-{{order_upload.product_order_upload_id}}"
                                                    data-slide-to="0" data-scene='{{order_upload.pk}}'>a</li>
                                                {% endif %}
                                                {% for o in order_upload.carousel_product_order_upload_list_index %}
                                                    <li data-target="#videoCarousel-{{ order_upload.product_order_upload_id }}"
                                                        data-slide-to="{% if order_upload.file %}{{forloop.counter}}{%else%}{{forloop.counter0}}{%endif%}"
                                                        class="{% if forloop.last %}active{% endif %}"
                                                        data-scene='{{ o.pk }}'>
                                                        {{ o.index }}
                                                    </li>
                                                {% endfor %}
                                            </ol>
                                        {% else %}
                                            <ol class="carousel-indicators hide">
                                                <li class="active" data-scene="{{ order_upload.pk }}"></li>
                                            </ol>
                                        {% endif %}
                                        <div class="carousel-inner">
                                            {% if order_upload.type_file_upload == 1 %}
                                                {% if order_upload.file %}
                                                    <div class="item {% if not order_upload.carousel_product_order_upload_list.exists %}active{% endif %}">
                                                        {% with video_info=order_upload.file|get_video_url_with_fallback %}
                                                        <video id="movie-{{ order_upload.pk }}" class="video-js" controls
                                                               preload="metadata"
                                                               poster="" data-setup="{}"
                                                               data-video-src="{{ video_info.url }}"
                                                               data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                                                               data-fallback-src="{% if order_upload.file %}{{ order_upload.file.url }}{% endif %}">
                                                            <source src="{{ video_info.url }}" type='video/mp4'>
                                                        {% endwith %}
                                                            <p class="vjs-no-js">
                                                                To view this video please enable JavaScript, and consider
                                                                upgrading to a
                                                                web browser that
                                                                <a href="http://videojs.com/html5-video-support/"
                                                                   target="_blank">
                                                                    supports HTML5 video
                                                                </a>
                                                            </p>
                                                        </video>
                                                    </div>
                                                {% endif %}
                                                {% for o in order_upload.carousel_product_order_upload_list %}
                                                    <div class="item {% if forloop.last %}active{% endif %}">
                                                        {% with video_info=o.file|get_video_url_with_fallback %}
                                                        <video id="movie-{{ o.pk }}" class="video-js" controls
                                                               preload="metadata"
                                                               poster="" data-setup="{}"
                                                               data-video-src="{{ video_info.url }}"
                                                               data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                                                               data-fallback-src="{% if o.file %}{{ o.file.url }}{% endif %}">
                                                            <source src="{{ video_info.url }}" type='video/mp4'>
                                                        {% endwith %}
                                                            <p class="vjs-no-js">
                                                                To view this video please enable JavaScript, and consider
                                                                upgrading to a
                                                                web browser that
                                                                <a href="http://videojs.com/html5-video-support/"
                                                                   target="_blank">
                                                                    supports HTML5 video
                                                                </a>
                                                            </p>
                                                        </video>
                                                    </div>
                                                {% endfor %}
                                            {% elif order_upload.type_file_upload == 0 %}
                                                {%  if order_upload.file %}
                                                    <div class="item {% if not order_upload.carousel_product_order_upload_list.exists %}active{% endif %} {% if not request.user_agent.is_pc %}carousel_scroll_sp{% endif %}">
                                                        <iframe src="{{ order_upload.file.url }}#page=1&view=FitH" width="100%" height="250px" frameborder="0"></iframe>
                                                        <a href="{{ order_upload.file.url }}">View on new tab</a>
                                                    </div>
                                                {% endif %}
                                                {% for o in order_upload.carousel_product_order_upload_list %}
                                                    <div class="item {% if forloop.last %} active{%endif%}{% if not request.user_agent.is_pc %} carousel_scroll_sp{% endif %}">
                                                        <iframe src="{{ o.file.url }}#page=1&view=FitH" width="100%" height="250px" frameborder="0"></iframe>
                                                        <a href="{{ o.file.url }}">View on new tab</a>
                                                    </div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="massenger__videodone">
                                        {% if user.role == 'master_admin' %}
                                            <span class="videoicon link-icon ic-null disable-click">
                                                <i class="fas fa-heart icon done"></i>
                                                <i class="far fa-heart icon null"></i>
                                            </span>
                                        {% else %}
                                            <a class="link-icon videoicon ic-null"
                                               href="javascript:void(0)" {% if user.role == 'master_client' %}onclick="updateTag(this)"{% endif %}>
                                                <i class="fas fa-heart icon done"></i>
                                                <i class="far fa-heart icon null"></i>
                                            </a>
                                            {% if order_upload.type_file_pdf == 2 and user.role == 'master_client' %}
                                                <span style="padding: 5px 10px; font-size: 0.4em">(ハート付けたら自動的に{{ order_upload.price_bill_pdf }}円を支払いますので、ご注意ください。)</span>
                                            {% endif %}
                                        {%  endif %}
                                    </div>
                                {% endif %}

                                <div class="massenger__config">
                                    <div class="massenger__config-content">
                                        <div class="massenger__config-close">
                                            {% if user.role == "master_admin" %}
                                                <a href="javascript:void(0)" onclick="deleteOrderUpload(this)">
                                                    <i class="far fa-times-circle"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-6">
                            {% for obj in order_upload.product_order_upload.all %}
                                <div class="comment">
                                    <div class="comment__form">
                                        {% with order_upload.product_order_upload.all.first.created_en as comment_time %}
                                            {% if forloop.counter == 1 %}
                                                <div class="comment__date">
                                                    <span>{{ obj.created_en }}</span>
                                                </div>
                                            {% elif comment_time != obj.created_en %}
                                                <div class="comment__date">
                                                    <span>{{ obj.created_en }}</span>
                                                </div>
                                                {% define obj.created_en as comment_time %}
                                            {% endif %}
                                            {% if obj.user.role == user.role %}
                                                <div class="comment__reply">
                                                    <div class="comment__reply-bgr">
                                                        <div class="comment__reply-right">
                                                            <div class="comment__reply-view">
                                                                <ul class="comment__reply-list unstyled">
                                                                    {% if obj.preview_comment_order.count > 3 %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            {% if forloop.counter <= 2 %}
                                                                                <li class="comment__view-item">
                                                                                    <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                         alt="" title="{{ pre.owner }}">
                                                                                </li>
                                                                            {% endif %}
                                                                        {% endfor %}

                                                                        <li class="comment__view-item comment__view-item--add">
                                                                            <a class="dropdown-link"
                                                                               data-toggle="dropdown">
                                                                                <span>+{{ obj.preview_comment_order.count|add:"-2" }}</span>
                                                                            </a>
                                                                            <ul class="comment__view-dropdown dropdown-menu unstyled">
                                                                                {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                                    {% if forloop.counter > 2 %}
                                                                                        <li class="dropdown-item">
                                                                                            <a href="#">
                                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                                     alt="" title="{{ pre.owner }}">
                                                                                                <p class="comment__view-name">{{ pre.owner }}</p>
                                                                                            </a>
                                                                                        </li>
                                                                                    {% endif %}
                                                                                {% endfor %}
                                                                            </ul>
                                                                        </li>
                                                                    {% else %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            <li class="comment__view-item">
                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                     alt="" title="{{ pre.owner }}">
                                                                            </li>
                                                                        {% endfor %}
                                                                    {% endif %}
                                                                </ul>
                                                            </div>
                                                            <div class="comment__reply-content">
                                                                <div class="comment__reply-setting">
                                                                    {% if obj.user == user %}
                                                                        <div class="comment__reply-link">
                                                                            <span>...</span>
                                                                        </div>
                                                                    {% endif %}
                                                                    <div class="comment__reply-edit-box">
                                                                        <div class="comment__reply-edit">
                                                                            <a href="{% url 'app:order_comment_change' obj.pk %}">
                                                                                <i class="fas fa-pencil-alt"> </i>
                                                                            </a>
                                                                        </div>
                                                                        <div class="comment__reply-remove">
                                                                            <a href="{% url 'app:order_comment_delete' obj.pk %}">
                                                                                <i class="far fa-times-circle"></i>
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="comment__reply-desc">
                                                                    <p>{{ obj.comment | linebreaks }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="comment__reply-avatar">
                                                            <img src="{% if obj.user.avatar %}{{ obj.user|get_avatar:'small' }}{% else %} {% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                 alt="" title="{{ obj.user }}">
                                                        </div>
                                                    </div>
                                                    {% if obj.file %}
                                                        <div class="comment__download comment__download--reply">
                                                            <i class="fas fa-download"></i>
                                                            <a class="comment__download-icon-down" href="{{ obj.file.url }}">{{ obj.get_file_name }}</a>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            {% else %}
                                                <div class="comment__item">
                                                    <div class="comment__item-bgr">
                                                        <div class="comment__avatar">
                                                            <img src="{% if obj.user.avatar %}{{ obj.user|get_avatar:'small' }}{% else %} {% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                 alt="" title="{{ obj.user }}">
                                                        </div>
                                                        <div class="comment__item-right">
                                                            <div class="comment__content comment__content-desc">
                                                                <p>{{ obj.comment | linebreaks }}</p>
                                                            </div>
                                                            <div class="comment__view">
                                                                <ul class="comment__view-list unstyled">
                                                                    {% if obj.preview_comment_order.count > 3 %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            {% if forloop.counter <= 2 %}
                                                                                <li class="comment__view-item">
                                                                                    <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                         alt="" title="{{ pre.owner }}">
                                                                                </li>
                                                                            {% endif %}
                                                                        {% endfor %}

                                                                        <li class="comment__view-item comment__view-item--add">
                                                                            <a class="dropdown-link"
                                                                               data-toggle="dropdown">
                                                                                <span>+{{ obj.preview_comment_order.count|add:"-2" }}</span>
                                                                            </a>
                                                                            <ul class="comment__view-dropdown dropdown-menu unstyled">
                                                                                {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                                    {% if forloop.counter > 2 %}
                                                                                        <li class="dropdown-item">
                                                                                            <a href="#">
                                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                                 alt="" title="{{ pre.owner }}">
                                                                                            <p class="comment__view-name">
                                                                                                {{ pre.owner }}
                                                                                            </p>
                                                                                            </a>
                                                                                        </li>
                                                                                    {% endif %}
                                                                                {% endfor %}
                                                                            </ul>
                                                                        </li>
                                                                    {% else %}
                                                                        {% for pre in obj.preview_comment_order|order_by:"-created" %}
                                                                            <li class="comment__view-item">
                                                                                <img src="{% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                     alt="" title="{{ pre.owner }}">
                                                                            </li>
                                                                        {% endfor %}
                                                                    {% endif %}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% if obj.file %}
                                                        <div class="comment__download comment__download--bottom">
                                                            <i class="fas fa-download"></i>
                                                            <a class="comment__download-icon-down" href="{{ obj.file.url }}">{{ obj.get_file_name }}</a>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        {% endwith %}
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="comment__textarea">
                                <form method="post" action="{% url 'app:product_order' product.product_id %}"
                                      enctype="multipart/form-data" class="comments">
                                    {% csrf_token %}
                                    <div class="comment__textarea-item">
                                        <div class="comment__textarea-link">
                                            <label class="comment__textarea-pin"
                                                   style="cursor: pointer; left: 10px">
                                                <i class="fas fa-paperclip"></i>
                                                <input type="file" name="file" class="hidden file-{{product.product_id}}" name="file" >
                                            </label>
                                        </div>
                                    </div>
                                    <textarea name="comment" cols="40" rows="1"
                                              class="textarea movie-{{ product.product_id }} comment__textarea-box"
                                              required maxlength="255"></textarea>
                                    <input type="hidden" name="owner_id" value="{{ owner_id }}" id="id_owner_id">
                                    <input type="hidden" name="product" value="{{ product.product_id }}" id="id_product_id">
                                    <input type="hidden" name="product_order_upload" value="{{ order_upload.pk }}"
                                           id="id_product_order_upload_id">
                                    <div class="comment__textarea-submit">
                                        <button type="submit" class="submit-link button_summit" style="padding: 0px">
                                            <i class="fas fa-paper-plane"></i></button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                {% endfor %}
                {% if user.role == 'master_admin' %}
                    <div class="row massenger__column">
                        <div class="col-md-6 col-sm-6">
                            <div class="massenger__add">
                                <a class="massenger__add-link col-md-6 col-sm-6"
                                   href="{% url 'app:product_order_upload' product.product_id %}?upload=pdf">
                                    <i class="far fa-file-pdf"></i>
                                </a>
                                <a class="massenger__add-link col-md-6 col-sm-6"
                                   href="{% url 'app:product_order_upload' product.product_id %}?upload=video">
                                    <i class="fas fa-video"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% compress js inline %}
    <script>
        var upload_file_dom = '.file-{{product.product_id}}';

        $(document).ready(function () {
            let list_comment_without_file = '{{ comment_without_file.first }}';
            if (list_comment_without_file) {
                $('.firt_comment').hide()
            }

            $('.textarea').attr({
                accept: 'image/*',
                oninvalid: "this.setCustomValidity('このフィールドは必須項目です。')",
                oninput: "this.setCustomValidity('')"
            });

        });
        $('.massenger__left').click(function () {
            $('.massenger__remove, .massenger__change').removeClass('show-item');
            $(this).find('.massenger__remove, .massenger__change').toggleClass('show-item');
        });

        $(document).on('change', upload_file_dom , function (e) {
            let fileName = e.target.files[0].name;
            let len_input_file = $(upload_file_dom).length;
            for(i = 0; i < len_input_file ; i ++){
                if($($(upload_file_dom)[i]).val() !== ''){
                    var clear_file_dom = "clear_" + i;
                }
            }
            let comment_box = $(this).closest('.comment__textarea');
            if (comment_box.find('.comment__textarea-file').length > 0) {
                comment_box.find('.comment__textarea-file span').text(fileName);
            } else {
                comment_box.find('.comment__textarea-link').prepend('<div class="comment__textarea-file"><span>' + fileName +
                    '</span><button type="button" class="close '+ clear_file_dom +'" id="clear_file"  >×</button></div>')
            }
        });

        let updateTag = function (obj) {
            let parent = $(obj).parents('.massenger__left')
            let active = parent.find('.carousel-indicators li.active');
            let list_li = parent.find('.carousel-indicators');
            let upload_id = active.data('scene');

            $.ajax({
                method: 'POST',
                url: "/product/order/tag/update/" + upload_id + "/",
                success: function(result) {
                    if('success' === result.status) {
                        if('un_favorite' === result.type) {
                            $(obj).removeClass('active');
                            $(obj).removeClass('ic-done');
                            $(obj).addClass('ic-null');
                            list_li.find('li.disable-click').not('.active').removeClass('disable-click');
                        } else if ('favorite' === result.type){
                            $(obj).addClass('active');
                            $(obj).removeClass('ic-null');
                            $(obj).addClass('ic-done');
                            list_li.find('li').not('.disable-click').not('.active').addClass('disable-click');
                        }

                        if(result.receipt_url != null) {
                            window.open(result.url, '_blank');
                            obj.nextElementSibling.innerHTML = ('<a href="'+ result.receipt_url +'">Receipt</a>')
                        }
                    } else {
                        alert(result.message)
                    }
                }
            });
        };

        let deleteOrderUpload = function (obj) {
            let active = $(obj).parents('.massenger__left').find('.carousel-indicators li.active');
            let order_upload_id = active.data('scene');
            window.location.href = '/product/order/file/delete/' + order_upload_id;
        };

        $('.comment__textarea-box').keydown(function () {
            var el = this;
            setTimeout(function () {
                el.style.cssText = 'height: auto';
                el.style.cssText = 'height:' + el.scrollHeight + 'px';
            }, 0);
        });

        $('.comment__textarea-box').keyup(function () {
            let comment_content = $(this).val();
            $(this).siblings('.comment__textarea-submit').find('button').css('color',"#258BCF");
            if (!comment_content) {
                $(this).siblings('.comment__textarea-submit').find('button').css('color',"#707070");
            }
        });

        $(document).on('click', '#clear_file', function () {
            $(this).parents('.comment__textarea-link').find('.comment__textarea-file').remove();
            let file_dom =  $(this)[0].classList[1].replace('clear_','');
            $($(upload_file_dom)[file_dom]).val('')
        });

        $('.setting-config').click(function () {
            $('.massenger__config').toggleClass('show-item');
            $('.setting-config').toggleClass('active');
            if ($('.massenger__config').hasClass('show-item')) {
                $('video').removeAttr('controls');
            } else {
                $('video').attr('controls', '');
            }
        });
    </script>
    {% endcompress %}
{% endblock content %}
