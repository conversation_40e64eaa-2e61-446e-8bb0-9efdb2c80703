{% load util %}
{% load static %}
{% load i18n %}

{% with artist.user as user %}
    <li class="u-block-wrapper u-mb16 block-artist-selected" data-artist="{{ artist.pk }}">
        <div class="u-row u-gap8 u-fill">
            <div class="c-avatar48 c-avatar">
                <img src="{{ user|get_avatar:'medium' }}" alt="">
                {% with artist|get_task_in_progress as task_progress %}
                    <div class="user-info__avatar-bandage {% if task_progress < 1 %}hide{% endif %}">{{ task_progress }}</div>
                {% endwith %}
            </div>
            <div class="u-col u-gap8 u-fill">
                <ul class="u-row-between u-w100">
                    <li class="heading-13-spacing u-fill">{{ user.get_display_name }}</li>
                </ul>
                <ul class="u-row-between u-wrap u-w100 u-gap6">
                    <li class="bodytext-11 u-line-height-100">
                        {% if user.position %}{{ user.position }}{% endif %}</li>
                    {#                    <li class="bodytext-11 u-line-height-100">#}
                    {#                        {% if user.enterprise %}{{ user.enterprise }}{% endif %}</li>#}
                </ul>
            </div>
        </div>
        <div class="hover-menu delete-block-artist">
                            <span class="material-symbols-rounded">
                                delete
    </span>
        </div>
    </li>
{% endwith %}

