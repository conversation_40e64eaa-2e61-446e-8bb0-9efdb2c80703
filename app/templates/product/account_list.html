{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}

{% block extrahead %}
    <style>
    </style>
{% endblock %}
{% block content %}
    <main class="acc-manager">
        <div class="banner">
            <div class="banner__img">
                <a class="link-project" href="{% url 'app:scene_index' %}?product_id={{ product.product_id }}">
                    <img src="{{ product|get_image }}" alt="">
                </a>
            </div>

            <div class="progressbar">
                <div class="progress">
                    <div class="progress-bar bg-success" style="width: {{ product.get_current_heart_rate }}%"></div>
                    <div class="progress-bar bg-warning" style="width: {{ product.get_current_scene_rate }}%"></div>
                </div>
                <span class="progressbar__title">{{ product.get_current_heart_rate }}%</span>
            </div>
        </div>
        <div class="acc-manager__content">
            <div class="container">
                <div class="acc-manager__header">
                    <h4 class="acc-manager__header-title">アカウント管理</h4>
                </div>
                <div class="acc-manager__main">
                    <table class="table">
                        <thead>
                        <tr>
                            <th class="acc-manager__title wd-45 text-center">メールアドレス</th>
                            <th class="acc-manager__title wd-31 text-center">IP</th>
                            <th class="wd-8 text-center">
                    <span class="acc-manager__done">
                      <i class="fas fa-heart"></i>
                    </span>
                            </th>
                            <th class="wd-8 text-center">
                    <span class="acc-manager__rating">
                      <i class="fa fa-star"></i>
                    </span>
                            </th>
                            <th class="wd-8 text-center"></th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for obj in object_list_user|filter_user_in_project:product_id %}
                            <tr>
                                <td class="wd-45">
                                    <div class="acc-manager__email">
                                        <a class="acc-manager__images" href="#">
                                            <img src="{% if obj.avatar %}{{ obj|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}" alt="">
                                        </a>
                                        <span>{{ obj.email }}</span>
                                    </div>
                                </td>
                                <td class="wd-31 text-center">
                                    <div class="acc-manager__ip">
                                        <span class="acc-manager__config">{{ obj.list_ip }}</span>
                                        <a class="acc-manager__seting" href="#">
                                            <i class="fas fa-cog"></i>
                                        </a>
                                    </div>
                                </td>
                                <td class="wd-8 text-center">
                                    <div class="checkbox">
                                        <input type="checkbox" class="checkbox"
                                               data-user={{ obj.id }} data-product={{ product_id }}
                                               name="favorite" title="favorite" {% if obj|check_is_favorite %}
                                               checked {% endif %}>
                                    </div>
                                </td>
                                <td class="wd-8 text-center">
                                    <div class="checkbox">
                                        <input type="checkbox" class="checkbox"
                                               data-user={{ obj.id }} data-product={{ product_id }}
                                               name="rating" title="rating" {% if obj|check_is_rating %}
                                               checked {% endif %}>
                                    </div>
                                </td>
                                <td class="wd-8 text-center">
                                    <a class="acc-manager__close btn-delete" href="javascript:void(0)"
                                       data-user={{ obj.id }} data-product={{ product_id }}>
                                        <i class="far fa-times-circle"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                        <tr>
                            <td class="text-center" colspan="5">
                                <a class="acc-manager__add" href="#" data-toggle="modal" data-target="#modal-add-user"
                                   data-product={{ product_id }}>
                                    <i class="fas fa-plus-circle"></i>
                                </a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <div class="modal fade" id="modal-add-user" role="dialog" style="z-index: 9999">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">プロジェクトにユーザーを追加</h4>
                </div>
                <div class="modal-body">
                    {% for item in object_list_user|filter_user_not_in_project:product_id %}
                        <div class="checkbox-modal">
                            <label><input type="checkbox" data-user={{ item.id }}>{{ item.email }} ({{ item.get_full_name }})</label>
                        </div>
                    {% endfor %}

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-add-user"
                            data-product={{ product_id }} data-dismiss="modal">サブミット
                    </button>
                </div>
            </div>

        </div>
    </div>


    <script>
        $(document).ready(function () {

            // Handle assign action (rating, favorite) to user in project
            $('.checkbox').change(function () {
                var action = $(this).attr("name");
                var user_id = $(this).attr("data-user");
                var product_id = $(this).attr("data-product");
                var data = {
                    product_id: product_id,
                    action: action,
                    user_id: user_id
                };
                $.ajax({
                    url: '/ajax/change_permission/',
                    method: 'POST',
                    data: data,
                    success: function (result) {
                        $(".alert-success").show();
                        setTimeout(function () {
                            $(".alert-success").hide();
                        }, 5000);
                    }
                })
            });

            // Handle remove user from project
            $('.btn-delete').click(function () {
                var user_id = $(this).attr("data-user");
                var product_id = $(this).attr("data-product");
                var data = {
                    product_id: product_id,
                    user_id: user_id
                };
                $.ajax({
                    url: '/ajax/delete_permission/',
                    method: 'POST',
                    data: data,
                    success: function (result) {
                        $(".alert-success").show();
                        location.reload();
                    },
                    error: function (result) {
                        $(".alert-warning").show();
                        location.reload();
                    }
                })
            });

            // Handle add user to project
            $('.btn-add-user').click(function () {
                var product_id = $(this).attr("data-product");
                var list_user_id = [];

                $("#modal-add-user").find("input:checked").each(function () {
                    list_user_id.push($(this).attr("data-user"));
                });
                var data = {
                    product_id: product_id,
                    list_user_id: list_user_id
                };
                $.ajax({
                    url: '/ajax/add-user-project/',
                    method: 'POST',
                    data: data,
                    success: function (result) {
                        $(".alert-success").show();
                        location.reload();
                    },
                    error: function (result) {
                        $(".alert-warning").show();
                        location.reload();
                    }
                })
            })
        });
    </script>

{% endblock content %}
