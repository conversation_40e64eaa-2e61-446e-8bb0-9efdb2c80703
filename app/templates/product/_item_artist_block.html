{% load util %}
{% load static %}
{% load i18n %}

{% with artist.user as user %}
  <div class="project-setting__block-list project-setting__block-list-selected" data-artist="{{artist.pk}}">
    <div class="block-list__user-info-left">
      <div class="user-info__avatar">
        <img src="{{ user|get_avatar:'medium' }}" alt="">
        {% with artist|get_task_in_progress as task_progress %}
          <div class="user-info__avatar-bandage {% if task_progress < 1 %}hide{% endif %}">{{ task_progress }}</div>
        {% endwith %}
      </div>
      <div class="user-info__artist-infor">
        <div class="artist-infor__name">
          <div class="artist-infor__name__content">
            {{ user.get_display_name }}
          </div>
          <a class="user-info__action user-info__delete" href="#">
            <i class="icon icon--sicon-trash"></i>
          </a>
        </div>
        <div class="artist-infor__detail">
          <div class="artist-infor__title">{% if user.position %}{{ user.position }}{% endif %}</div>
          <div class="artist-infor__organization-name">{% if user.enterprise %}{{ user.enterprise }}{% endif %}</div>
        </div>
      </div>
    </div>
    <div class="block-list__user-info-right">
      {#      <div class="block-list__identity-confirmation">#}
      {#        <span class="identity-confirmation__identification">本人確認</span>#}
      {#        <span class="identity-confirmation__NDA">NDA</span>#}
      {#      </div>#}
      <div class="block-list__value_tradeoff_slider">
        <div class="account__tradeoff">
          <div class="account__trade-slider">
            {% for i in '12345' %}
              <div class="account__trade-item {% if i == artist.get_trading_display|stringformat:"i" %}active{% endif %}"
                   data-option="{{ i }}"></div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>
{% endwith %}
