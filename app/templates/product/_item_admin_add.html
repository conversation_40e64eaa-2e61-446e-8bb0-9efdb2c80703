{% load util %}
{% load static %}
{% load i18n %}

{% for user in users %}
  <div class="block-list__results-search-detail add-director__results-search-detail" data-user="{{ user.pk }}" data-role="{{ user.role }}">
    <div class="project-setting__block-list" style="display: block;">
      <div class="block-list__user-info-left">
        <div class="user-info__avatar">
          <img src="{{ user|get_avatar:'medium' }}" alt="">
        </div>
        <div class="user-info__artist-infor">
          <div class="artist-infor__name">{{ user.get_display_name }}</div>
        </div>
      </div>
      <div class="block-list__user-info-right">
      </div>
    </div>
    <div class="block-list__register-action">
      <input type="button"
              class="add-artist-into-list add-admin-into-list"
              value="この管理者を追加" name="add-this-admin" style="margin-top: 12px;">
    </div>
  </div>
{% endfor %}
