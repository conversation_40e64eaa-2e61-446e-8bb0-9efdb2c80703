{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load widget_tweaks %}
{% load i18n %}
{% load compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css" integrity="sha512-aOG0c6nPNzGk+5zjwyJaoRUgCdOrfSDhmMID2u4+OIslr0GjpLKo7Xm0Ao3xmpM4T8AmIouRkqwj1nrdVsLKEQ==" crossorigin="anonymous" />
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
  <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
  {% endcompress %}
  {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
  <script src="{% static 'js/cropper.min.js' %}"></script>
  <script src="{% static 'js/main_cropping.js' %}"></script>
  {% endcompress %}
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top_admin.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/project_offer.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/project_list.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_contract.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
  {% compress css %}
  <style>
    .form_create {
      padding-top: 100px;
    }

    .col-xs-12 input[type=file] {
      position: absolute;
      bottom: 0;
      margin: 0;
      padding: 0;
      font-size: 20px;
      cursor: pointer;
      opacity: 0;
    }

    .no-file {
      font-size: 13px;
    }

    .button-small {
      line-height: unset;
      padding: 5px 15px;
      margin: 5px 10px;
    }

    .button--secondary {
      border: none;
    }

    #id_description {
      height: 250px;
    }
  </style>
  {% endcompress %}
{% endblock %}

{% block content %}

  <main class="owner-top {% if not request|is_pc %} on-mobile{% endif %}">
    <div class="container">
      <div class="new-video-menu">
        <div class="project-list">
          <div class="project-item {% if not request|is_pc %} on-mobile{% endif %}"
               data-project-id="{{ product.pk }}" data-user-role="{{ user.role }}">
            <a href="{% url 'app:top_page' %}?force=true">
              {% include 'top/_product_banner.html' with project=product user=user type_page='top_page_admin' is_pc=request|is_pc show_staff=True %}
            </a>

                  <div class="project-item_content">
                    <div class="project-item__filter">
                      <a href="{% url 'app:product_update' product.pk %}">
                        <div class="project-item__filter-item  button--active">設定
                        </div>
                      </a>
                      <a href="{% url 'app:message_owner'  product.pk %}">
                        <div class="project-item__filter-item">DM</div>
                      </a>
                    </div>
                    <div class="m10 row">
                      <div class="col-sm-offset-3 col-sm-6">
                        <form method="post" enctype="multipart/form-data">
                          {% csrf_token %}
                          {% bootstrap_form form exclude='auto_use_last,allow_url_share,information,acr_host,acr_access_key,acr_access_secret, share_link, owner, client_name' %}
                          <hr>
                          <div class="account__blocklist">
                            <div class="account__sub-title">オーナー</div>
                            <div class="account__form-group">
                              <div class="form-group account__form-label">
                                <select name="owners" id="owners" multiple
                                        style=" width: -webkit-fill-available;" data-product="{{ product.pk }}">
                                  {% for member in product.productuser_set.all %}
                                    <option {% if member.is_owner == '1' %}selected {% endif %}
                                            value="{{ member.user.id }}">{{ member.user.get_display_name }}</option>
                                  {% endfor %}
                                </select>
                              </div>
                            </div>
                            <div class="form-group row">
                              <div class="button button--gradient button--gradient-primary button--round button-small">
                                <div id="choice-owner" style="color: #fff; cursor: pointer;">更新</div>
                              </div>
                            </div>
                          </div>
                          <hr>

                          <div class="form-group select-container select_role sumo-select">
                            <label>エンドクライアント＊</label>
                            {{ form.client_name }}
                            <datalist id="client_name">
                              {% for end_client in list_end_client %}
                                <option>{{ end_client }}</option>
                              {% endfor %}
                            </datalist>
                            <div class="error-message">このフィールドは必須項目です。</div>
                          </div>
                          <hr>
                          <h4>納品形式</h4>
                          <div class="form-group">
                            <div class="checkbox">
                              <label for="id_auto_use_last">
                                {{ form.auto_use_last }} 最終動画ファイルの納品データにする</label>
                            </div>
                          </div>
                          <hr>
                          <h4>利用設定</h4>
                          <div class="form-group">
                            <div class="checkbox">
                              <label for="id_auto_use_last">
                                {{ form.share_link }} URLリンク共有を許可</label>
                            </div>
                          </div>
                          <hr>
                          <h4>ACR</h4>
                          <div class="form-group">
                            {{ form.acr_host.label_tag }}
                            {{ form.acr_host }}
                          </div>
                          <div class="form-group">
                            {{ form.acr_access_key.label_tag }}
                            {{ form.acr_access_key }}
                          </div>
                          <div class="form-group">
                            {{ form.acr_access_secret.label_tag }}
                            {{ form.acr_access_secret }}
                          </div>

                          <hr>
{#                          <div class="project-setting-price">#}
{#                            <div class="form-group">#}
{#                              <label for="id_total_budget">受託金額</label>#}
{#                              <div class="total-price rel">#}
{#                                {{ form.total_budget|add_class:"form-control right-align-input" }}#}
{#                                <span class="price-user__currency" data-current-total-in-active="{{ current_total_in_active }}">円</span>#}
{#                              </div>#}
{#                            </div>#}
{#                            <div class="project-setting-user-list">#}
{#                              {% for admin_pu in product_admins %}#}
{#                                {% with admin_pu.user as admin %}#}
{#                                  <div class="price-user" data-admin-id="{{ admin.pk }}">#}
{#                                    <div class="price-user__avatar background-avt">#}
{#                                      <img class="price-user__avatar-img"#}
{#                                           src="{{ admin|get_avatar:'medium' }}" alt="">#}
{#                                    </div>#}
{#                                    <div class="price-user__info">#}
{#                                      <div class="price-user__name">{{ admin.get_display_name }}</div>#}
{#                                      <div class="price-user__work">{{ admin|get_position }}</div>#}
{#                                      <div class="price-user__company">{{ admin|get_enterprise }}</div>#}
{#                                    </div>#}
{#                                    <div class="price-user__money">#}
{#                                      <div class="price-user__set">#}
{#                        <span class="price-user__set-number rel">#}
{#                          <input class="price-user__input right-align-input w-150" type="number" min="0"#}
{#                                 value="{{ admin_pu|get_budget }}">#}
{#                          <span class="price-user__currency">円</span>#}
{#                        </span>#}
{#                                      </div>#}
{#                                      <div class="price-user__min">#}
{#                        <span class="price-user__min-number pr-5">#}
{#                          {{ admin|get_current_budget_cost:product }}#}
{#                        </span>円#}
{#                                      </div>#}
{#                                    </div>#}
{#                                  </div>#}
{#                                {% endwith %}#}
{#                              {% endfor %}#}
{#                            </div>#}
{#                            <div class="project-setting-price__button">#}
{#                              <div class="button button--icon button--icon-add button--round button-select-admin-modal"></div>#}
{#                              <div class="project-setting-price__button-desc">ディレクターを任命</div>#}
{#                            </div>#}
{#                            <div class="project-setting-price__current">#}
{#                              <div class="project-setting-price__current-label">収支</div>#}
{#                              <div class="project-setting-price__current-value pr-5">#}
{#                                <span class="project-setting-price__current-number" data-total-inactive="{{ current_total_in_active }}">0</span> 円#}
{#                              </div>#}
{#                            </div>#}
{#                          </div>#}
{#                          <div class="modal fade" id="select-admin-modal" role="dialog">#}
{#                            <div class="modal-dialog" role="document">#}
{#                              <div class="modal-content">#}
{#                                <div class="modal-body">#}
{#                                  <div class="select-admin">#}
{#                                    <div class="select-admin__title">ディレクターを任命</div>#}
{#                                    <div class="select-admin__form">#}
{#                                      <input class="select-admin__input form-control" type="search" placeholder="検索">#}
{#                                      <div class="select-admin__list">#}
{#                                        <div class="no-search-result hide"><span>結果がありません。</span></div>#}
{#                                      </div>#}
{#                                      <input class="select-admin__price form-control"#}
{#                                             type="number" min="0" value=""#}
{#                                             placeholder="1000000円" disabled>#}
{#                                    </div>#}
{#                                    <div class="form-row">#}
{#                                      <div style="min-width: 325px;"#}
{#                                           class="button button--gradient button--gradient-primary button--round disabled btn-add-admin"#}
{#                                           role="button">ディレクターを任命#}
{#                                      </div>#}
{#                                    </div>#}
{##}
{#                                    <div class="form-row">#}
{#                                      <div class="button button--text button--text-gray button--round button--background-secondary"#}
{#                                           role="button" data-dismiss="modal">{% trans "cancel" %}#}
{#                                      </div>#}
{#                                    </div>#}
{##}
{#                                  </div>#}
{#                                </div>#}
{#                              </div>#}
{#                            </div>#}
{#                          </div>#}
{##}
{#                          <hr>#}
{#                          <h4>スタッフロール</h4>#}
{#                          <div class="form-group row">#}
{#                            <div class="button button--gradient button--gradient-primary button--round button-small">#}
{#                              <label for="id_information" style="color: #fff; cursor: pointer;">スタッフロールを更新</label>#}
{#                            </div>#}
{#                          </div>#}
{#                          {{ form.information|add_class:'hide' }}#}
{#                          <hr>#}
                          <h4>CSV エクスポート</h4>
                          <div class="row">
                            <div class="col-sm-4 button button--gradient button--gradient-primary button--round
                                        button-small">
                              <div id="export-acr"
                                   export-url="{% url 'app:acr_export_csv' product.pk %}"
                                   update-project-url="{% url 'app:product_update' product.pk %}">
                                楽曲認識照合レポート
                              </div>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-sm-4 button button--gradient button--gradient-primary button--round
                                        button-small">
                              <div id="export-comment" export-url="{% url 'app:export_csv' product.pk %}">
                                <a style="color: #fff;"
                                   href="{% url 'app:export_csv' product.pk %}">コメントリスト</a>
                              </div>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-sm-4 button button--gradient button--gradient-primary button--round
                                        button-small">
                              <div>
                                <a style="color: #fff;"
                                   href="{% url 'app:export_creator_offer_info' product.pk %}">取引情報</a>
                              </div>
                            </div>
                          </div>
                          <br>
                          {% buttons %}
                            <input type="submit" value="OK"
                                   class="col-sm-offset-3 col-sm-6 button button--gradient button--gradient-primary button--round"/>
                          {% endbuttons %}
                        </form>
                        <br>
                        <div class="row" style="margin: 20px">
                          <div class="col-sm-offset-3 col-sm-6 button btn btn-default button--gradient-primary
                                    button--round button--secondary">
                            <a href="{% url 'app:product_delete' product.pk %}" role="button">
                              <div id="delete-item">削除</div>
                            </a>
                          </div>
                        </div>
                        <br>
                      </div>
                    </div>
                  </div>
          </div>
          <div class="project-member-setting-modal modal fade" id="project-member-setting"
               role="dialog" style="display: none;">
            <!-- ajax will append data here -->
          </div>
        </div>
      </div>


      <div class="modal fade" id="modalCrop">
        <div class="modal-dialog" style="transform: translate(0,10%);">
          <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
              <h4 class="modal-title">画像登録</h4>
            </div>
            <div class="modal-body">
              <img src="" id="image" style="max-width: 100%;">
            </div>
            <div class="modal-footer">
              <div class="btn-group pull-left" role="group">
                <button type="button" class="btn btn-default js-zoom-in">
                  <span class="glyphicon glyphicon-zoom-in"></span>
                </button>
                <button type="button" class="btn btn-default js-zoom-out">
                  <span class="glyphicon glyphicon-zoom-out"></span>
                </button>
              </div>
              <button type="button" class="btn btn-primary js-crop-and-upload">登録する</button>
            </div>
          </div>
        </div>
      </div>
    {% include 'top/_modal_setting_setting.html'  with project_list=False%}
    </div>
  </main>
    {% compress js inline %}
  <script>
    let default_thumb = '{% static 'images/messenger-thumb.png' %}';
    let user = {{ user.id }};
  </script>
  <script src="{% static 'js/main.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
{#  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>#}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.2.5/moment-timezone-with-data.min.js" integrity="sha512-OvYMp/zgYHU6ojgDxmtCLsgHBtSrX34Cx/01Tv2bFfbJnlVMbIv5TVVTQ+0wU4GHVDIlHAMMsjFBpTzDnSqIiA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
  {% compress js inline %}
    <script src="{% static 'js/isInViewport.min.js' %}"></script>
  <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
  <script src="{% static 'js/top_page_admin.js' %}"></script>
  <script src="{% static 'js/top_page_member.js' %}"></script>
  <script src="{% static 'js/action_banner.js' %}"></script>
  <script src="{% static 'js/product_admin.js' %}"></script>
    {% endcompress %}
{% endblock content %}
{% block extra_script %}
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
{% endblock %}
