{% load util %}
{% load static %}
{% load i18n %}
{% for topic in topics %}
  {% with user|check_show_action_topic:topic as can_edit %}
    <div class="list-topics__topics-container splide__slide" data-topic="{{ topic.pk }}" data-can-edit="{{ can_edit }}"
         data-url="{% if can_edit and action != 'create_offer' %}{% url 'app:topic_create' %}?topic_id={{topic.pk}}{% else %}{% url 'app:topic_detail'  topic.pk %}{% endif %}">
      <div class="list-topics__content-top" style="background-image: url('{{ topic.get_thumbnail_in_topic }}')">
      </div>

      {% if can_edit and action != 'create_offer' %}
        <div class="gallery__topics-actions">
                <span class="gallery__btn-move" data-toggle="modal" data-target="">
                    <i class="icon icon--sicon-equal"></i>
                </span>

          <span class="gallery__btn-delete" data-toggle="modal" data-target="">
                    <i class="icon icon--sicon-trash"></i>
                </span>
        </div>
      {% endif %}
    </div>
  {% endwith %}
{% endfor %}
