{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}

{% if is_curator or list_work.salecontentlistwork_set.exists %}
  <div class="gallery__new-works__sub-component" data-list-id="{{ list_work.pk }}">
    <div class="gallery__title-new-works__container">
      <div class="gallery__title-new-works">{{ list_work.title }}</div>
      {% if is_curator %}
        <div class="gallery__title-new-works__action">
        <span class="gallery__btn-edit btn-edit-work-theme" data-toggle="modal" data-target="">
          <i class="icon icon--sicon-pencil"></i>
        </span>
          <span class="btn-delete-work-theme" data-toggle="modal" data-target="">
          <i class="icon icon--sicon-trash"></i>
        </span>
        </div>
      {% endif %}
    </div>
    <div class="gallery__sub-title-new-works">{{ list_work.description }}</div>
    <div class="gallery__list-new-works mscrollbar" id="list-new-works">
      {% include 'creator/_item_sale_content.html' with sale_items=list_work.salecontentlistwork_set.all is_curator=is_curator user=user %}
      {% if is_curator %}
        <span class="list-new-works__button-add-work">
          <div class="list-new-works__button-add-work__content">
              <i class="icon icon--sicon-add-cirlce"></i>
              <p>{% trans "Exhibit works" %}</p>
          </div>
      </span>
      {% endif %}
    </div>
  </div>
{% endif %}


