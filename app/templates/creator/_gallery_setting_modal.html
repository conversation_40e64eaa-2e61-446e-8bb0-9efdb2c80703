{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}


<!-- Modal crop thumbnail -->
<div class="modal fade" id="modalCrop" style="z-index: 9999">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">画像登録</h4>
      </div>
      <div class="modal-body" style="max-height: 75vh;">
        <img src="" id="image-crop" style="max-width: 100%;">
      </div>
      <div class="modal-footer">
        <div class="btn-group pull-left" role="group">
          <button type="button" class="btn btn-default js-zoom-in">
            <span class="glyphicon glyphicon-zoom-in"></span>
          </button>
          <button type="button" class="btn btn-default js-zoom-out">
            <span class="glyphicon glyphicon-zoom-out"></span>
          </button>
        </div>
        <button type="button" class="btn btn-primary js-crop-and-upload js-crop-and-upload-project">登録する</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal crop thumbnail -->

<!-- Modal add new work theme -->
<div class="modal popup-container fade" id="modal-add-new-work-theme" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <form action="" class="form-group" id="form-add-work">
          <label class="label_field_new-work" for="title-new-work">
            <span class="new-work_label" style="font-weight: 400;">{% trans "Work theme" %}</span>
            <input class="form-control" placeholder='New works' type='text' name='title-new-work' value='' maxlength="255"/>
          </label>
          <label class="label_field_new-work" for="description-new-work">
            <span class="new-work_label" style="font-weight: 400;">{% trans "description" %}</span>
            <input class="form-control" placeholder='最新の作品をお届け。' type='text' name='description-new-work' value='' maxlength="255"/>
          </label>
        </form>
      </div>
      <div class="popup-footer" style="text-align: right; padding-top: 24px; border-top: 1px solid #f0f0f0;">
        <button type="button" class="btn btn--tertiary" data-dismiss="modal" aria-label="Close">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send disabled">{% trans "yes" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal add new work theme -->

<!-- Modal delete work theme -->
<div class="modal popup-container fade" id="modal-delete-work-theme" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <div class="delete-work-title">
          {% trans "Do you really want to delete this?" %}
        </div>
      </div>
      <div class="popup-footer" style="text-align: right; padding-top: 24px; border-top: 1px solid #f0f0f0;">
        <button type="button" class="btn btn--primary" data-dismiss="modal" aria-label="Close">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--tertiary btn-popup-send" id="submit--form">{% trans "yes" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal delete work theme -->

<!-- Modal delete sub work -->
<div class="modal popup-container fade" id="modal-delete-sub-work" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <div class="delete-work-title">
          {% trans "Do you really want to delete this?" %}
        </div>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--primary" data-dismiss="modal" aria-label="Close">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--tertiary btn-popup-send" id="submit--form">{% trans "yes" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal delete sub-work -->

<!-- Modal add sub work -->
<div class="modal popup-container fade" id="modal-add-sub-work" role="dialog" style="z-index: 9998;">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <div class="sform-group__input-group sform-group__append-before">
          <input class="sform-control sform-control--input sform-control--full" id="new-work__add-work" type="search" placeholder='{% trans "Add a scene" %}' required="required">
          <i class="icon icon--sicon-close search-delete" style="display: none;"></i>
          <label class="sform-group__append" for="new-work__add-work">
            <i class="icon icon--sicon-search"></i>
          </label>
        </div>

        <div class="list-search-work mscrollbar">


        </div>
      </div>
      <div class="popup-footer" style="text-align: right; margin-top: 32px;">
        <button type="button" class="btn btn--tertiary" data-dismiss="modal" aria-label="Close">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send disabled" id="submit--form">OK</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal add sub-work -->


<!-- Modal delete topic -->
<div class="modal popup-container fade" id="modal-delete-topic" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <div class="delete-work-title">
          {% trans "Do you really want to delete this?" %}
        </div>
      </div>
      <div class="popup-footer" style="text-align: right; padding-top: 24px; border-top: 1px solid #f0f0f0;">
        <button type="button" class="btn btn--primary" data-dismiss="modal"
                aria-label="Close">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--tertiary btn-popup-send">{% trans "yes" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal delete topic -->


<!-- Modal get sale content into selection -->
<div class="modal popup-container fade" id="modal-search-sale-content-selection" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <div class="sform-group__input-group sform-group__append-before">
          <input class="sform-control sform-control--input sform-control--full" id="search-sale-content-selection" type="search" placeholder='{% trans "Add a scene" %}' required="required">
          <i class="icon icon--sicon-close search-delete" style="display: none;"></i>
          <label class="sform-group__append" for="search-sale-content-selection">
            <i class="icon icon--sicon-search"></i>
          </label>
        </div>

        <div class="list-search-work mscrollbar">


        </div>
      </div>
      <div class="popup-footer" style="text-align: right; margin-top: 32px;">
        <button type="button" class="btn btn--tertiary" data-dismiss="modal" aria-label="Close">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send disabled">OK</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal get sale content into selection -->


<!-- Modal Setcolor -->
<div class="modal popup-container fade" id="modal-setcolor" role="dialog" style="z-index: 9998;">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <div class="sform-group__input-group form-group">
          <input class="sform-control sform-control--input sform-control--full form-control" id="id-input-color" value="C4C4C4"
            required="required">
        </div>
      </div>
      <div class="popup-footer" style="text-align: right; margin-top: 32px;">
        <button type="button" class="btn btn--tertiary" data-dismiss="modal" aria-label="Close">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary" id="submit--setcolor">OK</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal Setcolor -->

