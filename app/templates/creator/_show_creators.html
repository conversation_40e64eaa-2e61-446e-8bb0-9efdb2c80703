{% load util %}
{% load static %}

{% for creator in temp_list_creators %}
  <div class="user-item can-sort" data-id="{{ creator.id }}" data-creator-id="{{ creator.id }}" data-creator_list-creator='' data-name="{{ creator.user.get_display_name.lower }}">
    <div class="user-item__avatar">
      <img class="user-item__avatar-img" src="{{ creator.user|get_avatar:'medium' }}" alt="">
      <div class="user-item__action">
        <a class="user-item__link" href="
                {% if creator.slug %}{% url 'app:creator_info' creator.slug %}{% else %}{% url 'accounts:accounts_creator' creator.user.pk %}{% endif %}"
           target="_blank">
          <div class="srm-icon srm-launch"></div>
        </a>
        <a class="user-item__delete" href="#">
          <div class="srm-icon srm-delete"></div>
        </a>
      </div>
    </div>
    <div class="user-item__info">
      <a class="user-item__name" href="
              {% if creator.slug %}{% url 'app:creator_info' creator.slug %}{% else %}{% url 'accounts:accounts_creator' creator.user.pk %}{% endif %}"
         target="_blank">{{ creator.user.get_display_name }}</a>
      <div class="user-item__work">{{ creator.user.type }}</div>
    </div>
  </div>
{% endfor %}
