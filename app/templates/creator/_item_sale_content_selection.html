{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}

{% for sale_content in sale_contents %}
  <div class="exhibition-works__component-container" data-id="" data-sale="{{ sale_content.pk }}">
    {% with sale_content|get_artist_of_sale_content as artist %}
      {% for album_variation in sale_content|get_audios:user %}
        {% with album_variation|get_file_type:user as file_type %}
          <div class="exhibition-works__component-content gallery__item" data-type="{{ sale_content.last_published_version.content_type }}"
              data-artist="{{ artist.get_display_name }}" data-thumbnail=""
              style="{{ sale_content|get_thumbnail_sale_content:user }}">
            <div class="list-search__item-playpause {% if file_type != 'audio' %}btn-preview-album{% endif%}"></div>
            <audio
                    preload="none"
                    class="gallery__item-banner"
                    src="{{ album_variation|get_audio:user }}"
                    data-name="{{ sale_content|get_title_sale_content:user }}"
                    data-album="{{ album_variation.pk }}"
                    data-file-type="{{ file_type }}"></audio>
          </div>
        {% endwith %}
      {% endfor %}
    {% endwith %}
    <div class="exhibition-works__component-action">
      <div class="exhibition-works__component-action-setcolor">
          <div class="avatar-color" style="background: #C4C4C4"></div>
          <div class="caption--11 value-color">C4C4C4</div>
      </div>
      <div class="exhibition-works__component-action-container">
                        <span class="button-move_exhibition-works">
                          <i class="icon icon--sicon-equal"></i>
                        </span>
        <span class="button-delete_exhibition-works">
                          <i class="icon icon--sicon-trash"></i>
                        </span>
      </div>
    </div>
  </div>
{% endfor %}
