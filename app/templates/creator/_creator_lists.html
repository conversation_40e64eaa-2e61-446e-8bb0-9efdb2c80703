{% load util %}
{% load static %}
{% load i18n %}

<div class="gallery-list {% if is_curator %}is_curator{% endif %}">
  {% include 'creator/_creator_list.html' with  creator_lists=creator_lists is_curator=is_curator %}
</div>
{% if is_curator %}
  <div class="modal fade" id="composer-new-modal" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <div class="composer-new">
            <div class="composer-new__title">新しいテーマを追加</div>
            <div class="composer-new__form">
              <div class="form-group">
                <label for="composer-name">テーマ</label>
                <input class="form-control form-control" type="text" placeholder="Popular Artists"
                       id="composer-name"/>
              </div>
              <div class="form-group">
                <label for="composer-desc">説明</label>
                <input class="form-control form-control" type="text" placeholder="Curated by SOREMO"
                       id="composer-desc"/>
              </div>
            </div>
            <div class="composer-modal__action">
              <a class="btn btn--tertiary button button--text button--text-gray btn-cancel-composer" href="#" role="button"
                 data-dismiss="modal">{% trans "cancel" %}</a>
              <a class="btn btn--primary button button--gradient button--gradient-primary button--round disabled btn-new-composer"
                 href="#" role="button">追加</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="composer-modal" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <div class="composer-modal">
            <div class="composer-modal__title">アーティストを追加</div>
            <div class="composer-modal__form form-group">
              <input class="composer-modal__input composer-modal_search form-control" type="search" placeholder="Search">
              <div class="">
                <div class="composer-modal__list"></div>
                <div class="composer-modal__not-found">適当なアーティストがいません。</div>
                <div class="composer-modal__existed">このアーティストがすでに追加いたしました。</div>

                <div class="composer-modal__action">
                  <a class="btn btn--tertiary button button--text button--text-gray btn-cancel-composer" href="#" role="button"
                     data-dismiss="modal">{% trans "cancel" %}</a>
                  <a class="btn btn--primary button button--gradient button--gradient-primary button--round disabled btn-add-composer"
                     href="#" role="button">追加</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="composer-update-modal" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <div class="composer-new">
            <div class="composer-new__title">テーマを更新</div>
            <div class="composer-new__form">
              <div class="form-group">
                <label for="composer-name">テーマ</label>
                <input class="form-control form-control" type="text" placeholder="Popular Artists"
                       id="composer-update-name"/>
              </div>
              <div class="form-group">
                <label for="composer-desc">説明</label>
                <input class="form-control form-control" type="text" placeholder="Curated by SOREMO"
                       id="composer-update-desc"/>
              </div>
            </div>
            <div class="composer-modal__action">
              <a class="btn btn--tertiary button button--text button--text-gray btn-cancel-composer" href="#" role="button"
                 data-dismiss="modal">{% trans "cancel" %}</a>
              <a class="btn btn--primary button button--gradient button--gradient-primary button--round btn-new-composer"
                 href="#" role="button">更新</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endif %}
