{% extends "base_nofooter_refactor.html" %}
{% load static %}
{% load util %}

{% block title %}
  <title>REVIEW | SOREMO</title>
  <!-- material Icon -->
  <!-- css読み込み -->
  <link rel="stylesheet" type="text/css" href="{% static 'css/__test.css' %}" />
</head>
{% endblock %}

{% block extrahead %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/__test.css' %}" />
{% endblock %}

{% block content %}
  <main class="u-col-center u-justify-start u-h100 u-gap8">
    <div class="u-wrapper-reading u-h100 u-relative">
      <div class="c-fab u-gap8" id="fab-review">
        <span class="material-symbols-rounded">
          rate_review
        </span>
        レビューを書く
      </div>
      <div class="c-message-system heading-18-spacing u-w100 u-mt8 u-mb8">検収完了</div>
      <div class="c-message-container-their">
        <div class="c-avatar40"></div>
        <div class="c-message-their bodytext-13">ここに相手側のメッセージが入ります。</div>
      </div>
      <div class="c-message-container-ours">
        <div class="c-message-ours bodytext-13">ここに自分側のメッセージが入ります。</div>
      </div>
    </div>
  </main>

  <dialog id="review-dialog">
    <section id="review-section-1" class="u-col u-gap16 u-w100">
      <p class="u-w100">レビューを送信しましょう。<br>
        この内容は、{相手の名前}さんだけに共有されます。
      </p>
      <div class="u-w100 u-row u-gap16">
        <label class="c-radio-icon u-col-center u-gap4">
          <input type="radio" name="rating" value="good" checked>
          <span class="material-symbols-rounded u-fontsize-32">thumb_up</span>
          <a class="bodytext-11">良かった</a>
        </label>
        <label class="c-radio-icon u-col-center u-gap4">
          <input type="radio" name="rating" value="bad">
          <span class="material-symbols-rounded u-fontsize-32">thumb_down</span>
          <a class="bodytext-11">残念だった</a>
        </label>
      </div>
      <textarea id="review-text" rows="3" placeholder="演奏の細部に至るまでのこだわりが感じられ、特に微妙なニュアンスやダイナミクスの変化が素晴らしい。"></textarea>
      <p>✨たたきはこちら
        <a class="c-keyword">プロフェッショナリズム</a>
        <a class="c-keyword">技術</a>
        <a class="c-keyword">創造性</a>
        <a class="c-keyword">オリジナリティ</a>
        <a class="c-keyword">新規性</a>
        <a class="c-keyword">安定性</a>
        <a class="c-keyword">柔軟さ</a>
        <a class="c-keyword">モチベーション</a>
        <a class="c-keyword">コミュニケーション</a>
        <a class="c-keyword">チームワーク</a>
        <a class="c-keyword">暗黙知</a>
        <a class="c-keyword">目的の理解</a>
        <a class="c-keyword">提案力</a>
        <a class="c-keyword">納期遵守</a>
      </p>

      <div class="u-row-btn u-gap8 u-w100">
        <button class="c-btn-tertiary" id="prevBtn1">キャンセル</button>
        <button class="c-btn-primary" id="nextBtn1" disabled>次へ</button>
      </div>
    </section>

    <section id="review-section-2" class="u-col u-gap24 u-w100 is-hidden">
      <p class="u-w100">もし{相手の名前}さんがレビューを気に入ったら、プロフィールサイトに推薦文として使わせていただけますか？</p>
      <div class="c-message-ours">
        <div class="c-quote u-w100 u-relative">
          <span class="material-symbols-rounded c-icon-quote">format_quote</span>
          <div class="u-row u-items-start">
            <p id="review-text-quote" class="bodytext-quote u-text-justify u-overflow-y-auto">
              ここにレビューの内容が入ります。</p>
          </div>
          <div class="u-row-end u-gap16 u-mt8">
            <p class="heading-16 c-quote-line">{自分の名前}</p><span class="bodytext-11">{自分の肩書き}</span>
          </div>
        </div>
      </div>

      <div class="u-row u-w100 c-segment-control">
        <label><input type="radio" name="review-option" value="ok" checked>OK</label>
        <label><input type="radio" name="review-option" value="anonymous">匿名ならOK</label>
        <label><input type="radio" name="review-option" value="not-ok">許可しない</label>
      </div>
      <div class="u-row-btn u-gap8 u-w100">
        <button class="c-btn-tertiary" id="prevBtn2">戻る</button>
        <button class="c-btn-primary" id="nextBtn2">送信</button>
      </div>
    </section>
  </dialog>
{% endblock %}

{% block extra_script %}
  <script>
    // fab-reviewをクリックしたときにレビューダイアログを表示
    const fabReview = document.getElementById('fab-review');
    const dialog = document.getElementById('review-dialog');

    fabReview.addEventListener('click', () => {
      dialog.showModal();
      fabReview.style.display = 'none';
    });

    // レビューのラジオボタンをクリックしたときに、すでにチェックされている場合はチェックを外す
    let lastCheckedRadio = null;

    document.querySelectorAll('input[type="radio"]').forEach(radio => {
      radio.addEventListener('click', function (event) {
        if (lastCheckedRadio === this) {
          this.checked = false;
          lastCheckedRadio = null;
        } else {
          lastCheckedRadio = this;
        }
      });
    });

    // テキストエリアの高さ自動調整
    let textarea = document.querySelector("textarea");
    textarea.addEventListener("input", (e) => {
      resizeElement(e.target, 576);
    });

    // keywordをクリックしたら テキストエリアに雛形を追加
    document.querySelectorAll('.c-keyword').forEach(keyword => {
      keyword.addEventListener('click', function (e) {
        const selectedKeyword = e.target.textContent;
        let newText;
        do {
          newText = draft[selectedKeyword][Math.floor(Math.random() * draft[selectedKeyword].length)];
        } while (newText === document.getElementById('review-text').value); // 同じテキストが選ばれた場合は再選択
        document.getElementById('review-text').value = newText;
        nextBtn1.disabled = reviewText.value.trim() === '';
        reviewTextQuote.textContent = reviewText.value;
      });
    });

    // テキストエリアの入力を監視
    const reviewText = document.getElementById('review-text');
    const nextBtn1 = document.getElementById('nextBtn1');

    reviewText.addEventListener('input', () => {
      // テキストエリアが空でないかつ1500文字以下の場合、ボタンを有効化
      nextBtn1.disabled = reviewText.value.trim() === '' || reviewText.value.length > 1500;

      // 1500文字を超えた場合にtextareaの下にメッセージを表示
      const messageElement = document.getElementById('length-warning'); // メッセージ要素を取得
      const remainingChars = reviewText.value.length - 1500; // 残りの文字数を計算
      if (reviewText.value.length > 1500) {
        if (!messageElement) { // メッセージがまだ表示されていない場合
          document.getElementById('review-text').insertAdjacentHTML('afterend',
            '<div id="length-warning" class="bodytext u-w100 u-row-center u-gap4 c-group u-bg-green u-text-white"><span class="material-symbols-rounded u-text-white">warning</span>' + remainingChars + '文字くらい減らしてみませんか</div>');
        } else {
          messageElement.innerHTML = '<span class="material-symbols-rounded u-text-white">warning</span>' + remainingChars + '文字くらい減らしてみませんか'; // メッセージを更新
        }
      } else {
        if (messageElement) { // メッセージが表示されている場合
          messageElement.remove(); // メッセージを非表示にする
        }
      }
    });


    // テキストエリアの情報を取得して、引用部分に表示
    const reviewTextQuote = document.getElementById('review-text-quote');

    reviewText.addEventListener('input', () => {
      reviewTextQuote.textContent = reviewText.value;

    });




    // 引用部分の表示切り替え
    document.querySelectorAll('input[name="review-option"]').forEach(radio => {
      radio.addEventListener('change', function () {
        const cQuote = document.querySelector('.c-quote');
        const cIconQuote = document.querySelector('.c-icon-quote');
        const lastChild = cQuote.lastElementChild;

        if (this.value === 'ok') {
          cQuote.style.display = 'block';
          cIconQuote.style.display = 'block';
          lastChild.style.display = 'flex';
        } else if (this.value === 'anonymous') {
          cQuote.style.display = 'block';
          cIconQuote.style.display = 'block';
          lastChild.style.display = 'none';
        } else if (this.value === 'not-ok') {
          cQuote.style.display = 'block';
          cIconQuote.style.display = 'none';
          lastChild.style.display = 'none';
        }
      });
    });

    // レビューダイアログのボタンをクリックしたときにレビューダイアログを閉じる
    const prevBtn1 = document.getElementById('prevBtn1');
    const prevBtn2 = document.getElementById('prevBtn2');
    const nextBtn2 = document.getElementById('nextBtn2');

    prevBtn1.addEventListener('click', () => {
      dialog.close();

      // fab - reviewアイコンを消す
      const fabReview = document.getElementById('fab-review');
      fabReview.style.display = 'flex';
    });

    prevBtn2.addEventListener('click', () => {
      document.getElementById('review-section-1').style.display = 'flex';
      document.getElementById('review-section-2').style.display = 'none';
    });

    nextBtn1.addEventListener('click', () => {
      document.getElementById('review-section-1').style.display = 'none';
      document.getElementById('review-section-2').style.display = 'flex';
      // 引用部分の高さを最適化
      resizeElement(reviewTextQuote, 464);
    });

    nextBtn2.addEventListener('click', () => {
      dialog.close();

      // fab - reviewアイコンを消す
      const fabReview = document.getElementById('fab-review');
      fabReview.style.display = 'none';

      //ここにレビューを送信する処理を入れる

      // メッセンジャーへの反映
      // ユーザーの入力を取得
      const ratingInput = document.querySelector('input[name="rating"]:checked');
      const rating = ratingInput ? ratingInput.value : null; // nullチェックを追加

      const reviewText = document.getElementById('review-text').value;
      const reviewOption = document.querySelector('input[name="review-option"]:checked').value;

      // mainのメッセンジャーに反映
      const wrapper = document.querySelector('main .u-wrapper-reading');

      // 評価アイコンの選択
      let iconHtml = '';
      if (rating === 'good') {
        iconHtml = '<div class="material-symbols-rounded u-fontsize-16 u-text-blue u-pb8 u-w100 u-text-right">thumb_up</div>';
      } else if (rating === 'bad') {
        iconHtml = '<div class="material-symbols-rounded u-fontsize-16 u-text-gray u-pb8 u-w100 u-text-right">thumb_down</div>';
      } else {
        //未評価
      }

      // HTMLを追加
      wrapper.insertAdjacentHTML('beforeend', `
        <div class="c-message-container-ours">
          <div class="u-col">
            <ul class="u-row-between u-w100">
              <li class="bodytext-11">{相手の名前}さんへのレビュー</li>
            </ul>
            <div class="c-message-ours bodytext-13">
              ${iconHtml}
              <div class="c-quote u-w100 u-relative">
                <span class="material-symbols-rounded c-icon-quote">format_quote</span>
                <div class="u-row u-items-start">
                  <p id="review-text-quote" class="bodytext-quote u-text-justify">
                    ${reviewText}</p>
                </div>
                <div class="u-row-end u-gap16 u-mt8" id="reviewer">
                  <p class="heading-16 c-quote-line">{自分の名前}</p><span class="bodytext-11">{自分の肩書き}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      `);


      // review-optionに応じた表示
      const cQuote = document.querySelector('.c-quote');
      const reviewer = document.getElementById('reviewer');
      const cIconQuote = document.querySelector('.c-icon-quote');

      if (reviewOption === 'ok') {
        cIconQuote.style.display = 'block'; // c-icon-quoteも表示
        cQuote.style.display = 'block'; // 全て表示
        reviewer.style.display = 'flex'; // reviewerも表示
      } else if (reviewOption === 'anonymous') {
        cIconQuote.style.display = 'block'; // c-icon-quoteは表示
        cQuote.style.display = 'block'; // c-quoteは表示
        reviewer.style.display = 'none'; // reviewerを非表示
      } else {
        cIconQuote.style.display = 'none'; // c-icon-quoteを非表示
        cQuote.style.display = 'block'; // c-quoteは表示
        reviewer.style.display = 'none'; // reviewerは非表示
      }
    }
    );

    const draft = {
      'プロフェッショナリズム': [
        '迅速かつ的確な対応に感謝します。いつも高いプロフェッショナリズムを発揮しており、信頼できるパートナーです。',
        '常に高品質な仕事を提供してくれます。プロジェクトの成功に欠かせない存在です。',
        'プロフェッショナルな対応が際立っています。期待を超える成果をいつもありがとうございます。',
        '期待を超える成果をいつもありがとうございます。プロジェクトがスムーズに進行しました。',
        'プロジェクトがスムーズに進行しました。迅速でプロフェッショナルな対応に感謝します。'
      ],
      '創造性': [
        '独創的なアイデアに驚かされました。常に新鮮でクリエイティブな提案をいただきます。',
        '常に新鮮でクリエイティブな提案をいただきます。創造力豊かなアプローチが素晴らしいです。',
        '創造力豊かなアプローチが素晴らしいです。斬新な視点でプロジェクトを進めてくれます。',
        '斬新な視点でプロジェクトを進めてくれます。予想以上のクリエイティブな成果に感謝します。',
        '予想以上のクリエイティブな成果に感謝します。独自の視点と技術により、プロジェクトが一層引き立ちました。'
      ],
      'オリジナリティ': [
        '独自性あふれる仕事に感動しました。他にはないオリジナルなアイデアを提供してくれます。',
        '他にはないオリジナルなアイデアを提供してくれます。唯一無二のアプローチが魅力的です。',
        '唯一無二のアプローチが魅力的です。常にオリジナルな視点での提案が素晴らしいです。',
        '常にオリジナルな視点での提案が素晴らしいです。個性的で独特な作品に感謝します。',
        '個性的で独特な作品に感謝します。独自のスタイルがプロジェクトに新しい風を吹き込みました。'
      ],
      '新規性': [
        '斬新なアイデアが光ります。新しい視点でプロジェクトを進めてくれました。',
        '新しい視点でプロジェクトを進めてくれました。革新的な提案に感謝します。',
        '革新的な提案に感謝します。常に最新のアプローチを提供してくれます。',
        '常に最新のアプローチを提供してくれます。新しい風を吹き込んでくれました。',
        '新しい風を吹き込んでくれました。プロジェクトに新しい息吹を与えてくれます。'
      ],
      '納期遵守': [
        'いつも納期を守ってくれます。迅速な納品に感謝します。',
        '迅速な納品に感謝します。時間厳守で信頼できます。',
        '時間厳守で信頼できます。納期内に高品質な成果を提供してくれます。',
        '納期内に高品質な成果を提供してくれます。スケジュール通りに進行してくれました。',
        'スケジュール通りに進行してくれました。信頼できるパートナーです。'
      ],
      '技術': [
        '卓越した技術力に感謝します。プロジェクトの成功に不可欠な存在です。',
        '高い技術力を持ち、常に最先端の方法を取り入れています。頼りになるパートナーです。',
        '技術力の高さに驚かされました。問題解決のプロフェッショナルです。',
        'プロフェッショナルな技術力を提供してくれます。いつも期待以上の成果を上げてくれます。',
        '技術的な課題に迅速に対応してくれます。安心してプロジェクトを任せられます。'
      ],
      '安定性': [
        '常に安定した成果を提供してくれます。信頼性の高いパートナーです。',
        '安定したクオリティに感謝します。期待を裏切らない仕事ぶりです。',
        'プロジェクトの進行がスムーズで、安定したパフォーマンスに感謝します。',
        '安定した成果を提供してくれます。信頼できる存在です。',
        '常に安定した成果を提供し、プロジェクトの成功に貢献してくれます。'
      ],
      '柔軟さ': [
        '柔軟な対応に感謝します。状況に応じた適応力が素晴らしいです。',
        'プロジェクトの変化に柔軟に対応してくれます。頼りになるパートナーです。',
        '柔軟なアプローチで問題を解決してくれます。信頼性の高いパートナーです。',
        '状況に応じた柔軟な対応に感謝します。迅速な対応力が素晴らしいです。',
        '柔軟な対応でプロジェクトを進めてくれました。頼りにしています。'
      ],
      'モチベーション': [
        '常に高いモチベーションを持って取り組んでくれます。プロジェクトの成功に大きく貢献しています。',
        'モチベーションが高く、エネルギッシュに仕事を進めてくれます。信頼できるパートナーです。',
        '高いモチベーションでプロジェクトに取り組んでくれます。成果に満足しています。',
        'モチベーションが高く、積極的にプロジェクトを推進してくれます。信頼性の高いパートナーです。',
        '常に高いモチベーションで仕事を進めてくれます。プロジェクトの成功に貢献しています。'
      ],
      'コミュニケーション': [
        '優れたコミュニケーション能力でプロジェクトを円滑に進めてくれます。',
        'コミュニケーションがスムーズで、迅速な対応に感謝します。',
        '明確で効果的なコミュニケーションがプロジェクトを成功に導いてくれました。',
        '優れたコミュニケーションスキルで問題を迅速に解決してくれます。',
        'コミュニケーションが円滑で、常に迅速な対応をしてくれます。'
      ],
      'チームワーク': [
        'チームワークが素晴らしく、プロジェクトがスムーズに進行しました。',
        'チーム全体が協力し合い、目標を達成することができました。素晴らしい仕事でした。',
        '協力的な姿勢でチームワークを発揮し、プロジェクトを成功に導いてくれました。',
        'チームメンバーとのコミュニケーションが円滑で、成果を最大化できました。',
        'チームワークが抜群で、プロジェクトが期待以上に進行しました。感謝しています。'
      ],
      '暗黙知': [
        '深い理解と暗黙知の共有がプロジェクトの成功に寄与しました。',
        '経験に基づく暗黙知がプロジェクトの進行に役立ちました。',
        '暗黙知を活かした対応が素晴らしいです。信頼性の高いパートナーです。',
        '豊富な経験と暗黙知の共有でプロジェクトがスムーズに進行しました。',
        '暗黙知を活用した迅速な対応に感謝します。'
      ],
      '目的の理解': [
        'プロジェクトの目的を深く理解し、それに沿った提案をしてくれます。',
        '目的をしっかり理解し、それに基づいた適切な対応に感謝します。',
        'プロジェクトの目標を理解し、それに向けて最善を尽くしてくれます。',
        '目的を共有し、それに基づいた対応がプロジェクトの成功に貢献しました。',
        'プロジェクトの目的を的確に理解し、それに応じた対応に感謝します。'
      ],
      '提案力': [
        '常に的確で実現可能な提案をしてくれます。プロジェクトの進行に大いに役立ちました。',
        '効果的な提案がプロジェクトの成功に寄与しました。ありがとうございます。',
        '優れた提案力でプロジェクトを進めてくれます。信頼できるパートナーです。',
        '提案力が高く、プロジェクトの進行をスムーズにしてくれます。感謝しています。',
        '具体的で実行可能な提案がプロジェクトの成功に大きく貢献しました。'
      ]
    };


    // aタグのクリックイベントをキャプチャし、テキストエリアの内容を取得してDjangoビューに送信
    // document.getElementById('sendReview').addEventListener('click', function () {
    //       let reviewText = document.getElementById('review-text').value;

    //       fetch('/process_review/', {
    //         method: 'POST',
    //         headers: {
    //           'Content-Type': 'application/json',
    //           'X-CSRFToken': '{{ csrf_token }}'
    //         },
    //         body: JSON.stringify({ 'review_text': reviewText })
    //       })
    //         .then(response => response.json())
    //         .then(data => {
    //           let resultContainer = document.getElementById('resultContainer');
    //           let result = document.createElement('div');
    //           result.textContent = data.result;
    //           resultContainer.appendChild(result);
    //         });
    //     }); 
  </script>

  <!-- js読み込み -->
  <script type="text/javascript" src="{% static 'js/__test.js' %}"></script>
{% endblock %}
