{% load static %}

<!DOCTYPE html>
<html lang="jp">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Gate Keeper | SOREMO</title>
    {% include '_gtag_head.html' %}
    <!-- googleフォント -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=M+PLUS+1p:wght@100;300;400&family=Zen+Maru+Gothic:wght@300;900&display=swap"
        rel="stylesheet">
    <!-- material Icon -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <!-- adobe font -->
    <script>
        let csrf = '{% csrf_token %}';
        let sign = "{{ sign }}";
        (function (d) {
            var config = {
                kitId: 'wqn4pyt',
                scriptTimeout: 3000,
                async: true
            },
                h = d.documentElement, t = setTimeout(function () { h.className = h.className.replace(/\bwf-loading\b/g, "") + " wf-inactive"; }, config.scriptTimeout), tk = d.createElement("script"), f = false, s = d.getElementsByTagName("script")[0], a; h.className += " wf-loading"; tk.src = 'https://use.typekit.net/' + config.kitId + '.js'; tk.async = true; tk.onload = tk.onreadystatechange = function () { a = this.readyState; if (f || a && a != "complete" && a != "loaded") return; f = true; clearTimeout(t); try { Typekit.load(config) } catch (e) { } }; s.parentNode.insertBefore(tk, s)
        })(document);
    </script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}"; </script>
    <!-- css読み込み -->
    <link rel="stylesheet" type="text/css" href="{% static 'css/mgk.css' %}" />
    <!-- js読み込み -->
    <script type="text/javascript" src="{% static 'js/mgk.js' %}" defer></script>

</head>

<body id="bg">
    {% include '_gtag_body.html' %}
    <nav>
        <h1>MUSIC GATE KEEPER</h1>
        <p>類似曲著作権リサーチ</p>
    </nav>
    <main>
        <section id="step1">
            <article class="catch">
                <h2 class="catchphrase">どんな名作も、<wbr>誰かのインスパイアで出来ている</h2>
                <p class="description">
                    あなたの作品が既存の楽曲と類似していないかを<wbr>調査しましょう。<br>私たちは、オリジナリティを保ち、<wbr>名作を誤って損なわないためのサービスを提供しています。</p>
                <img src="{% static 'images/mgk.jpg' %}" alt="Music Gate Keeper">

            </article>
            <article class="movie">
                <video src="{% static 'images/mgk_teaser.mp4' %}" controls controlsList="nodownload" autoplay loop
                    muted></video>
            </article>
            <article class="u-wrapper">
                <div class="u-wrapper-reading"
                    style="padding: 0 8px; margin-bottom: 128px; text-align: justify; text-justify: inter-ideograph;">
                    <p>
                        オリジナルの映画やゲーム作品において、音楽は作品の魅力を引き立てる非常に重要な要素です。しかし、意図せず既存の作品と類似してしまったり、それを連想させてしまう場合、作品の独自性や価値が損なわれるリスクがあります。このような問題を未然に防ぐことは、制作ディレクターやコンポーザーにとって大きな課題です。無意識のうちに既存の作品から影響を受けることがあり、それに気づきにくいケースも多々あります。
                    </p>
                    <p>
                        そこで、私たちは「MGK(MusicGateKeeper)サービス」をご提案します。専用のWEBページに音源をアップロードするだけで、類似した曲を簡単にリサーチできます。既存の原盤や類似メロディ、さらに市場に出ていないカスタムコンテンツとも照合し、類似率、利用範囲、利用時間といった詳細なレポートをご提供します。
                    </p>
                </div>
                <img src="{% static 'images/mgk_report_sample.png' %}" loading="lazy" alt="レポートサンプル">
            </article>
            <article class="workflow">
                <h3 class="u-border-top">WORKFLOW</h3>
                <div class="workflow-flex-container">
                    <div class="workflow-flex-item">
                        <div class="workflow-title-wrapper">
                            <div class="counter">1</div>
                            <h4>あなたの作品をアップロード</h4>
                        </div>
                        <img src="{% static 'images/mgk_workflow1.png' %}" loading="lazy" alt="あなたの作品をアップロード">
                        <p>専用アカウントでサインインして、作品をアップロードしましょう。ドラッグ&ドロップするだけで、分析をスタートできます。オーディオファイルとビデオファイルに対応しています。</p>
                    </div>
                    <div class="workflow-flex-item">
                        <div class="workflow-title-wrapper">
                            <div class="counter">2</div>
                            <h4>あなたの作品を分析</h4>
                        </div>
                        <img src="{% static 'images/mgk_workflow2.png' %}" loading="lazy" alt="あなたの作品を分析">

                        <p>楽曲の照合には数分かかります。既存の楽曲と何らかの一致性がある場合は、指紋アイコンが青にかわります。</p>
                        <a>※アイコンが消えたら、一致する楽曲がないことを意味しています。</a>
                    </div>
                    <div class="workflow-flex-item">
                        <div class="workflow-title-wrapper">
                            <div class="counter">3</div>
                            <h4>類似した曲を検出</h4>
                        </div>
                        <img src="{% static 'images/mgk_workflow3.png' %}" loading="lazy" alt="類似した曲を検出">
                        <p>指紋アイコンをクリックして、結果を確認しましょう。もし原盤が利用されていると、照合率（％）と利用範囲、利用時間が確認できます。メロディが似ているかどうか、複製（カバーソング）の候補もチェックしてみてください。
                        </p>
                    </div>
                </div>
            </article>
            <article class="pamphlet">
                <h3 class="u-border-top">PAMPHLET</h3>
                <a href="{% static 'pdf/MusicGateKeeper.pdf' %}" download="MusicGateKeeper.pdf"
                    class="c-btn-download">説明資料をダウンロード</a>
            </article>
            <aside class="widget">
                <div class="logo"></div>
                <div class="legal">
                    <a>Copyright SOREMO Co.,ltd. All rights reserved.</a>
                    <a href="https://soremo.jp/termsofuser">利用規約</a>
                    <a href="https://soremo.jp/privacypolicy">プライバシーポリシー</a>
                </div>
            </aside>
        </section>
        <section id="step2">
            <form action="submit-path" method="post" class="u-mb-footer">
                <h3 class="u-border-top u-wrapper">PLAN</h3>
                <section class="plans">
                    <label for="plan1">
                        <input type="radio" id="plan1" name="plan" value="plan1" style="display:none">
                        <div class="p-plan" data-tilt data-tilt-max="1" data-tilt-scale="1.015" data-tilt-glare
                            data-tilt-max-glare="1.0">
                            <h3>STANDARD</h3>
                            <h4 class="u-mb16">ツールのみ</h4>
                            <ul>
                                <li><span class="material-symbols-rounded">person</span>5アカウント（ID、パスワード）</li>
                                <li><span class="material-symbols-rounded">storage</span>御社専用クラウドサーバー</li>
                                <li><span class="material-symbols-rounded">calendar_clock</span>10ファイル（時間上限10分）/日</li>
                            </ul>
                            <div class="price">
                                110,000<span class="bodytext">円（税込）/月</span>
                            </div>
                        </div>
                    </label>
                    <label for="plan2">
                        <input type="radio" id="plan2" name="plan" value="plan2" style="display:none">
                        <div class="p-plan" data-tilt data-tilt-max="1" data-tilt-scale="1.015" data-tilt-glare
                            data-tilt-max-glare="1.0">
                            <h3>PRO</h3>
                            <h4 class="u-mb16">ツール & ヒューマンリサーチ</h4>
                            <ul>
                                <li><span class="material-symbols-rounded">person</span>5アカウント（ID、パスワード）</li>
                                <li><span class="material-symbols-rounded">storage</span>御社専用クラウドサーバー</li>
                                <li><span class="material-symbols-rounded">calendar_clock</span>30ファイル（時間上限30分）/日
                                </li>
                            </ul>
                            <div class="price"><span class="bodytext">個別見積</span></div>
                        </div>
                    </label>
                </section>
                <section class="options">
                    <h3 class="u-border-top" style="display:none">OPTION</h3>
                    <label class="u-wrapper-reading" style="display:none">
                        <input type="checkbox" name="option" id="option1" value="option1" style="display:none">
                        <div class="p-option" data-tilt data-tilt-max="1" data-tilt-glare data-tilt-max-glare="1.0"
                            data-tilt-scale="1.025">
                            <div class="p-option-image1"></div>
                            <h4>カスタムコンテンツ</h4>
                            <p>お客様だけのデータベースを構築し、まだリリースされていない未発表音源などのツール内照合を可能にします。</p>
                            <a>55,000円（税込）/月</a>
                        </div>
                    </label>
                    <label class="u-wrapper-reading" style="display:none">
                        <input type="checkbox" name="option" id="option2" value="option2" style="display:none">
                        <div class="p-option" data-tilt data-tilt-max="1" data-tilt-glare data-tilt-max-glare="1.0"
                            data-tilt-scale="1.025">
                            <div class="p-option-image2"></div>
                            <h4>ヒューマンリサーチ</h4>
                            <p>楽曲知識に精通したスペシャリストによる複眼的リサーチ。シリーズ作品、似た世界観、競合コンテンツなど、影響をうけうる元ネタを探ります。
                            </p>
                            <a>個別見積（目安: 0.25人月 x 3名）</a>
                        </div>
                    </label>
                    <label class="u-wrapper-reading" style="display:none">
                        <input type="checkbox" name="option" id="option3" value="option3" style="display:none">
                        <div class="p-option" data-tilt data-tilt-max="1" data-tilt-glare data-tilt-max-glare="1.0"
                            data-tilt-scale="1.025">
                            <div class="p-option-image3"></div>
                            <h4>詳細レポート</h4>
                            <p>期間内にアップロード頂いた全ファイルの詳細情報をリスト化。懸念事項を総評にまとめ、レポートとして提出します。</p>
                            <a>個別見積（目安: 0.5人月）</a>

                        </div>
                    </label>
                </section>
            </form>
        </section>

        <section id="step3" class="u-wrapper u-mb-footer">
            <h3 class=" u-border-top">CUSTOMER INFOMATION</h3>
            <form class="u-wrapper-reading">
                <div class="u-row8">
                    <label>お名前</label><span class="label-8 u-text-blue">必須</span>
                </div>
                <input type="text" id="fullname" name="fullname" placeholder="岡部 健一">
                <div class="u-row8 u-mt24">
                    <label>メールアドレス</label><span class="label-8 u-text-blue">必須</span>
                </div>
                <input type="email" id="email" name="email" placeholder="<EMAIL>">

                <div class="u-row8 u-mt8">
                    <label>メールアドレス（確認）</label><span class="label-8 u-text-blue">必須</span>
                </div>
                <input type="email" id="email_confirm" name="email_confirm"
                    placeholder="<EMAIL>（再入力）">

                <div class="u-row8 u-mt24">
                    <label>ポジション</label><span class="label-8 u-text-light-gray">任意</span>
                </div>
                <input type="text" id="position" name="position" placeholder="コンプライアンスマネージャー">

                <div class="u-row8 u-mt24">
                    <label>会社名</label><span class="label-8 u-text-light-gray">任意</span>
                </div>
                <input type="text" id="company" name="company" placeholder="ネオストリームエンターテインメント株式会社">

                <div class="u-row8 u-mt24">
                    <label>電話番号（日中連絡がとれる番号）</label><span class="label-8 u-text-light-gray">任意</span>
                </div>
                <input type="tel" id="phone" name="phone" placeholder="03-6457-1780" pattern="\d{2,4}-\d{2,4}-\d{4}">

                <div class="u-row8 u-mt24">
                    <label>ご希望の連絡方法</label>
                </div>
                <div class="c-segment-control">
                    <label class="bodytext">
                        <input type="radio" name="contact" value="email" style="display:none" checked>
                        メール</label>
                    <label class="bodytext">
                        <input type="radio" name="contact" value="phone" style="display:none">
                        電話</label>
                </div>

                <div class="u-row8 u-mt24">
                    <label>メッセージ</label><span class="label-8 u-text-light-gray">任意</span>
                </div>
                <textarea id="message" name="message" rows="5"
                    placeholder="こちらのサービスを活用して、著作権侵害のリスクを低減したいと考えています。詳しい説明資料をいただけますか？"></textarea>
            </form>
        </section>
        <section id="step4" class="u-wrapper u-mb-footer">
            <h3 class="u-border-top">CONFIRMATION</h3>
            <article class="c-group u-wrapper-reading">
                <dl>
                    <dt class="u-text-light-gray">サービスプラン</dt>
                    <dd id="planConfirm"></dd>
                </dl>
                <dl>
                    <dt class="u-text-light-gray">オプション</dt>
                    <dd>
                        <ul>
                            <li id="option1Confirm"></li>
                            <li id="option2Confirm"></li>
                            <li id="option3Confirm"></li>
                        </ul>
                    </dd>
                </dl>
                <dl>
                    <dt></dt>
                    <dd></dd>
                </dl>
                <hr>
                <dl>
                    <dt class="u-text-light-gray">お名前</dt>
                    <dd id="fullnameConfirm"></dd>
                </dl>
                <dl>
                    <dt class="u-text-light-gray">メールアドレス</dt>
                    <dd id="emailConfirm"></dd>
                </dl>
                <dl>
                    <dt class="u-text-light-gray">ポジション</dt>
                    <dd id="positionConfirm"></dd>
                </dl>
                <dl>
                    <dt class="u-text-light-gray">会社名</dt>
                    <dd id="companyConfirm"></dd>
                </dl>
                <dl>
                    <dt class="u-text-light-gray">電話番号</dt>
                    <dd id="phoneConfirm"></dd>
                </dl>
                <dl>
                    <dt class="u-text-light-gray">ご希望の連絡方法</dt>
                    <dd id="contactConfirm"></dd>
                </dl>
                <dl>
                    <dt class="u-text-light-gray">メッセージ</dt>
                    <dd id="messageConfirm"></dd>
                </dl>
            </article>
            <p class="u-wrapper-reading bodytext-11 u-text-light-gray">※SOREMOでは、個人情報の保護に関する法令を遵守するとともに、
                プライバシーポリシーに基づき、お客様の個人情報を取り扱います。</p>

        </section>
        <dialog>
            <p>ご相談を承りました。<br>
                内容をメールしましたので、ご確認ください。</p>
            <a href=""><button class="c-btn-primary bodytext">サービスTOPへ</button></a>
        </dialog>

    </main>
    <footer>
        <section id="step1-footer" class="u-wrapper-btn">
            <div class="u-row16">
                <a href="https://soremo.jp"><button class="c-btn-tertiary bodytext">戻る</button></a>
                <button id="btn-step2" class="c-btn-secondary bodytext">プランを見る</button>
            </div>
        </section>
        <section id="step2-footer" class="u-wrapper-btn">
            <div class="estimate-area">
                <span id="totalPrice"></span>
            </div>
            <div class="u-row16">
                <button id="btn-step1" class="c-btn-tertiary bodytext">戻る</button>
                <button id="btn-step3" class="c-btn-secondary bodytext">次へ</button>
            </div>
        </section>
        <section id="step3-footer" class="u-wrapper-btn">
            <div class="u-row16">
                <button id="btn-back2" class="c-btn-tertiary bodytext">戻る</button>
                <button id="btn-step4" class="c-btn-secondary bodytext">内容の確認</button>
            </div>
        </section>
        <section id="step4-footer" class="u-wrapper-btn">
            <div class="u-row16">
                <button id="btn-back3" class="c-btn-tertiary bodytext">戻る</button>
                <button id="btn-submit" class="c-btn-secondary bodytext">送信</button>
            </div>
        </section>

    </footer>

    <!-- gsap読み込み -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.4/gsap.min.js"></script>
    <!-- tilt.js読み込み -->
    <script type="text/javascript" src="{% static 'js/vanilla-tilt.min.js' %}"></script>
    <script type="text/javascript">
        VanillaTilt.init(document.querySelectorAll(".p-plan", ".p-option"), {
            reverse: false,
            axis: "x",
            max: 5,
            perspective: 1000,
            scale: 2,
            speed: 500,
            glare: true,
            "max-glare": 1,
        });
    </script>

    <!-- adobeフォント（DNPShueiMinPr6N）読み込み -->
    <script>
        (function (d) {
            var config = {
                kitId: 'zsw6vgy',
                scriptTimeout: 3000,
                async: true
            },
                h = d.documentElement, t = setTimeout(function () { h.className = h.className.replace(/\bwf-loading\b/g, "") + " wf-inactive"; }, config.scriptTimeout), tk = d.createElement("script"), f = false, s = d.getElementsByTagName("script")[0], a; h.className += " wf-loading"; tk.src = 'https://use.typekit.net/' + config.kitId + '.js'; tk.async = true; tk.onload = tk.onreadystatechange = function () { a = this.readyState; if (f || a && a != "complete" && a != "loaded") return; f = true; clearTimeout(t); try { Typekit.load(config) } catch (e) { } }; s.parentNode.insertBefore(tk, s)
        })(document);
    </script>
    <!-- realtypeフォント（AXIS）読み込み -->
    <script src="https://font.realtype.jp/api/script/v4" data-rt-user="KjY7L1I5bsCwmfxaTsXLGp9QrSvIL1BX"
        data-rt-input=true data-rt-nofliker=true data-rt-layout=true data-rt-cache=true></script>
</body>

</html>