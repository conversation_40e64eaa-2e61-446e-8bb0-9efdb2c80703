{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}
{% load user_agents %}
{% load compress %}

{% block extrahead %}
  <script>
    $('html').attr('prefix', 'og: http://ogp.me/ns# fb: http://ogp.me/ns/ fb# article: http://ogp.me/ns/article#')
  </script>
  <meta property="og:url" content="https://soremo.jp/gallery" />
  <meta property="og:type" content="article" />
  <meta property="og:title" content="GALLERY" />
  <meta property="og:description" content="私たちはあらゆるシーンに寄り添い、そこでしか味わえない音を提供します。" />
  <meta property="og:site_name" content="SOREMO | Gallery" />
  <meta property="og:image" content="{{ request.scheme }}://{{ request.META.HTTP_HOST }}{% static 'images/OGP_logo.png' %}" />
  <meta property="og:image:width" content="400" />
  <meta property="og:image:height" content="230" />
{% endblock extrahead %}

{% block content %}
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/css/splide.min.css">
    {% compress css %}
      <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
      <link rel="stylesheet" type="text/css" href="{% static 'css/creator_style.css' %}"/>
      <link rel="stylesheet" type="text/css" href="{% static 'css/topic_style.css' %}"/>
      <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
      <link rel="stylesheet" type="text/css" href="{% static 'css/gallery.css' %}"/>
      <link rel="stylesheet" type="text/css" href="{% static 'css/components/utils.css' %}"/>
      <link rel="stylesheet" type="text/css" href="{% static 'css/audio-navi.css' %}"/>
    {% endcompress %}
  <main>

    {% include "creator/_gallery_setting_modal.html" with user=user %}
    {% include "creator/_modal_detail_topic.html" with user=user %}
    <div class="container gallery-container">
      <div class="gallery__content-container">
        <div class="sform-row psearch-keyword">
          <div class="sform-group sform-group--required">
            <div class="sform-group__input-group sform-group__append-before">
              <input class="sform-control sform-control--input sform-control--full" id="gallery__search-album-input"
                     type="search" placeholder="タグや作品名で検索" required="required">
              <i class="icon icon--sicon-close gallery-search-delete"></i>
              <label class="sform-group__append" for="gallery__search-album-input">
                <i class="icon icon--sicon-search"></i>
              </label>
            </div>
          </div>
        </div>
      </div>

      {% include "creator/_topic_list.html" with user=user topics=topics gallery=True topic_ids=topic_ids total_page=total_page %}

      <div class="gallery__new-works-container {% if not is_curator %}cannot-check{% endif %}" data-list-ids="{{ new_work_ids }}" data-total-page="{{ total_page_new_work }}">

{#        {% include 'creator/_load_more_list_new_work.html' with list_works=list_works user=user is_curator=is_curator %}#}

      </div>

      {#      <div class="list-search">#}
      {#        <div class="list-search__reload"></div>#}
      {#      </div>#}
    </div>

    {% include 'messenger/_modal_open_file.html' %}
    {% include 'messenger/_modal_confirm_step.html' with add_by_sale=True hide_step_3=True page='gallery' %}
    <div id='loading_animation'>
      <svg class='loader-example' viewBox='0 0 100 100'>
        <defs>
            <filter id='goo'>
                <feGaussianBlur in='SourceGraphic' stdDeviation='8' result='blur' />
                <feColorMatrix in='blur' mode='matrix' values='1 0 0 0 0
                                                              0 1 0 0 0
                                                              0 0 1 0 0
                                                              0 0 0 25 -8' result='goo' />
                <feBlend in='SourceGraphic' in2='goo' />
            </filter>
        </defs>
        <g filter='url(#goo)' fill='#f0f0f0' stroke='#fcfcfc'>
            <g transform='translate(50, 50)'>
                <g class='circle -a'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='25' cy='50' r='9' />
                    </g>
                </g>
            </g>
            <g transform='translate(50, 50)'>
                <g class='circle -b'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='50' cy='25' r='8'  />
                    </g>
                </g>
            </g>
            <g transform='translate(50, 50)'>
                <g class='circle -c'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='75' cy='50' r='7' />
                    </g>
                </g>
            </g>
            <g transform='translate(50, 50)'>
                <g class='circle -d'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='50' cy='75' r='6' />
                    </g>
                </g>
            </g>
            <g transform='translate(50, 50)'>
                <g class='circle -e'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='25' cy='50' r='5' />
                    </g>
                </g>
            </g>
            <g transform='translate(50, 50)'>
                <g class='circle -f'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='50' cy='25' r='4' />
                    </g>
                </g>
            </g>
            <g transform='translate(50, 50)'>
                <g class='circle -g'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='75' cy='50' r='3' />
                    </g>
                </g>
            </g>
            <g transform='translate(50, 50)'>
                <g class='circle -h'>
                    <g transform='translate(-50, -50)'>
                        <circle cx='50' cy='75' r='2' />
                    </g>
                </g>
            </g>
        </g>
      </svg>
    </div>
    <div class="audio-navi">
      <div class="audio-navi-titlebar">
        <div class="audio-navi-titlebar-info">
          <div class="audio-navi-titlebar-info-title"></div>
          <div class="audio-navi-titlebar-info-name"></div>
        </div>
        <div class="audio-navi-titlebar-copylink">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 20H5V7C5 6.45 4.55 6 4 6C3.45 6 3 6.45 3 7V20C3 21.1 3.9 22 5 22H15C15.55 22 16 21.55 16 21C16 20.45 15.55 20 15 20ZM20 16V4C20 2.9 19.1 2 18 2H9C7.9 2 7 2.9 7 4V16C7 17.1 7.9 18 9 18H18C19.1 18 20 17.1 20 16ZM18 16H9V4H18V16Z" fill="#A7A8A9"/>
          </svg>
        </div>
        <div class="audio-navi-titlebar-bookmark"><span class="icon icon--sicon-bookmark-o fa-bookmark"></div>
      </div>
  
      <div class="audio-navi-nowplayingbar">
        <div class="audio-navi-playpause">
          <svg class="audio-navi-play" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g>
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 15.5V8.5C10 8.09 10.47 7.85 10.8 8.1L15.47 11.6C15.74 11.8 15.74 12.2 15.47 12.4L10.8 15.9C10.47 16.15 10 15.91 10 15.5Z" fill="#A7A8A9"/>
            </g>
          </svg>
            
          <svg class="audio-navi-pause hide" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g>
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16C9.45 16 9 15.55 9 15V9C9 8.45 9.45 8 10 8C10.55 8 11 8.45 11 9V15C11 15.55 10.55 16 10 16ZM14 16C13.45 16 13 15.55 13 15V9C13 8.45 13.45 8 14 8C14.55 8 15 8.45 15 9V15C15 15.55 14.55 16 14 16Z" fill="#A7A8A9"/>
            </g>
          </svg>
        </div>
        <div class="audio-navi-wave"></div>
        <div class="audio-navi-time">0:00</div>
      </div>
    </div>
  </main>
  <script>
  let user_role = '{{ user.role }}';
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/js/splide.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/splide-extension-auto-scroll.js' %}"></script>
    {% endcompress %}
  <script src="{% url 'javascript-catalog' %}"></script>
        {% compress js inline %}
  <script src="{% static 'js/utils.js' %}"></script>
  <script src="{% static 'js/topic_detail.js' %}"></script>
  <script src="{% static 'js/creator_index.js' %}"></script>
  <script src="{% static 'js/modal_contact.js' %}"></script>
  <script src="{% static 'js/album_preview.js' %}"></script>
  <script src="{% static 'js/gallery.js' %}"></script>
  <script src="{% static 'js/save_order_contact.js' %}"></script>
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
  <script src="{% static 'js/audio-navi.js' %}"></script>
    {% endcompress %}
{% endblock %}
