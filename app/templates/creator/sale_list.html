{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load compress %}
{% block content %}
    <main class="sale-content">
      <div class="sale-content__content">
        <div class="container">
          <div class="sale-content__original">
            <div class="sale-content__title">
              <div class="sale-content__section-title">Originals</div>
              {% if user.role == 'creator' %}
              <div class="sale-content__button">
                <a class="header-button header-button--next" href="/content_sale/create/{{creator_id}}">コンテンツを追加</a>
              </div>
              {%endif%}
            </div>
            <div class="sale-content__original-list columns-3">
              {% for sale in contentsale_list%}
              <div class="original-item">
                <div class="original-item__content">
                  <div class="original-item__image">
                    <div class="original-item__image-bg" style="background-image:
                      url('{%if sale.get_img %}{{sale.get_img}}{%else%}{% static 'images/original-item-img.png' %}{%endif%}')"></div>
                  </div>
                  <div class="original-item__info">
                    <div class="original-item__info-top">
                      <div class="original-item__title">{{sale.title}}</div>
                      <div class="original-item__status" style="min-width: 75px">{{sale.get_status_display}}</div>
                    </div>
                    <div class="original-item__price">{{sale.format_price}}円</div>
                    <div class="original-item__desc">{{sale.desc}}</div>
                  </div>
                </div>
                <div class="original-item__audio">
                  {% if sale.audio_sale.all %}
                  <div class="audio-player">
                    <div class="audio-waveform"></div>
                    <div class="audio-content">
                      <div class="audio-list">
                        <div class="audio-list__title"></div>
                        <div class="audio-list__items" id="playlist">
                          {% for audio in sale.audio_sale.all %}
                          <a class="audio-list__item" href="{{audio.file.url}}" data-title="{{audio}}"></a>
                          {%endfor%}
                        </div>
                      </div>
                      <div class="audio-controls" style="z-index:10">
                        <div class="audio-control">
                          <div class="audio-control__prev"></div>
                          <div class="audio-control__playpause"></div>
                          <div class="audio-control__next"></div>
                        </div>
                        <div class="audio-remain">0:00</div>
                      </div>
                      <a class="button button--icon button--icon-dot button--round" href="#" role="button"></a>
                    </div>
                  </div>
                  {% endif %}
                </div>
                <div class="original-item__button">
                  <a class="button button--gradient button--gradient-primary button--round" href="/content_sale/update/{{sale.pk}}" role="button">
                    {% if creator_id and current_creator_id == creator_id %}更新する{% else %}購入する{% endif %}
                  </a>
                  {%if user.role == 'creator' %}
                    {% if sale.status == 'publish' %}
                      <a class="button button--text button--text-gray" href="javascript:void(0)"
                        role="button" onclick="ChangeStatusAlbum('{{sale.pk}}', '{{creator_id}}','unpublished')">非公開にする</a>
                    {% elif sale.status == 'unpublished' %}
                      <a class="button button--text button--text-gray" href="javascript:void(0)"
                        role="button" onclick="ChangeStatusAlbum('{{sale.pk}}', '{{creator_id}}','publish')">公開中</a>
                    {% endif %}
                    <a class="button button--text button--text-gray" href="javascript:void(0)"
                      role="button" onclick="ChangeStatusAlbum('{{sale.pk}}', '{{creator_id}}', 'deleted')">DELETE</a>
                  {%endif%}
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </main>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/wavesurfer.js' %}"></script>
    <script type="text/javascript">
      {% if not request.user_agent.is_pc %}
      $(document).ready(function() {
        $('.original-item__desc').each(function(index, item){
          if($(item).text().length >= 20){
            $(item).text(item.innerText.substring(0,20).concat('...'))
          }
        })
      });
      {%endif%}

      var ChangeStatusAlbum = function(content_sale, creator_id, action){
        $.ajax({
          url: '/change_status_sale_content',
          type: 'POST',
          dataType: 'json',
          data: {
            'content_sale': content_sale,
            'action': action,
            'creator_id': creator_id
          },
          success: function(data){
            location.reload()
          }
        })
      }
    </script>
    {% endcompress %}
{%endblock%}
