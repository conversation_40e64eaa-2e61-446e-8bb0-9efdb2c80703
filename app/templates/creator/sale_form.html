{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load compress %}
{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css">
    {% compress css %}
    <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
    {% endcompress %}
{% endblock %}
{% block content %}
    {% compress css %}
    <style type="text/css">
        .button--round {
            border: 0px;
        }

        .sale-content__file-preview {
            display: block;
        }

        .close {
            position: absolute;
            top: 20px;
            right: 5px;
        }

        .sale-content__preview-content {
            position: relative;
        }

        .sale-content__file-list {
            margin: unset;
        }

        .error-messager:before {
            content: '';
        }

        .sale-content__file .error-messager {
            justify-content: center;
        }
    </style>
    {% endcompress %}
    <main class="sale-content">
        <div class="sale-content__content">
            <form action="/content_sale/{% if object %}update{% else %}create{% endif %}/{{ pk }}" method="post"
                  enctype="multipart/form-data">
                {% csrf_token %}
                <div class="container">
                    <div class="sale-content__page-title">Originals</div>
                    <div class="sale-content__cover">
                        <div class="sale-content__cover-image" style="{% if object and object.get_img %}background-image: url('{{ object.get_img }}');{% else %}background: silver;{% endif %}">
                            {% if user.role == 'creator' %}
                                <label class="button button--background button--background-gray button--round button--small"
                                       for="id_image">
                                    サムネイル画像を選択
                                </label>
                                <input type="file" name="image" class="hidden" id="id_image" accept="image/*"
                                       oninvalid="this.setCustomValidity('このフィールドは必須項目です。')"
                                       oninput="setCustomValidity('')">
                                {{ form.x }}
                                {{ form.y }}
                                {{ form.width }}
                                {{ form.height }}
                                {{ form.owner }}
                            {% endif %}
                        </div>
                        {% if form.errors.image %}
                            <div class="error-messager">
                                {{ form.errors.image }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="sale-content__info">
                        <div class="form-group">
                            <label for="title">タイトル *</label>
                            {{ form.title|add_class:'form-control' }}
                            {% if form.errors.title %}
                                <div class="error-messager">
                                    {{ form.errors.title }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="sale-content__price">
                            <div class="sale-content__price-row">
                                <div class="form-group select-container select_selling-price sumo-select">
                                    <label>販売価格 *</label>
                                    <select class="select__value-list SumoUnder" name="price" data-placeholder=""
                                            tabindex="-1">
                                        {% for i in form.price %}
                                            <option value="3000"
                                                    {% if i.data.value == '3000' %}selected="selected"{% endif %}>3,000円
                                            </option>
                                            <option value="5000"
                                                    {% if i.data.value == '5000' %}selected="selected"{% endif %}>5,000円
                                            </option>
                                            <option value="7000"
                                                    {% if i.data.value == '7000' %}selected="selected"{% endif %}>7,000円
                                            </option>
                                            <option value="10000"
                                                    {% if i.data.value == '10000' %}selected="selected"{% endif %}>
                                                10,000円
                                            </option>
                                            <option value="15000"
                                                    {% if i.data.value == '15000' %}selected="selected"{% endif %}>
                                                15,000円
                                            </option>
                                            <option value="30000"
                                                    {% if i.data.value == '30000' %}selected="selected"{% endif %}>
                                                30,000円
                                            </option>
                                            <option value="50000"
                                                    {% if i.data.value == '50000' %}selected="selected"{% endif %}>
                                                50,000円
                                            </option>
                                            <option value="100000"
                                                    {% if i.data.value == '100000' %}selected="selected"{% endif %}>
                                                100,000円
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="sale-content__price-row">
                                <div class="sale-content__commission-label">販売手数料</div>
                                <div class="sale-content__commission-value">
                                    <span class="sale-content__commission-number">2,136</span>
                                    <span class="currency">円</span>
                                </div>
                            </div>
                            <div class="sale-content__price-row">
                                <div class="sale-content__profit-label">販売利益</div>
                                <div class="sale-content__profit-value">
                                    <span class="sale-content__profit-number">7,864</span>
                                    <span class="currency">円</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-customize sale-content__description" rows="5">
                            <label for="description">説明 *</label>
                            <div class="form-textarea" id="description">
                                {{ form.desc|add_class:'form-control' }}
                                {% if form.errors.desc %}
                                    <div class="error-messager">
                                        {{ form.errors.desc }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sale-content__file">
                    <div class="container">
                        {% if user.role == 'creator' %}
                            <label class="button--background button--background-gray button--round button--small"
                                   for="id_file">ファイルを選択</label>
                        {% endif %}
                        {{ form.file|add_class:'hidden' }}
                        {% if form.errors.file %}
                            <div class="error-messager">
                                {{ form.errors.file }}
                            </div>
                        {% endif %}
                        {% if object %}
                            <div class="sale-content__file-list audio_update">
                                {% for audio in object.audio_sale.all %}
                                    <div class="sale-content__file-preview">
                                        <div class="sale-content__preview-{{ forloop.counter0 }}">
                                            <a class="audio-list__item" href="{{ audio.file.url }}"></a>
                                        </div>
                                        <div class="sale-content__preview-content">
                                            <div class="sale-content__preview-title">{{ audio }}</div>
                                            <div class="sale-content__preview-time-{{ forloop.counter0 }}">0:00</div>
                                            {% if user.role == 'creator' %}
                                                <div type='button' class="close clear_audio_update"
                                                     data-audio='{{ audio.id }}'>
                                                    <span aria-hidden="true">&times;</span>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                            <input type="hidden" name="list_clear_audio" id='list_clear'>
                        {% endif %}
                        <div class="sale-content__file-list audio_new">
                        </div>
                        <input type="hidden" name="remove_audio_new" id='remove_audio_new'>
                    </div>
                </div>
                {% if user.role == 'creator' %}
                    <div class="sale-content__policy">
                        <div class="container">
                            <div class="checkbox input-checkbox">
                                <input type="checkbox" name="before-delivery" id="before-delivery">
                                <label for="before-delivery">
                                    <a href="javascript:void(0)">販売同意書</a>を確認しました。
                                </label>
                            </div>
                        </div>
                    </div>
                {% endif %}
                <div class="sale-content__action">
                    {% if user.role == 'creator' %}
                        <div class="sale-content__form-group">
                            {% buttons %}
                                <input type="submit" value="OK"
                                       class="button button--gradient button--gradient-primary button--round disabled"
                                       onclick="this.disabled=true,this.form.submit();"/>
                            {% endbuttons %}
                        </div>

                        <div class="sale-content__form-group">
                            <a class="button button--background button--background-gray button--round button--small"
                               href="#" onclick="location.reload();" role="button">Cancel</a>
                        </div>
                    {% else %}
                        <div class="sale-content__form-group">
                            <a class="button button--gradient button--gradient-primary button--round admin-check"
                               data-action='approve' href="javascript:void(0)">承認</a>
                        </div>

                        <div class="sale-content__form-group">
                            <a class="button button--background button--background-gray button--round button--small admin-check"
                               data-action='needfix' href="javascript:void(0)">修正依頼</a>
                        </div>
                    {% endif %}
                </div>
            </form>
            <div class="modal fade" id="modalCrop">
                <div class="modal-dialog" style="transform: translate(0,10%);">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title">画像登録</h4>
                        </div>
                        <div class="modal-body">
                            <img src="" id="image" style="max-width: 100%;">
                        </div>
                        <div class="modal-footer">
                            <div class="btn-group pull-left" role="group">
                                <button type="button" class="btn btn-default js-zoom-in">
                                    <span class="glyphicon glyphicon-zoom-in"></span>
                                </button>
                                <button type="button" class="btn btn-default js-zoom-out">
                                    <span class="glyphicon glyphicon-zoom-out"></span>
                                </button>
                            </div>
                            <button type="button" class="btn btn-primary js-crop-and-upload">登録する</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    {% compress js inline %}
        <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    <script src="{% static 'js/cropper.min.js' %}"></script>
    <script src="{% static 'js/main_cropping.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
    {% endcompress %}
{% endblock %}
{% block extra_script %}
    {% compress js inline %}
    <script>
        var formatTime = function (time) {
            return [
                Math.floor((time % 3600) / 60), // minutes
                ('00' + Math.floor(time % 60)).slice(-2) // seconds
            ].join(':');
        };

        var check_extend_file = function (files) {
            result = true
            for (var i = 0; i <= files.length - 1; i++) {
                if (!files[i].name.match(/\.(mp3|wav|ogg|MP3|WAV|OGG)$/)) {
                    result = false
                }
            }
            return result
        };

        function initWave(target) {
            if (target[0].files && check_extend_file(target[0].files)) {
                $('.audio_new').show();
                $('.audio_new').empty();
                var dom_list = '';

                for (var i = 0; i <= target[0].files.length - 1; i++) {
                    console.log(i)
                    file_name = target[0].files[i].name.split('.')[0];
                    dom_list = '<div class="sale-content__file-preview">'
                            + '<div class="sale-content__preview-' + i + '">'
                            + '</div>'
                            + '<div class="sale-content__preview-content">'
                            + '<div class="sale-content__preview-title">' + file_name + '</div>'
                            + '<div class="sale-content__preview-time-' + i + '">0:00</div>'
                            + '<div type="button" class="close clear_audio_new" data-index=' + i + '>'
                            + '<span aria-hidden="true">&times;</span>'
                            + '</div>'
                            + '</div>'
                            + '</div>';

                    $('.audio_new').append(dom_list);

                    index = i;
                    let wavesurfer = WaveSurfer.create({
                        container: '.audio_new .sale-content__preview-' + i,
                        waveColor: '#a7a8a9',
                        progressColor: '#36aac4',
                        cursorColor: 'rgba(0,157,196,0.29)',
                        barWidth: 3,
                        barRadius: 3,
                        cursorWidth: 3,
                        barGap: 3,
                        mediaControls: false,
                        height: 80,
                        responsive: true,
                        hideScrollbar: true
                    });

                    let setCurrentSong = function (file) {
                        wavesurfer.load(URL.createObjectURL(file));
                        let targetDom = $(wavesurfer.container).siblings().find('div[class^="sale-content__preview-time-"]');
                        wavesurfer.on('ready', function () {
                            targetDom.text(formatTime(wavesurfer.getDuration()));
                        });
                    };

                    wavesurfer.on('error', function (e) {
                        console.warn(e);
                    });

                    setCurrentSong(target[0].files[i]);
                }

            } else if (target[0].files.length === 0) {
                $('.audio').empty();
                return false;
            } else {
                alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
                $('.audio').empty();
                target.val('').clone(true);
            }
        }

        var content_id = '{{pk}}';

        var user_id = '{{user.id}}';

        var file_dom = $('#id_file');

        $(document).ready(function () {

            var imageCropper = {
                viewMode: 1,
                rotatable: false,
                aspectRatio: 1,
                minCropBoxWidth: 200,
                minCropBoxHeight: 200,
                minContainerHeight: 400,
            };

            $('#id_image').on('change', function () {
                readDataUpload("#id_image", imageCropper)
            });

            {% if not object %}
                $('#id_x,#id_y,#id_height,#id_width').val(0);
            {% endif %}

            {% if not object %}
                $('#id_owner').val('{{pk}}');
                $('.audio_update').hide();
            {% endif %}

            {% if user.role != 'creator' %}
                $('#id_title').prop('disabled', true);
                $('.select__value-list').prop('disabled', true);
                $('#id_desc').prop('disabled', true);
                $('#id_policy').prop('disabled', true);
                $('.close').addClass('button-disabled');
            {% endif %}

            file_dom.attr({accept: '.mp3,.wav,.ogg'});

            file_dom.on('change', function () {
                initWave($(this));
            });

            {% if object %}
                if ($('.audio_update').children().length > 0) {
                    for (var i = 0; i <= $('.audio_update').children().length - 1; i++) {
                        index = i;
                        let wavesurfer = WaveSurfer.create({
                            container: '.audio_update .sale-content__preview-' + i,
                            waveColor: '#a7a8a9',
                            progressColor: '#36aac4',
                            cursorColor: 'rgba(0,157,196,0.29)',
                            barWidth: 3,
                            barRadius: 3,
                            cursorWidth: 3,
                            barGap: 3,
                            mediaControls: false,
                            height: 80,
                            responsive: true,
                            hideScrollbar: true
                        });


                        let setCurrentSong = function (file) {
                            wavesurfer.load(file.prop('href'));
                            let targetDom = $(wavesurfer.container).siblings().find('div[class^="sale-content__preview-time-"]')
                            wavesurfer.on('ready', function () {
                                targetDom.text(formatTime(wavesurfer.getDuration()));
                            });
                        };

                        wavesurfer.on('error', function (e) {
                            console.warn(e);
                        });

                        setCurrentSong($('.audio_update .sale-content__preview-' + i + ' a'));
                    }
                }

                $('.clear_audio_update').on('click', function (event) {
                    event.preventDefault();
                    let list_clear = $('#list_clear').val();
                    list_clear += $(this).data('audio');
                    $('#list_clear').val(list_clear + ',');
                    $(this).parents('.sale-content__file-preview').remove();
                });
            {%endif%}

            $('#before-delivery').on('click', function () {
                $('.button--gradient-primary').toggleClass('disabled')
            });

            $('.admin-check').on('click', function () {
                action = $(this).data('action');
                $.ajax({
                    url: '/admin_review',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        'csrfmiddlewaretoken': '{{csrf_token}}',
                        'action': action,
                        'content_id': content_id,
                        'user_id': user_id
                    },
                    success: function () {
                        window.location.href = '/admin_content_sale/'
                    }
                })
            })

            $('.sale-content__file').on('drag dragstart dragend dragover dragenter dragleave drop', function () {
                initWave($(this).find('#id_file'));
            });
        });

        $(document).on('click', '.clear_audio_new', function (event) {
            event.preventDefault();
            let remove_audio_new = $(this).data('index');
            $(this).parents('.sale-content__file-preview').remove();
            if ($('.audio_new').children().length === 0) {
                file_dom.val('').clone();
                $('#remove_audio_new').val('')
            } else {
                $('#remove_audio_new').val(remove_audio_new + ',')
            }
        });
    </script>
    {% endcompress %}
{% endblock %}