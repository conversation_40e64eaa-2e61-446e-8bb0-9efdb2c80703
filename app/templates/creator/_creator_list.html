{% load util %}
{% load static %}

{% if creator_lists.exists %}
  {% for creator_list in creator_lists %}
    {% if is_curator or creator_list.creatorlistcreator_set.all.exists %}
      {% with creator_list.to_string_id as list_id %}
        <div class="list-composer"
             data-order="{{ creator_list.order }}" data-list-id="{{ creator_list.id }}">
          <div class="creator-list_header">
            <div class="list-composer__title">{{ creator_list.title }}</div>
            <div class="list-composer__sub-title">{{ creator_list.description }}</div>
            {% if is_curator and not creator_list.is_default %}
              <div class="creator-list-chapter-title-edit comment-edit-button-group">
                <button class="edit-creator-list" data-toggle="modal" data-target="#composer-update-modal">
                  <img src="/static/images/edit.svg" alt="">
                </button>
                <button class="delete-creator-list">
                  <img src="/static/images/delete.svg" alt="">
                </button>
              </div>
            {% endif %}
          </div>
          <div class="list-composer__list nice-scroll {% if is_curator %} is_curator{% endif %}">
            {% if is_curator %}
              <div class="list-composer__add">
                <a class="list-composer__add-btn" data-toggle="modal" data-target="#composer-modal">
                          <span class="srm-icon-wrap">
                      <div class="srm-icon srm-add"></div>
                    </span>
                </a>
                <div class="list-composer__add-desc">アーティストを選択</div>
              </div>
            {% endif %}
              {% for creator_list_creator in creator_list.creatorlistcreator_set.all.distinct %}
                {% with creator_list_creator.creator as creator %}
                  <a href="


                          {% if creator.slug %}{% url 'app:creator_info' creator.slug %}{% else %}{% url 'accounts:accounts_creator' creator.user.pk %}{% endif %}"
                     target="_blank">
                    <div class="user-item can-sort" data-creator-id="{{ creator.id }}"
                         data-creator_list-creator={{ creator_list_creator.pk }}>
                      {% include 'creator/_item_creator.html' with user=user creator=creator creator_list=creator_list %}
                    </div>
                  </a>
                {% endwith %}
              {% endfor %}
          </div>
        </div>
      {% endwith %}
    {% endif %}
  {% endfor %}
{% endif %}
