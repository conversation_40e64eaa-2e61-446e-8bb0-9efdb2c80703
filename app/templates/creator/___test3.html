{% load static %} {% load util %}

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>REVIEW | SOREMO</title>
  {% include '_gtag_head.html' %}
  <!-- material Icon -->
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
  <!-- css読み込み -->
  <link rel="stylesheet" type="text/css" href="{% static 'css/soremo_style_2024.css' %}" />
  <link rel="stylesheet" type="text/css" href="{% static 'css/__test.css' %}" />
</head>

<style>
  html {
    background: linear-gradient(180deg,
        rgba(249, 255, 186, 0.13) 0%,
        rgba(255, 255, 255, 0.13) 100%),
      linear-gradient(0deg, #fff 0%, #83f0ff 75.44%, #00e0ff 100%);
  }

  .c-parent-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .c-parent-tags label {
    display: inline-block;
    padding: 0px 0px;
    margin: 0px 4px;

    cursor: pointer;
  }

  .c-parent-tags input[type="radio"] {
    display: none;
  }

  .c-parent-tags label:has(input[type="radio"]:checked) {
    color: #009ace;
    border-bottom: 2px solid #009ace;
  }

  .c-child-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .c-child-tags li {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #a7a8a9;
    background-color: #fff;

    cursor: pointer;
  }

  .c-thumbnail96 {
    width: 96px;
    height: 96px;

    background-image: url(" ./images/roles/role_guitar.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .c-thumbnail128 {
    width: 128px;
    height: 128px;

    background-color: #00e0ff;

    background-image: url("./images/roles/role_guitar.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
</style>

<body id="bg">
  <main class="u-wrapper u-mt32 u-h100">
    <div class="heading-spacing-2448 u-pt8 u-pb32 u-border-top">
      Import JSON
    </div>
    <ul class="c-parent-tags u-mb8" id="roles-category">
    </ul>
    <ul id="roles-info" class="u-col u-gap4">
    </ul>
  </main>


  <script>
    document.addEventListener("DOMContentLoaded", () => {
      // JSONファイルを読み込む関数
      async function fetchJSON() {
        try {
          // JSONファイルのパスを指定してfetchを呼び出す
          const response = await fetch("{% static '/fixtures/roles.json' %}");

          // レスポンスからJSONデータを取得する
          const data = await response.json();

          //   // 取得したデータをコンソールに表示
          //   console.log(data);

          // データを使ってHTMLを更新する
          displayUsers(data); // data自体が配列なのでそのまま渡す
        } catch (error) {
          console.error("Error fetching JSON:", error);
        }
      }

      // ユーザー情報を表示する関数
      function displayUsers(users) {
        //roles.jsonの“category”キーのすべてを読み取り、#roles-category内にli要素で追加します。（重複は削除）
        const categoryList = document.getElementById("roles-category");

        categoryList.innerHTML = ""; // 既存の内容をクリア

        const categorySet = new Set(users.map((user) => user.category));

        categorySet.forEach((category) => {
          const categoryItemHTML = `
        <li>
          <label>
            <input type="radio" name="category" value="${category}">
            ${category}
          </label>
        </li>
      `;
          categoryList.insertAdjacentHTML("beforeend", categoryItemHTML);
        });

        // カテゴリーが選択されたときのイベントリスナーを追加
        categoryList.addEventListener("change", (event) => {
          if (event.target.name === "category") {
            const selectedCategory = event.target.value;
            const selectedUsers = users.filter(
              (user) => user.category === selectedCategory
            );
            displaySelectedUsers(selectedUsers);
          }
        });
      }

      // 選択されたロール情報を#roles-infoに表示する関数
      function displaySelectedUsers(users) {
        const rolesInfo = document.getElementById("roles-info");
        rolesInfo.innerHTML = ""; // 既存の内容をクリア

        users.forEach((user) => {
          const userInfoHTML = `
        <li class="p-cards u-row u-gap8">
        <div class="c-thumbnail128 u-flex-none" style="background-image: url('./images/roles/${user.thumbnail}');"></div>
        <ul class="u-col u-gap8">
          <li class="heading-spacing">${user.name}</li>
          <li class="bodytext-13 u-line-height-150">${user.description}</li>
          <li class="bodytext-11 u-text-light-gray">${user.credits}</li>
        </ul>
        <li>
      `;
          rolesInfo.insertAdjacentHTML("beforeend", userInfoHTML);
        });
      }

      // JSONデータの読み込みを実行
      fetchJSON();
    });

  </script>



  <!-- js読み込み -->
  <script type="text/javascript">
    window.CSRF_TOKEN = "{{ csrf_token }}";
  </script>
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <script type="text/javascript" src="{% static 'js/__test.js' %}"></script>
</body>