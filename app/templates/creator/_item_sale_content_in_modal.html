{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}

{% for sale_content in sale_contents %}
  {% with sale_content|get_new_sale as sale_content %}
    {% with sale_content|get_artist_of_sale_content as artist %}
      {% with artist.get_link_profile as profile_url %}
      <div class="list-search-work__component">
        <div class="list-new-works__item-container" data-sale-content-id="{{ sale_content.pk }}">
          <div class="list-new-works__media" style="{{ sale_content|get_thumbnail_sale_content:user }}"
               data-type="{{ sale_content.last_published_version.content_type }}"
               data-artist="artist name">
            <label class="checkbox-container">
              <input type="checkbox" class="check-to-add-into-list">
              <span class="checkmark"></span>
            </label>
          </div>
          <div class="list-new-works__content">
            <div class="list-new-works__title">{{ sale_content|get_title_sale_content:user }}</div>
            <div class="list-new-works__sub-title" data-url="{{ profile_url }}">{{ artist.get_display_name }}</div>
            <div class="list-new-works__hint">{{ sale_content|get_sale_type_text:user }}</div>
          </div>
        </div>
      </div>
      {% endwith %}
    {% endwith %}
  {% endwith %}
{% endfor %}
