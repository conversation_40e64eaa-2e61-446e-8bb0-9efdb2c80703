{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}
{% load compress %}

{% block content %}
    {% compress css %}
        <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/topic_style.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/creator_style.css' %}"/>
        <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
        <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/components/utils.css' %}"/>
    {% endcompress %}
  <main>
    <div class="container">
    {% include "creator/_gallery_setting_modal.html" %}
    <!-- Form 1 -->
        <div class="topic-container__topic-form-1">
            <form action="{% url 'app:topic_create' %}" method="post" class="" id="form-create_topic" {% if artist %}data-artist-id="{{ artist.pk }}"{% endif %}>
            {% csrf_token %}
                <div class="topic-form-1__title" style="margin-bottom: 28px;">トピックを編集</div>
                <div class="form-container">
                  {#                upload new thumbnail #}
                  <div class="tocpic-form-1__upload-video" style="margin-top: 48px;">
                    <span class="topic__field-label">{% trans "thumbnail" %}<span class="topic__jp-astarisk">[必須]</span></span>
                    <div class="topic__field-label-hint">1.414:1のアスペクト比で画像をアップロードしましょう。</div>

                    <div class="topic-upload__image" data-src="{% if topic and topic.image %}true{% endif %}">
                      <img src="{% if topic and topic.image %}{{ topic.image.url }}{% else %}{% static 'images/bg_f0f0f0.png' %}{% endif %}" alt="" style="height: 96px; width: 68px; object-fit: cover; border-radius: 12px;">
                    </div>
                    <label class="label-form label-upload-image" for="id_image" style="margin-top: 0px;">
                      <span class="btn btn--secondary btn-upload-image"
                            style="padding: 8px 24px !important;">{% trans "Configuration" %}</span>
                      <input type="file" accept="image/*" hidden style="display: none;" id="id_image"
                             name="image">
                    </label>
                  </div>
                {# UPLOAD VIDEO #}
                <div class="tocpic-form-1__upload-video">
                    <label class="label-form" for="">
                        <span class="topic__field-label">{% trans "Movie" %}<span class="topic__jp-astarisk-op">[任意]</span></span>
                      {% if topic and topic.video %}
                        <div class="account__file">
                          <i class="icon icon--sicon-clip"></i>
                          <div class="account__file-name">{{ topic.video_real_name }}</div>
                          <i class="icon icon--sicon-close icon--sicon-close-video"></i>
                        </div>

                      {% endif %}

                        <div class="account_upload-file mattach mattach-form">
                          <div class="mcomment-attached">
                            <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
                              <div class="mattach-previews mattach-previews-form collection">
                                <div class="mattach-template mattach-template-form collection-item item-template">
                                  <div class="mattach-info" data-dz-thumbnail="">
                                    <div class="mcommment-file">
                                      <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                      <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                      <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                              class="icon icon--sicon-close"></i>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div id="dropzoneUpLoadVideoTopic" class="fallback dropzone">
                          </div>
                        </div>
                      </label>
                </div>
                <div class="tocpic-form-1__upload-video" style="margin-top: 48px;">
                  <span class="topic__field-label">キービジュアル<span class="topic__jp-astarisk">[必須]</span></span>
                  <div class="topic__field-label-hint">{% trans "Upload your image with a 16: 9 aspect ratio." %}</div>

                  <div class="topic-upload__thumbnail" data-src="{% if topic and topic.thumbnail %}true{% endif %}">
                    <img src="{% if topic and topic.thumbnail %}{{ topic.thumbnail.url }}{% endif %}" alt="">
                  </div>
                  <label class="label-form label-upload-image" for="id_thumbnail" style="margin-top: 0px;">
                    <span class="btn btn--secondary btn-upload-thumbnail" style="padding: 8px 24px !important;">{% trans "Configuration" %}</span>
                    <input type="file" accept="image/*" hidden style="display: none;" id="id_thumbnail" name="thumbnail">
                  </label>
                </div>

                <div class="input-form-container">
                    <label class="label-form" for="id_title">
                        <span class="topic__field-label">{% trans "Topic title" %}<span class="topic__jp-astarisk">[必須]</span></span>
                        <input type="text" class="form-control topic__topic-title" {% if topic %}value="{{ topic.title }}"{% endif %} id="id_title" placeholder='{% trans "Topic title" %}' name="title" maxlength="255">
                    </label>
                </div>

                <div class="input-form-container">
                    <label class="label-form" for="id_category">
                        <span class="topic__field-label">{% trans "Constituent elements" %}<span class="topic__jp-astarisk-op">[任意]</span></span>
                        <div class="topic__field-label-hint">{% trans "Set the word delimiter with a space." %}</div>
                        <input type="text" class="form-control topic__constituent-elements" id="id_category" value="{{ topic.get_categories_in_topic }}"
                        placeholder='{% trans "Interactive music sound effects audio guide program distribution" %}' name="category">
                    </label>
                </div>

                <div class="input-form-container">
                    <label class="label-form" for="id_overview">
                        <span class="topic__field-label">{% trans "Overview" %}<span class="topic__jp-astarisk-op">[任意]</span></span>
                        <input type="text" class="form-control topic__over-view" id="id_overview" maxlength="255" {% if topic.overview %}value="{{ topic.overview }}"{% endif %}
                        placeholder='{% trans "We will produce the space sound design and audio guide for the exhibition." %}' name="overview">
                    </label>
                </div>

                <div class="input-form-container">
                    <label class="label-form" for="id_description">
                        <span class="topic__field-label">{% trans "detail" %}<span class="topic__jp-astarisk-op">[任意]</span></span>
                        <textarea name="description" rows="8" class="form-textarea form-control topic_detail" id="id_description" maxlength="1000"
                            placeholder="展示会やプロジェクションマッピング、インスタレーション作品のサウンド演出にもっと向き合ってみませんか。イマーシブで再生環境に寄り添い、かつ、タッチや各種センサーに反応する動的なサウンドデザインを提供。テクノロジーを駆使した演出にしっかり応えます。また、展示作品の解説に必要なオーディオガイド、QRコードで入場者限定で配信できる作品解説番組も制作できます。"
                            style="resize: none;">{% if topic %}{{ topic.description }}{% endif %}</textarea>
                    </label>
                </div>

                <div class="input-form-container">
                    <label class="label-form" for="id_hashtag">
                        <span class="topic__field-label">{% trans "hashtag" %}<span class="topic__jp-astarisk-op">[任意]</span></span>
                        <div class="topic__field-label-hint">{% trans "Set the word delimiter with #." %}</div>
                        <input type="text" class="form-control topic__constituent-elements" id="id_hashtag" {% if topic %}value="{{ topic.get_hash_tags_in_topic }}"{% endif %}
                        placeholder='{% trans "#Metabirds #Sonic Branding" %}' name="hashtag">
                    </label>
                </div>


{#                UPLOAD FILE #}
                <div class="topic-form-1__upload-detailed-materials">
                    <label class="label-form" for="">
                        <span class="topic__field-label">{% trans "Detailed materials" %}<span class="topic__jp-astarisk-op">[任意]</span></span>

                      {% if topic and topic.file %}
                        <div class="account__file">
                          <i class="icon icon--sicon-clip"></i>
                          <div class="account__file-name">{{ topic.file_real_name }}</div>
                          <i class="icon icon--sicon-close icon--sicon-close-file"></i>
                        </div>
                      {% endif %}


                        <div class="account_upload-file mattach mattach-form">
                          <div class="mcomment-attached">
                            <div class="mattach-preview-container mattach-preview-container-form-upload">
                              <div class="mattach-previews mattach-previews-form collection">
                                <div class="mattach-template mattach-template-form collection-item item-template">
                                  <div class="mattach-info" data-dz-thumbnail="">
                                    <div class="mcommment-file">
                                      <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                      <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                      <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                              class="icon icon--sicon-close"></i>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div id="dropzoneUpLoadDetailedMaterials" class="fallback dropzone">
                          </div>
                        </div>
                      </label>
                </div>

                <div class="topic-form-1__choices-container">
                    <label class="label-form" for="">
                        <span class="topic__field-label">{% trans "Choices" %}<span class="topic__jp-astarisk-op">[任意]</span></span>
                        <div class="list-choice-container">

                          {% for selection in topic.selections.all %}
                            {% include 'creator/_item_selection.html' with selection=selection user=user topic=topic %}
                          {% endfor %}

                            <a class="list-choices__btn-add-choice">
                                <i class="icon icon--sicon-add-cirlce"></i>
                                <p>{% trans "Add selection" %}</p>
                            </a>
                        </div>
                    </label>
                </div>
{##}
{#                <input type="hidden" name="x" value="0.0" id="id_x">#}
{#                <input type="hidden" name="y" value="0.0" id="id_y">#}
{#                <input type="hidden" name="width" value="0.0" id="id_width">#}
{#                <input type="hidden" name="height" value="0.0" id="id_height">#}

                <div class="topic-form-1__footer-container">
                    <a href="{% if artist %}{{ artist.get_link_profile }}{% elif topic and topic.user %}{{ .get_link_profile }}{% else %}/gallery{% endif %}" class="btn btn--tertiary btn-cancel-create-topic" style="margin-right: 16px;">{% trans "return" %}</a>
                    <button type="submit" class="btn btn--primary button-submit-form1">OK</button>
                </div>
              </div>
            </form>
        </div>
      <!-- End form 1 -->

      <!-- Form 2 -->
      <div class="topic-container__topic-form-2">
        <!-- <form id="id-form-2"> -->
          <div class="form-container">
          <div class="input-form-container">
            <label class="label-form" for="id_choice_title">
                <span class="topic__field-label">{% trans "Choice title" %}</span>
                <input type="text" class="form-control topic__constituent-elements" id="id_choice_title"
                placeholder='{% trans "Choice title" %}' name="choice_title" style="font-weight: 400;">
            </label>
          </div>

          <div class="input-form-container">
            <label class="label-form" for="id_explanation">
                <span class="topic__field-label">{% trans "Explanation" %}</span>
                <textarea name="explanation" rows="2" class="form-textarea form-control" id="id_explanation"
                    placeholder="ここに選択項目の説明がはいります。&#10;ここに選択項目の説明がはいります。"
                    style="resize: none; font-weight: 400;"></textarea>
            </label>
          </div>

          <div class="input-form-container">
            <label class="label-form" for="">
                <span class="topic__field-label">{% trans "Exhibition works" %}</span>

                <div class="exhibition-works-list__container mscrollbar">
{#                  {% for i in '123456' %}#}
{#                  {% endfor %}#}

                  <a class="exhibition-works__button-add">
                    <i class="icon icon--sicon-add-cirlce"></i>
                    <p>{% trans "Add work" %}</p>
                  </a>
                </div>
            </label>
          </div>

          <div class="input-form-container">
            <label class="label-form" for="list-radio">
                <span class="topic__field-label">{% trans "Select box" %}</span>
                <div class="list-radio" id="list-radio">

                  <div class="radio-component">
                    <label class="input-radio">
                      <input type="radio" name="content-radio-box" index="0" data-value="" checked/>
                      <div class="check-mark" style="top: 0;"></div>
                    </label>

                    <input type="text" class="form-control" id="id_input-checkbox"
                    placeholder=' {% trans "Ask the artist of this work to make a new work" %}' name="input-checkbox">

                    <div class="radio-action">
                      <div class="radio-action-button-container">
                        <span class="button-move_radio">
                          <i class="icon icon--sicon-equal"></i>
                        </span>
                        <span class="button-delete_radio">
                          <i class="icon icon--sicon-trash"></i>
                        </span>
                      </div>
                    </div>
                  </div>

                  <div class="radio-component">
                    <label class="input-radio">
                      <input type="radio" name="content-radio-box" index="1" data-value=""/>
                      <div class="check-mark" style="top: 0;"></div>
                    </label>

                    <input type="text" class="form-control" id="id_input-checkbox"
                    placeholder='{% trans "Do not request this item" %}' name="input-checkbox">

                    <div class="radio-action">
                      <div class="radio-action-button-container">
                        <span class="button-move_radio">
                          <i class="icon icon--sicon-equal"></i>
                        </span>
                        <span class="button-delete_radio">
                          <i class="icon icon--sicon-trash"></i>
                        </span>
                      </div>
                    </div>
                  </div>

                  <a class="list-radio__add-action">
                    <i class="icon icon--sicon-add-cirlce"></i>
                    <p>{% trans "Add select box" %}</p>
                  </a>
                </div>
            </label>
          </div>

          <div class="input-form-container">
            <label class="label-form" for="">
              <span class="topic__field-label">{% trans "toggle" %}</span>

              <div class="list-toggle-input">
                <div class="toggle-input-container">
                  <div class="form-check custom-switch">
                    <label class="form-check-label">
                      <div class="form-check-group" style="margin-bottom: 0px;">
                        <input class="form-check-input switch-checkbox switch-toggle-account" type="checkbox" name="switch-toggle-account"><span class="switch-slider"></span>
                      </div>
                    </label>
                  </div>
                  <input type="text" placeholder='{% trans "Request an English translation" %}' class="form-control" id="inputbox-toggle" name="inputbox-toggle">
                  <div class="toggle-input-action">
                    <div class="toggle-input-button-container">
                      <span class="button-move_toggle">
                        <i class="icon icon--sicon-equal"></i>
                      </span>
                      <span class="button-delete_toggle">
                        <i class="icon icon--sicon-trash"></i>
                      </span>

                    </div>
                  </div>
                </div>

                <a class="list-toggle__add-action">
                  <i class="icon icon--sicon-add-cirlce"></i>
                  <p>{% trans "Add toggle" %}</p>
                </a>
              </div>
            </label>
          </div>

          <div class="topic-form-2__footer-container">
            <button type="button" class="btn btn--tertiary button-cancel-form2" style="margin-right: 16px;">{% trans "cancel" %}</button>
            <button type="submit" class="btn btn--primary button-submit-form2">OK</div>
          </div>

        <!-- </form> -->
      </div>
      </div>
      <!-- End form 2 -->

      <!-- Form 3 -->
      <div class="topic-container__topic-form-3">
        <form action="">
          <div class="topic-form-3__upload-file-work">
              <label class="label-form" for="">
                <span class="topic__field-label">{% trans "Register your work" %}</span>
                <div class="account_upload-file mattach mattach-form">
                  <div class="mcomment-attached">
                    <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
                      <div class="mattach-previews mattach-previews-form collection">
                        <div class="mattach-template mattach-template-form collection-item item-template">
                          <div class="mattach-info" data-dz-thumbnail="">
                            <div class="mcommment-file">
                              <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                              <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                              <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                      class="icon icon--sicon-close"></i>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="dropzoneUpLoadWork" class="fallback dropzone">
                  </div>
                </div>
              </label>
          </div>
          <div class="input-form-container">
            <label class="label-form" for="">
                <span class="topic__field-label">{% trans "Type of work" %}</span>
                <label class="input-radio">
                  <input type="radio" name="content-work"
                         value="2mix" checked
                         index="0" required="true"
                         id="2mix-radio-btn" data-value="0"/> 2mix
                  <div class="check-mark"></div>
                </label>
                <label class="input-radio">
                  <input type="radio" name="content-work"
                         value="music"
                         index="1" required="true"
                         id="music-radio-btn" data-value="0"/> MUSIC
                  <div class="check-mark"></div>
                </label>
                <label class="input-radio">
                  <input type="radio" name="content-work"
                         value="sound-effects"
                         index="2" required="true"
                         id="sound-effects-radio-btn" data-value="0"/> SOUND EFFECTS
                  <div class="check-mark"></div>
                  </label>

                <label class="input-radio">
                  <input type="radio" name="content-work"
                         value="voice"
                         index="3" required="true"
                         id="voice-radio-btn" data-value="0"/> VOICE
                  <div class="check-mark"></div>
                </label>

            </label>
          </div>

          <div class="input-form-container">
            <label class="label-form" for="id_work_title">
                <span class="topic__field-label">{% trans "Title" %}</span>
                <input type="text" class="form-control topic__work-title" id="id_work_title"
                placeholder='{% trans "Topic title" %}' name="work_title">
            </label>
          </div>
          <div class="input-form-container" style="margin-bottom: 41px;">
            <label class="label-form" for="id_artist_name">
                <span class="topic__field-label">{% trans "artist name" %}</span>
                <input type="text" class="form-control topic__constituent-elements" id="id_artist_name"
                placeholder='{% trans "Topic title" %}' name="artist_name">
            </label>
          </div>

          <div class="topic-form-3__footer-container">
            <div class="btn btn--primary button-submit-form3">OK</div>
          </div>
        </form>
      </div>
      <!-- End form 3 -->
    </div>
    {% include 'messenger/_modal_open_file.html' %}
  </main>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
    {% compress js inline %}
    <script src="{% static 'js/cropper.min.js' %}"></script>
        {% endcompress %}
    <script src="{% url 'javascript-catalog' %}"></script>
{% compress js inline %}
  <script src="{% static 'js/topic_action.js' %}"></script>
  <script src="{% static 'js/utils.js' %}"></script>
  <script src="{% static 'js/album_preview.js' %}"></script>
    {% endcompress %}
{% endblock %}

{% comment %} <div class="tocpic-form-1__upload-video">
  <div class="account_upload-file mattach mattach-form">
    <div class="mcomment-attached">
      <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
        <div class="mattach-previews mattach-previews-form collection">
          <div class="mattach-template mattach-template-form collection-item item-template">
            <div class="mattach-info" data-dz-thumbnail="">
              <div class="mcommment-file">
                <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                        class="icon icon--sicon-close"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="dropzoneUpLoadVideoTopic" class="fallback dropzone">
    </div>
  </div>
</div> {% endcomment %}
