{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load user_agents %}
{% load static %}
{% load compress %}

{% block extrahead %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    {% compress css %}
<style type="text/css">
    .border-width-button {
        border-width: inherit;
    }

    .editor-datetime {
        position: relative;
    }

    .audio_form {
        margin-top: 5em;
    }

    .upload_sample form {
        margin-top: 15%;
        margin-left: 25%;
        width: 50%;
    }

    .button-upload {
        margin: 0 20%;
        width: 60%;
    }

    .button-upload:hover {
        background: linear-gradient(45deg, rgba(0, 138, 194, 0.73), #009fc6);
        box-shadow: 2px 3px 10px #b9d4d6;
    }

    .audio-control__playpause {
        margin: 0 40%;
        width: 20%;
        height: calc(1.2vw + 1.2vh);
    }

    .profile__audio {
        background: transparent;
        padding: 15px 0;
    }

    .audio-control__playpause {

    }
    footer {
        bottom: 0;
        position: absolute;
    }
</style>
    {% endcompress %}
{% endblock %}
{% block content %}
    <div class="upload_sample container">
        <form method="post" action="{% url 'app:audio_add' creator_id %}" class="col-sm-6 audio_form" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="form-group">
              <label for="name">ファイル名</label>
              {{form.name|add_class:"form-control"}}
            </div>
            <div class="hide form-group select-container select_role sumo-select">
              <label>肩書き</label>
              <select class="select__value-list form-control" name="role" data-placeholder="…">
                <option value="music" selected="selected">Music</option>
                <option value="sound effect">Sound Effect</option>
                <option value="voice">Voice</option>
              </select>
            </div>
            {{ form.type|append_attr:"style:display:none;"}}

            <input type="hidden" name="user_id" value="{{creator_id}}">

            <div class="form-group">
              <label for="audio">
                アップロードする
              </label>
              {{ form.audio|append_attr:"accept:.mp3" }}
            </div>

            {% buttons %}
            <input type="submit" value="UPLOAD" class="button button--gradient button--gradient-primary button--round border-width-button button-upload"/>
            {% endbuttons %}
        </form>
    </div>
    {% compress js inline %}
    {% if request|is_pc %}
    <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    {% else %}
    <script type="text/javascript" src="{% static 'js/wavesurfer.js' %}"></script>
    {% endif %}

<script>
    $(document).ready(function(){
        var storefile = 'false'
        $('#id_audio').on('click', function () {
            if(storefile !='false'){
                storefile = this.files
            }
        })

        var wavesurfer = WaveSurfer.create({
            container: '#waveformx',
            waveColor: '#a7a8a9',
            progressColor: '#009ace',
            backgroundColor: '#fafbfb',
            cursorColor: 'transparent',
            barWidth: 2,
            barHeight: 1,
            barGap: 1,
            barRadius: 20,
            // backend: 'MediaElement',
            mediaControls: true,
            height: 60,
            partialRender: true,
        });

        $('#id_audio').on('change', function(){
            if (this.files && this.files[0] && this.files[0].name.match(/\.(mp3|wav|ogg|MP3|WAV|OGG)$/)) {
                var reader = new FileReader();
                var file = this.files[0];


                // Load a track by index and highlight the corresponding link
                $('.profile__audio').show();

                var setCurrentSong = function() {
                    wavesurfer.empty();
                    wavesurfer.load(URL.createObjectURL(file));
                };

                wavesurfer.on('ready', function() {
                    $('.audio-remain').text( formatTime(wavesurfer.getDuration()) );
                    // wavesurfer.play(); // Autoplay
                    $('.audio-control__playpause').off().on('click', function(){
                      wavesurfer.playPause();
                    });

                });

               // Check Playpause button
                wavesurfer.on('pause', function () {
                    $('.audio-control__playpause').removeClass('active');
                });

                wavesurfer.on('play', function () {
                    $('.audio-control__playpause').addClass('active');
                });

                  // Display player time
                wavesurfer.on('audioprocess', function () {
                    if ( wavesurfer.getDuration() - wavesurfer.getCurrentTime() > 0 ) {
                      $('.audio-remain').text( formatTime(wavesurfer.getDuration() - wavesurfer.getCurrentTime()) );
                    } else {
                      $('.audio-remain').text('0:00' );
                    }
                });

                var formatTime = function (time) {
                    return [
                      Math.floor((time % 3600) / 60), // minutes
                      ('00' + Math.floor(time % 60)).slice(-2) // seconds
                    ].join(':');
                };

                wavesurfer.on('error', function(e) {
                    console.warn(e);
                });

                setCurrentSong();
            } else {
                if (storefile == 'false'){
                    alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
                    $('.profile__audio').hide();
                    $('#id_audio').val('').clone(true);
                } else {
                    this.files = storefile;
                }
            }
        })
    })

    $('#id_type').val($('.select__value-list option:selected').val());
    $('.select__value-list').on('change', function() {
      $('#id_type').val($('.select__value-list option:selected').val());
    });
</script>
    {% endcompress %}
{% endblock content %}
