{% load static %}
{% load i18n %}
{% load util %}

<div class="choice-component">
  <div class="choice-content" data-choice-id="{{ selection.pk }}" data-selection="{{ selection.pk }}">
    <div class="choice-title">{{ selection.title }}</div>
    <div class="choice-description">{{ selection.description }}</div>
    <div class="choice-list-media">

      {% for sale_content_selection in selection.salecontentselection_set.all %}
        {% with sale_content_selection.sale_content as sale_content %}
          {% with sale_content|get_artist_of_sale_content as artist %}
            {% for album_variation in sale_content|get_audios:user %}
              <div class="choice-list-media__component"
                   style="{{ sale_content_selection|get_thumbnail_sale_content_selection }}"
                   data-thumbnail="{{ sale_content|get_thumbnail_sale_content:user }}"
                   data-sound="{{ album_variation|get_audio:user }}"
                   data-color="{{ sale_content_selection|get_thumbnail_sale_content_selection }}"
                   data-type="{{ sale_content.last_published_version.content_type }}"
                   data-artist="{{ artist.get_display_name }}"
                   data-sale="{{ sale_content.pk }}" data-name="{{ sale_content|get_title_sale_content:user }}"
                   data-sale-item="{{ sale_content_selection.pk }}"
                   data-file-type="{{ album_variation|get_file_type:user }}">
              <div class="choice-list-color__style">background: #{{ sale_content_selection|get_thumbnail_sale_content_selection }}</div>
              <div class="choice-list-color__value">{{ sale_content_selection.default_thumbnail }}</div>
              </div>

            {% endfor %}
          {% endwith %}
        {% endwith %}
      {% endfor %}

    </div>
    <div class="data-container data-radio-container">
      {% for option in selection.get_selection_content %}
        <div class="data-radio-component"
             {% if option.status == 'on' %}data-checked="true"{% endif %}>{{ option.detail }}</div>
      {% endfor %}
    </div>
    <div class="data-container data-toggles-container">
      {% for option in selection.get_toggle_content %}
        <div class="data-toggle-component"
             {% if option.status == 'on' %}data-checked="true"{% endif %}>{{ option.detail }}</div>
      {% endfor %}
    </div>
  </div>
  <div class="choice-action">
    <div class="choice-action__button-container">
                                        <span class="button-move_choice">
                                            <i class="icon icon--sicon-equal"></i>
                                        </span>
      <span class="button-delete_choice">
                                            <i class="icon icon--sicon-trash"></i>
                                        </span>
    </div>
  </div>
</div>
