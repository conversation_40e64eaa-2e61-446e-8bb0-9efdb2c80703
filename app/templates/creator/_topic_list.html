{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}
{% if can_edit or not can_edit and topics.exists %}
<div class="gallery__topics-container {% if can_edit %} editable{% endif %}">
  <div class="gallery__list-topics" data-topic-ids="{{ topic_ids }}" data-total-page="{{ total_page }}">
      <div class="splide__track" >
        <div class="splide__list nice-scroll">
            {% include 'creator/_load_more_topics.html' with topics=topics user=user action=action %}
            {% if total_page < 2 and can_edit %}
              <a class="list-topics__button-add-topic splide__slide" href="{% if gallery %}/gallery/create_topic_form{% else %}/gallery/create_topic_form?artist_id={{artist_id}}{% endif %}">
                <div class="list-topics__button-add-topic__content">
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M31.9997 5.33594C17.2797 5.33594 5.33301 17.2826 5.33301 32.0026C5.33301 46.7226 17.2797 58.6693 31.9997 58.6693C46.7197 58.6693 58.6663 46.7226 58.6663 32.0026C58.6663 17.2826 46.7197 5.33594 31.9997 5.33594ZM42.6663 34.6693H34.6663V42.6693C34.6663 44.1359 33.4663 45.3359 31.9997 45.3359C30.533 45.3359 29.333 44.1359 29.333 42.6693V34.6693H21.333C19.8663 34.6693 18.6663 33.4693 18.6663 32.0026C18.6663 30.5359 19.8663 29.3359 21.333 29.3359H29.333V21.3359C29.333 19.8693 30.533 18.6693 31.9997 18.6693C33.4663 18.6693 34.6663 19.8693 34.6663 21.3359V29.3359H42.6663C44.133 29.3359 45.333 30.5359 45.333 32.0026C45.333 33.4693 44.133 34.6693 42.6663 34.6693Z" fill="#F0F0F0"/>
                  </svg> <p>トピックを追加</p>
                </div>
              </a>
            {% endif %}
        </div>
      </div>
  </div>
</div>
{% endif %}
