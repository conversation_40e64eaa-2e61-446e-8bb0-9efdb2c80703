{% load static %} {% load util %}

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TEST | SOREMO</title>
  {% include '_gtag_head.html' %}
  <!-- material Icon -->
  <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
  <!-- css読み込み -->
  <link rel="stylesheet" type="text/css" href="{% static 'css/soremo_style_2024.css' %}" />
  <link rel="stylesheet" type="text/css" href="{% static 'css/__test.css' %}" />
</head>

<body>

  <section class="u-col-center p-project-members">
    <section class="c-group u-bg-blue u-w100 u-col u-gap8">
      {% for master_client in master_clients %}
        <div class="u-row-between p-lists-user">
          <div class="u-col u-gap8">
            <p class="heading-13-spacing u-text-black u-mb0">{{ master_client.get_display_name }}</p>
            <div class="u-col u-gap6">
              <p class="bodytext-11 u-line-height-100 u-mb0">{{ master_client.position|hide_if_empty }}<span class="u-text-light-gray">（責任者）</span></p>
              <p class="bodytext-11 u-line-height-100 u-text-light-gray u-mb0">{% if master_client.enterprise %} {{ master_client.enterprise }}{% endif %}</p>
            </div>
          </div>
          <div class="u-relative">
            <span class="material-symbols-rounded u-text-blue c-icon-more-horiz">more_horiz</span>
            <ul class="c-dropdown">
              {% for project_member in project_members %}
                {% if project_member.user.id == master_client.id %}
              {% comment %} チェックできる場合 {% endcomment %}
                  {% if project_member.is_favorite %}
                    <li class="u-row-between">
                      閲覧のみに設定<span class="material-symbols-rounded u-text-border">edit</span>
                    </li>
                  {% else %}
                    <li class="u-row-between u-text-blue">
                      閲覧のみ（コメント不可）<span class="material-symbols-rounded u-text-blue">edit_off</span>
                    </li>
                  {% endif %}
                {% else %}
                {% endif %}
              {% endfor %}

              {% comment %} IP制限を設定している場合 {% endcomment %}
              {% if master_client.is_check_ip %}
                <li class="u-row-between u-text-blue">
                  IP制限を設定中<span class="material-symbols-rounded u-text-blue">
                    lock
                  </span>
                </li>
              {% else %}
                <li class="u-row-between">
                  IP制限を設定<span class="material-symbols-rounded u-text-border">
                    lock_open
                  </span>
                </li>
              {% endif %}
              <li class="u-row-between">
                プロジェクトから退出<span class="material-symbols-rounded">
                  exit_to_app
                </span>
              </li>
            </ul>
          </div>
        </div>
      {% endfor %}
    </section>
  </section>

  <!-- js読み込み -->
  <script type="text/javascript">
    window.CSRF_TOKEN = "{{ csrf_token }}";
  </script>
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <script type="text/javascript" src="{% static 'js/__test.js' %}"></script>
</body>