{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.css">
    {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/messenger_project.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/messenger_director.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
    {% compress css %}
  <style>
    .messenger__item.messenger__item-project {
      padding: 0;
    }

    .messenger__column-right {
      overflow: hidden;
    }
  </style>
    {% endcompress %}
  <main class="messenger-director">
    <div class="container">
      <div class="new-video-menu">
        <div class="project-list">
          <div class="project-item active">
              <div class="project-tab project-tab-processing active">
                    <div class="messenger-director-processing">
                        <div class="row messenger__column">
                            <div class="col-md-5 col-sm-5 messenger__column-left">
                                <div class="messenger__list custom-scrollbar">
                                  {% if user.role == 'master_admin' %}
                                    {% if offer_products.exists %}
                                      {% for offer_product in offer_products %}
                                      {% if offer_product.project %}
                                        {% with offer_product|is_project_seen:user as seen %}
                                          {% include 'direct/_messenger_item.html' with offer_product=offer_product seen=seen user=user is_done=is_done case='1'%}
                                        {% endwith %}
                                      {% elif offer_product.status == '1' %}
                                          {% with offer_product|is_project_seen:user as seen %}
                                              {% include 'direct/_messenger_item.html' with offer_product=offer_product seen=seen user=user is_done=is_done case='2' %}
                                          {% endwith %}
                                      {% else %}
                                            {% with offer_product|is_project_seen:user as seen %}
                                                {% include 'direct/_messenger_item.html' with offer_product=offer_product seen=seen user=user is_done=is_done case='3' %}
                                            {% endwith %}
                                        {% endif %}
                                      {% endfor %}
                                    {% endif %}
                                  {% elif user.role == 'master_client' %}
                                    {% if status == 'new' %}
                                      <div class="messenger__item1 {% if not offers %} offers-none {% endif %} btn-create-offer">
                                        <a class="messenger-popup background"
                                           href='javascript:;' data-toggle='modal' data-target='#messenger-popup'>
                                            <img src="{% static "images/icon-plus.svg" %}" alt="">
                                            <p>新規で相談</p>
                                        </a>
                                      </div>
                                    {% endif %}
                                    {% if  offer_products.exists %}
                                      {% for offer_product in offer_products %}
                                        {% with offer_product as offer %}
                                          {% if offer.project %}
                                            {% with offer|is_project_seen:user as seen %}
                                              {% include 'direct/_messenger_item.html' with offer_product=offer_product seen=seen user=user case='4' %}
                                            {% endwith %}
                                          {% elif offer.status == '1' %}
                                            {% with offer|is_project_seen:user as seen %}
                                              {% include 'direct/_messenger_item.html' with offer_product=offer_product seen=seen user=user case='5' %}
                                            {% endwith %}
                                          {% else %}
                                            {% with offer|is_project_seen:user as seen %}
                                              {% with offer.variation_offer.first.owner as owner %}
                                                {% include 'direct/_messenger_item.html' with offer_product=offer_product seen=seen owner=owner user=user case='6' %}
                                              {% endwith %}
                                            {% endwith %}
                                          {% endif %}
                                      {% endwith %}
                                      {% endfor %}
                                    {% endif %}
                                  {% endif %}
                                    {% if has_done and not is_done or has_progress and is_done %}
                                        <div class="show_done" style="height: 150px"></div>
                                    {% endif %}
                                </div>
                                <div class='carosel-pdf hide'></div>
                                {% if has_done and not is_done or has_progress and is_done %}
                                    <div class="project-progress-action" style="z-index: 12">
                                        <div class="project-progress-action-content">
                                            <div class="project-progress-action-notice">
                                                {% if is_done %}
                                                    <div class="project-progress-action-message">やりとり中の見積相談はこちら。</div>
                                                {% else %}
                                                    <div class="project-progress-action-message">完了したプロジェクトはこちら。</div>
                                                {% endif %}
                                            </div>
                                            <div class="project-progress-action-btn">
                                                <a class="button button--gradient button--gradient-primary button--round"
                                                   href="{% if is_done %}{% url 'app:direct_inbox' %}{% else %}{% url 'app:direct_inbox' %}?is_done=1{% endif %}"
                                                   role="button">{% if is_done %}戻る{% else %}領収書をみる{% endif %}</a>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-7 col-sm-7 messenger__column-right">
                            </div>
                        </div>
                    </div>
                  {% if user.role == 'master_client' %}
                    <div class="messenger-popup modal fade animate" id="messenger-popup" role="dialog">
                      <form method="post" action="{% url 'app:direct_inbox' %}" id="create_inbox"
                            enctype="multipart/form-data">
                        <div class="messenger-popup__content">
                          <div class="messenger-popup__form">
                            <div class="messenger-popup__form-text">
                              お問い合わせありがとうございます。<br>動画や資料をこちらからお送り下さい。<br>機密厳守。お見積もりは無料です。
                            </div>
                            {% csrf_token %}
                            <div class="messenger-form__confirm align-center">
                              <div class="checkbox input-checkbox">
                                <input type="checkbox" name="contract-confirm" id="contract-confirm">
                                <label for="contract-confirm">
                                  <a href="#">販売同意書</a>を確認しました。
                                </label>
                              </div>
                            </div>
                            <div class="messenger-popup__form-input input-description hide">
                              <div style="text-align: left; margin-bottom: 10px;">ご相談内容</div>
                              <textarea name="messenger-input" placeholder="（例)&#10新規ゲームタイトルの音付けを相談したいです。添付企画書を踏まえて、ナレーションは、候補をいくつか提案ください。楽曲イメージは、仕様書を元にお任せします。"
                                        class="messenger-detail__input-text cs-textarea autoExpand"
                                        id="description"
                                        style="height: 250px;"></textarea>
                            </div>
                            <div class="messenger-popup__form-input">
                              <div class="messenger-popup__file">
                                <div class="messenger-popup__file-content">
                                  <input class="messenger-popup__file-input hide" type="file"
                                         name="files[]"
                                         id="file-create" multiple>
                                  <button class='messenger-popup__file-btn button--gradient background button--background-gray button--round button--small hide'>
                                    ファイルを添付
                                  </button>
                                </div>
                              </div>
                            </div>
                            <br><br>
                            <div class="messenger-popup__form-input input-date hide">
                              <div style="text-align: left; margin-bottom: 10px;"> 希望納期</div>
                              <input class="form-control form-group form-datepicker from_date_datepicker" type="text"
                                     name="to-date"
                                     value=""
                                     id="to-date"/>
                            </div>
                            <div class="messenger-popup__form-input input-budget hide">
                              <div class="currency">
                                <div style="text-align: left; margin-bottom: 10px;"> 予算目安（税抜）</div>
                                <input class="form-control " type="number" min="0" name="budget"
                                       value="" id="budget"/><span>円</span>
                              </div>
                            </div>
                          </div>

                          <div class="messenger-popup__action">
                            <div class="messenger-popup__action-button">
                              <a class="gradient button--gradient button--gradient-primary button--round button--disabled"
                                 id="start">相談を始める
                              </a>
                              <button class="button button--gradient button--gradient-primary button--round hide"
                                      role="button" id="submit">この内容で相談
                              </button>
                              <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a>
                                へ電話で相談
                              </div>
                              <br>
                            </div>
                            <div class="messenger-popup__action-button">
                              <button style="background-color: white;" data-dismiss="modal"
                                      class="close-modal">キャンセル
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="messenger-popup modal fade animate" id="modal-add-card" role="dialog">
                      <div class="messenger-popup__content">
                        <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">支払い情報がまだ登録していませんので、<br>まず、支払い情報をご登録申し上げます。
                      </div>
                    </div>
                        <div class="messenger-popup__action">
                          <div class="messenger-popup__action-button">
                            <a class="gradient button--gradient button--gradient-primary button--round link-add--card"
                            >支払い情報を登録</a>
                            <br>
                          </div>
                          <div class="messenger-popup__action-button">
                            <button style="background-color: white;" data-dismiss="modal"
                                    class="close-modal">キャンセル
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="messenger-popup modal fade animate" id="modal-choose--card" role="dialog">
                        <div class="messenger-popup__content">
                          <div class="messenger-popup__form">
                            <br>
                            <div style="text-align: left"><span>合計: </span><span class="total-bill" style="position: absolute;left: 50%;"></span></div>
                          <div class="messenger-popup__action">
                            <div class="messenger-popup__action-button">
                              <button id="payment_submit" class="button button--gradient button--gradient-primary button--round button--disabled"
                                      role="button">決済する
                              </button>
                              <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a>
                                へ電話で相談
                              </div>
                              <br>
                            </div>
                            <div class="messenger-popup__action-button">
                              <button style="background-color: white;" data-dismiss="modal"
                                      class="close-modal">キャンセル
                              </button>
                            </div>
                          </div>
                        </div>
                        </div>
                    </div>
                    <div class="messenger-popup modal fade animate" id="payment-success-popup" role="dialog">
                  <div class="messenger-popup__content">
                    <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text"> 決済が完了しました。
                      </div>
                    </div>
                    <div class="messenger-popup__action">
                      <div class="messenger-popup__action-button">
                        <a class="gradient button--gradient button--gradient-primary button--round close-modal"
                           data-dismiss="modal">OK</a>
                        <br>
                      </div>
                    </div>
                  </div>
                </div>
                  {% endif %}
                {% if user.role == 'master_admin' %}
                  <div class="messenger-popup-product modal fade animate" id="messenger-popup-product" role="dialog"
                       data-offer="">
                    <form method="post" action="{% url 'app:create_product' %}" id="create_product"
                          enctype="multipart/form-data">
                      {% csrf_token %}
                      <div class="messenger-popup__content_plan">
                        <div class="messenger-popup__form">
                          <div class="messenger-popup__form-text">プロジェクトを作成
                          </div>
                        </div>
                        <input type="hidden" name="offer-active" id="offer-id" value="">
                        <label for="choice-project"> すでにあるプロジェクトを選択</label>
                        <input type="checkbox" id="choice-project" name="choice-project">
                        <br>
                        <label style="text-align: left">プロジェクト名</label>
                        <div class="messenger-popup__form-input create-new-project">
                          <input type="text" class="messenger-input-text input-project" name="project-name"
                                 id="project-name"
                                 placeholder="プロジェクト名">
                        </div>
                        <div class="form-group select-old-project hide">
                          <select class="messenger-input-text" name="old_product" id="old_products"
                                  style="background-color: #FFFFFF;max-width: 80%;height: 30px;">
                            {% for product in product_exist %}
                              <option data-value="{{ product.pk }}"
                                      data-name="{{ product.name }}">{{ product.name }}</option>
                            {% endfor %}
                          </select>
                        </div>
                        <div class="messenger-popup__action">
                          <div class="messenger-popup__action-button">
                            <a class="button button--gradient button--gradient-primary button--round"
                               href="#"
                               role="button" id="submit-create-product">登録</a>
                          </div>
                          <div class="messenger-popup__action-button">
                            <a class="button button--text button--text-gray close-modal" href="#" role="button"
                               data-dismiss="modal" style="background-color: white;">キャンセル</a>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                  <div class="messenger-popup-product modal fade animate" id="messenger-popup-bill" role="dialog"
                       data-offer="">
                    <form method="post" action="{% url 'app:upload_bill' %}" id="upload_bill"
                          enctype="multipart/form-data">
                      {% csrf_token %}
                      <div class="messenger-popup__content_plan">
                        <input type="hidden" name="offer-active" class="offer-active" value="">
                        <div class="messenger-popup__form-input">
                          <label for="project-name" style="text-align: left">合計</label>
                          <input type="number" class="messenger-input-text input-project" name="project-amount"
                                 id="project-amount"
                                 placeholder="amount" style="text-align: right; border: none;" min="50" max="99999999"><span> 円</span>
                        </div>
                        <div class="messenger-popup__form-input">
                            <div class="messenger-popup__file">
                              <div class="messenger-popup__file-content">
                                <input class="messenger-popup__file-input hide" type="file" name="bill"
                                       id="upload-bill-file" accept="application/pdf">
                                <label for="upload-bill-file" class='messenger-popup__file-btn button--gradient background button--background-gray button--round button--small'>
                                  ファイルを選択
                                </label>
                                <div class="bill-file"></div>
                              </div>
                            </div>
                          </div>
                        <div class="messenger-popup__action">
                          <div class="messenger-popup__action-button">
                            <a class="button button--gradient button--gradient-primary button--round button--disabled"
                               href="#"
                               role="button" id="submit-upload-bill">アップロード</a>
                          </div>
                          <div class="messenger-popup__action-button">
                            <a class="button button--text button--text-gray close-modal" href="#" role="button"
                               data-dismiss="modal" style="background-color: white;">キャンセル</a>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                {% endif %}
                <div class="messenger-popup modal fade animate" id="create-offer-popup" role="dialog">
                  <div class="messenger-popup__content">
                    <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">資料及びオーダーを承りました。<br>内容を確認の上、お見積もりプランをご提案しますので、お待ちください。
                      </div>
                    </div>
                    <div class="messenger-popup__action">
                      <div class="messenger-popup__action-button">
                        <a class="gradient button--gradient button--gradient-primary button--round close-modal"
                           data-dismiss="modal">OK</a>
                        <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a> へ電話で相談
                        </div>
                        <br>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="messenger-popup modal fade animate" id="choose-plan-popup" role="dialog">
                  <div class="messenger-popup__content">
                    <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">注文を承りました。<br>契約書ドラフトをご用意しますので、お待ちください。
                      </div>
                    </div>
                    <div class="messenger-popup__action">
                      <div class="messenger-popup__action-button">
                        <a class="gradient button--gradient button--gradient-primary button--round close-modal"
                           data-dismiss="modal">OK</a>
                        <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a> へ電話で相談
                        </div>
                        <br>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="messenger-popup modal fade animate" id="accept-contact-popup" role="dialog">
                  <div class="messenger-popup__content">
                    <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">注文を承りました。<br>プロジェクトを御用意しますので、お待ちください。
                      </div>
                    </div>
                    <div class="messenger-popup__action">
                      <div class="messenger-popup__action-button">
                        <a class="gradient button--gradient button--gradient-primary button--round close-modal"
                           data-dismiss="modal">OK</a>
                        <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a> へ電話で相談
                        </div>
                        <br>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  </main>
    {% compress js inline %}
  <script>
      let is_pc = '{{ request|is_pc }}' === 'True';
      var dict_chatsocket = new Map();
      var sk_protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
      var socket_url = sk_protocol + window.location.host;

      function initSocket(offer_id) {
          let current_chatsocket = dict_chatsocket.get(offer_id);
          if (!current_chatsocket) {
              current_chatsocket = new WebSocket(
                  socket_url + '/ws/messenger/' + offer_id);
              dict_chatsocket.set(offer_id, current_chatsocket);
          }
          current_chatsocket.onmessage = function (e) {
              let data = JSON.parse(e.data);
              let offer_id = data.event.offer_id;
              let target_offer;
              let get_offer = $('.offer-' + offer_id);
              let current_receiver = '';
              if ($('.messenger__item.messenger__item--selected').length > 0) {
                  current_receiver = $('.messenger__item.messenger__item--selected').data('offer').toString();
              }

              let receiver = "";
              $('.messenger__item').each(function () {
                  let offers = $(this).attr('data-offers');
                  if (offers.includes(String(offer_id))) {
                      target_offer = $(this);
                      return false;
                  }
              });

              switch (data.event.action) {
                  case 'new_message':
                      let message = JSON.parse(data.event.message)[0].fields;
                      let role = '{{  user.role }}';
                      let old_version = '';
                      let role_owner = data.event.owner_role;
                      let new_version = data.event.new_version;
                      if (new_version === false) {
                          old_version = 'gray'
                      }

                      receiver = data.event.list_user.toString();
                      if (current_receiver === offer_id) {
                          let owner_avatar;
                          if (data.event.owner_avatar) {
                              owner_avatar = data.event.owner_avatar;
                          } else {
                              if (role_owner === 'master_admin') {
                                  owner_avatar = '{% static 'images/default-avatar-master-admin.png' %}';
                              } else {
                                  owner_avatar = '{% static 'images/default-avatar-client.png' %}';
                              }
                          }
                          let variation_id = message.variation;
                          let type = message.type;
                          let class_role = 'messenger-director__item--left';
                          if (role_owner === '{{ user.role }}') {
                              class_role = 'messenger-director__item--right'
                          }

                          let new_message = `<div class="messenger-director__item">
                                    <div class="messenger-director__item-content">
                                        <div class="messenger-director__item-avatar background-avt">
                                            <img class="messenger-director__item-avatar-img"
                                                 src="${owner_avatar}" alt="">
                                            <div class="messenger-director__item-time"></div>
                                        </div>
                                        `;
                              if (message.type !== '1') {
                                  new_message += `<div class="messenger-director__item-info">
                                            <div class="video-pin-time ${old_version}" data-variation="${variation_id}"
                                                  data-type="${type}">
                                            </div>
                                            <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                            <div class="messenger-director__item-seen background-avt"></div>
                                        </div>
                                    </div>
                                </div>`;
                              } else {
                                  new_message += `<div class="messenger-director__item-info">
                                            <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                            <div class="messenger-director__item-seen background-avt"></div>
                                        </div>
                                    </div>
                                </div>`;
                              }

                              $(new_message).insertBefore(get_offer.find('.messenger-director__item-reply'));
                              if (message.real_name) {
                                  $(`<div style="justify-content: center;display: flex;padding-bottom: 10px;">
                                       <a class="messenger-director__item-file" target="_blank"
                                       style="display: contents;" href="${message.file}">
                                       ${message.real_name}</a></div>`).insertBefore(get_offer.find('.messenger-director__item-action'))
                              }

                          if (message.owner !== {{ user.pk }}) {
                              get_offer.find('.messenger-director__list').addClass('not-seen');
                              target_offer.addClass('messenger__item--new');
                              target_offer.find('.messenger__tag').removeClass('hide');
                              target_offer.find('.messenger__seen').addClass('hide');
                              if (parseInt(target_offer.find('.messenger__count').html()) > 0) {
                                  let count_message = parseInt(target_offer.find('.messenger__count').html()) + 1;
                                  target_offer.find('.messenger__count').html(count_message)
                              } else {
                                  target_offer.find('.messenger__count').removeClass('hide');
                                  target_offer.find('.messenger__count').html('1')
                              }
                          }
                          $('.messenger-detail-content').mCustomScrollbar('scrollTo', 'last', {
                              scrollInertia: 300
                          });
                      } else {
                          if (target_offer.length) {
                              get_offer.find('.messenger-director__list').addClass('not-seen');
                              target_offer.addClass('messenger__item--new');
                              target_offer.find('.messenger__tag').removeClass('hide');
                              target_offer.find('.messenger__seen').not('.hide').addClass('hide');
                              if (parseInt(target_offer.find('.messenger__count').html()) > 0) {
                                  let count_message = parseInt(target_offer.find('.messenger__count').html()) + 1;
                                  target_offer.find('.messenger__count').html(count_message)
                              } else {
                                  target_offer.find('.messenger__count').removeClass('hide');
                                  target_offer.find('.messenger__count').html('1')
                              }
                          }
                      }
                      break;
                  case 'seen':
                      let viewer_role = data.event.viewer_role;
                      let viewer_avt = data.event.viewer_avt;
                      let viewer_id = data.event.viewer_id;
                      if (!viewer_avt) {
                          if (viewer_role === 'master_admin') {
                              viewer_avt = '{% static 'images/default-avatar-master-admin.png' %}';
                          } else {
                              viewer_avt = '{% static 'images/default-avatar-client.png' %}';
                          }
                      }
                      $('.video-comment-seen-item-img[data-user^=' + viewer_id + ']').addClass('hide');
                      let img_seen = ` <img class="video-comment-seen-item-img ${viewer_role}" data-user=${viewer_id}
                                                 src="${viewer_avt}" alt="">`;

                      let message_last = get_offer.find('.messenger-director__item-seen').last();

                      message_last.find('.video-comment-seen-item-img').removeClass('hide');
                      if ({{ user.pk }} !== data.event.viewer_id) {
                          get_offer.find('.messenger-director__list[data-user^=' + viewer_id + ']').removeClass('not-seen');
                          message_last.append(img_seen);
                      }
                      if (target_offer.data('user') === viewer_id) {
                          target_offer.find('.messenger__tag').addClass('hide');
                          target_offer.removeClass('messenger__item--new');
                          target_offer.find('.messenger__seen').removeClass('hide');
                          target_offer.not('.messenger__item--new').find('.messenger__count').html('0');
                          target_offer.not('.messenger__item--new').find('.messenger__count').addClass('hide');
                          target_offer.find('.messenger__seen').removeClass('hide');
                      }

                      break;
                  default:
                      break;
              }
          };
          current_chatsocket.onclose = function (e) {
              console.error('Chat socket closed!');
          };
      }

      if (is_logged_in === 'True') {
          initSocket({{ user.id }});
      }
  </script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
  <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/additional-methods.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/direct.js' %}"></script>
    {% endcompress %}
{% endblock %}
