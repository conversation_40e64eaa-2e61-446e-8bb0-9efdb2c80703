{% load static %}
{% load util %}
{% load i18n %}
{% with offer.get_bill_in_offer as file %}
  <div class="minfo-contract">
    <div class="minfo-contract-icon get-data-offer minfo-file_info" data-file-id="{{ file.pk }}"
             data-type="{{ file.is_audio_file }}"
             data-link="{{ file.file.url }}"
             data-name="{{ file.real_name }}"
             data-toggle="modal"
             data-target="#modal-{{ file.is_audio_file }}-popup"
             data-message-id="{{ file.message.pk }}">
      <i class="icon icon--sicon-accept"></i>
    </div>
    <div class="minfo-accept minfo-confirm" style="padding-top: 10px;">
      <div class="minfo-accept__policy" style="margin-bottom: 10px">
        <div class="form-check custom-checkbox file-name--confirm minfo-file_info" data-file-id="{{ file.pk }}"
             data-type="{{ file.is_audio_file }}"
             data-link="{{ file.file.url }}"
             data-name="{{ file.real_name }}"
             data-toggle="modal"
             data-target="#modal-{{ file.is_audio_file }}-popup"
             data-message-id="{{ file.message.pk }}"><i class="icon icon--sicon-clip"
                                                   style="line-height: unset"></i>{{ file.real_name }}
        </div>
      </div>
      <div class="minfo-accept__action payment--bill">
        <a class="btn btn--primary btn--blue btn--lg"
           href="javascript:void(0)">
          <span class="btn-text">{% trans "button payment" %}</span>
        </a>
      </div>
    </div>
  </div>
{% endwith %}
