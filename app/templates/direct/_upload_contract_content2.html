{% load i18n %}
{% load util %}

<div class="contract__form-wrap">
  <div class="contract__form-group">
    <div class="form-row row">
      <div class="col-sm-12 form-group" style="width: 100%;">
        <div class="account_upload-file mattach mattach-form mattach-form-messenger-owner">
          <div class="mcomment-attached">
            <div class="mattach-preview-container mattach-preview-container-form">
              <div class="mattach-previews mattach-previews-form collection">
                {% if form_contract_and_plan.id and form_contract_and_plan.creation_method == 'upload' and product_message_file %}
                <div class="mattach-template mattach-template-form collection-item item-template file-original">
                  <div class="mattach-info" data-dz-thumbnail="">
                    <div class="mcommment-file">
                      <div class="determinate" style="width: 100%;" data-dz-uploadprogress=""></div>
                      <div class="mcommment-file__name mcommment-file__name-form" data-dz-name="">{{ product_message_file.real_name }}<i class="icon icon--sicon-clip"></i></div>
                      <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i class="icon icon--sicon-close close-file-original"></i>
                      </div>
                    </div>
                  </div>
                </div>
                {% else %}
                <div class="mattach-template mattach-template-form collection-item item-template hide">
                  <div class="mattach-info" data-dz-thumbnail="">
                    <div class="mcommment-file">
                      <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                      <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                      <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                        <i class="icon icon--sicon-close"></i>
                      </div>
                    </div>
                  </div>
                </div>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="fallback dropzone" id="uploadFile">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    