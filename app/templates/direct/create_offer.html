{% extends "base_user_nofooter.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util %}
{% load i18n %}
{% load compress %}
{% block extrahead %}
<link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css"
        integrity="sha512-aOG0c6nPNzGk+5zjwyJaoRUgCdOrfSDhmMID2u4+OIslr0GjpLKo7Xm0Ao3xmpM4T8AmIouRkqwj1nrdVsLKEQ=="
        crossorigin="anonymous"/>
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/css/splide.min.css">
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
  {% endcompress %}
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css"/>
    {% compress css %}
        <link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/creator_style.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/topic_style.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/components/utils.css' %}"/>
        <link rel="stylesheet" href="{% static 'css/flatpickr_soremo.css' %}">
        <link rel="stylesheet" href="{% static 'css/soremo_style_2024.css' %}" />
        <link href="{% static 'css/order_step.css' %}" rel="stylesheet">
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
{% endblock %}

{% block content %}
    {% compress css %}
<style>
  html {
    background: #fff;
  }

  body {
    background-color: #fff !important;
  }

  .gallery__list-topics {
    justify-content: center
  }
</style>
    {% endcompress %}
  <div class="container-wrap" style="background: #ffffff;">
    {% include "creator/_modal_detail_topic.html" with user=user %}
    <div class="">
      <div class="order-step__main">
        <div class="order-step__heading">

        </div>

        <div class="" style="margin-bottom: 180px;">
          <form class="order-step__form row" id="form-order-step" method="post" action="" enctype="multipart/form-data"
                data-add-by-sale="{{ add_by_sale }}" data-hide-step-3="{{ hide_step_3 }}"
                data-contact-artist-profile="{{ contact_artist_profile }}" data-artist-name="{{ artist_name }}"
                data-name-file="{{ list_name_file }}" data-name-folder="{{ list_name_folder }}"
                {% if order_data %}data-order-id="{{ order_data.pk }}" data-artist-id="{{ order_data.artist.pk }}" data-sale-id="{{ order_data.sale_content.pk }}"
                data-message="{{ order_data.message }}" data-budget="{{ order_data.budget }}" data-sale-name="{{ order_data.sale_name }}"
                {% endif %}>
            {% csrf_token %}
            <!-- Step 1 -->
            <div class="order-step__form-tab {% if not add_by_sale %}active{% endif %}">
              <div class="order-step__form-heading">
                {% comment %} <p class="caption--11">{% trans "Choose the type of object." %}</p> {% endcomment %}
              </div>
              <div class="order-step__form-group">
                <div class="order-step__option">
                  {% include "creator/_topic_list.html" with user=user topics=topics action="create_offer" gallery=False topic_ids=topic_ids total_page=total_page %}
                </div>
              </div>
            </div>
            <!-- End step 1 -->

            <!-- Step 2 -->
            <div class="order-step__form-tab">
              <div class="order-step__form-heading">
                <h3 class="heading--18">{% trans "Contents" %}
                  {% comment %} <span class="caption--11">(2/3)</span> {% endcomment %}
                </h3>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-12 form-group">
                    <label for="id_message">
                      <div class="order-step__form-label heading--13">{% trans "message" %}<span
                              class="blue-label--8">{% trans "required" %}</span></div>
                    </label>
                    <div class="form-textarea">
                      {{ form.description }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-12 form-group">
                    <label for="id_message">
                      <div class="order-step__form-label heading--13">{% trans "label document" %}<span
                              class="grey-label--8">{% trans "any" %}</span></div>
                    </label>
                    <div class="order-step_upload-file mattach mattach-form">
                      <div class="mcomment-attached">
                        <div class="mattach-preview-container mattach-preview-container-form">
                          <div class="mattach-previews mattach-previews-form collection">
                            <div class="mattach-template mattach-template-form collection-item item-template">
                              <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                  <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                  <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                  <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                          class="icon icon--sicon-close"></i>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div id="uploadFile" class="fallback dropzone">
                      </div>
                      <p class="order-step__field-text">{% trans "You can send the entire folder by dragging and dropping." %}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-4 form-group">
                    <label for="id_budget">
                      <div class="order-step__form-label heading--13">{% trans "Hope budget" %}<span
                              class="grey-label--8">{% trans "any" %}</span>
                      </div>
                    </label>
                    <div class="order-step__form-budget">
                      <input type="text" name="budget" placeholder="1,000,000"
                             class="form-control order-step__input-text"
                             maxlength="19" id="id_budget" min="0">
                      <span class="bodytext--13">{% trans "yen without tax" %}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="order-step__submit order-step__action">
                <div class="container">
                  {% buttons %}
                    <button class="btn btn--tertiary btn-previous" id="btn__previous_2">{% trans "return" %}</button>
                    <button class="btn btn--primary btn-next" id="btn__next_2">{% trans "to the next" %}</button>
                  {% endbuttons %}
                </div>
              </div>
            </div>
            <!-- End step 2 -->

            <!-- Step 3 -->
            <div class="order-step__form-tab {% if add_by_sale and not hide_step_3 %}active{% endif %}">
              <div class="order-step__form-heading">
                <h3 class="heading--18">{% trans "Conditions" %}
                  {% comment %} <span class="caption--11">(3/3)</span> {% endcomment %}
                </h3>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-4 form-group">
                    <label for="deadline_date">
                      <div class="order-step__form-label heading--13">{% trans "Production period" %}<span
                              class="grey-label--8">{% trans "any" %}</span></div>
                    </label>
                    <div class="sform-group__input-group">
                      <input class="datePicker js-daterangepicker"
                             id="deadline_date" type="text" placeholder="yyyy/mm/dd ~ yyyy/mm/dd" autocomplete="off">
                      <label class="sform-group__append" for="deadline_date">
                        <i class="icon icon--undefined"></i>
                        <i class="icon icon--sicon-calendar"></i>
                      </label>
                    </div>
                  </div>
                  {{ form.start_time|append_attr:"style:display:none;" }}
                  {{ form.end_time|append_attr:"style:display:none;" }}
                </div>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-4 form-group">
                    <label for="id_dob">
                      <div class="order-step__form-label heading--13">{% trans "deadline" %}<span
                              class="grey-label--8">{% trans "any" %}</span></div>
                    </label>
                    <div class="form-row row form-row__mobile">
                      <div class="col-sm-8 form-row__mobile-date">
                        <div class="sform-group__input-group">
                          {{ form.deadline|append_attr:"style:display:none;" }}
                          <input type="text" name="deadline_1" placeholder="yyyy/mm/dd"
                                 class="mcalendar mcalendar--small form-control" id="id_deadline_1">
                          <label class="sform-group__append" for="id_deadline_1">
                            <i class="icon icon--undefined"></i>
                            <i class="icon icon--sicon-calendar"></i>
                          </label>
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <select class="input-time" name="hours" id="id_time" style="background: #FFFFFF;">
                          <option value="00:00" selected>00:00</option>
                          <option value="01:00">01:00</option>
                          <option value="02:00">02:00</option>
                          <option value="03:00">03:00</option>
                          <option value="04:00">04:00</option>
                          <option value="05:00">05:00</option>
                          <option value="06:00">06:00</option>
                          <option value="07:00">07:00</option>
                          <option value="08:00">08:00</option>
                          <option value="09:00">09:00</option>
                          <option value="10:00">10:00</option>
                          <option value="11:00">11:00</option>
                          <option value="12:00">12:00</option>
                          <option value="13:00">13:00</option>
                          <option value="14:00">14:00</option>
                          <option value="15:00">15:00</option>
                          <option value="16:00">16:00</option>
                          <option value="17:00">17:00</option>
                          <option value="18:00">18:00</option>
                          <option value="19:00">19:00</option>
                          <option value="20:00">20:00</option>
                          <option value="21:00">21:00</option>
                          <option value="22:00">22:00</option>
                          <option value="23:00">23:00</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-12 form-group">
                    <div class="order-step__form-label heading--13">{% trans "Handling of rights" %}</div>
                    <div class="order-step__sub-group">
                      {% for choice in form.contract_type %}
                        <div class="order-step__form-multi">
                          <label class="input-radio">
                            <input type="radio" name="{{ choice.data.name }}"
                                   {% if choice.data.selected %}checked{% endif %}
                                   value="{{ choice.data.value }}" index="{{ choice.data.index }}"
                                   required="{{ choice.data.attrs.required }}"
                                   id="{{ choice.data.attrs.id }}"
                                   data-value="{{ choice.data.label }}"/>{{ choice.data.label }}
                            <div class="check-mark"></div>
                          </label>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-12 form-group">
                    <div class="order-step__form-label heading--13">{% trans "contract" %}</div>
                    <div class="order-step__sub-group">
                      {% for choice in form.ownership_type %}
                        <div class="order-step__form-multi">
                          <label class="input-radio">
                            <input type="radio" name="{{ choice.data.name }}"
                                   {% if choice.data.selected %}checked{% endif %}
                                   value="{{ choice.data.value }}" index="{{ choice.data.index }}"
                                   required="{{ choice.data.attrs.required }}"
                                   id="{{ choice.data.attrs.id }}"
                                   data-value="{{ choice.data.label }}"/>{{ choice.data.label }}
                            <div class="check-mark"></div>
                          </label>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              </div>
              <div class="order-step__form-group">
                <div class="form-row row">
                  <div class="col-sm-12 form-group">
                    <div class="order-step__form-label heading--13">{% trans "Achievements released" %}</div>
                    <p class="caption--11">{% trans "In the case of a work whose production results can be disclosed to a third party, the range of participating artists will expand." %}</p>
                    <div class="order-step__sub-group">
                      {% for choice in form.disclosure_rule %}
                        <div class="order-step__form-multi">
                          <label class="input-radio">
                            <input type="radio" name="{{ choice.data.name }}"
                                   {% if choice.data.selected %}checked{% endif %}
                                   value="{{ choice.data.value }}" index="{{ choice.data.index }}"
                                   required="{{ choice.data.attrs.required }}"
                                   id="{{ choice.data.attrs.id }}"
                                   data-value="{{ choice.data.label }}"/>{{ choice.data.label }}
                            <div class="check-mark"></div>
                          </label>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              </div>
              <div class="order-step__submit order-step__action">
                <div class="container">
                  {% buttons %}
                      <button class="btn btn--tertiary btn-previous {% if add_by_sale %}hide{% endif %}" id="btn__previous_3">{% trans "return" %}</button>
                    {% if not user.is_authenticated %}
                      <button class="btn btn--primary btn-next" id="btn__next_3">{% trans "to the next" %}</button>
                    {% else %}
                      <button class="btn btn--primary btn-next"
                              id="submit-contact">{% trans "Next (confirmation of contents)" %}</button>
                    {% endif %}
                  {% endbuttons %}
                </div>
              </div>
            </div>
            <!-- End step 3 -->
            {% if not user.is_authenticated %}
              <!-- Step 4 -->
              <div class="order-step__form-tab order-step__form-tab-4 {% if add_by_sale and hide_step_3 %}active{% endif %}">
                <div class="order-step__form-heading">
                  <h3 class="heading--18">{% trans "Contact information" %}</h3>
                </div>
                <div style="display: none">
                  {{ form_contract.purpose|append_attr:"style:display:none;" }}
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_fullname">
                        <div class="order-step__form-label heading--13">{% trans "Full name" %}<span
                                class="blue-label--8">{% trans "required" %}</span></div>
                      </label>
                      {{ form_contract.fullname }}
                    </div>
                  </div>
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_email">
                        <div class="order-step__form-label heading--13">{% trans "Email address" %}<span
                                class="blue-label--8">{% trans "required" %}</span>
                        </div>
                      </label>
                      {{ form_contract.email }}
                    </div>
                  </div>
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_email_confirm">
                        <div class="order-step__form-label heading--13">{% trans "Email address (confirmation)" %}<span
                                class="blue-label--8">{% trans "required" %}</span>
                        </div>
                      </label>
                      {{ form_contract.email_confirm }}
                    </div>
                  </div>
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_job_type">
                        <div class="order-step__form-label heading--13">{% trans "job type" %}<span
                                class="grey-label--8">{% trans "any" %}</span>
                        </div>
                      </label>
                      {{ form_contract.job_type }}
                    </div>
                  </div>
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_enterprise">
                        <div class="order-step__form-label heading--13">{% trans "company name" %}<span
                                class="grey-label--8">{% trans "any" %}</span></div>
                      </label>
                      {{ form_contract.enterprise }}
                    </div>
                  </div>
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_company_url">
                        <div class="order-step__form-label heading--13">{% trans "website" %}<span
                                class="grey-label--8">{% trans "any" %}</span>
                        </div>
                      </label>
                      {{ form_contract.company_url }}
                    </div>
                  </div>
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_phone">
                        <div class="order-step__form-label heading--13">{% trans "phone number" %}<span
                                class="grey-label--8">{% trans "any" %}</span></div>
                      </label>
                      {{ form_contract.phone }}
                    </div>
                  </div>
                  <div class="form-row row">
                    <div class="col-sm-12 form-group">
                      <div class="order-step__form-label heading--13">{% trans "Your preferred contact method" %}
                      </div>
                      <div class="order-step__sub-group">
                        {% for choice in form_contract.contact_channel %}

                          <div class="order-step__form-multi">
                            <label class="input-radio">
                              <input type="radio" name="{{ choice.data.name }}"
                                     {% if choice.data.selected %}checked{% endif %}
                                     value={{ choice.data.value }} index={{ choice.data.index }}
                                     required={{ choice.data.attrs.required }} data-value="{{ choice.data.label }}"
                                     id="{{ choice.data.attrs.id }}"/>{{ choice.data.label }}
                              <div class="check-mark"></div>
                            </label>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__submit order-step__action">
                  <div class="container">
                    {% buttons %}
                      <button class="btn btn--tertiary btn-previous {% if hide_step_3 %}hide{% endif %}" id="btn__previous_4">{% trans "return" %}</button>
                      <button class="btn btn--primary btn-next"
                              id="submit-contact">{% trans "Next (confirmation of contents)" %}</button>
                    {% endbuttons %}
                  </div>
                </div>
              </div>
              <!-- End step 4 -->
            {% endif %}
          </form>
        </div>
      </div>
    </div>
  </div>

  <div class="upload-final-product-file upload-button-wrapper">
    <p>{% trans "Uploading" %}</p>
    <div class="fill">
      <div class="process"></div>
    </div>
    <div class="fa fa-check"></div>
  </div>

  {% include 'messenger/_modal_confirm_step.html' %}
  {% include 'messenger/_modal_open_file.html' %}

{% endblock content %}
{% block extra_script %}
  <script>
      let csrf = '{% csrf_token %}';
  </script>
  <script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}";</script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/js/splide.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/splide-extension-auto-scroll.js' %}"></script>
  <script src="{% static 'js/flatpickr_soremo.js' %}"></script>
    {% endcompress %}
  <script src="{% url 'javascript-catalog' %}"></script>
        {% compress js inline %}
  <script src="{% static 'js/isInViewport.min.js' %}"></script>
  <script src="{% static 'js/main.js' %}"></script>
  <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
  <script src="{% static 'js/common_variable.js' %}"></script>
  <script src="{% static 'js/upload_file.js' %}"></script>
  <script src="{% static 'js/validate_contract.js' %}"></script>
  <script src="{% static 'js/topic_detail.js' %}"></script>
  <script src="{% static 'js/order_step.js' %}"></script>
  <script src="{% static 'js/album_preview.js' %}"></script>
  <script src="{% static 'js/utils.js' %}"></script>
    {% endcompress %}
{% endblock %}
