{% load static %}
{% load util %}
<div class="mscene mitem type_item_project_line {% if offer.condition in '2,3,8,9' %}mprogress{% elif offer.condition in '4,5,6' %}mchecked{% endif %}"
     data-offer="{{ real_offer.pk }}" data-type-offer="messenger_owner" data-offer-side="0" data-reward="{{ real_offer.budget_or_reward }}" data-created="{{ real_offer.created|get_datetime }}">
  <div class="mscene__top">
    <div class="mmessage-user">
      {% with offer.project|get_other_member_in_project:user as members %}
        {% for member in members %}
          {% if forloop.counter0 < 5 %}
            <div class="mmessage-user-seen seen--owner">
              <div class="avatar avatar--image avatar--32 avatar--round stooltip background-avt" data-toggle="tooltip"
                   data-placement="bottom" data-html="true" title="">
                <img class="avatar-image" src="{{ member|get_avatar:'medium' }}" style="padding-bottom: 0">
              </div>
            </div>
          {% endif %}
        {% endfor %}
        {% with members.count|minus:5 as member_count %}
          <div class="mmessage-user-count {% if member_count <= 0 %}hide{% endif %}">
            {% if member_count >= 100 %}
              +99+
            {% else %}
              +{{ member_count }}
            {% endif %}</div>
        {% endwith %}
      {% endwith %}
      <div class="user-status-icon offline"></div>
    </div>

  </div>
  <div class="mscene__bottom guide-popup__wrap-info">
    <div class="mscene__name">
      <div class="thread-name">{{ offer.project.get_name_by_contract }}</div>
      {% with offer|new_message_count:user as count_new %}
        <div class="notification notification--blue notification--round {% if count_new == 0 %}hide{% endif %}">
          {% if count_new < 100 %}{{ count_new }}{% else %}99+{% endif %}</div>
      {% endwith %}
    </div>
    <div class="mscene__date">{{ real_offer.modified|get_weekday_new }}</div>
  </div>
</div>
