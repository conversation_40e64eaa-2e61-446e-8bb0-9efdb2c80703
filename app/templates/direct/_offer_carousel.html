{% load static %}
{% load util %}
<div class="messenger-director__item_carousel" data-offer="{{ offer.pk }}">
  {% with offer|project_done as done %}
    {% if offer.type == '1' or offer.type == '4' %}
      {% if offer.variation_offer.exists %}
        <div class="messenger-director__item no-border carousel-file">
          <div class="messenger-carousel" data-offer="{{ offer.pk }}">
            <div class="messenger-carousel-for">
              {% with offer|get_variation_choosed as variation_choosed %}
                {% if variation_choosed %}
                  <div class="messenger-carousel-item offer-variation-choosed"
                       data-variation="{{ variation_choosed.pk }}">
                    {% if done %}
                      {% if variation_choosed.bill %}
                        <div class="messenger-carousel-file carousel--bill variation---choosed" data-type="4">
                          <div class="messenger-carousel-item__type next-to-contract">請求書</div>
                          {% if user.role == 'master_client' and not offer.receipt_id and not is_done %}
                            <div class="pdf-item-button-bottom mark-as-ok-bill payment--bill hide">支払い</div>
                          {% endif %}
                          {% if user.role == 'master_admin' and not offer.receipt_id and not is_done %}
                            <div class="pdf-item-button-bottom upload__file-admin upload__bill hide">アップロード</div>
                          {% endif %}
                          <iframe src="{{ offer.bill.url }}#toolbar=0&page=2&view=FitV" width='100%'
                          ></iframe>
                          <div class="comment__download comment__download--bottom">
                            <i class="icon-check-status icon-download download__file-pdf"></i>
                          </div>
                        </div>
                      {% elif user.role == 'master_admin' and offer.status == '6' and not is_done %}
                        <div class="messenger-carousel-file carousel--bill variation---choosed">
                          <div class="messenger-carousel-item__type next-to-contract">請求書</div>
                          <div class="pdf-item-button-bottom upload__file-admin upload__bill hide">アップロード</div>
                          <div class="upload--file"></div>
                        </div>
                      {% endif %}
                    {% endif %}

                    {% if variation_choosed.contract %}
                      <div class="messenger-carousel-file carousel--contract variation---choosed {% if user.role == 'master_admin' and done and not is_done or variation_choosed.bill and done and user.role == 'master_client' %}hide{% endif %}"
                           data-type="3">
                        <div class="messenger-carousel-item__type next-to-bill">契約書</div>
                        {% if user.role == 'master_admin' and offer.status == '4' and not is_done %}
                          <div class="pdf-item-button-bottom upload__file-admin upload__contract">アップロード </div>
                        {% endif %}
                        {% if user.role == 'master_client' and offer.status == '4' and not is_done %}
                          <div class="pdf-item-button-bottom mark-as-ok-contract accept-contact">OK</div>
                        {% endif %}
                        <iframe src="{{ variation_choosed.contract.url }}#toolbar=0&page=2&view=FitV"
                                width='100%'></iframe>
                        <div class="comment__download comment__download--bottom">
                          {% if offer.status != '4' %}
                            <span class="icon-check-status icon-contract-done"></span>
                          {% elif offer.status == '4' %}
                            <span class="icon-check-status icon-contract-undone {% if user.role == 'master_client' and not is_done %}accept-contact{% endif %}"></span>
                          {% endif %}
                          <i class="icon-check-status icon-download download__file-pdf"></i>
                        </div>
                      </div>
                    {% elif offer.status == '3' and user.role == 'master_admin' and not is_done %}
                      <div class="messenger-carousel-file carousel--contract variation---choosed">
                        <div class="messenger-carousel" data-offer="{{ offer.pk }}">
                          <div class="messenger-carousel-item__type next-to-contract">契約書</div>
                          <div class="pdf-item-button-bottom upload__file-admin upload__contract hide">アップロード</div>
                          <div class="upload--file"></div>
                        </div>
                      </div>
                    {% endif %}
                    <div class="messenger-carousel-file carousel--plan variation---choosed {% if variation_choosed.contract or offer.status == '6' or offer.status == '3' and user.role == 'master_admin' and not is_done %}hide{% endif %}"
                         data-type="2">
                      <div class="messenger-carousel-item__type next-to-bill">お見積書</div>
                      <iframe src="{{ variation_choosed.plan.url }}#toolbar=0&page=2&view=FitV" width='100%'></iframe>
                      <div class="comment__download comment__download--bottom">
                        <span class="icon-check-status icon-plan-done"></span>
                        <i class="icon-check-status icon-download download__file-pdf"></i>
                      </div>
                    </div>
                  </div>
                {% endif %}
                {% for variation in offer|get_variation:variation_choosed %}
                  <div class="messenger-carousel-item" data-variation="{{ variation.pk }}">
                    <div class="messenger-carousel-file" data-type="2">
                      <div class="messenger-carousel-item__type next-to-bill">お見積書</div>
                      {% if offer.status != '6' %}
                        {% if user.role == 'master_client' and not variation_choosed and not is_done %}
                          <div class="pdf-item-button-bottom mark-as-ok-plan choose--plan ">とりあえずOK</div>
                        {% endif %}
                      {% endif %}
                      {% if user.role == 'master_admin' and offer.status == '2' and not is_done %}
                        <div class="pdf-item-button-bottom upload__file-admin upload__plan">アップロード</div>
                      {% endif %}
                      <iframe src="{{ variation.plan.url }}#toolbar=0&page=2&view=FitV" width='100%'></iframe>
                      <div class="comment__download comment__download--bottom">
                        {% if variation_choosed %}
                          <span class="icon-check-status icon-plan-disable"></span>
                        {% else %}
                          <span class="icon-check-status icon-plan-undone {% if user.role == 'master_client' and not is_done %}choose--plan{% endif %}"></span>
                        {% endif %}
                        <i class="icon-check-status icon-download download__file-pdf"></i>
                      </div>
                    </div>
                  </div>
                {% endfor %}
              {% endwith %}
            </div>
          </div>
        </div>
      {% elif user.role == 'master_admin' and offer.status == '1' and not is_done %}
        <div class="messenger-director__item no-border carousel-file carousel--plan">
          <div class="messenger-carousel" data-offer="{{ offer.pk }}">
            <div class="messenger-carousel-for">
              <div class="messenger-carousel-item offer-plan">
                <div class="messenger-carousel-item__type">お見積書</div>
                <div class="pdf-item-button-bottom upload__file-admin upload__plan">アップロード</div>
                <div class="upload--file"></div>
              </div>
            </div>
          </div>
        </div>
      {% endif %}

    {% elif offer.type == '2' %}
      {% with offer.variation_offer.first as variation %}
        <div class="messenger-director__item no-border carousel-file">
          <div class="messenger-carousel" data-offer="{{ offer.pk }}">
            <div class="messenger-carousel-for">
              <div class="messenger-carousel-item offer-variation-choosed"
                   data-variation="{% if variation %}{{ variation.pk }}{% endif %}">
                {% if done %}
                  {% if variation and variation.bill %}
                    <div class="messenger-carousel-file carousel--bill variation---choosed" data-type="4">
                      <div class="messenger-carousel-item__type next-to-contract">請求書</div>
                      {% if user.role == 'master_client' and not offer.receipt_id and not is_done %}
                        <div class="pdf-item-button-bottom mark-as-ok-bill payment--bill hide">支払い</div>
                      {% endif %}
                      {% if user.role == 'master_admin' and not offer.receipt_id and not is_done %}
                        <div class="pdf-item-button-bottom upload__file-admin upload__bill hide">アップロード</div>
                      {% endif %}
                      <iframe src="{{ variation.bill.url }}#toolbar=0&page=2&view=FitV" width='100%'
                      ></iframe>
                      <div class="comment__download comment__download--bottom">
                        <i class="icon-check-status icon-download download__file-pdf"></i>
                      </div>
                    </div>
                  {% elif user.role == 'master_admin' and offer.status == '6' and not is_done %}
                    <div class="messenger-carousel-file carousel--bill variation---choosed">
                      <div class="messenger-carousel-item__type next-to-contract">請求書</div>
                      <div class="pdf-item-button-bottom upload__file-admin upload__bill hide">アップロード</div>
                      <div class="upload--file"></div>
                    </div>
                  {% endif %}
                {% endif %}

                {% if  variation and variation.contract %}
                  <div class="messenger-carousel-file carousel--contract variation---choosed {% if user.role == 'master_admin' and done and not is_done or variation.bill and done and user.role == 'master_client' %}hide{% endif %}" data-type="3">
                    <div class="messenger-carousel-item__type ">契約書</div>
                    {% if user.role == 'master_admin' and not offer.receipt_id and not is_done %}
                      <div class="pdf-item-button-bottom upload__file-admin upload__contract">アップロード</div>
                    {% endif %}
                    <iframe src="{{ variation.contract.url }}#toolbar=0&page=2&view=FitV"
                            width='100%'></iframe>
                    <div class="comment__download comment__download--bottom">
                      <span class="icon-check-status icon-contract-done"></span>
                      <i class="icon-check-status icon-download download__file-pdf"></i>
                    </div>
                  </div>
                {% elif user.role == 'master_admin' and not is_done %}
                  <div class="messenger-carousel-file carousel--contract variation---choosed {% if done %}hide{% endif %}">
                      <div class="messenger-carousel-item__type next-to-contract">契約書</div>
                      <div class="pdf-item-button-bottom upload__file-admin upload__contract hide">アップロード</div>
                      <div class="upload--file"></div>
                  </div>
                {% endif %}

                {% if variation and variation.plan %}
                  <div class="messenger-carousel-file carousel--plan variation---choosed {% if variation.contract or user.role == 'master_admin' and not is_done %}hide{% endif %}" data-type="2">
                    <div class="messenger-carousel-item__type next-to-bill">お見積書</div>
                    {% if user.role == 'master_admin' and not offer.receipt_id %}
                      <div class="pdf-item-button-bottom upload__file-admin upload__plan">アップロード</div>
                    {% endif %}
                    <iframe src="{{ variation.plan.url }}#toolbar=0&page=2&view=FitV" width='100%'></iframe>
                  <div class="comment__download comment__download--bottom">
                        <span class="icon-check-status icon-plan-done"></span>
                        <i class="icon-check-status icon-download download__file-pdf"></i>
                      </div>
                  </div>
                {% elif user.role == 'master_admin' and not is_done %}
                  <div class="messenger-carousel-file carousel--plan variation---choosed hide">
                    <div class="messenger-carousel-item offer-plan">
                      <div class="messenger-carousel-item__type">お見積書</div>
                      <div class="pdf-item-button-bottom upload__file-admin upload__plan" data-plan="one">アップロード</div>
                      <div class="upload--file"></div>
                    </div>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      {% endwith %}
    {% endif %}
  {% endwith %}
</div>
