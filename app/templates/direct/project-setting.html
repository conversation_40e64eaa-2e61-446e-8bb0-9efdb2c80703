{% extends 'base_nofooter_refactor.html' %}
{% load static %}
{% load i18n %}
{% load util %}
{% load bootstrap3 %}

{% block title %}
    <title>設定 | {{product.code_name|default:''}}</title>
{% endblock %}


{% block extrahead %}
    <!-- material Icon -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />

        <!-- css読み込み -->
    <!-- <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"/> -->
    <link rel="stylesheet" href="{% static 'css/project-setting.css' %}">
    <link rel="stylesheet" href="{% static 'css/soremo_style_2024.css' %}" />
    <link rel="stylesheet" href="{% static 'css/product_banner.css' %}" />
    <link rel="stylesheet" href="{% static 'css/drop_file.css' %}" />
    <link rel="stylesheet" href="{% static 'css/message.css' %}" />

    <!-- swiper読み込み -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css"/>
{% endblock %}


{% block content %}
    {% comment %} <header class="sheader d-block-header" style="font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'Noto Sans Japanese', 'sans-serif';">
        {% include '_menu_navbar.html' with user=user request=request hide_milaeage_popup=True%}
    </header> {% endcomment %}
    <nav id="project-banner" class="{{ product.color_setting.code }}">
        <div id="code-name-display" class="{{ product.font_setting.code }}">{{ product.code_name|default:'' }}</div>
        <div class="u-row u-gap8" style="position: relative;">
{#            <div class="project-avatars">#}
{#                <span class="c-avatar16 u-mr-4"></span>#}
{#                <span class="c-avatar16 u-mr-4"></span>#}
{#                <span class="c-avatar16 u-mr-4"></span>#}
{#                <span class="c-vertical-line"></span>#}
{#                <span class="c-avatar16 u-mr-4"></span>#}
{#                <span class="c-avatar16 u-mr-4"></span>#}
{#                <span class="c-avatar16 u-mr-4"></span>#}
{#            </div>#}
            <div class="p-project-members-mask is-hidden"></div>
            <aside class="p-project-members is-hidden">
                <ul>
                    <li class="u-row u-gap8">
                        <span class="c-avatar40"></span>
                        <div class="u-col u-gap8 u-fill">
                            <span class="heading-13-spacing u-text-black">澪標 理央</span>
                            <div class="bodytext-11 u-line-height-100 u-text-light-gray">作編曲（M1）
                            </div>
                        </div>
                        <span class="material-symbols-rounded">navigate_next</span>
                    </li>
                </ul>
            </aside>
{#            <span id="p-schedule-open" class="material-symbols-rounded">#}
{#                event_note#}
{#            </span>#}
            <div class="p-schedule-mask is-hidden"></div>
            <aside class="p-schedule is-hidden">
                <div class="c-date-pickers"></div>
                <div class="u-wrapper"> <label>24/2/10（木）</label>
                    <ul>
                        <li class="u-row u-gap8">
                            <span class="c-avatar40"></span>
                            <div class="u-col u-gap8 u-fill">
                                <span class="heading-13-spacing">澪標 理央</span>
                                <div class="bodytext-11 u-line-height-100 u-text-light-gray">作編曲（M1）
                                </div>
                            </div>
                            <span class="material-symbols-rounded">navigate_next</span>
                        </li>
                    </ul>
                </div>
            </aside>
        </div>
    </nav>
    <nav id="top-app-bar">
        <div class="c-navigate-before heading-spacing is-hidden"><span class="material-symbols-rounded">
            navigate_before
        </span>戻る</div>
    </nav>
    <main id="project-setting" class="u-col-to-row u-gap16" data-product-id="{{ product.pk }}">
        <ul class="p-left-column u-col u-border-top resize-drag">
            <li class="p-lists-headline">
                <input type="radio" id="item1" name="item">
                <label for="item1" class="heading-spacing">コードネーム<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item2" name="item">
                <label for="item2" class="heading-spacing">目的物<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item3" name="item">
                <label for="item3" class="heading-spacing">予算<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item4" name="item" checked>
                <label for="item4" class="heading-spacing">バナー<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item5" name="item">
                <label for="item5" class="heading-spacing">全体スケジュール<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item6" name="item">
                <label for="item6" class="heading-spacing">概要<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item7" name="item">
                <label for="item7" class="heading-spacing">チェックポイント<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item8" name="item">
                <label for="item8" class="heading-spacing">エンドクライアント<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item9" name="item">
                <label for="item9" class="heading-spacing">楽曲認識機能<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <li class="p-lists-headline">
                <input type="radio" id="item10" name="item">
                <label for="item10" class="heading-spacing">ブロックリスト<span class="material-symbols-rounded">
                    navigate_next
                </span></label>
            </li>
            <div class="resize-handle"></div>
        </ul>
        <section class="p-right-column u-wrapper-reading">
            <article id="article1" style="display:none;">
                <img src="{% static 'images/make_project_step1.jpg' %}" alt="step1">
                <p>コードネームは、私たちだけの秘密の名前。安全に識別するために使います。どんな名前にする？</p>
                <p class="u-mt8">✨インスピレーションはこちら：<a class="c-keyword">英数字</a><a class="c-keyword">地名</a><a
                    class="c-keyword">デザート</a><a class="c-keyword">天体</a><a class="c-keyword">神話</a><a
                        class="c-keyword">動物</a><a class="c-keyword">色</a><a class="c-keyword">宝石</a></p>
                <form class="u-mt16">
                    <div class="u-row u-gap8">
                        <label for="code_name">コードネーム</label><span class="label-8 u-text-blue">必須</span>
                    </div>
                    <input type="text" id="code_name" name="code_name" value="{{ product.code_name|default:'' }}" maxlength="128" placeholder="R1X">
                </form>
            </article>

            <article id="article2" style="display:none;">
                <img src="{% static 'images/make_project_step2.jpg' %}" alt="step2">
                <p>目的物は、私たちの成果物がどのような作品に貢献するかを示します。<br>
                    具体的な作品名が決まっていないなら、<br>
                    （プラットフォーム）向け（メディア）「タイトル（仮）」<br>
                    という記載で伝えよう。</p>
                <p>📌例：<br>
                    ・Netflix向け短編映画「未来の夢（仮）」<br>
                    ・PlayStation 5向けゲーム「星の冒険者（仮）」<br>
                    ・YouTube向けPV「次世代のエコテクノロジー（仮）」）</p>
                <!-- テキストボックス -->
                <form class="u-mt16">
                    <div class="u-row u-gap8">
                        <label>目的物</label><span class="label-8 u-text-blue">必須</span>
                    </div>
                    <input type="text" id="product-name" name="product-name" value="{{ product.name|default:'' }}" maxlength="256"
                           placeholder="（プラットフォーム）向け（メディア）「タイトル（仮）」">
                </form>
            </article>

            <article id="article3" style="display:none;">

                <img src="{% static 'images/make_project_step3.jpg' %}" alt="step3" class="u-mb16">
                <div class="c-group u-bg-blue u-text-white u-col u-item-start u-gap8">
                    <p>プロジェクト残高</p>
                    <a class="bodytext-11">
                        <span class="heading-32-spacing">{{ remaining_budget|display_currency }}</span>円（税込）/ <span>{{ total_budget|display_currency }}</span>円（税込）</a>
                </div>
                <div class="u-column u-gap4">
                    <progress value="{{ percentage }}" max="100"></progress><span>{{ percentage|floatformat:2 }}%</span>
                </div>

            </article>

            <article id="article4">
                <div class="c-tabs">
                    <input type="radio" id="tab1" name="project-banner-tab" checked style="display:none;">
                    <label for="tab1" class="bodytext">フォントとカラー</label>
                    <input type="radio" id="tab2" name="project-banner-tab" style="display:none;">
                    <label for="tab2" class="bodytext">画像アップロード</label>
                </div>
                <section class="c-tab1">
                    <ul class="u-col-center">
                        {% for font in font_settings %}
                            <li class="u-w100">
                                <label class="p-option-font {{ font.code }} u-row-center">
                                    <input type="radio" id="{{ font.code }}" data-id="{{ font.pk }}" name="code-font" {% if font.pk == product.font_setting.pk %}checked{% endif %}
                                           style="display:none;">{{ font.sample_code }}</label>
                            </li>
                        {%  endfor %}
                    </ul>
                    <div class="color-container u-row u-gap16">
                        {% for color in color_settings %}
                            <label class="{{ color.code }}"><input type="radio" id="{{ color.code }}" data-id="{{ color.pk }}" name="banner-color" {% if color.pk == product.color_setting.pk %}checked{% endif %}
                                                                   style="display:none;"></label>
                        {% endfor %}
                    </div>
                </section>
                <section class="c-tab2 is-hidden">
                    <div class="project__upload_banner">
                        {% if product.image %}
                            <div class="uploaded-file-container">
                                <div class="account__file uploaded-file">
                                    <div class="icon--attach">
                                        <span class="material-symbols-rounded">attach_file</span>
                                    </div>
                                    <a href="{{ product.image.url }}" target="_blank">
                                        <div class="account__file-name" style="margin: 0">
                                            {% if product.image_name %}{{ product.image_name }}{% else %}
                                                {{ product.image.name }}{% endif %}</div>
                                    </a>
                                    <div class="icon--close">
                                        <span class="material-symbols-rounded">close</span>
                                    </div>

                                </div></div>
                        {% endif %}
                        <div class="mcomment-attached">
                            <div class="account__file">
                                <div class="icon--attach">
                                    <span class="material-symbols-rounded">attach_file</span>
                                </div>
                            </div>
                        </div>
                        <div style="position: relative; transform: translate3d(0,0,0);">
                            <img src="{% if product.image%} {{ product|get_image }} {% endif %} " class="image-banner">
                            <div class="c-upload-image" id="dropzoneUpLoadThumbnail">
                                <span class="material-symbols-rounded" id="thumnail-add-icon">image</span>
                            </div>
                        </div>
                        <input type="hidden" name="y" value="{{ product.x }}" id="id_y">
                        <input type="hidden" name="x" value="{{ product.y }}" id="id_x">
                        <input type="hidden" name="width" value="{{ product.width }}" id="id_width">
                        <input type="hidden" name="height" value="{{ product.height}}" id="id_height">
                    </div>
                </section>
            </article>

            <article id="article5" style="display:none;">
                <!-- テキストボックス -->
                <div class="u-row u-gap8">
                    <label>期間</label><span class="label-8 u-text-light-gray">任意</span>
                </div>
                <div class="u-row u-gap8 u-relative"><input id="daterangePicker" type="text" placeholder="期間を選んでね" data-start-time="{{ product.start_time|get_updated_datetime }}" data-end-time="{{ product.end_time|get_updated_datetime }}">
                    <span class="material-symbols-rounded c-icon-date-range">
                        date_range
                    </span></div>
                <hr class="u-mt16">
                <ul class="u-row u-gap8 u-text-light-gray u-mt16">
                    <li class="th-milestone-date">期日</li>
                    <li>マイルストーン</li>
                </ul>
                <div class="u-col u-gap4">
                    {% for milestone in product.milestones.all %}
                        <div class="u-block-wrapper milestone-field">
                            <div class="u-row u-gap8 u-relative">
                                <input class="datePicker" type="text" name="milestone-date" data-select-date="{{ milestone.time|get_updated_datetime }}" value="{{ milestone.time|get_updated_datetime }}" style="width: 128px;">
                                <span class="material-symbols-rounded c-icon-event">
                                    event
                                </span>
                            </div>
                            <input type="text" id="milestone-name" name="milestone-name" value="{{ milestone.name }}">
                            <div class="hover-menu">
                                <span class="material-symbols-rounded delete-milestone" id="delete-milestone">
                                    delete
                                </span>
                            </div>
                        </div>
                    {% endfor %}

                </div>
                <div class="c-add-block u-mt16" id="add-milestone">
                    <span class="material-symbols-rounded">
                        add_circle
                    </span><a>マイルストーンを追加</a>
                </div>
            </article>

            <article id="article6" style="display:none;">
                <img src="{% static 'images/project_overview.jpg' %}" alt="project_overview" class="indicator-image u-mb16">
                <!-- <img src="{% static 'images/make_project_step2.jpg' %}" alt="step2"> -->
                <p class="u-text-center">プロジェクトの概要と達成しようとする具体的な目標を共有しよう。</p>
                <div class="u-row u-gap8 u-mt16">
                    <label>概要</label><span class="label-8 u-text-light-gray">任意</span>
                </div>
                <textarea rows="10" id="project-overview" name="project-overview" maxlength="400"
                          placeholder="ここに概要を書いてください">{{ product.description|default:'' }}</textarea>
                <div class="u-row-end u-text-light-gray"><span id="overview-count"></span><span>/400</span>
                </div>
            </article>

            <article id="article7" style="display:none;">

                <div class="indicator-icon"><span class="material-symbols-rounded">
                    check_circle
                </span></div>
                <div class="u-col-center " style="gap:8px;">
                    <p class="u-text-center">クライアントにチェックしてもらう数を設定しよう。<br>2以上に設定すると、進捗率も表示できます。</p>
                    <div class="u-row u-gap8 u-mt16">
                        <label for="checkpoints">チェックポイント（{{ product.current_scene }}-999）</label>
                    </div>
                    <div class="scene-input">
                        <input type="number" id="checkpoints" name="checkpoints" placeholder="0" min="{{ product.current_scene|default:'0' }}" max="999" value="{{ product.max_scene }}"
                               style="width:64px;" inputmode="numeric" maxlength="3">
                        <span class="error-input" style="visibility: hidden">{{ product.current_scene|default:'0' }}から999で設定してください。</span>
                    </div>
                    <progress id="completion-rate-meter" max="100"></progress>
                    <p>ひとつのチェックで進捗率が、<span id="completion-rate">0</span>％進みます</p>
                    <div id="infographic-checkpoints" class="u-text-center"></div>
                </div>
            </article>


            <article id="article8" style="display:none;">
                <img src="{% static 'images/make_project_step2.jpg' %}" alt="step2">
                <p class="u-text-center">最終的な成果物の受領者は、あなたの直接のクライアントですか？<br>
                    そうでない場合は、エンドクライアント情報を更新できます。<br>
                    これで、目的物と期待される品質基準がよりはっきりします。</p>
                <div class="u-mt16 u-row u-gap8">
                    <label>エンドクライアント</label><span class="label-8 u-text-light-gray">任意</span>
                </div>
                <input type="text" id="end-client" name="end-client" maxlength="256" value="{{ product.client_name|default:'' }}">
            </article>


            <article id="article9" style="display:none;">

                <div class="indicator-icon"><span class="material-symbols-rounded">
                    fingerprint
                </span></div>
                <p>ACR Cloudの設定を登録し、楽曲認識機能を有効にしよう。<br>
                    <p>この機能はオプションです。利用を希望する場合は、担当にお問い合わせください。</p>
                    <form>
                        <div class="u-row u-gap8 u-mt16">
                            <label>host</label><span class="label-8 u-text-light-gray">任意</span>
                        </div>
                        <input type="text" id="acr_host" name="acr_host" maxlength="100"
                               placeholder="identify-ap-southeast-1.acrcloud.com" value="{{ product.acr_host|default:''  }}">
                        <div class="u-row u-gap8 u-mt16">
                            <label>access key</label><span class="label-8 u-text-light-gray">任意</span>
                        </div>
                        <input type="text" id="acr_access_key" name="acr_access_key" maxlength="100"
                               placeholder="9250e67d17f10c8592ab95ad12ada80c" value="{{ product.acr_access_key|default:''  }}">
                        <div class="u-row u-gap8 u-mt16">
                            <label>access secret</label><span class="label-8 u-text-light-gray">任意</span>
                        </div>
                        <input type="text" id="acr_access_secret" name="acr_access_secret" maxlength="100"
                               placeholder="tTRY8oZfuk2FoVO7qkblSqJPN7D3NA7bvnd88KyT" value="{{ product.acr_access_secret|default:''  }}">
                    </form>
                </article>
                <article id="article10" style="display:none;">
                    <div class="indicator-icon"><span class="material-symbols-rounded">
                        block
                    </span></div>
                    <p class="u-text-center">このプロジェクトへの招待やオファーを、特定のユーザーに対し制限できます。<br>
                        {% for artist in product.artists_block.all %}
                            {% include 'product/_item_artist_block2.html' with project=project artist=artist.user_creator.first %}
                        {% endfor %}
                        <div class="project-setting__block-list-artist"></div>
                        <div class="c-add-block" id="add-block"><span class="material-symbols-rounded">add_circle</span><a>ブロックリストに追加</a>
                        </div>
                    </article>
                </section>
                <dialog id="creation-notice">
                    <div><span class="material-symbols-rounded" style="font-size: 128px;">
                        rocket_launch
                    </span></div>
                    <p>新プロジェクトのスタート、おめでとうございます！<br>
                        プロジェクトの設定は、ホーム画面のサイドバーからいつでも変更可能です。<br>
                        プロジェクトバナーの右上から、クライアントを招待しましょう。ホームから成果物をアップロードして、チェックバックを依頼できます。<br>
                        個別DMから、オファーを送ることができます。<br>
                    </p>
                    <button class="c-btn-primary" id="notice-confirm-button">OK</button>
                </dialog>
                <dialog class ="modal popup-container" id="modal-search-block-list">
                    <div class="modal-dialog popup-dialog">
                        <div class="modal-content popup-content">
                            <div class="modal-header">
                                <a class="smodal-close smodal-close--prev"  data-dismiss="modal" aria-label="Close">
                                    <span class="material-symbols-rounded">chevron_left</span>
                                </a>
                            </div>
                            <div class="popup-body">
                                <div class="col-sm-12 popup-body__part-content">
                                    <h3>{% trans "Add block list" %}</h3>
                                    <span>ブロックする会社名でアーティストを検索できます。</span>
                                </div>

                                <div class="form-group">
                                    <form action="">
                                        <label class="label-form" for="id_block-list__search">
                                            <input type="text" name="block-list__search" value="" placeholder="会社名で検索"
                                                   class="form-control account__input-text block-list__search"
                                                   id="id_block-list__search">
                                            <span class="material-symbols-rounded icon-search">search</span>
                                        </label>
                                    </form>
                                </div>
                                <div class="block-list__results">
                                    <div class="block-list__results-search mscrollbar">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </dialog>
                <dialog class ="modal popup-container" id="modal-crop-banner">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title">画像登録</h4>
                        </div>
                        <div class="modal-body">
                            <img src="" id="image-crop" style="max-width: 50%;">
                        </div>
                        <div class="modal-footer">
                            <div class="btn-group pull-left" role="group">
                                <button type="button" class="btn btn-default js-zoom-in">
                                    <span class="material-symbols-rounded">zoom_in</span>
                                </button>
                                <button type="button" class="btn btn-default js-zoom-out">
                                    <span class="material-symbols-rounded">zoom_out</span>
                                </button>
                            </div>
                            <button type="button" class="btn btn-primary js-crop-and-upload js-crop-and-upload-project">
                                登録する
                            </button>
                        </div>
                    </div>
                </dialog>
            </main>
            {% include 'top/_navigation_bar_refactor.html' with project=project user=user type_page='top_page' project_list=False is_pc=True show_staff=True %}
    <!-- <footer id="bottom-app-bar">
        <div class="u-row-btn u-wrapper">
            <button id="save-project-button" class="c-btn-primary" >{% if is_create_new %} プロジェクトを始める {% else %} OK {% endif %}</button>
        </div> -->

        <!-- <ul class="u-row-between">
            <li class="c-icon-btn u-col-center u-gap2"><span class="material-symbols-rounded">
                    home
                </span><span class="label-8">ホーム</span>
            </li>
            <li class="c-icon-btn u-col-center u-gap2"><span class="material-symbols-rounded">
                    forum
                </span><span class="label-8">トークルーム</span></li>
            <a href="dm.html">
                <li class="c-icon-btn u-col-center u-gap2"><span class="material-symbols-rounded">mail</span><span
                        class="label-8">個別DM</span></li>
            </a>
        </ul> -->
    <!-- </footer> -->
{% endblock %}


{% block extra_script %}
    <!-- adobeフォント -->
    <script>
        (function (d) {
            var config = {
                kitId: 'zsw6vgy',
                scriptTimeout: 3000,
                async: true
            },
                h = d.documentElement, t = setTimeout(function () { h.className = h.className.replace(/\bwf-loading\b/g, "") + " wf-inactive"; }, config.scriptTimeout), tk = d.createElement("script"), f = false, s = d.getElementsByTagName("script")[0], a; h.className += " wf-loading"; tk.src = 'https://use.typekit.net/' + config.kitId + '.js'; tk.async = true; tk.onload = tk.onreadystatechange = function () { a = this.readyState; if (f || a && a != "complete" && a != "loaded") return; f = true; clearTimeout(t); try { Typekit.load(config) } catch (e) { } }; s.parentNode.insertBefore(tk, s)
        })(document);
    </script>

    <!-- resize-drag -->
    <script src="https://cdn.jsdelivr.net/npm/interactjs@1.10.11/dist/interact.min.js"></script>
    <script>
        interact('.resize-drag')
            .resizable({
                edges: { left: false, right: true, bottom: false, top: false },

                listeners: {
                    move(event) {
                        let { x, y } = event.target.dataset;

                        x = (parseFloat(x) || 0) + event.deltaRect.left;
                        y = (parseFloat(y) || 0) + event.deltaRect.top;

                        Object.assign(event.target.style, {
                            width: `${event.rect.width}px`,
                            height: `${event.rect.height}px`,
                            transform: `translate(${x}px, ${y}px)`
                        });

                        Object.assign(event.target.dataset, { x, y });
                    }
                },
                modifiers: [
                    // 以下のコードはリサイズの最小/最大サイズを制限します
                    interact.modifiers.restrictSize({
                        min: { width: 224 },
                        max: { width: 640 }
                    })
                ]
            });
        var is_create_new = {% if is_create_new %}true{% else %}false{% endif %};
    </script>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- tilt.js読み込み -->
    <script type="text/javascript" src="{% static 'js/vanilla-tilt.min.js' %}" defer></script>

    <!-- gsap読み込み -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.4/gsap.min.js" defer></script>

    <!-- js読み込み -->
    <script type="module" src="{% static 'js/project-setting.js' %}" defer></script>
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.css" />

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>

    <style>
        {% for font in font_settings %}
            .{{ font.code }} {
                {{ font.css_code|safe }}
            }
        {%  endfor %}

        {% for color in color_settings %}
            .{{ color.code }} {
                {{ color.text_color_css|safe }}
                {{ color.css_code|safe }}
            }
        {% endfor %}
    </style>
{% endblock %}
