{% load i18n %}
{% load util %}
{% load compress %}
{% compress css %}
<style>
  .popup-footer {
    text-align: center;
  }

  #loading_animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    z-index: 9999;
    background-size: 32px;
    background-position: center;
    background-repeat: no-repeat;
  }

  #loading_animation svg {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
      display: block;
      height: 64px;
      width: 64x;
  }

  #loading_animation.opa50 {
      opacity: 0.5;
  }
</style>
{% endcompress %}
<!-- Modal confirm contract -->
<div class="modal popup account__popup-container modal-confirm" id="modal-confirm-contract-offer" tabindex="-1"
  role="dialog" aria-hidden="true">
  <div class="modal-dialog popup-dialog" role="document">
    <div class="modal-content popup-content">
      <div class="popup-header"></div>
      <div class="popup-body">
        <p class="popup-text" style="text-align: left !important;">{% trans "Do you approve this contract" %}</p>
      </div>
      <div class="popup-footer" style="text-align: right !important;">
        <button type="button" class="btn btn--tertiary btn-popup-close" data-dismiss="modal">{% trans "no" %}</button>
        <button type="button" class="btn btn--primary button-confirm-contract-offer">{% trans "yes" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal confirm contract -->

<!-- Modal done offer -->
<div class="modal popup account__popup-container modal-confirm" id="modal-confirm-done-offer" tabindex="-1"
  role="dialog" aria-hidden="true">
  <div class="modal-dialog popup-dialog" role="document">
    <div class="modal-content popup-content">
      <div class="popup-header"></div>
      <div class="popup-body">
        <p class="popup-text" style="text-align: left;">{% trans "Would you like to accept this project" %}</p>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-close" data-dismiss="modal">{% trans "no" %}</button>
        <button type="button" class="btn btn--primary button-confirm-done-offer">{% trans "yes" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal done offer -->

<!-- Modal upload file -->

<form class="modal popup-container" id="modal-upload-contract" role="dialog">
  {% include 'direct/_upload_contract.html' %}
</form>

<form class="modal popup-container" id="modal-upload-plan" role="dialog">
  {% include 'direct/_upload_plan.html' %}
</form>
{% include 'direct/_upload_file.html' with hide_class='' %}
<!-- End modal upload file -->

<!-- Modal confirm upload contract form -->
{% comment %} <div class="modal popup-container" id="modal-confirm-upload" role="dialog">
  <div class="modal-dialog popup-dialog modal-lg" role="document">
    <div class="modal-content popup-content">
      <div class="popup-header">
        <hr />
        <h3 class="heading--40">{% trans "Verification" %}
        </h3>
      </div>
      <div class="popup-body nice-scroll mscrollbar">
        <div class="sub-container-body">
        <div class="contract__form-wrap">
          <div class="popup-body__list">
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "Subject" %}</div>
              <div class="text-content bodytext--13" id="id_subject_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "Contents" %}</div>
              <div class="mscrollbar" id="id_business_content_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "Deliverables" %}</div>
              <div class="text-content bodytext--13" id="id_delivery_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="form-check custom-switch">
                <label class="form-check-label">
                  <div class="form-check-group">
                    <input class="form-check-input switch-checkbox" type="checkbox" name="is_delivery_checked_confirm" id="id_delivery_checked_confirm" disabled><span class="switch-slider"></span>
                  </div>
                  <span class="switch-label" style="color: #000000">{% trans "Make a quasi-delegation contract" %}
                  </span>
                </label>
              </div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "Consideration" %}</div>
              <div class="text-content bodytext--13" id=""><span id="id_tax_value"></span> {% trans "yen" %}</div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "Period" %}</div>
              <div class="text-content bodytext--13" id="id_date_schedule_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "label deadline" %}</div>
              <div class="text-content bodytext--13"><span id="id_deadline_d_value"></span>　<span id="id_time_value"></span></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "Remarks" %}</div>
              <div class="text-content bodytext--13 bodytext--13 confirm-note" id="id_note_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="form-check custom-switch">
                <label class="form-check-label">
                  <div class="form-check-group">
                    <input class="form-check-input switch-checkbox" type="checkbox" name="is_public_confirm" id="id_public_confirm" disabled><span class="switch-slider"></span>
                  </div>
                  <span class="switch-label" style="color: #000000">{% trans "I agree to publish the case" %}
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
        <hr />

        <div class="contract__form-wrap">
          <div class="popup-body__list">
            <div class="popup-body__item">
              <table class="table table-borderless">
                <thead>
                  <tr>
                    <th class="header-table">{% trans "Address (A)" %}</th>
                    <th class="header-table">{% trans "From (B)" %}</th>
                  </tr>
                </thead>
                <tbody>
                    <tr>
                      <td id="id_address_owner_value">東京都江東区青海二丁目7番4号</td>
                      <td id="id_address_value">東京都江東区青海二丁目7番4号</td>
                    </tr>
                    <tr>
                      <td id="id_company_name_owner_value">株式会社ソレモ</td>
                      <td id="id_company_name_value">株式会社ソレモ</td>
                    </tr>
                    <tr>
                      <td id="id_job_title_owner_value">代表取締役</td>
                      <td id="id_job_title_value">代表取締役</td>
                    </tr>
                    <tr>
                      <td id="id_fullname_owner_value">善里 信哉</td>
                      <td id="id_fullname_value">善里 信哉</td>
                    </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <hr />

        <div class="contract__form-wrap" id="form_type">
          {% include 'direct/_form_type.html' %}
        </div>
      </div>
      </div>
      <div class="popup-footer">
        <div class="button-container">
          <!-- <button type="button" class="btn btn--tertiary" data-dismiss="modal">{% trans "return" %}</button>
          <button type="button" class="btn btn--primary" id="btn__submit-upload">
            {% trans "Presented with this content" %}
          </button> -->
          {% include 'buttons/conponent_button.html' with className='btn--tertiary small' value='戻る' attribute='data-dismiss="modal"' %}
          {% include 'buttons/conponent_button.html' with className='btn--primary medium' value='この内容で提示' attribute='id="btn__submit-upload"' %}
        </div>
      </div>
    </div>
  </div>
</div> {% endcomment %}

<div class="smodal smodal--extra-large smodal--document modal fade" data-message-id="" id="modal-confirm-upload"
     role="dialog" style="z-index: 2001">
  <div class="document-top">
  </div>
  <div class="modal-dialog" role="document" style="background: transparent; box-shadow: none;">
    <div class="modal-content">
      <a class="smodal-close" href="#" data-dismiss="modal" aria-label="Close">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19.8802 8.94469C19.3602 8.42469 18.5202 8.42469 18.0002 8.94469L11.8802 15.0647C11.3602 15.5847 11.3602 16.4247 11.8802 16.9447L18.0002 23.0647C18.5202 23.5847 19.3602 23.5847 19.8802 23.0647C20.4002 22.5447 20.4002 21.7047 19.8802 21.1847L14.7069 15.998L19.8802 10.8247C20.3869 10.3047 20.3869 9.45135 19.8802 8.94469Z" fill="#A7A8A9"/>
        </svg>
      </a>
      <div class="document-popup">
        <div class="document-popup__content">
          <iframe class="scrollbar" frameborder="0" allowfullscreen src='about:blank'>
          </iframe>
          <div class="checkbox-and-button-container">
            <div class="form-check custom-checkbox">
              <input class="form-check-input" type="checkbox" name="form-approve-checkbox" id="form-approve-checkbox">
              <label class="form-check-label" for="form-approve-checkbox" style="padding-left: 25px; margin-bottom: 16px;">
                <span class='bodytext--13'>内容をチェックし、同意しました</span></label>
            </div>
            {% include 'buttons/conponent_button.html' with className='btn--primary large disable' icon_class_start='icon-send-plane' value='お見積りを送る' attribute='id="btn__submit-upload"' %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{#    <div class="document-popup__title scaption">#}
{#      <i class="icon icon--sicon-clip"></i><span class="file-name_modal"></span>#}
{#    </div>#}
{#    <a class="smodal-download" href="javascript:void(0)">#}
{#      <i class="icon icon--sicon-download"></i>#}
{#    </a>#}
{#  </div>#}
{#</div>#}
<!-- End modal confirm upload contract form -->
{#    <div class="document-popup__title scaption">#}
{#      <i class="icon icon--sicon-clip"></i><span class="file-name_modal"></span>#}
{#    </div>#}
{#    <a class="smodal-download" href="javascript:void(0)">#}
{#      <i class="icon icon--sicon-download"></i>#}
{#    </a>#}
{#  </div>#}
{#</div>#}

<!-- Modal add card -->
<div class="modal popup account__popup-container modal-confirm" id="modal-add-card" tabindex="-1" role="dialog"
  aria-hidden="true">
  <div class="modal-dialog popup-dialog" role="document">
    <div class="modal-content popup-content">
      <div class="popup-header"></div>
      <div class="popup-body">
        <p class="popup-text">{% trans "I haven't registered my payment information yet, so" %}<br> {% trans "First, please register your payment information" %}
        </p>
      </div>
      <div class="messenger-popup__action-button popup-footer">
        <a class="btn btn--primary btn-popup-ok link-add--card" target="_blank">{% trans "Register payment information" %}</a>
        <br>
      </div>
      <div class="popup-footer">
        <button type="button" class="btn btn--tertiary btn-popup-close" data-dismiss="modal">{% trans "cancel" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal add card -->

<!-- Modal choose card -->
<div class="messenger-popup modal fade animate" id="modal-choose--card" role="dialog">
  <div class="messenger-popup__content">
    <div class="messenger-popup__form">
      <br>
      <div style="text-align: left"><span>{% trans "Payment amount" %}: </span><span class="total-bill"
          style="position: absolute;left: 50%;"></span></div>
      <div class="messenger-popup__action">
        <div class="messenger-popup__action-button">
          <button data-dismiss="modal" class="btn btn--tertiary close-modal">{% trans "cancel" %}
          </button>
          <button id="payment_submit" class="btn btn--primary btn-popup-ok disable" role="button">{% trans "Settle"%}
          </button>
          <br>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- End modal choose card -->

<!-- Modal success -->
<div class="modal popup account__popup-container modal-confirm" id="payment-success-popup" tabindex="-1" role="dialog"
  aria-hidden="true">
  <div class="modal-dialog popup-dialog" role="document">
    <div class="modal-content popup-content">
      <div class="popup-header"></div>
      <div class="popup-body">
        <p class="popup-text">{% trans "Payment is complete." %}</p>
      </div>
      <div class="popup-footer">
        <button type="button" class="btn btn--primary btn-popup-ok" data-dismiss="modal">OK</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal success -->
