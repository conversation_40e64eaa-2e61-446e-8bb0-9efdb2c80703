{% extends "base_nofooter_refactor.html" %}
{% load static %}
{% load user_agents util compress %}
{% load bootstrap3 %}
{% load i18n %}

{% block title %}
    <title>プロジェクト作成 | SOREMO</title>
{% endblock%}

{% block extrahead %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/artist_create_product.css' %}"/>
{% endblock %}

{% block content %}
    <nav>
        <hr>
        <h2>プロジェクトを作成</h2>
    </nav>
    <section class="step1 active">
        <ol class="stepper-container">
            <li class="stepper active"><span>コードネーム</span></li>
            <li class="stepper"><span>目的物</span></li>
            <li class="stepper"><span>予算</span></li>
            <li class="stepper"><span>支払い</span></li>
        </ol>
        <img src="{% static 'images/make_project_step1.jpg' %}" alt="step1">
        <p>まずは、プロジェクトのコードネームを決めましょう。これは、私たちだけの秘密の名前。安全に識別するために使います。どんな名前にする？</p>
        <p>✨インスピレーションはこちら：<a class="keyword">英数字</a><a class="keyword">地名</a><a class="keyword">デザート</a><a
            class="keyword">天体</a><a class="keyword">神話</a><a class="keyword">動物</a><a class="keyword">色</a><a
                class="keyword">宝石</a></p>
    <!-- テキストボックス -->
        <div class="form-input">
            <div class="label-wrapper">
                <label>コードネーム</label><span>必須</span>
            </div>
            <input type="text" id="codeName" name="code_name" maxlength="500" placeholder="Apollo">
        </div>
    </section>
    <footer class="step1 active">
        <div class="button-wrapper">
            <button id="prevBtn1" class="c-btn-tertiary" type="button">ホーム画面へ</button>
            <button id="nextBtn1" class="c-btn-primary" type="button">次へ</button>
        </div>
    </footer>
    <section class="step2 hidden">
        <ol class="stepper-container">
            <li class="stepper completed"><span>コードネーム</span></li>
            <li class="stepper active"><span>目的物</span></li>
            <li class="stepper"><span>予算</span></li>
            <li class="stepper"><span>支払い</span></li>
        </ol>

        <img src="{% static 'images/make_project_step2.jpg' %}" alt="step2">
        <p>次に、目的物を設定しましょう。<br>
            この情報は、プロジェクト内の個別契約書に反映され、参加メンバーに対して、成果物に期待される品質基準や、それがどのように利用されるかを示します。</p>

        <p>✨作品の形式またはジャンル：
            <div class="c-parent-tags u-line-height-200">
            </div>
        </p>

        <p>✨対象プラットフォームまたはサービス：
            <div class="c-child-tags u-line-height-200">
            </div>
        </p>

        <div class="form-input">
            <div class="label-wrapper">
                <label>目的物</label><span>必須</span>
            </div>
            <input type="text" id="nameProject" name="name" maxlength="500"
                   placeholder="（プラットフォーム）向け（メディア）「タイトル（仮）」">
        </div>
    </section>
    <footer class="step2 hidden">
        <div class="button-wrapper">
            <button id="prevBtn2" class="c-btn-tertiary" type="button">戻る</button>
            <button id="nextBtn2" class="c-btn-primary" type="button">次へ</button>
        </div>
    </footer>
    <section class="step3 hidden">
        <ol class="stepper-container">
            <li class="stepper completed"><span>コードネーム</span></li>
            <li class="stepper completed"><span>目的物</span></li>
            <li class="stepper active"><span>予算</span></li>
            <li class="stepper"><span>支払い</span></li>
        </ol>

        <img src="{% static 'images/make_project_step3.jpg' %}" alt="step3">
        <p>プロジェクトに適した予算を決めしょう。その予算内で対価を設定し、オファーを送ることができます。プロジェクトが完了し、予算が余った場合は、自分の残高に返金されます。</p>
        <div class="group form-input">
            <div class="label-wrapper">
                <label>予算</label><span>必須</span>
            </div>
            <div class="input-wrapper">
                <input id="budgetInput" name="total_budget" type="number" inputmode="numeric" max="999999999999999"  maxlength='15'
                       oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"
                       placeholder="0"><span>円（税抜）</span>
            </div>
            <div class="p-top-up">
                <a class="c-btn-small-primary">+15,000</a>
                <a class="c-btn-small-primary">+30,000</a>
                <a class="c-btn-small-primary">+100,000</a>
                <a class="c-btn-small-primary">+300,000</a>
                <a class="c-btn-small-primary">+1,000,000</a>
                <a class="c-btn-small-primary">+3,000,000</a>
            </div>

            <ul>
                <li><a class="card-image" style="{% if user_info.mileage_rank.image %}background-image: url({{ user_info.mileage_rank.image.url }}){% else %}background-image: url({% static 'images/img_default_stage_card.png' %}){% endif %};">
                </a>
                    <input type="hidden" id="serviceFree" value="{{ user.get_usage_fee_for_user_to_show }}">
                    <span>{{ user_info.mileage_rank.rank_name_jp }}（{{ user.get_usage_fee_for_user_to_show }}%）</span>

                </li>
                <li><a>サービス利用料</a><a id="serviceFeeDisplay" class="currency"></a><span>円（税抜）</span>
                </li>
                <input type="hidden" id="userBalanceAvailable" value="{{ user_info.balance_available }}">
                <li><a>現在のウォレット残高</a><a id="walletBalanceDisplay" class="currency">0</a><span>円（税抜）</span>
                </li>
                <hr>
                <li class="show-if-no-deficit"><a>支払後のウォレット残高</a><a id="postPaymentBalanceDisplay"
                                                                    class="currency"></a><span>円（税抜）</span>
                </li>
                <li class="show-if-deficit"><a>不足デポジット</a><a id="insufficientDepositDisplay"
                                                             class="currency"></a><span>円（税抜）</span></li>
                <li class="show-if-deficit"><a>消費税（10%）</a><a id="consumptionTaxDisplay"
                                                              class="currency"></a><span>円</span></li>
                <hr class="show-if-deficit">
                <li class="show-if-deficit"><a>小計</a><a id="subtotalDisplay"
                                                        class="currency"></a><span>円（税込）</span>
                </li>
                <li class="show-if-deficit"><a>stripe決済手数料（3.73%）</a><a id="stripeProcessingFeeDisplay"
                                                                        class="currency"></a><span>円（税込）</span>
                </li>
                <hr class="show-if-deficit">
                <li class="show-if-deficit"><a>合計</a><a id="totalAmountDisplay"
                                                        class="currency"></a><span>円（税込）</span>
                </li>
                <input type="hidden" id="amount" name="amount">
            </ul>
        </div>
    </section>
    <footer class="step3 hidden">
        <div class="button-wrapper">
            <button id="prevBtn3" class="c-btn-tertiary" type="button">戻る</button>
            <button id="nextBtn3" class="c-btn-primary" type="button">次へ</button>
        </div>
    </footer>
    <section class="step4 hidden">
        <ol class="stepper-container">
            <li class="stepper completed"><span>コードネーム</span></li>
            <li class="stepper completed"><span>目的物</span></li>
            <li class="stepper completed"><span>予算</span></li>
            <li class="stepper active"><span>支払い</span></li>
        </ol>
        <div class="group form-input">
            <ul>
                <li><a>お支払い合計</a><a id="confirmationTotalAmountDisplay" class="currency"></a><span>円（税込）</span>
                </li>
            </ul>
        </div>
        <div class="center-container">
            <div class="center-container">
                <div class="indicator-icon"><span class="material-symbols-rounded">credit_card</span></div>
                <p>予算の不足分をクレジットカードで決済します。よろしいですか。</p>
            </div>
        </div>
        <dialog id="select-card-modal">
            <div class="swiper">
                <div class="swiper-wrapper">
                    {% for card in user_info.cards %}
                        {% with random_number=1|random_number:4 %}
                            <div class="swiper-slide credit-card{{ random_number }}" data-card-id="{{ card.id }}">
                                <div class="card-number">**** **** **** {{ card.last4}}</div>
                                <div class="card-expiry">{{ card.exp_month }}/{{ card.exp_year|slice:"-2:" }}</div>
                                <div class="card-brand">{{ card.brand }}</div>
                            </div>
                        {% endwith %}
                    {% endfor %}
                </div>
            </div>
            <p>お支払いに利用するカードを選んでください。</p>
            <button id="select-card-btn" class="c-btn-primary">このカードを利用</button>
        </dialog>
        <dialog id="add-card-modal">
            <div class="new-card-input">
                <div class="payment">
                    <div class="container preload">
                        <div class="creditcard">
                            <div class="front">
                                <div id="ccsingle"></div>
                                <svg version="1.1" id="cardfront" xmlns="http://www.w3.org/2000/svg"
                                     xmlns:xlink="http://www.w3.org/1999/xlink"
                                     x="0px" y="0px" viewBox="0 0 750 471" style="enable-background:new 0 0 750 471;"
                                     xml:space="preserve">
                                    <g id="Front">
                                        <g id="CardBackground">
                                            <g id="Page-1_1_">
                                                <g id="amex_1_">
                                                    <path id="Rectangle-1_1_" class="lightcolor grey" d="M40,0h670c22.1,0,40,17.9,40,40v391c0,22.1-17.9,40-40,40H40c-22.1,0-40-17.9-40-40V40
                                                                                                         C0,17.9,17.9,0,40,0z"/>
                                                </g>
                                            </g>
                                            <path class="darkcolor greydark"
                                                  d="M750,431V193.2c-217.6-57.5-556.4-13.5-750,24.9V431c0,22.1,17.9,40,40,40h670C732.1,471,750,453.1,750,431z"/>
                                        </g>
                                        <text transform="matrix(1 0 0 1 60.106 295.0121)" id="svgnumber" class="st2 st3 st4">#### #### #### ####</text>
                                        <text transform="matrix(1 0 0 1 54.1064 428.1723)" id="svgname"
                                              class="st2 st5 st6">#### ####</text>
                                        <text transform="matrix(1 0 0 1 54.1074 389.8793)"
                                              class="st7 st5 st8">cardholder name</text>
                                        <text transform="matrix(1 0 0 1 479.7754 388.8793)" class="st7 st5 st8">expiration</text>
                                        <text transform="matrix(1 0 0 1 65.1054 241.5)" class="st7 st5 st8">card number</text>
                                        <g>
                                            <text transform="matrix(1 0 0 1 574.4219 433.8095)" id="svgexpire"
                                                  class="st2 st5 st9">##/##</text>
                                            <text transform="matrix(1 0 0 1 479.3848 417.0097)" class="st2 st10 st11">VALID</text>
                                            <text transform="matrix(1 0 0 1 479.3848 435.6762)" class="st2 st10 st11">THRU</text>
                                            <polygon class="st2" points="554.5,421 540.4,414.2 540.4,427.9 		"/>
                                        </g>
                                        <g id="cchip">
                                            <g>
                                                <path class="st2" d="M168.1,143.6H82.9c-10.2,0-18.5-8.3-18.5-18.5V74.9c0-10.2,8.3-18.5,18.5-18.5h85.3
                                                                     c10.2,0,18.5,8.3,18.5,18.5v50.2C186.6,135.3,178.3,143.6,168.1,143.6z"/>
                                            </g>
                                            <g>
                                                <g>
                                                    <rect x="82" y="70" class="st12" width="1.5" height="60"/>
                                                </g>
                                                <g>
                                                    <rect x="167.4" y="70" class="st12" width="1.5" height="60"/>
                                                </g>
                                                <g>
                                                    <path class="st12" d="M125.5,130.8c-10.2,0-18.5-8.3-18.5-18.5c0-4.6,1.7-8.9,4.7-12.3c-3-3.4-4.7-7.7-4.7-12.3
                                                                          c0-10.2,8.3-18.5,18.5-18.5s18.5,8.3,18.5,18.5c0,4.6-1.7,8.9-4.7,12.3c3,3.4,4.7,7.7,4.7,12.3
                                                                          C143.9,122.5,135.7,130.8,125.5,130.8z M125.5,70.8c-9.3,0-16.9,7.6-16.9,16.9c0,4.4,1.7,8.6,4.8,11.8l0.5,0.5l-0.5,0.5
                                                                          c-3.1,3.2-4.8,7.4-4.8,11.8c0,9.3,7.6,16.9,16.9,16.9s16.9-7.6,16.9-16.9c0-4.4-1.7-8.6-4.8-11.8l-0.5-0.5l0.5-0.5
                                                                          c3.1-3.2,4.8-7.4,4.8-11.8C142.4,78.4,134.8,70.8,125.5,70.8z"/>
                                                </g>
                                                <g>
                                                    <rect x="82.8" y="82.1" class="st12" width="25.8" height="1.5"/>
                                                </g>
                                                <g>
                                                    <rect x="82.8" y="117.9" class="st12" width="26.1" height="1.5"/>
                                                </g>
                                                <g>
                                                    <rect x="142.4" y="82.1" class="st12" width="25.8" height="1.5"/>
                                                </g>
                                                <g>
                                                    <rect x="142" y="117.9" class="st12" width="26.2" height="1.5"/>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                    <g id="Back">
                                    </g>
                                </svg>
                            </div>
                            <div class="back">
                                <svg version="1.1" id="cardback" xmlns="http://www.w3.org/2000/svg"
                                     xmlns:xlink="http://www.w3.org/1999/xlink"
                                     x="0px" y="0px" viewBox="0 0 750 471" style="enable-background:new 0 0 750 471;"
                                     xml:space="preserve">
                                    <g id="Front">
                                        <line class="st0" x1="35.3" y1="10.4" x2="36.7" y2="11"/>
                                    </g>
                                    <g id="Back">
                                        <g id="Page-1_2_">
                                            <g id="amex_2_">
                                                <path id="Rectangle-1_2_" class="darkcolor greydark" d="M40,0h670c22.1,0,40,17.9,40,40v391c0,22.1-17.9,40-40,40H40c-22.1,0-40-17.9-40-40V40
                                                                                                        C0,17.9,17.9,0,40,0z"/>
                                            </g>
                                        </g>
                                        <rect y="61.6" class="st2" width="750" height="78"/>
                                        <g>
                                            <path class="st3" d="M701.1,249.1H48.9c-3.3,0-6-2.7-6-6v-52.5c0-3.3,2.7-6,6-6h652.1c3.3,0,6,2.7,6,6v52.5
                                                                 C707.1,246.4,704.4,249.1,701.1,249.1z"/>
                                            <rect x="42.9" y="198.6" class="st4" width="664.1" height="10.5"/>
                                            <rect x="42.9" y="224.5" class="st4" width="664.1" height="10.5"/>
                                            <path class="st5"
                                                  d="M701.1,184.6H618h-8h-10v64.5h10h8h83.1c3.3,0,6-2.7,6-6v-52.5C707.1,187.3,704.4,184.6,701.1,184.6z"/>
                                        </g>
                                        <text transform="matrix(1 0 0 1 621.999 227.2734)" id="svgsecurity"
                                              class="st6 st7">xxx</text>
                                        <g class="st8">
                                            <text transform="matrix(1 0 0 1 518.083 280.0879)"
                                                  class="st9 st6 st10">security code</text>
                                        </g>
                                        <rect x="58.1" y="378.6" class="st11" width="375.5" height="13.5"/>
                                        <rect x="58.1" y="405.6" class="st11" width="421.7" height="13.5"/>
                                        <text transform="matrix(1 0 0 1 59.5073 228.6099)" id="svgnameback"
                                              class="st12 st13">Full Name</text>
                                    </g>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="form-container">
                        <form id="payment-form" action="{% url 'accounts:payment_add_card' %}" method="post">
                            {% csrf_token %}
                            <div class="field-container">
                                <label for="cardname">Name</label>
                                <input id="cardname" maxlength="20" type="text" placeholder="Name">
                            </div>
                            <div class="field-container">
                                <label for="cardnumber">Card Number</label>
                                <div id="cardnumber"></div>
                            {#                <input id="cardnumber" type="text" pattern="[0-9]*" inputmode="numeric">#}
                                <svg id="ccicon" class="ccicon" width="750" height="471" viewBox="0 0 750 471" version="1.1"
                                     xmlns="http://www.w3.org/2000/svg"
                                     xmlns:xlink="http://www.w3.org/1999/xlink">

                                </svg>
                            </div>
                            <div class="field-container">
                                <label for="cardexpiry">Expiration (MM/YY)</label>
                                <div id="cardexpiry"></div>
                            {#                <input id="expirationdate" type="text" pattern="[0-9]*" inputmode="numeric">#}
                            </div>
                            <div class="field-container">
                                <label for="cardcvc">Security Code</label>
                                <div id="cardcvc"></div>
                            {#                <input id="securitycode" type="text" pattern="[0-9]*" inputmode="numeric">#}
                            </div>
                        </form>
                    </div>
                </div>
                <input type="text" hidden id="cardnumber-hidden">
                <input type="text" hidden id="expirationdate-hidden">
                <input type="text" hidden id="securitycode-hidden">
                <div class="user-info__submit col-sm-12 text-right payment__action">
                    <button id="cancelButton" class="c-btn-tertiary" type="button">戻る</button>
                    {% buttons %}
                        <input type="submit" value='支払う' class="button btn c-btn-primary" id="cardsubmit"
                               style="box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);"/>
                    {% endbuttons %}
                </div>
            </div>
        </dialog>
        <dialog id="progress-modal">
            <div class="progressbar">
                <progress id="progress" value="0" max="100"></progress>
                <span>30%</span>
            </div>
            <p id="progress-message">決済しています...</p>
        </dialog>
    </section>
    <footer class="step4 hidden">
        <div class="button-wrapper">
            <button id="prevBtn4" class="c-btn-tertiary" type="button">戻る</button>
            <button id="nextBtn4" class="c-btn-primary" type="button">支払う</button>
        </div>
    </footer>

    <section class="step5 hidden">
        <ol class="stepper-container">
            <li class="stepper completed"><span>コードネーム</span></li>
            <li class="stepper completed"><span>目的物</span></li>
            <li class="stepper completed"><span>予算</span></li>
            <li class="stepper active"><span>支払い</span></li>
        </ol>

        <div class="group form-input">
            <ul>
                <li><a>ウォレットお支払い</a><a id="walletDebitDisplay" class="currency"></a><span>円（税抜）</span></li>
            </ul>
        </div>
        <div class="center-container">
            <div class="indicator-icon"><span class="material-symbols-rounded">wallet</span></div>
            <p>予算をウォレット残高から支払います。よろしいですか。</p>
        </div>
    </section>
    <footer class="step5 hidden">
        <div class="button-wrapper">
            <button id="prevBtn5" class="c-btn-tertiary" type="button">戻る</button>
            <button id="nextBtn5" class="c-btn-primary" type="button">支払う</button>
        </div>
    </footer>

    <section class="step6 hidden">
        <div class="center-container">
            <div class="indicator-icon"><span class="material-symbols-rounded">check_circle</span></div>
            <p>おめでとうございます。</p>
            <p>プロジェクトが作成されました。</p>
            <div class="group">
                <ul>
                    <li><a>コードネーム</a><a id="codenameDisplay"></a></li>
                    <hr>
                    <li><a>目的物</a><a id="projectOutputDisplay"></a></li>
                    <hr>
                    <li><a>予算</a><a id="projectBudgetDisplay" class="currency"></a><span>円（税込）</span></li>
                </ul>
            </div>
            <p>そのまま、プロジェクトの設定を続けますか？</p>
        </div>
    </section>
    <footer class="step6 hidden">
        <div class="button-wrapper">
            <button id="prevBtn6" class="c-btn-tertiary" type="button">ホーム画面へ</button>
            <button id="nextBtn6" class="c-btn-primary" type="button">続ける</button>
        </div>
    </footer>
{% endblock %}

{% block extra_script %}
    <!-- swiper読み込み -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script>let is_logged_in = '{{ user.is_authenticated }}';</script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <script src="https://js.stripe.com/v3/"></script>
    <script type="text/javascript" src="{% static 'js/artist_create_product.js' %}"></script>
{% endblock %}