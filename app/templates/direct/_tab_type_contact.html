{% load i18n %}
{% load util %}

<div class="contract__form-group">
    <div class="form-row row">
      <div class="col-sm-12 form-group" style="margin-bottom: 16px;">
        <label for="id_delivery">
          <div class="contract__form-label heading--13">{% trans "Deliverables" %}<span class="blue-label--8"> {% trans "required" %}</span></div>
        </label>
        {% with options='オーナーの指定する形式で納品, スタジオにて実演, WAVデータ一式をオーナーの指定する形式で納品'|create_option_component_select:'オーナーの指定する形式で納品, スタジオにて実演, WAVデータ一式をオーナーの指定する形式で納品' data_type='オーナーの指定する形式で納品, スタジオにて実演, WAVデータ一式をオーナーの指定する形式で納品'|check_exist_data_on_string:form_contract_and_plan.delivery_format|default_if_none:'' %}
          {% include 'input_box/component_input.html' with attribute='id="id_delivery" name="delivery_format"' className='required-field' value=form_contract_and_plan.delivery_format|default_if_none:'' placeholder='ＷＡＶデータ一式を甲の指定形式で納品' type='select-input' options=options data_type=data_type data_selected_value=data_type %}
        {% endwith %}
      </div>
    </div>
  </div>

  <div class="contract__form-group">
    <div class="form-row row">
      <div class="col-sm-12 form-group" style="margin-bottom: 0px">
        <label for="id_date_schedule">
          <div class="contract__form-label heading--13">{% trans "Period" %}</div>
        </label>
        {% include 'input_box/component_input.html' with className='js-daterangepicker' value=form_contract_and_plan.get_schedule_with_format type='rangetime' placeholder='yyyy/mm/dd - yyyy/mm/dd' attribute='id="id_date_schedule" name="schedule" autocomplete="off"' %}
      </div>
    </div>
  </div>

  <div class="contract__form-group">
    <div class="form-row row">
      <div class="col-sm-12 form-group" style="margin-bottom: 0">
        <label for="id_deadline_d">
          <div class="contract__form-label heading--13">予定納期</div>
        </label>
        <div class="form-row row form-row__mobile">
          <div class="col-sm-8 form-row__mobile-date" style="padding-right: 8px;">
            {% include 'input_box/component_input.html' with className='mcalendar mcalendar--small' value=form_contract_and_plan.get_date_of_deadline type='datetime' attribute='id="id_deadline_d" name="deadline_d"' placeholder="yyyy/mm/dd" %}
          </div>
          <div class="col-sm-4" style="padding-left: 0;">
            {% include 'input_box/component_input.html' with value=form_contract_and_plan.get_time_of_deadline type='time' placeholder='10:00' attribute='name="deadline_hours" id="id_time"' %}
          </div>
        </div>
      </div>
    </div>
  </div>
