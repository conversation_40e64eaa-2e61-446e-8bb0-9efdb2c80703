{% load static %}
{% load util %}
{% load i18n %}

<div class="mcolumn-header">
  <a class="mcolumn-back" href="#">
    <i class="icon icon--sicon-prev"></i>
  </a>
  <a class="mcolumn-toggle" href="#">
    <div class="mcolumn-header-toggle"><i class="icon icon--sicon-next"></i></div>
  </a>
</div>
<div class="mcolumn-content" data-offer="{{ real_offer.pk }}"
     data-role-offer="">
  <div class="minfo-wrap mscrollbar mscrollbar--vertical">
    <div class="accordion accordion--normal" id="accordionInformation">
      <div class="accordion-item">
        <div class="accordion-header" id="heading1">
          <div class="accordion-heading sheading sheading--13">
            {% if offer|get_offer_user:user == 'master_admin' and offer.is_deletable %}
              <div class="scene-title__action active">
                <a class="button-delete_offer" data-toggle="modal" data-target="#delete-offer">
                  <i class="icon icon--sicon-trash"></i>
                </a>
              </div>
            {% endif %}
          </div>
        </div>
        <div class="accordion-content collapse in" id="collapse1" aria-labelledby="heading1"
             data-parent="#accordionInformation" aria-expanded="true">
          <div class="accordion-content-container mscrollbar mscrollbar--vertical">
            <div class="minfo-section">
              <div class="minfo-list">
                <div class="infor-offer {% if offer.is_not_approved %}hide{% endif %}">
                  {% include 'direct/_item_infor_offer.html' with offer=offer %}
                </div>
                <div class="minfo--action">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="minfo-section minfor-document">
      <div class="minfo-upload">
      </div>
      <div class="tfile tfile-offer">
        <div class="tfile-list">
          <div class="mlast__file-offer"></div>
          {% for object in files_offer %}
            {% include 'direct/_item_file.html' with file=object type_comment='messenger_owner' user=user %}
          {% endfor %}
        </div>
      </div>
    <hr>
      <div class="tfile">
        <div class="tfile-list">
          <div class="mlast__file"></div>
          {% for key in dict_files %}
            {% with key|get_message_file_folder:type_comment as object %}
              {% if object %}
                {% if object.file_id %}
                  <div class="tfile-item">
                    <div class="tfile-item-time">{{ object.modified|get_updated_time }}</div>
                    <div class="tfile-item-content tfile-item-offer">
                      {% include 'top/_file_infor.html' with file=object message=object.message type_comment='messenger_owner' name=object.real_name user=user %}
                    </div>
                  </div>
                {% elif object.folder_id %}
                  <div class="tfile-item-time">{{ object.modified|get_updated_time }}</div>
                  <div class="tfile-item-content">
                    <div class="tfile-file">
                      {% include 'top/_sfolder.html' with folder=object message=object.message type_comment='messenger_owner' user=user%}
                    </div>
                  </div>
                {% endif %}
              {% endif %}
            {% endwith %}
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</div>
