{% load util %}

{% if case in '1,4' %}
<div class="messenger__item messenger__item-project {% if not seen %} messenger__item--new{% endif %}"
     data-offer="{{ offer_product.pk }}"
     data-offers="{{ offer_product|get_list }}"
     data-user="{{ user.pk }}">
    <div>
        <div class="project-item__member">
            <div class="project-item__member-list">
                {% with offer_product.project|get_owners_project  as owners %}
                    {% if owners %}
                        {% for user in owners %}
                            <div class="project-item__member-item background-avt"
                                 title="{{ user.get_display_name }}">
                                <img class="{% if not user.avatar %}owner-project{% endif %}"
                                     src="{{ user|get_avatar:'medium' }}" alt="">
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
        </div>
        <img src="{{ offer_product.project|get_image }}"
             alt="" style="width: 100%; border-radius: 10px"/>
    </div>
    <div class="messenger__status">
        <div class="messenger__tag {% if seen %} hide {% endif %}">
            New
        </div>
    </div>

    {% include "direct/_menu.html" with case=case offer_product=offer_product is_done=is_done %}
</div>
{% elif case in '2,5' %}
<div class="messenger__item messenger__item-project {% if not seen %} messenger__item--new{% endif %}"
     data-offer="{{ offer_product.pk }}"
     data-offers="{{ offer_product|get_list }}" style="height: 80px"
     data-user="{{ user.pk }}">

    <div>
        <div class="project-item__member">
            <div class="project-item__member-list">
                <div class="project-item__member-item background-avt"
                     title="{{ offer_product.master_client.get_display_name}}">
                    <img class="{% if not offer_product.master_client.avatar %}owner-project{% endif %}"
                         src="{{ offer_product.master_client|get_avatar:'medium' }}" alt="">
                </div>
            </div>
        </div>
    </div>

    <div class="messenger__info">
        <div class="messenger__mess">
            <div class="messenger__count {% if seen %}hide{% endif %}">{{ offer_product|count_new_message:user }}</div>
        </div>
    </div>
    <div class="messenger__status">
        <div class="messenger__tag {% if seen %} hide {% endif %}">
            New
        </div>
    </div>

    {% include "direct/_menu.html" with case=case offer_product=offer_product is_done=is_done %}
</div>
{% elif case == '3' %}
<div class="messenger__item {% if not seen %}messenger__item--new{% endif %}"
     data-offer="{{ offer_product.pk }}"
     data-offers="{{ offer_product|get_list }}"
     data-user="{{ user.pk }}">

    <div class="messenger__avatar background-avt">
        <img class="messenger__avatar-img"
             src="{{ offer_product.master_client|get_avatar:'medium' }}"
             alt=""/>
        <div class="messenger__user-active hide"></div>
    </div>
    <div class="messenger__info">
        <div class="messenger__name">{{ offer_product.master_client.get_full_name }}</div>
        <div class="messenger__work">{{ offer_product.master_client.position|hide_if_empty }}</div>
        <div class="messenger__mess">{{ offer_product.master_client.enterprise|hide_if_empty }}
            <div class="messenger__seen background-avt {% if not seen %}hide{% endif %}">
                <img class="messenger__seen-img"
                     src="{{ user|get_avatar:'small' }}"
                     alt="">
            </div>
            <div class="messenger__count {% if seen %}hide{% endif %}">{{ offer_product|count_new_message:user }}</div>
        </div>
    </div>
    <div class="messenger__status">
        <div class="messenger__tag {% if seen %}hide{% endif %}">
            New
        </div>
    </div>
    <div class="messenger__time">{{ offer_product.modified|get_weekday }}</div>

    {% include "direct/_menu.html" with case=case offer_product=offer_product is_done=is_done %}
</div>
{% elif case == '6' %}
    <div class="messenger__item {% if not seen %} messenger__item--new{% endif %}"
         data-offer="{{ offer_product.pk }}"
         data-offers="{{ offer_product|get_list }}"
         data-user="{{ user.pk }}">
        <div class="messenger__avatar background-avt">
            <img class="messenger__avatar-img"
                 src="{{ owner|get_avatar:'medium' }}"
                 alt=""/>
            <div class="messenger__user-active hide"></div>
        </div>
        <div class="messenger__info">
            <div class="messenger__name">{{ owner.get_full_name }}</div>
            <div class="messenger__work">{{ owner.position|hide_if_empty }}</div>
            <div class="messenger__mess">{{ owner.enterprise|hide_if_empty }}
                <div class="messenger__seen background-avt {% if not seen %}hide{% endif %}">
                    <img class="messenger__seen-img"
                         src="{{ user|get_avatar:'small' }}"
                         alt="">
                </div>
                <div class="messenger__count {% if seen %}hide{% endif %}">{{ offer_product|count_new_message:user }}</div>
            </div>
        </div>
        <div class="messenger__status">
            <div class="messenger__tag {% if seen %} hide {% endif %}">
                New
            </div>
        </div>
        <div class="messenger__time">{{ offer_product.modified|get_weekday }}</div>

        {% include "direct/_menu.html" with case=case offer_product=offer_product is_done=is_done %}
    </div>
{% endif %}
