{% load static %}
{% load util %}
{% load i18n %}

{% with offer|get_offer_user:user as offer_user %}
  {% if offer.condition in '1, 2' and offer_user == 'master_admin' %}
    {#  master admin upload file contract #}
    <div class="minfo-contract">
      {% if offer.condition == '1' %}
        <div class="minfo-contract-icon get-data-offer" data-toggle="modal" data-target="#modal-upload-contract-plan">
          <i class="icon icon--sicon-contract"></i>
        </div>
      {% else %}
        {% with offer.get_contract_in_offer as file %}
          <div class="minfo-contract-icon get-data-offer minfo-file_info" data-file-id="{{ file.pk }}"
               data-type="{{ file.is_audio_file }}"
               data-link="{{ file.file.url }}"
               data-name="{{ file.real_name }}"
               data-toggle="modal"
               data-target="#modal-{{ file.is_audio_file }}-popup"
               data-message-id="{{ file.message.pk }}">
            <i class="icon icon--sicon-contract"></i>
          </div>
        {% endwith %}
      {% endif %}
      <div class="minfo-contract-btn get-data-offer">
        <button class="btn btn--primary btn--blue btn--lg" data-toggle="modal"
                data-target="#modal-upload-contract-plan">
          <span class="btn-text">{% trans "button upload contract" %}</span>
        </button>
      </div>
    </div>

  {% elif offer.condition == '2' and offer_user == 'owner' %}
    {#  master client checked contract #}
    {% comment %} {% include 'direct/_message_check_contract.html' with offer=offer user=user %} {% endcomment %}

  {% elif offer.condition == '3' and offer_user == 'owner' %}
    {#  master client check done offer #}
    {% comment %} {% with offer.get_contract_in_offer as file %}
      <div class="minfo-contract">
        <div class="form-check custom-checkbox file-name--confirm minfo-file_info" data-file-id="{{ file.pk }}"
             data-type="{{ file.is_audio_file }}"
             data-link="{{ file.file.url }}"
             data-name="{{ file.real_name }}"
             data-toggle="modal"
             data-target="#modal-{{ file.is_audio_file }}-popup"
             data-message-id="{{ file.message.pk }}">
          <div class="minfo-contract-icon get-data-offer minfo-file_info" data-file-id="{{ file.pk }}">
            <i class="icon icon--sicon-accept"></i>
          </div>
        </div>
        <div class="minfo-accept minfo-confirm" style="margin-top: 20px">
          <div class="minfo-accept__action get-data-offer" data-toggle="modal"
               data-target="#modal-confirm-done-offer">
            <a class="btn btn--primary btn--blue btn--lg"
               href="javascript:void(0)">
              <span class="btn-text">{% trans "button check done offer" %}</span>
            </a>
          </div>
        </div>
      </div>
    {% endwith %} {% endcomment %}


  {% elif offer.condition in '4, 5' and offer_user == 'master_admin' and not offer.receipt_id %}
    {#  master admin upload bill #}
    <div class="minfo-contract">
      {% if offer.condition == '4' %}
        <div class="minfo-contract-icon get-data-offer" data-toggle="modal" data-target="#modal-drag-file">
          <i class="icon icon--sicon-contract"></i>
        </div>
      {% else %}
        {% with offer.get_bill_in_offer as file %}
          <div class="minfo-contract-icon get-data-offer minfo-file_info" data-file-id="{{ file.pk }}"
               data-type="{{ file.is_audio_file }}"
               data-link="{{ file.file.url }}"
               data-name="{{ file.real_name }}"
               data-toggle="modal"
               data-target="#modal-{{ file.is_audio_file }}-popup"
               data-message-id="{{ file.message.pk }}">
            <i class="icon icon--sicon-contract"></i>
          </div>
        {% endwith %}
      {% endif %}
      <div class="minfo-contract-btn">
        <button class="btn btn--primary btn--blue btn--lg get-data-offer" data-toggle="modal"
                data-target="#modal-drag-file">
          <span class="btn-text">{% trans "button upload bill" %}</span>
        </button>
      </div>
    </div>

  {% elif offer.condition == '5' and offer_user == 'owner' and not offer.receipt_id %}
    {#  master client payment #}
    {% comment %} {% include 'direct/_message_payment.html' with offer=offer user=user %} {% endcomment %}
  {% endif %}
{% endwith %}
