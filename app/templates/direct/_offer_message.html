{% load static %}
{% load util %}
{% load user_agents %}
{% load compress %}
{% compress css %}
<style>
  .video-comment-seen-item-img {
    width: 15px;
    height: 15px;
    border-radius: 50%;
  }

  .messenger-detail .messenger-director__item-seen {
      top: auto;
      bottom: -20px;
      background: transparent;
      height: 20px;
      overflow: hidden;
      position: absolute;
      left: 0;
      margin: 0;
  }
</style>
{% endcompress %}
{#   go to project#}
{% if offer.project and user.role == 'master_client' %}
  <div class="messenger-popup__action go-to-project">
    <div class="messenger-popup__action-button">
      <a class="gradient button--gradient button--gradient-primary button--round button-modal"
         href="{% url 'app:top_project_detail' offer.project.pk %}" target="_blank"
      >プロジェクトを観る
      </a>
    </div>
  </div>
{% endif %}
<div class="messenger-detail-content {% if request|is_pc %}custom-scrollbar custom-scrollbar--bottom{% endif %}">
  <div class="messenger-director__list {% if is_seen == False %}not-seen{% endif %}" data-offer="{{ offer.pk }}" data-user="{{ user.id }}" >
    <div class="messenger-director__item messenger-director__item{% if is_owner %}--right {% endif %} no-border">
        {% if offer.description %}
            <div class="messenger-director__item-content">
                <div class="messenger-director__item-avatar background-avt">
                    <img class="messenger-director__item-avatar-img"
                         src="{{ offer.master_client|get_avatar:'medium' }}" alt=""/>
                    <div class="messenger-director__item-time">{{ offer.created|get_weekday }}</div>
                </div>
                <div class="messenger-director__item-info info-offer">
                    <div class="messenger-director__item-mess"><span>{{ offer.description }}</span></div>
                    {% if offer.file %}
                        <div class="messenger-director__item-mess align-center">
                            <div class="comment__download comment__download--bottom">
                                <i class="fas fa-download" data-offer="{{ offer.pk }}"></i>
                                <a class="comment__download-icon-down"
                                   href="{{ offer.file.url }}" target="_blank">{{ offer.get_file_name }}</a>
                            </div>
                        </div>
                    {% endif %}
                    <div class="messenger-form">
                        {% if offer.budget %}
                            <div class="messenger-popup__form-input ">
                                <div style="text-align: left; margin-bottom: 10px;"> 予算目安（税抜）</div>
                                <input class="form-control " type="text" name="budget"
                                       value="{{ offer.budget }} 円" readonly/>
                            </div>
                        {% endif %}
                        {% if offer.deadline %}
                            <div class="messenger-popup__form-input ">
                                <div style="text-align: left; margin-bottom: 10px;"> 期限</div>
                                <input class="form-control" type="text"
                                       value="{{ offer.get_deadline_date }}" readonly="readonly"/>
                            </div>
                        {% endif %}
                        <div class="messenger-director__column-2">
                            <div class="form-group">
                                <div class="form-description">期限までの残り時間</div>
                            </div>
                            {% if offer.deadline %}
                                <div class="form-group">
                                    <div class="form-description">{{ offer.get_remaining_day }}days</div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
  {% with offer|list_latest_seen_message as list_message %}
    {% for message in offer.message_product.all %}
      {% if message.status != '4' %}
          <div class="messenger-director__item {% if message.owner.role == user.role %}messenger-director__item--right{% else %}messenger-director__item--left{% endif %}">
              <div class="messenger-director__item-content">
                  <div class="messenger-director__item-avatar background-avt">
                      <img class="messenger-director__item-avatar-img"
                           src="{{ message.owner|get_avatar:'medium' }}" alt="">
                      <div class="messenger-director__item-time">{{ message.created|get_weekday }}</div>
                  </div>
                  <div class="messenger-director__item-info">
                    {% if message.variation and message.type != '1' %}
                        <div class="video-pin-time {% if not message|is_message_for_old_version:offer %}gray{% endif %}"
                             data-variation="{{ message.variation.pk }}"
                             data-type="{{ message.type }}">
                        </div>
                    {% endif %}
                      <div class="messenger-director__item-mess"><span>{{ message.content }}</span></div>
                    {% with  offer.last_message as latest_message %}
                      {% if message in list_message %}
                          <div class="messenger-director__item-seen background-avt">
                            {% with  message|list_viewers_seen:user as viewers %}
                              {% for viewer in viewers %}
                                  <img class="video-comment-seen-item-img"
                                       src="{{ viewer|get_avatar:'small' }}" data-user="{{ viewer.pk }}"
                                       alt="">
                              {% endfor %}
                            {% endwith %}
                          </div>
                        {% else %}
                          <div class="messenger-director__item-seen background-avt">
                          </div>
                      {% endif %}
                    {% endwith %}
                  </div>
              </div>
          </div>
      {% endif %}
      {% if message.real_name %}
        <div style="justify-content: center;display: flex;padding-bottom: 10px;">
          <a class="messenger-director__item-file" target="_blank" style="display: contents;"
             href="{{ message.file }}"> {{ message.real_name }}</a>
        </div>
      {% endif %}
    {% endfor %}
  {% endwith %}

    {#  create project#}
    {% if user.role == 'master_admin' and offer.status == '5' %}
      <div class="messenger-popup__action">
        <div class="messenger-popup__action-button">
          <a class="gradient button--gradient button--gradient-primary button--round button-modal create--product"
             id="create-product-{{ offer.pk }}" data-offer="{{ offer.pk }}">プロジェクトを作成
          </a>
        </div>
      </div>
    {% endif %}

    {#    input message#}
    <div class="messenger-director__item-reply">
      <div class="messenger-detail__input">
        <div class="video-comment-input-pin active">
          <img src="{% static 'images/icon-pin.svg' %}" alt="">
        </div>
        <textarea class="messenger-detail__input-text cs-textarea autoExpand"
                  name="messenger-input"
                  placeholder="コメントを入力…"></textarea>
        <div class="messenger-detail__button-send">
          <a class="button button--text button--text-primary button--disabled" href="javascript:void(0)"
             role="button">送信</a>
        </div>
      </div>
    </div>
  </div>
</div>
