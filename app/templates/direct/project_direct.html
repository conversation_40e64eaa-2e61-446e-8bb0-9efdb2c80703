{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.css">
    {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/messenger_project.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/messenger_director.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
  <style>
      .messenger__item {
          padding: 0;
      }


      .messenger__column-right {
          overflow: hidden;
      }
  </style>
  <main class="messenger-director">
    <div class="container">
      <div class="new-video-menu">
        <div class="project-list">
          <div class="project-item active">
            <div class="project-item__content">
              <div class="project-item__top">
                <div class="project-item__filter">
                  <a href="/direct/new?tab=new">
                    <div class="project-item__filter-item {% if status == 'new' %} active {% endif %}" data-show="new">
                    </div>
                  </a>
                  <a href="{% url 'app:direct_inbox_negotiating' %}">
                    <div class="project-item__filter-item {% if status == 'negotiating' %} active {% endif %}"
                         data-show="waiting"></div>
                  </a>
                  <a href="{% url 'app:direct_inbox_processing' %}">
                    <div class="project-item__filter-item {% if status == 'processing' %} active {% endif %}"
                         data-show="processing">Project
                    </div>
                  </a>
                </div>
              </div>
                <div class="project-tab project-tab-processing active">
                    <div class="messenger-director-processing">
                        <div class="row messenger__column">
                            <div class="col-md-5 col-sm-5 messenger__column-left">
                                <div class="messenger__list custom-scrollbar">
                                  {% if user.role == 'master_admin' %}
                                    {% if offer_products.exists %}
                                      {% for offer in offer_products %}
                                          {% with offer|is_project_seen:user as seen %}
                                              <div class="messenger__item {% if not seen %}messenger__item--new{% endif %}"
                                                   data-user="{{ offer.master_client.pk }}"
                                                   data-offer="{{ offer.pk }}"
                                                   data-offers="{{ offer|get_list }}">
                                                <div>
                                                  <img src="{{ offer.project|get_image }}"
                                                       alt="" style="width: 100%; border-radius: 10px"/>
                                                </div>
                                              </div>
                                          {% endwith %}
                                      {% endfor %}
                                    {% endif %}
                                  {% elif user.role == 'master_client' %}
                                    {% if offer_products.exists %}
                                      {% for offer in offer_products %}
                                          {% with offer|is_project_seen:user as seen %}
                                              <div class="messenger__item {% if not seen %}messenger__item--new{% endif %}"
                                                   data-user="master_admin"
                                                   data-offer="{{ offer.pk }}"
                                                   data-offers="{{ offer|get_list }}">
                                                <div>
                                                  <img src="{{ offer.project|get_image }}"
                                                       alt="" style="width: 100%; border-radius: 10px" />
                                                </div>
                                              </div>
                                          {% endwith %}
                                      {% endfor %}
                                    {% endif %}
                                  {% endif %}
                                </div>
                            </div>
                            <div class="col-md-7 col-sm-7 messenger__column-right">
                            </div>
                        </div>
                    </div>
                  {% if user.role == 'master_client' %}
                    <div class="messenger-popup modal fade animate" id="messenger-popup" role="dialog">
                      <form method="post" action="{% url 'app:direct_inbox' %}" id="create_inbox"
                            enctype="multipart/form-data">
                        <div class="messenger-popup__content">
                          <div class="messenger-popup__form">
                            <div class="messenger-popup__form-text">
                              お問い合わせありがとうございます。<br>動画や資料をこちらからお送り下さい。<br>機密厳守。お見積もりは無料です。
                            </div>
                            {% csrf_token %}
                            <div class="messenger-form__confirm align-center">
                              <div class="checkbox input-checkbox">
                                <input type="checkbox" name="contract-confirm" id="contract-confirm">
                                <label for="contract-confirm">
                                  <a href="#">販売同意書</a>を確認しました。
                                </label>
                              </div>
                            </div>
                            <div class="messenger-popup__form-input input-description hide">
                              <div style="text-align: left; margin-bottom: 10px;">ご相談内容</div>
                              <textarea name="messenger-input" placeholder="（例)&#10新規ゲームタイトルの音付けを相談したいです。添付企画書を踏まえて、ナレーションは、候補をいくつか提案ください。楽曲イメージは、仕様書を元にお任せします。"
                                        class="messenger-detail__input-text cs-textarea autoExpand"
                                        id="description"
                                        style="height: 250px;"></textarea>
                            </div>
                            <div class="messenger-popup__form-input">
                              <div class="messenger-popup__file">
                                <div class="messenger-popup__file-content">
                                  <input class="messenger-popup__file-input hide" type="file"
                                         name="files[]"
                                         id="file-create" multiple>
                                  <button class='messenger-popup__file-btn button--gradient background button--background-gray button--round button--small hide'>
                                    ファイルを添付
                                  </button>
                                </div>
                              </div>
                            </div>
                            <br><br>
                            <div class="messenger-popup__form-input input-date hide">
                              <div style="text-align: left; margin-bottom: 10px;"> 希望納期</div>
                              <input class="form-control form-group form-datepicker from_date_datepicker" type="text"
                                     name="to-date"
                                     value=""
                                     id="to-date"/>
                            </div>
                            <div class="messenger-popup__form-input input-budget hide">
                              <div class="currency">
                                <div style="text-align: left; margin-bottom: 10px;"> 予算目安（税抜）</div>
                                <input class="form-control " type="number" min="0" name="budget"
                                       value="" id="budget"/><span>円</span>
                              </div>
                            </div>
                          </div>

                          <div class="messenger-popup__action">
                            <div class="messenger-popup__action-button">
                              <a class="gradient button--gradient button--gradient-primary button--round button--disabled"
                                 id="start">相談を始める
                              </a>
                              <button class="button button--gradient button--gradient-primary button--round hide"
                                      role="button" id="submit">この内容で相談
                              </button>
                              <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a>
                                へ電話で相談
                              </div>
                              <br>
                            </div>
                            <div class="messenger-popup__action-button">
                              <button style="background-color: white;" data-dismiss="modal"
                                      class="close-modal">キャンセル
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="messenger-popup modal fade animate" id="modal-add-card" role="dialog">
                      <div class="messenger-popup__content">
                        <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">支払い情報がまだ登録していませんので、<br>まず、支払い情報をご登録申し上げます。
                      </div>
                    </div>
                        <div class="messenger-popup__action">
                          <div class="messenger-popup__action-button">
                            <a class="gradient button--gradient button--gradient-primary button--round link-add--card"
                            >支払い情報を登録</a>
                            <br>
                          </div>
                          <div class="messenger-popup__action-button">
                            <button style="background-color: white;" data-dismiss="modal"
                                    class="close-modal">キャンセル
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="messenger-popup modal fade animate" id="modal-choose--card" role="dialog">
                        <div class="messenger-popup__content">
                          <div class="messenger-popup__form">
                            <br>
                            <div style="text-align: left"><span>合計: </span><span class="total-bill" style="position: absolute;left: 50%;"></span></div>
                          <div class="messenger-popup__action">
                            <div class="messenger-popup__action-button">
                              <button id="payment_submit" class="button button--gradient button--gradient-primary button--round button--disabled"
                                      role="button">決済する
                              </button>
                              <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a>
                                へ電話で相談
                              </div>
                              <br>
                            </div>
                            <div class="messenger-popup__action-button">
                              <button style="background-color: white;" data-dismiss="modal"
                                      class="close-modal">キャンセル
                              </button>
                            </div>
                          </div>
                        </div>
                        </div>
                    </div>
                  {% endif %}
                {% if user.role == 'master_admin' %}
                  <div class="messenger-popup-product modal fade animate" id="messenger-popup-product" role="dialog"
                       data-offer="">
                    <form method="post" action="{% url 'app:create_product' %}" id="create_product"
                          enctype="multipart/form-data">
                      {% csrf_token %}
                      <div class="messenger-popup__content_plan">
                        <div class="messenger-popup__form">
                          <div class="messenger-popup__form-text">プロジェクトを作成
                          </div>
                        </div>
                        <input type="hidden" name="offer-active" id="offer-id" value="">
                        <div class="messenger-popup__form-input">
                          <label for="project-name" style="text-align: left">プロジェクト名</label>
                          <input type="text" class="messenger-input-text input-project" name="project-name"
                                 id="project-name"
                                 placeholder="プロジェクト名">
                        </div>
                        <div class="messenger-popup__action">
                          <div class="messenger-popup__action-button">
                            <a class="button button--gradient button--gradient-primary button--round"
                               href="#"
                               role="button" id="submit-create-product">登録</a>
                          </div>
                          <div class="messenger-popup__action-button">
                            <a class="button button--text button--text-gray close-modal" href="#" role="button"
                               data-dismiss="modal" style="background-color: white;">キャンセル</a>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                  <div class="messenger-popup-product modal fade animate" id="messenger-popup-bill" role="dialog"
                       data-offer="">
                    <form method="post" action="{% url 'app:upload_bill' %}" id="upload_bill"
                          enctype="multipart/form-data">
                      {% csrf_token %}
                      <div class="messenger-popup__content_plan">
                        <input type="hidden" name="offer-active" class="offer-active" value="">
                        <div class="messenger-popup__form-input">
                          <label for="project-name" style="text-align: left">合計</label>
                          <input type="number" class="messenger-input-text input-project" name="project-amount"
                                 id="project-amount"
                                 placeholder="amount" style="text-align: right; border: none;" min="100" max="99999999" required ><span> 円</span>
                        </div>
                        <div class="messenger-popup__form-input">
                            <div class="messenger-popup__file">
                              <div class="messenger-popup__file-content">
                                <input class="messenger-popup__file-input hide" type="file" name="bill"
                                       id="upload-bill-file" accept="application/pdf">
                                <label for="upload-bill-file" class='messenger-popup__file-btn button--gradient background button--background-gray button--round button--small'>
                                  ファイルを選択
                                </label>
                                <div class="bill-file"></div>
                              </div>
                            </div>
                          </div>
                        <div class="messenger-popup__action">
                          <div class="messenger-popup__action-button">
                            <a class="button button--gradient button--gradient-primary button--round button--disabled"
                               href="#"
                               role="button" id="submit-upload-bill">アップロード</a>
                          </div>
                          <div class="messenger-popup__action-button">
                            <a class="button button--text button--text-gray close-modal" href="#" role="button"
                               data-dismiss="modal" style="background-color: white;">キャンセル</a>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                {% endif %}
                <div class="messenger-popup modal fade animate" id="create-offer-popup" role="dialog">
                  <div class="messenger-popup__content">
                    <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">資料及びオーダーを承りました。<br>内容を確認の上、お見積もりプランをご提案しますので、お待ちください。
                      </div>
                    </div>
                    <div class="messenger-popup__action">
                      <div class="messenger-popup__action-button">
                        <a class="gradient button--gradient button--gradient-primary button--round close-modal"
                           data-dismiss="modal">OK</a>
                        <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a> へ電話で相談
                        </div>
                        <br>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="messenger-popup modal fade animate" id="choose-plan-popup" role="dialog">
                  <div class="messenger-popup__content">
                    <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">注文を承りました。<br>契約書ドラフトをご用意しますので、お待ちください。
                      </div>
                    </div>
                    <div class="messenger-popup__action">
                      <div class="messenger-popup__action-button">
                        <a class="gradient button--gradient button--gradient-primary button--round close-modal"
                           data-dismiss="modal">OK</a>
                        <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a> へ電話で相談
                        </div>
                        <br>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="messenger-popup modal fade animate" id="accept-contact-popup" role="dialog">
                  <div class="messenger-popup__content">
                    <div class="messenger-popup__form">
                      <div class="messenger-popup__form-text">注文を承りました。<br>プロジェクトを御用意しますので、お待ちください。
                      </div>
                    </div>
                    <div class="messenger-popup__action">
                      <div class="messenger-popup__action-button">
                        <a class="gradient button--gradient button--gradient-primary button--round close-modal"
                           data-dismiss="modal">OK</a>
                        <div class="button-telephone">または <a href="tel:+81-3-6457-1780">03-6457-1780</a> へ電話で相談
                        </div>
                        <br>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
    {% compress js inline %}
  <script>
      let is_pc = '{{ request|is_pc }}' === 'True';
      var dict_chatsocket = new Map();
      var sk_protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
      var socket_url = sk_protocol + window.location.host;

      $(document).ready(function () {
          var list_contacts = $('.messenger__item');
          $(list_contacts).each(function (index, item) {
              let offer_ids = $(item).data('offers');
              for (let i = 0; i < offer_ids.length; i++) {
                  let chatSocket = new WebSocket(
                      socket_url + '/ws/messenger/' + offer_ids[i]);
                  dict_chatsocket.set(offer_ids[i], chatSocket);
              }
          });
      });

      function initSocket(offer_id) {
          var current_chatsocket = dict_chatsocket.get(offer_id);
          current_chatsocket.onmessage = function (e) {
              let data = JSON.parse(e.data);
              let offer = e.target.url.replace(socket_url + '/ws/messenger/', '');
              let target_offer;
              let get_offer = $('.offer-' + offer_id);
              let current_receiver = $('.messenger__item.messenger__item--selected').attr('data-user').toString();
              let receiver = "";
              $('.messenger__item').each(function () {
                  let offers = $(this).attr('data-offers');
                  if (offers.includes(String(offer_id))) {
                      target_offer = $(this)
                  }
              });

              switch (data.event.action) {
                  case 'new_message':
                      let message = JSON.parse(data.event.message)[0].fields;
                      let role = '{{  user.role }}';
                      let role_seen = '';
                      let role_message = '';
                      let role_owner = data.event.owner_role;
                      if (role === 'master_admin') {
                          if (data.event.master_client_id) {
                              receiver = data.event.master_client_id.toString()
                          }
                      }
                      if (role === 'master_client') {
                          receiver = data.event.master_admin_id.toString()
                      }
                      if (current_receiver === receiver) {
                          let seen_avatar;
                          let owner_avatar;
                          if (data.event.owner_avatar) {
                              owner_avatar = data.event.owner_avatar;
                          } else {
                              owner_avatar = '{% static 'images/default-avt.png' %}';
                              if (role_owner === 'master_admin'){
                                  role_message = 'master_admin'
                              }else {
                                  role_message = 'master_client'
                              }
                          }
                          if ('{{ user.role }}' === 'master_client') {
                              if (data.event.master_admin_avatar) {
                                  seen_avatar = data.event.master_admin_avatar
                              }else {
                                  seen_avatar = '{% static 'images/default-avt.png' %}';
                                  role_seen = 'master_admin';
                              }

                          } else {
                              if (data.event.master_client_avatar) {
                                  seen_avatar = data.event.master_client_avatar;
                              } else {
                                  seen_avatar = '{% static 'images/default-avt.png' %}';
                                  role_seen = 'master_client';
                              }

                          }

                          if (message.owner === {{ user.pk }}) {
                              let new_message = `<div class="messenger-director__item messenger-director__item--right">
                                    <div class="messenger-director__item-content">
                                        <div class="messenger-director__item-avatar background-avt">
                                            <img class="messenger-director__item-avatar-img"
                                                 src="${owner_avatar}" alt="">
                                            <div class="messenger-director__item-time"></div>
                                        </div>
                                        `;
                              if (message.status === '1') {
                                  new_message += `<div class="messenger-director__item-info">
                                            <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                            <div class="messenger-director__item-seen background-avt hide ${role_seen}">
                                                <img class="messenger__seen-img"
                                                     src="${seen_avatar}" alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>`
                              } else if (message.status === '4') {
                                  new_message += `<div class="messenger-director__item-info">
                                            <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                            <div class="messenger-popup__action show--bill">
                                                <div class="messenger-popup__action-button">
                                                  <a class="button button--black button--gradient-primary
                                                  button--round show-bill button-show" role="button">請求書</a>
                                                </div>
                                             </div>
                                            <div class="messenger-director__item-seen background-avt hide ${role_seen}">
                                                <img class="messenger__seen-img"
                                                     src="${seen_avatar}" alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>`
                              } else if (message.status === '2') {
                                  new_message += `<div class="messenger-director__item-info">
                                                          <div class="messenger-director__item-mess">
                                                          <span>${message.content}</span></div>
                                                          <div class="messenger-popup__action">
                                                          <div class="messenger-popup__action-button">
                                                          <a class="button button--black button--gradient-primary
                                                          button--round show-plan button-show" role="button">見積書</a>
                                                           </div>
                                                      </div>
                                                        <div class="messenger-director__item-seen background-avt
                                                        hide ${role_seen}">
                                                      <img class="messenger__seen-img" src="${seen_avatar}" alt="">
                                                      </div>
                                                      </div>
                                                      </div>
                                                       </div>`
                              } else if (message.status === '3') {
                                  new_message += `<div class="messenger-director__item-info">
                                            <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                            <div class="messenger-popup__action show--contract">
                                                <div class="messenger-popup__action-button">
                                                    <a class="button button--black button--gradient-primary
                                                    button--round show-contract button-show" role="button">契約書</a>
                                                 </div>
                                            </div>
                                            <div class="messenger-director__item-seen background-avt hide ${role_seen}">
                                                <img class="messenger__seen-img"
                                                     src="${seen_avatar}" alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>`
                              }

                              $(new_message).insertBefore(get_offer.find('.messenger-director__item-reply'));
                              if (message.real_name) {
                                  $(`<div style="justify-content: center;display: flex;padding-bottom: 10px;">
                                       <a class="messenger-director__item-file" target="_blank"
                                       style="display: contents;" href="${message.file}">
                                       ${message.real_name}</a></div>`).insertBefore(get_offer.find('.messenger-director__item-action'))
                              }
                          } else {
                              let new_message = `<div class="messenger-director__item messenger-director__item--left">
                                    <div class="messenger-director__item-content">
                                        <div class="messenger-director__item-avatar  background-avt">
                                            <img class="messenger-director__item-avatar-img ${role_message}"
                                                 src="${owner_avatar}" alt="">
                                            <div class="messenger-director__item-time"></div>
                                        </div>`;
                              if (message.status === '1') {
                                  new_message += `<div class="messenger-director__item-info">
                                            <div class="messenger-director__item-mess"><span>${message.content}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>`
                              } else if (message.status === '4') {
                                  new_message += `<div class="messenger-director__item-info">
                                  <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                  <div class="messenger-popup__action show--bill">
                                       <div class="messenger-popup__action-button">
                                          <a class="button button--black button--gradient-primary button--round
                                                 show-bill button-show" role="button">請求書</a>
                                       </div>
                                  </div>
                                   </div>
                                   </div></div>`
                              } else if (message.status === '2') {
                                  new_message += `<div class="messenger-director__item-info">
                                  <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                  <div class="messenger-popup__action">
                                    <div class="messenger-popup__action-button">
                                      <a class="button button--black button--gradient-primary
                                      button--round show-plan button-show" role="button">見積書</a>
                                    </div>
                                  </div>
                                  </div>
                                  </div>
                                </div>`
                              } else if (message.status === '3') {
                                  new_message += `<div class="messenger-director__item-info">
                                  <div class="messenger-director__item-mess"><span>${message.content}</span></div>
                                  <div class="messenger-popup__action show--contract">
                                     <div class="messenger-popup__action-button">
                                        <a class="button button--black button--gradient-primary
                                        button--round show-contract button-show" role="button">契約書</a>
                                     </div>
                                  </div>
                                  </div></div></div>`
                              }
                              $(new_message).insertBefore(get_offer.find('.messenger-director__item-reply'));
                              if (message.real_name) {
                                  $(`<div style="justify-content: center;display: flex;padding-bottom: 10px;">
                                       <a class="messenger-director__item-file" target="_blank"
                                       style="display: contents;" href="${message.file}">
                                       ${message.real_name}</a></div>`).insertBefore(get_offer.find('.messenger-director__item-action'))
                              }
                              get_offer.find('.messenger-director__list').addClass('not-seen');
                              target_offer.addClass('messenger__item--new');
                              target_user.find('.messenger__tag').removeClass('hide');
                              target_user.find('.messenger__seen').not('.hide').addClass('hide');
                          }
                          $('.messenger-detail-content').mCustomScrollbar('scrollTo', 'last', {
                              scrollInertia: 300
                          });
                          target_offer.insertBefore($('.messenger__item')[0]);
                      } else {
                          if (target_offer.length) {
                              target_offer.addClass('messenger__item--new');
                              target_offer.find('.messenger__tag').removeClass('hide');
                              target_offer.find('.messenger__seen').not('.hide').addClass('hide');
                              target_offer.insertBefore($('.messenger__item')[0]);
                          }
                      }
                      target_offer.insertBefore($('.messenger__item')[0]);
                      break;
                  case 'seen':
                      get_offer.find(('.messenger-director__item-seen ')).removeClass('hide');
                      target_offer.find('.messenger__tag').addClass('hide');
                      target_offer.removeClass('messenger__item--new');
                      get_offer.find('.messenger__seen').removeClass('hide');
                      get_offer.find('.messenger-director__list').removeClass('not-seen');
                      break;
                  default:
                      break;
              }
          };
          current_chatsocket.onclose = function (e) {
              console.error('Chat socket closed!');
          };
      }
  </script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
  <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
  <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/additional-methods.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/direct.js' %}"></script>
    {% endcompress %}
{% endblock %}
