{% load static %}
{% load util %}
{% load i18n %}
{% with offer.get_contract_in_offer as file %}
  <div class="minfo-contract">
    <div class="form-check custom-checkbox file-name--confirm minfo-file_info" data-file-id="{{ file.pk }}"
             data-type="{{ file.is_audio_file }}"
             data-link="{{ file.file.url }}"
             data-name="{{ file.real_name }}"
             data-toggle="modal"
             data-target="#modal-{{ file.is_audio_file }}-popup"
             data-message-id="{{ file.message.pk }}">
      <div class="minfo-contract-icon get-data-offer minfo-file_info">
        <i class="icon icon--sicon-accept"></i>
      </div>

      <div class="minfo-accept__policy" style="margin: 10px 0">
        <div><i class="icon icon--sicon-clip" style="line-height: unset"></i>{{ file.real_name }}
        </div>
      </div>

    </div>
    <div class="minfo-accept minfo-confirm">
      <div class="minfo-accept__action get-data-offer btn-confirm-contract-offer">
        <a class="btn btn--primary btn--blue btn--lg button-confirm-contract-offer disable"
           href="javascript:void(0)">
          <span class="btn-text">{% trans "button approve contract" %}</span>
        </a>
      </div>
    </div>
  </div>
{% endwith %}
