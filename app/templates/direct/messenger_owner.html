{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load i18n %}
{% load compress %}

{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/input_component.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/main.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/modal.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/project_list.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/top.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/top_admin.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/video_modal.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/uploading-button.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/product_banner.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/message.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/project_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/modal_manager.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/scene_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/message_file.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/drop_file.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/modal_contract.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/upload_contract.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/calendar.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/creator_style.css' %}"/>
    {% endcompress %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/main_new.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/skeleton.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/offer_search.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/button_components.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/comment_project_new.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/project_budget_log.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/project_detail_new.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/schedule_calendar.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/message_owner.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
  <style>
    #modal-image-popup, #modal-video-popup, #modal-document-popup {
      top: 40px
    }

    #modal-image-popup .modal-content, #modal-video-popup .modal-content, #modal-document-popup.modal-content {
      max-width: 100vh;
      min-height: auto;
    }
  .pdf-component {
    margin-top: 0;
  }
  </style>
    <main class="main__skeleton">
        <div class="container" style="display: none;">
            <div class="skeleton__top">
                <div class="skeleton__top-left">
                    <div class="skeleton small-item"></div>
                    <div class="skeleton big-item"></div>
                </div>
                <div class="skeleton skeleton__top-right">
                </div>
            </div>

            <div class="skeleton__body">
                <div class="skeleton skeleton__body-item"></div>
                <div class="skeleton skeleton__body-item"></div>
                <div class="skeleton skeleton__body-item"></div>
                <div class="skeleton skeleton__body-item"></div>
            </div>
        </div>
    </main>
    <main class="owner-top prdt {% if not request|is_pc %} on-mobile{% endif %}" data-title-page="{{ title_page }}">
        <div class="container">
            <div class="new-video-menu">
                <div class="project-list">
                    <div class="project-item active{% if not request|is_pc %} on-mobile{% endif %}"
                         data-project-id="{{ project.pk }}" data-user-role="{{ role }}"
                         data-unresolved-comment="{{ project.product_owner_comments.count }}" data-page="top_page">
{#                        <a href="{% url 'app:top_page' %}?force=true{% if is_done_project %}?is_done=1&force=true{% endif %}" style="display: none">#}
{#                            {% include 'top/_product_banner.html' with project=project user=user type_page='top_page' project_list=False is_pc=request|is_pc show_staff=True %}#}
{#                        </a>#}
                   {% include 'top/_top_app_bar.html' with project=project %}
                    {% include 'top/_navigation_bar.html' with project=project user=user type_page='top_page' project_list=False is_pc=request|is_pc show_staff=True %}
                     {% include 'top/_left_sidebar.html' with product_scenes=product_scenes project=project user=user%}
                        <div class="popover-overlay"></div>
                      <div class="popover project-video-item">
                      </div>
                        <div class="project-item__content">
                            <div class='loader' style='display: none;'>
                                <img src="{% static 'images/icon-loading-outline-w.svg' %}" alt="">
                            </div>
                            <div class="project-tab project-tab-new" data-tab="new">
                                <div class="project-item__product-comment">


                                </div>
                            </div>

                            <div class="project-tab project-tab-product-comment" data-tab="product-comment">
                                <div class="project-item__video-list">

                                </div>
                            </div>

                        <div class="project-tab project-tab-progress {% if view_only %} view_only cannot-check{% endif %}" data-tab="progress">
                            <div class="tab--video-progress tab--video-all">
                                {% if user.role  == 'admin' %}
                                <div class="tab--video-watting_checkback processing-list-item d-none-chapter chapter-block"></div>
                                {% endif %}
                            </div>

                          </div>

                          <div class="project-tab project-tab-messenger active"
                               data-tab="messenger">
                            <div class="tab--messenger-artist refactor">

                            </div>

                          </div>

                          <div class="project-setting">
                            <div class="project-setting__filter">
                              <div class="project-setting__filter-item active" data-show="owner">詳細</div>
                              <div class="project-setting__filter-item" data-show="director">詳細</div>
                              <div class="project-setting__filter-item" data-show="staff">スタッフ</div>
                            </div>
                            <div class="setting-content-tab">
                              <!-- ajax will append data here -->
                            </div>
                          </div>
                          {% include 'product/project_budget_info.html' %}
                        </div>
                    </div>
                    <div class="project-member-setting-modal modal fade" id="project-member-setting"
                         role="dialog" style="display: none;">
                        <!-- ajax will append data here -->
                    </div>
                </div>
            </div>
        </div>

      {% include 'top/_modal_show_folder.html' %}

        <div class="modal fade share-modal" role="dialog" id="shareModal" style="z-index: 9999;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title"></h6>
                        <button class="close" data-dismiss="modal"
                                type="button">閉じる
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="video-time-slider-item">
                            <div class="video-time-slider-content">
                                <div class="video-time-slider-start">00:00
                                </div>
                                <div class="video-time-slider-bar"></div>
                                <div class="video-time-slider-end hide">00:00
                                </div>
                            </div>
                            <div class="video-time-slider-label">
                                <div class="video-time-slider-label-start">
                                    開始位置を指定
                                </div>
                                <div class="video-time-slider-label-end hide">
                                    終了位置も指定
                                </div>
                            </div>
                        </div>
                        <div class="modal-share-link">
                            <div class="video-item-share-input">
                                <input class="video-share-link"
                                       id="video-share-link" type="text"
                                       name="video-share-link"
                                       placeholder=""
                                       value="">
                            </div>
                            <a class="button button--text button--text-primary video-item-share-btn"
                               href="javascript:;" role="button">URLをコピー</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" role="dialog" id="processingSceneModal">
            <div class="modal-dialog modal-lg" role="document" style="width: 100%">
                <div class="modal-content">
                    <div class="modal-body container">
                    </div>
                </div>
            </div>
        </div>

        {% include 'messenger/_modal_open_file.html' %}

        {% include 'messenger/_modal_contract_offer.html' %}

        <div class="modal fade" id="searchModal" role="dialog" style="overflow: hidden;">
            <div class="modal-content" style="height: 100vh">
                <div class="modal-body container" style="max-height: 80vh; background: white">
                    <h2>検索結果</h2>
                </div>
            </div>
        </div>
        {% include 'top/_edit_chapter_name.html' with csrf=csrf_token project=project %}
        {% if role == 'admin' %}
            {% include 'top/_create_new_chapter.html' with csrf=csrf_token project=project %}
            {% include 'top/_upload_form.html' with csrf=csrf_token user=user titles=titles project=project %}
        {% endif %}
        <div class="modal fade video-modal" id="video-modal" role="dialog" style="z-index: 1001;">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                    </div>
                </div>

                <div class="upload-button-wrapper">
                    <p>アップロード中</p>
                    <div class="fill"><div class="process"></div></div>
                    <div class="fa fa-check"></div>
                </div>
            </div>
        </div>
        <div class="upload-final-product-file upload-button-wrapper">
            <p>アップロード中</p>
            <div class="fill">
                <div class="process"></div>
            </div>
            <div class="fa fa-check"></div>
        </div>

    </main>
    {% include 'top/_modal_setting_setting.html' with project_list=False%}
{#    {% include 'top/_modal_users_join_project.html' with project_list=False %}#}
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/init_socket_messenger.js' %}"></script>

    <script>
        let csrf = '{% csrf_token %}';
        let default_thumb = '{% static 'images/messenger-thumb.png' %}';
        let user = {{ user.id }};
        let is_pc = '{{ request|is_pc }}';
        let user_role = '{{ role }}';

        if (is_logged_in === 'True') {
            initSocket({{ user.id }});
        }
        $(document).ready(function () {
            let project_id = $('.project-item').attr('data-project-id');
            const url_string = window.location.href;
            let url = new URL(url_string);
            let offer_active = url.searchParams.get("offer");
            get_messenger_artist(project_id, offer_active, null);

            $('.project-item').find('.project-item__filter-item, .pbanner-tab').off().on('click', function (e) {
                e.preventDefault();
                let tab_item = $(this).data('show');
                if ('progress,product-comment'.includes(tab_item)) {
                    let url = $(this).attr('data-url');
                    window.location.href = url;
                } else {
                    if (tab_item === 'messenger' && $('.psearch-main').length) {
                        get_messenger_artist(project_id, null, 'waiting');
                    }
                }
                changePlaceHolderSearch();
            });

        })
    </script>
    <script src="{% static 'js/isInViewport.min.js' %}"></script>
    <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
    <script src="{% static 'js/top_page_admin.js' %}"></script>
    <script src="{% static 'js/top_page_member.js' %}"></script>
    <script src="{% static 'js/utils.js' %}"></script>
    <script src="{% static 'js/sortable.js' %}"></script>
    <script src="{% static 'js/video_modal.js' %}"></script>
    {% endcompress %}
{% endblock %}

{% block extra_script %}
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
  <script src="//rawgithub.com/indrimuska/jquery-editable-select/master/dist/jquery-editable-select.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.4/gsap.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/wavesurfer.js/3.3.3/plugin/wavesurfer.cursor.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
    {% endcompress %}
  <script src="{% url 'javascript-catalog' %}"></script>
        {% compress js inline %}
  <script src="{% static 'js/common_variable.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/offer_modal.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/utils.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/soremo.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/action_banner.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/main.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/project_detail.js' %}"></script>
  <script src="{% static 'js/messenger_owner.js' %}"></script>
  <script src="{% static 'js/upload_contract.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/offer_modal.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/messenger_artist.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/search_creator.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/component_expand.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/component_tab.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/load_more_message.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/project_budget_log.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/schedule_calendar.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/project_detail_new.js' %}"></script>
    {% endcompress %}
     <script src="https://cdn.jsdelivr.net/npm/interactjs@1.10.11/dist/interact.min.js"></script>
    <script>
        interact('.resize-drag')
            .resizable({
                edges: { left: false, right: true, bottom: false, top: false },

                listeners: {
                    move(event) {
                        let { x, y } = event.target.dataset;

                        x = (parseFloat(x) || 0) + event.deltaRect.left;
                        y = (parseFloat(y) || 0) + event.deltaRect.top;

                        Object.assign(event.target.style, {
                            width: `${event.rect.width}px`,
                            height: `${event.rect.height}px`,
                            transform: `translate(${x}px, ${y}px)`
                        });

                        Object.assign(event.target.dataset, { x, y });
                    }
                },
                modifiers: [
                    // 以下のコードはリサイズの最小/最大サイズを制限します
                    interact.modifiers.restrictSize({
                        min: { width: 224 },
                        max: { width: 640 }
                    })
                ]
            });
        var is_create_new = {% if is_create_new %}true{% else %}false{% endif %};
    </script>
{% endblock %}
