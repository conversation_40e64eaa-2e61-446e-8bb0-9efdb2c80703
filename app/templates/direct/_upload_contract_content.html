{% load i18n %}
{% load util %}

<div class="contract__form-wrap">
  {% if not form_contract_and_plan.semi_delegate %}
    {% with '請負型, 準委任型'|create_option_component_select:'contract_type, quasi-delegation_type' as options %}
      {% include 'tabs/component_tab.html' with options=options type='segment' filename='direct/_tab_type_contact.html, direct/_tab_type_semi-delegation.html'|create_option_component_select:'' className="tab-option-form-contract" selected_tab=False %}
    {% endwith %}
  {% else %}
    {% with '請負型, 準委任型'|create_option_component_select:'contract_type, quasi-delegation_type' as options %}
        {% include 'tabs/component_tab.html' with options=options type='segment' filename='direct/_tab_type_contact.html, direct/_tab_type_semi-delegation.html'|create_option_component_select:'' className="tab-option-form-contract" selected_tab='quasi-delegation_type' %}
    {% endwith %}
  {% endif %}
  {% with form_contract_and_plan|get_allow_public_val as allow_public %}
  <div class="contract__form-group" style="margin-bottom: 24px;">
    <div class="form-row row">
      <div class="col-sm-12 form-group" style="margin-bottom: 0;">
        <div class="form-check custom-switch switch-checkbox-public {% if allow_public %}checked{% endif %}">
          <label class="form-check-label">
            <div class="form-check-group">
              <input class="form-check-input switch-checkbox" type="checkbox" name="allow_public_contract"
                id="id_public" {% if allow_public %}checked="checked"{% endif %}><span class="switch-slider"></span>
            </div>
            <span class="switch-label label_public">{% trans "I agree to publish the case" %}
            </span>
          </label>
          <img class="svg_active_share {% if form_contract_and_plan.id and not form_contract_and_plan.allow_public_contract %}hide{% endif %}" src="/static/images/icon_share_active.svg">
          <img class="svg_inactive_share {% if not form_contract_and_plan.id or form_contract_and_plan.allow_public_contract %}hide{% endif %}" src="/static/images/icon_share_inactive.svg">
          {% comment %} {% include 'direct/_svg_share_active.html' %} {% endcomment %}
          {% comment %} {% include 'direct/_svg_share_inactive.html' %} {% endcomment %}
        </div>
      </div>
    </div>
  </div>
  {% endwith %}
</div>

<div class="contract__form-group">
  <div class="form-row row">
    <div class="col-sm-12 form-group" style="margin-bottom: 24px;">
      <div class="form-textarea-form-contract-container">
        <label for="id_note">
          <div class="contract__form-label heading--18">{% trans "Remarks" %}<span class="grey-label--8"> {% trans "any" %}</span></div>
        </label>
        <hr/>
        {% with '自由記載, 制限付, 利用権, 指名'|create_option_component_select:'free_text, restricted, usage_rights, nominated' as options %}
          {% include 'tabs/component_tab.html' with className='tab-note-form-contract' selected_tab=form_contract_and_plan.note_type options=options filename='direct/_tab_note_text_area_form_contract.html, direct/_tab_note_text_area_form_contract.html, direct/_tab_note_text_area_form_contract.html, direct/_tab_note_text_area_form_contract.html'|create_option_component_select:'' %}
        {% endwith %}
      </div>
    </div>
  </div>
</div>

<div class="contract__form-wrap" id="owner-info">
  {% include 'expand/component_expand.html' with label='お客様の情報' className='expand-address-owner' filename='direct/_address_owner_form_contract.html' %}
</div>

<div class="button-scroll-top">
  <div class="button-scroll-top-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path d="M0 8L1.41 9.41L7 3.83V16H9V3.83L14.58 9.42L16 8L8 0L0 8Z" fill="#A7A8A9"/>
    </svg>
  </div>
</div>
    