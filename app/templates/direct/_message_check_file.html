{% load static %}
{% load util %}
{% load i18n %}

{% if offer.condition == '2' and user.role == 'master_client' %}
  {% with offer.get_contract_in_offer as file %}
    {% if file %}
    <div class="mmessage-container refactor">
      <div class="mmessage mmessage--received mmessage-confirm" data-offer='{{ offer.pk }}'>
        <div class="mmessage-main">
          <div class="c-avatar32" style="background-image: url({{ file.user|get_avatar:'medium' }})"></div>
          <div class="mmessage-content">
            {% include 'direct/_message_check_contract.html' with offer=offer user=user %}
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  {% endwith %}
{% elif offer.condition == '5' and user.role == 'master_client' and not offer.receipt_id %}
  {% with offer.get_bill_in_offer as file %}
    {% if file %}
    <div class="mmessage-container refactor">
      <div class="mmessage mmessage--received mmessage-confirm" data-offer='{{ offer.pk }}'>
        <div class="mmessage-main">
          <div class="c-avatar32" style="background-image: url({{ file.user|get_avatar:'medium' }})"></div>
          <div class="mmessage-content">
            {% include 'direct/_message_payment.html' with offer=offer user=user %}
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  {% endwith %}
{% endif %}
