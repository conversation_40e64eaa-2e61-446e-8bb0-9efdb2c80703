{% load i18n %}
{% load util %}

<div id="id-form-contract-and-plan" hidden data-value="{{ form_contract_and_plan.id|default_if_none:'' }}" default-value-time-form="{{project|get_default_deadline_from_project}}"></div>
<div id="form-type-original" hidden data-value="{{ form_contract_and_plan.form_type|default_if_none:'' }}"></div>
<div id="creation-method-original" hidden data-value="{{ form_contract_and_plan.creation_method|default_if_none:'' }}"></div>
<div id="attach-file-id" hidden data-value="{% if product_message_file %}{{ product_message_file.pk }}{% endif %}"></div>
<div class="modal-dialog popup-dialog modal-lg" role="document">
  <div class="modal-content popup-content">
    <div class="popup-header">
      <hr />
      <h3 class="heading--40">{% trans "Create a contract" %}
      </h3>
    </div>
    <div class="popup-body nice-scroll mscrollbar">
      <div class="sub-container-body">
        {% with '見積書から作成,PDFをアップロード'|create_option_component_select:'generate,upload' as options %}
          {% include 'tabs/component_tab.html' with className='form-upload-contract-tab' selected_tab=form_contract_and_plan.creation_method options=options filename='direct/_upload_contract_content.html, direct/_upload_contract_content2.html'|create_option_component_select:'' %}
        {% endwith %}
      </div>
    </div>
    <div class="popup-footer" style="text-align: right !important;">
      <div class="button-footer-component">
        {% include 'buttons/conponent_button.html' with className='btn--tertiary small btn-popup-close' value='キャンセル' attribute='data-dismiss="modal"' %}

        {% if not form_contract_and_plan.id %}
          {% include 'buttons/conponent_button.html' with className='btn--primary medium btn-popup-ok btn-upload-file-messenger-owner disable' value='内容の確認' icon_end="next" %}
        {%else %}
          {% include 'buttons/conponent_button.html' with className='btn--primary medium btn-popup-ok btn-upload-file-messenger-owner' value='内容の確認' icon_end="next" %}
        {% endif %}
      </div>
    </div>
  </div>
</div>

