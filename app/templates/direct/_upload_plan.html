{% load i18n %}
{% load util %}

<div class="modal-dialog popup-dialog modal-lg" role="document">
  <div class="modal-content popup-content">
    <div class="popup-header">
      <hr />
      <h3 class="heading--40">お見積りの作成
      </h3>
    </div>
    <div class="popup-body nice-scroll mscrollbar">
      <div class="sub-container-body">
        <div class="contract__form-wrap">
          <div class="contract__form-group">
            <div class="form-row row">
              <div class="col-sm-12 form-group" style="margin-bottom: 8px;">
                <label for="id_owner_infor" data-initial="{{ project|get_owner_infor_from_project }}">
                  <div class="contract__form-label heading--16">宛先 <span class="grey-label--8">{% trans "any" %}</span></div>
                </label>
                {% include 'input_box/component_input.html' with attribute='id="id_owner_infor" name="owner_infor"' value=form_contract_and_plan.owner_infor|default_if_none:'' placeholder='' %}
              </div>
            </div>
            <div class="form-row row">
              <div class="col-sm-12 form-group" style="margin-bottom: 8px;">
                <label for="id_subject">
                  <div class="contract__form-label heading--16">{% trans "Subject" %} <span class="blue-label--8"> {% trans "required" %}</span></div>
                </label>
                {% include 'input_box/component_input.html' with attribute='id="id_subject" name="subject"' value=form_contract_and_plan.subject|default_if_none:'' placeholder='ぱちんこ遊技機「 アアアアア」 （仮称）音源制作' %}
              </div>
            </div>
          </div>

          <div class="contract__form-group" style="margin-bottom: 24px;">
            <div class="col-sm-12 form-group" style="margin-bottom: 0px; padding: 0;">
              <label for="list_item">
                <div class="contract__form-label heading--16">{% trans "Contents" %}<span class="blue-label--8"> {% trans "required" %}</span></div>
              </label>
            </div>

            <div class="form-row row nice-scroll row-data-scroll-container mscrollbar" id="list_item" style="overflow-x: auto; overflow-y: hidden; width: 100%; margin-left: 0;">
              <div class="col-sm-12 form-group row-data-contract" style="margin-bottom: 0px; padding: 8px 50px 8px 16px;">
                <div class="contract__form-content-wrap" style="width: 819px;">
                  <div class="contract__form-content-label">
                    <div class="contract__form-input input_item">
                      <label for="id_item">
                        <div class="bodytext--13">{% trans "Item" %}</div>
                      </label>
                    </div>
                    <div class="contract__form-input input_price">
                      <label for="id_price">
                        <div class="bodytext--13">{% trans "Unit price (excluding tax)" %}</div>
                      </label>
                    </div>
                    <div class="equal" style="margin-right: 17px;"></div>
                    <div class="contract__form-input input_quantity">
                      <label for="id_quantity">
                        <div class="bodytext--13">{% trans "Quantity" %}</div>
                      </label>
                    </div>
                    <div class="contract__form-input input_unit">
                      <label for="id_unit">
                        <div class="bodytext--13">{% trans "Unit" %}</div>
                      </label>
                    </div>
                    <div style="margin-right: 19px;"></div>
                    <div class="contract__form-input input_total">
                      <label for="id_total">
                        <div class="bodytext--13">{% trans "Amount of money" %}</div>
                      </label>
                    </div>
                    <div class="contract__form-input input_note">
                      <label for="id_content_note">
                        <div class="bodytext--13">{% trans "Content note" %}</div>
                      </label>
                    </div>
                  </div>
                  <div class="contract__form-content-list">
                  {% if form_contract_and_plan.get_work_content %}
                    {% for content in form_contract_and_plan.get_work_content %}
                    <div class="contract__form-content-item" data-index-order="{{ forloop.counter }}">
                      <div class="contract__form-input input_item">

                        {% with '進行管理, 開発, ディレクター, QA, 楽曲, 効果音, 実演, スタジオ, デザイン, 撮影, 動画編集, 作編曲, 編曲, 作詞, 歌唱, 演奏, エンジニア'|create_option_component_select:'1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17' as options %}
                          {% include 'input_box/component_input.html' with value=content.work_type_dsp placeholder='進行管理' type='select-input' options=options dataScroll='#modal-upload-contract-plan, #modal-upload-contract-plan .contract__form-content-wrap' data_type=content.work_type data_selected_value=content.work_type className='work-type contract__form-select-item' %}
                        {% endwith %}

                      </div>
                      <div class="contract__form-input input_price">
                        <div class="contract__form-price">
                          <input type="text" placeholder="100,000" class="form-control" maxlength="19" id="id_price"
                            min="0" value="{{ content.price|display_currency }}">
                          <span class="bodytext--13">{% trans "Yen" %}</span>
                        </div>
                      </div>
                      <div class="multiplication" style="display: flex; align-items: center;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="9" height="10" viewBox="0 0 9 10" fill="none">
                          <path d="M4.929 4.995L8.491 1.433C8.556 1.368 8.556 1.238 8.491 1.173L8.322 1.004C8.257 0.939 8.127 0.939 8.062 1.004L4.5 4.566L0.938 1.004C0.873 0.939 0.743 0.939 0.678 1.004L0.509 1.173C0.444 1.238 0.444 1.368 0.509 1.433L4.071 4.995L0.509 8.557C0.444 8.622 0.444 8.752 0.509 8.817L0.678 8.986C0.743 9.051 0.873 9.051 0.938 8.986L4.5 5.424L8.062 8.986C8.127 9.051 8.257 9.051 8.322 8.986L8.491 8.817C8.556 8.752 8.556 8.622 8.491 8.557L4.929 4.995Z" fill="#A7A8A9"/>
                        </svg>
                      </div>
                      <div class="contract__form-input input_quantity">
                        <input type="text" class="form-control contract__form-quantity" id="id_quantity"
                          placeholder="1" value="{{ content.quantity }}">
                      </div>
                      <div class="contract__form-input input_unit">
                        {% with '人月, 曲, 演出, 名, 時間, 式, 人日'|create_option_component_select:'1, 2, 3, 4, 5, 6, 7' as options %}
                          {% include 'input_box/component_input.html' with value=content.unit_dsp placeholder='人月' type='select-input' options=options dataScroll='#modal-upload-contract-plan, #modal-upload-contract-plan .contract__form-content-wrap' data_type=content.unit data_selected_value=content.unit className='unit-select' %}
                        {% endwith %}

                      </div>
                      <div class="equal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="11" height="10" viewBox="0 0 11 4" fill="none">
                          <path d="M10.193 3.126H0.807C0.703 3.126 0.625 3.204 0.625 3.308V3.555C0.625 3.659 0.703 3.75 0.807 3.75H10.193C10.297 3.75 10.375 3.659 10.375 3.555V3.308C10.375 3.204 10.297 3.126 10.193 3.126ZM10.193 0.239999H0.807C0.703 0.239999 0.625 0.331 0.625 0.435V0.682C0.625 0.786 0.703 0.864 0.807 0.864H10.193C10.297 0.864 10.375 0.786 10.375 0.682V0.435C10.375 0.331 10.297 0.239999 10.193 0.239999Z" fill="#A7A8A9"/>
                        </svg>
                      </div>
                      <div class="contract__form-input input_total">
                        <div class="contract__form-total">
                          <span class="bodytext--13" id="id_total">0</span>
                          <span class="bodytext--13">{% trans "Yen" %}</span>
                        </div>
                      </div>
                      <div class="contract__form-input input_note">
                        <input type="text" class="form-control contract__form-content_note not-required"
                          id="id_content_note" placeholder=" " value="{{ content.note }}">
                      </div>
                      <div class="contract__form-action-content {% if form_contract_and_plan.get_work_content|length <= 1 %}disabled{% endif %}">
                        <i class="drag-content icon icon--sicon-equal"></i>
                        <i class="delete-content icon icon--sicon-trash"></i>
                      </div>
                    </div>
                    {% endfor %}
                  {% else %}
                    <div class="contract__form-content-item blank_item" data-index-order="1">
                      <div class="contract__form-input input_item">
                        {% with '進行管理, 開発, ディレクター, QA, 楽曲, 効果音, 実演, スタジオ, デザイン, 撮影, 動画編集, 作編曲, 編曲, 作詞, 歌唱, 演奏, エンジニア'|create_option_component_select:'1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17' as options %}
                          {% include 'input_box/component_input.html' with value=content.work_type_dsp placeholder='進行管理' type='select-input' options=options dataScroll='#modal-upload-contract-plan, #modal-upload-contract-plan .contract__form-content-wrap' data_type=content.work_type data_selected_value=content.work_type className='work-type' %}
                        {% endwith %}

                      </div>
                      <div class="contract__form-input input_price">
                        <div class="contract__form-price">
                          <input type="text" placeholder="100,000" class="form-control" maxlength="19" id="id_price"
                            min="0">
                          <span class="bodytext--13">{% trans "Yen" %}</span>
                        </div>
                      </div>
                      <div class="multiplication" style="display: flex; align-items: center;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="9" height="10" viewBox="0 0 9 10" fill="none">
                          <path d="M4.929 4.995L8.491 1.433C8.556 1.368 8.556 1.238 8.491 1.173L8.322 1.004C8.257 0.939 8.127 0.939 8.062 1.004L4.5 4.566L0.938 1.004C0.873 0.939 0.743 0.939 0.678 1.004L0.509 1.173C0.444 1.238 0.444 1.368 0.509 1.433L4.071 4.995L0.509 8.557C0.444 8.622 0.444 8.752 0.509 8.817L0.678 8.986C0.743 9.051 0.873 9.051 0.938 8.986L4.5 5.424L8.062 8.986C8.127 9.051 8.257 9.051 8.322 8.986L8.491 8.817C8.556 8.752 8.556 8.622 8.491 8.557L4.929 4.995Z" fill="#A7A8A9"/>
                        </svg>
                      </div>
                      <div class="contract__form-input input_quantity">
                        <input type="text" class="form-control contract__form-quantity" id="id_quantity"
                          placeholder="1">
                      </div>
                      <div class="contract__form-input input_unit">
                        {% with '人月, 曲, 演出, 名, 時間, 式, 人日'|create_option_component_select:'1, 2, 3, 4, 5, 6, 7' as options %}
                          {% include 'input_box/component_input.html' with value=content.unit_dsp placeholder='人月' type='select-input' options=options dataScroll='#modal-upload-contract-plan, #modal-upload-contract-plan .contract__form-content-wrap' data_type=content.unit data_selected_value=content.unit className='unit-select' %}
                        {% endwith %}

                      </div>
                      <div class="equal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="11" height="10" viewBox="0 0 11 4" fill="none">
                          <path d="M10.193 3.126H0.807C0.703 3.126 0.625 3.204 0.625 3.308V3.555C0.625 3.659 0.703 3.75 0.807 3.75H10.193C10.297 3.75 10.375 3.659 10.375 3.555V3.308C10.375 3.204 10.297 3.126 10.193 3.126ZM10.193 0.239999H0.807C0.703 0.239999 0.625 0.331 0.625 0.435V0.682C0.625 0.786 0.703 0.864 0.807 0.864H10.193C10.297 0.864 10.375 0.786 10.375 0.682V0.435C10.375 0.331 10.297 0.239999 10.193 0.239999Z" fill="#A7A8A9"/>
                        </svg>
                      </div>
                      <div class="contract__form-input input_total">
                        <div class="contract__form-total">
                          <span class="bodytext--13" id="id_total">0</span>
                          <span class="bodytext--13">{% trans "Yen" %}</span>
                        </div>
                      </div>
                      <div class="contract__form-input input_note">
                        <input type="text" class="form-control contract__form-content_note not-required"
                          id="id_content_note" placeholder=" ">
                      </div>
                      <div class="contract__form-action-content disabled">
                        <i class="drag-content icon icon--sicon-equal"></i>
                        <i class="delete-content icon icon--sicon-trash"></i>
                      </div>
                    </div>
                  {% endif %}
                  </div>
                </div>

                <div class="col-md-12 form-group" id="contract__add-button">
                  <div class="contract__add" id="add_blocklist">
                    <div class="contract__add-row">
                      <div class="contract__add-wrap contract__add-btn">
                        <div class="contract__add-icon"><i class="icon icon--sicon-plus"></i>
                        </div>
                        <p class="bodytext--13">{% trans "Add item" %}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-12 form-group contract__form-total-wrap" style="margin-bottom: 0px; padding-left: 8px;">
                  <div class="contract__form-total-list">
                    <div class="contract__form-item">
                      <div class="contract__form-item-title">
                        <div class="bodytext--13">{% trans "Taxable amount" %}</div>
                      </div>
                      <div class="contract__form-item-total">
                        <span class="bodytext--13" id="id_total_row">0</span>
                        <span class="bodytext--13"> {% trans "Yen" %}</span>
                      </div>
                    </div>
                    <div class="contract__form-item">
                      <div class="contract__form-item-title">
                        <div class="bodytext--13">{% trans "Consumption tax (10 percent)" %}</div>
                      </div>
                      <div class="contract__form-item-total">
                        <span class="bodytext--13" id="id_tax_pj">0</span>
                        <span class="bodytext--13"> {% trans "Yen" %}</span>
                      </div>
                    </div>
                    <div class="contract__form-item" style="margin-bottom: 0px;">
                      <div class="contract__form-item-title">
                        <div class="bodytext--13">{% trans "total" %}</div>
                      </div>
                      <div class="contract__form-item-total">
                        <span class="bodytext--13" id="id_total_pj">0</span>
                        <span class="bodytext--13"> {% trans "Yen" %}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="contract__form-wrap row-data-contract issue-container" >
            <div class="contract__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group" style="margin: 0;">
                  <label for="id_issue">
                    <div class="contract__form-label heading--13">{% trans "Date of issue" %}</div>
                  </label>
                  {% include 'input_box/component_input.html' with className='mcalendar mcalendar--small' value=form_contract_and_plan.get_release_time_with_format_date type='datetime' attribute='id="id_issue" name="release_time"' placeholder="yyyy/mm/dd" %}
                </div>
              </div>
            </div>
            <div class="contract__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group" style="margin-bottom: 0px;">
                  <label for="id_issue">
                    <div class="contract__form-label heading--13">{% trans "Date of expiry" %}</div>
                  </label>
                  <div class="form-row row form-row__mobile">
                    <div class="col-xs-6 form-row__mobile-date" style="padding-right: 8px;">
                    {% include 'input_box/component_input.html' with className='mcalendar mcalendar--small' value=form_contract_and_plan.get_valid_time_with_format_date type='datetime' attribute='id="id_valid" name="valid_date"' placeholder="yyyy/mm/dd" %}
                    </div>
                    <div class="col-xs-6" style="padding-left: 0;">
                      {% include 'input_box/component_input.html' with value=form_contract_and_plan.get_valid_time_with_format_time type='time' placeholder='10:00' attribute='name="valid_hours" id="valid_time"' %}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="contract__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group" style="margin: 0;">
                  <label for="id_pre_deadline">
                    <div class="contract__form-label heading--13">受領期日</div>
                  </label>
                  {% include 'input_box/component_input.html' with className='mcalendar mcalendar--small' value=form_contract_and_plan.get_pre_deadline_with_format_date type='datetime' attribute='id="id_pre_deadline" name="pre_deadline"' placeholder="yyyy/mm/dd" %}
                </div>
              </div>
            </div>

            <div class="contract__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group" style="margin-bottom: 0px;">
                  <label for="id_delivery_place">
                    <div class="contract__form-label heading--13">{% trans "Delivery place" %}</div>
                  </label>
                  {% with 'SOREMO WEBサービス内, 御社指定'|create_option_component_select:'1, 2' as options %}
                    {% include 'input_box/component_input.html' with value=form_contract_and_plan.delivery_place placeholder='Placeholder' type='select' options=options attribute='id="id_delivery_place" name="delivery_place"' %}
                  {% endwith %}
                </div>
              </div>
            </div>
            <div class="contract__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group" style="margin: 0;">
                  <label for="id_pickup">
                    <div class="contract__form-label heading--13">{% trans "How to pick up" %}</div>
                  </label>
                  {% with 'SOREMO WEBサービス内, 御社指定'|create_option_component_select:'1, 2' as options %}
                    {% include 'input_box/component_input.html' with value=form_contract_and_plan.pick_up_method placeholder='Placeholder' type='select' options=options attribute='id="id_pickup" name="pick_up_method"' %}
                  {% endwith %}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="contract__form-wrap" id="producer-info">
          {% include 'expand/component_expand.html' with label='送り主' className='expand-address-producer' filename='direct/_address_producer_form_contract.html' %}
        </div>
        <div class="button-scroll-top">
          <div class="button-scroll-top-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M0 8L1.41 9.41L7 3.83V16H9V3.83L14.58 9.42L16 8L8 0L0 8Z" fill="#A7A8A9"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div class="popup-footer" style="text-align: right !important;">
      <div class="button-footer-component">
        {% include 'buttons/conponent_button.html' with className='btn--tertiary small btn-popup-close' value='キャンセル' attribute='data-dismiss="modal" style="font-family: A+mfCv-AXISラウンド 50 L StdN"' %}

        {% if not form_contract_and_plan.id %}
          {% include 'buttons/conponent_button.html' with className='btn--primary medium btn-popup-ok btn-upload-file-messenger-owner disable' value='内容の確認' icon_end="next" %}
        {%else %}
          {% include 'buttons/conponent_button.html' with className='btn--primary medium btn-popup-ok btn-upload-file-messenger-owner' value='内容の確認' icon_end="next" %}
        {% endif %}
      </div>
    </div>
  </div>
</div>
