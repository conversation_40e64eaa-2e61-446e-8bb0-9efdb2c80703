{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css"/>
    {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_contract.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/creator_style.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
    {% compress css %}
  <style>
    .project-item__filter-item {
      width: auto;
      min-width: 50px;
      height: 50px;
      padding: 0;
    }



    .project-item__filter-item.active svg path {
      fill: #009ace;
    }

    .messenger__item--new {
      background-color: #fff;
    }

    .messenger-detail .messenger-detail-content {
      max-height: calc(100vh - 390px);
    }

    .messenger-director .messenger__list {
      max-height: calc(100vh - 390px);
    }

    .bootbox.modal {
      margin-top: 30vh;
    }

    .mrow {
      margin-top: 0;
    }

    .project-item__content {
      display: flex;
    }

    .mmessage-near {
      margin-bottom: 2px;
      margin-left: 32px;
    }

    .editing .s-text, .editing .s-file {
      background-color: #2CC84D;
    }

    .mcommment.border-editing {
      border: 1px solid #2CC84D;
    }

    .messenger-detail {
      height: 0;
      margin: 0;
    }

    .custom-checkbox .form-check-label {
      margin-bottom: 0;
    }

  </style>
    {% endcompress %}
  <main>
    <div class="mcontainer">
      <div class="p-martist">
        <div class="psearch-top">
          <a href="{% url 'app:messenger_project' %}{% if is_done_project %}?is_done=1{% endif %}">
            <div class="psearch-banner" style="background-image: url({{ project|get_image }})"></div>
          </a>
          <div class="psearch-progress">
            <div class="progress pprogress">
              {% if user.role == 'admin' %}
                {% with budget_done=user|get_budget_admin_offer_done:project budget_offer=user|get_budget_admin_offer:project %}
                  <div class="progress-bar" role="progressbar" style="width: {{ budget_done }}%" aria-valuenow="40"
                       aria-valuemin="0"
                       aria-valuemax="100"></div>
                  <div class="progress-bar bg-warning" role="progressbar"
                       style="width: {{ budget_offer|minus:budget_done }}%" aria-valuenow="40" aria-valuemin="0"
                       aria-valuemax="100"></div>
                {% endwith %}
              {% else %}
                <div class="progress-bar" role="progressbar" style="width: {{ project.get_current_heart_rate }}%"
                     aria-valuenow="40"
                     aria-valuemin="0"
                     aria-valuemax="100"></div>
                <div class="progress-bar bg-warning" role="progressbar"
                     style="width: {{ project.get_current_scene_rate }}%" aria-valuenow="40" aria-valuemin="0"
                     aria-valuemax="100"></div>
              {% endif %}

            </div>
          </div>
          <div class="psearch-tabs">
            <a class="psearch-tab psearch-tab--exchange active"
                 data-show="waiting" href="{% url 'app:messenger_waiting' %}?project_id={{ project.pk }}&tab=1">
                <i class="icon icon--sicon-chat"></i> メッセンジャー
            </a>
          </div>
        </div>
        <div class="psearch-main">

          {% include 'messenger/_gallery_sale_content.html' with sale_contents=sale_contents %}

          <div class="psearch-section psearch-form-wrap">
            <div class="psearch-title sheading sheading--18">検索</div>
            <div class="psearch-content psearch-form">
              <div class="sform-row psearch-keyword">
                <div class="sform-group sform-group--required">
                  <div class="sform-group__input-group sform-group__append-before">
                    <input class="sform-control sform-control--input sform-control--full" id="pm-search-creator" type="search"
                           placeholder="アーティスト名で検索" required="required"/>
                    <i class="icon icon--sicon-close search-delete"></i>
                    <label class="sform-group__append" for="pm-search-creator">
                      <i class="icon icon--sicon-search"></i>
                    </label>
                  </div>
                </div>
              </div>
              <div class="psearch-submit">
                <input class="btn btn--blue btn--lg" type="button" value="検索する">
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    {% if user.role == 'admin' %}
       {% include 'messenger/_modal_form_offer.html' with page=page project=project list_scenes=list_scenes list_data_format=list_data_format list_quantity=list_quantity%}
      {% include 'messenger/_modal_over_budget.html' %}
    {% endif %}

  </main>

  <script>
      let is_pc = '{{ request|is_pc }}';
      let csrf = '{% csrf_token %}';
  </script>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/utils.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/soremo.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/offer_modal.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/main.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/search_creator.js' %}"></script>
    {% endcompress %}
{% endblock %}
