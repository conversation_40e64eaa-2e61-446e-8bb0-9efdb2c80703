{% load static %}
{% load util %}
{% if sale_contents.exists %}
  <div class="psearch-gallery">
    <div class="gallery">
      <div class="gallery__list">
        <div class="gallery__row gallery__row-small">
          {% for music in sound_sample_music|slice:"0:5" %}
            <div class="gallery__item"
                 data-type="{{ music.last_published_version.content_type }}"
                 data-artist="{{ music.profile.get_stage_name }}"
                 style="{{ music|get_thumbnail_sale_content }}" data-creator="{{music.profile.creator.pk}}">
              <div class="list-search__item-playpause"></div>
              {% with music|get_audios as audio %}
                <audio preload='none' class="gallery__item-banner" src="{{ audio.0 }}"
                       data-name="{{ music|get_title_sale_content }}"></audio>
              {% endwith %}
            </div>
          {% endfor %}
        </div>
        <div class="gallery__row gallery__row-small">
          {% for music in sound_sample_music|slice:"5:" %}
            <div class="gallery__item"
                 data-type="{{ music.last_published_version.content_type }}"
                 data-artist="{{ music.profile.get_stage_name }}"
                 style="{{ music|get_thumbnail_sale_content }}" data-creator="{{music.profile.creator.pk}}">
              <div class="list-search__item-playpause"></div>
              {% with music|get_audios as audio %}
                <audio preload='none' src="{{ audio.0|get_audio }}"
                       data-name="{{ music|get_title_sale_content }}" ></audio>
              {% endwith %}
            </div>
          {% endfor %}
        </div>
        <div class="gallery__row gallery__row-large">
          {% for effect in sound_sample_effect %}
            <div class="gallery__item"
                 data-type="{{ effect.last_published_version.content_type }}"
                 data-artist="{{ effect.profile.get_stage_name }}"
                 style="{{ effect|get_thumbnail_sale_content }}"  data-creator="{{effect.profile.creator.pk}}">
              <div class="list-search__item-playpause"></div>
              {% with effect|get_audios as audio %}
                <audio preload='none' src="{{ audio.0|get_audio }}"
                       data-name="{{ effect|get_title_sale_content }}"></audio>
              {% endwith %}
            </div>
          {% endfor %}
        </div>
        <div class="gallery__row gallery__row-small">
          {% for voice in sound_sample_voice|slice:"0:5" %}
            <div class="gallery__item"
                 data-type="{{ voice.last_published_version.content_type }}"
                 data-artist="{{ voice.profile.get_stage_name }}"
                 style="{{ voice|get_thumbnail_sale_content }}"  data-creator="{{voice.profile.creator.pk}}">
              <div class="list-search__item-playpause"></div>
              {% with voice|get_audios as audio %}
                <audio preload='none' src="{{ audio.0|get_audio }}"
                       data-name="{{ voice|get_title_sale_content }}"></audio>
              {% endwith %}
            </div>
          {% endfor %}
        </div>
        <div class="gallery__row gallery__row-small">
          {% for voice in sound_sample_voice|slice:"5:" %}
            <div class="gallery__item"
                 data-type="{{ voice.last_published_version.content_type }}"
                 data-artist="{{ voice.profile.get_stage_name }}"
                 style="{{ voice|get_thumbnail_sale_content }}"  data-creator="{{voice.profile.creator.pk}}">
              <div class="list-search__item-playpause"></div>
              {% with voice|get_audios as audio %}
                <audio preload='none' src="{{ audio.0|get_audio }}"
                       data-name="{{ voice|get_title_sale_content }}"></audio>
              {% endwith %}
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
{% endif %}
