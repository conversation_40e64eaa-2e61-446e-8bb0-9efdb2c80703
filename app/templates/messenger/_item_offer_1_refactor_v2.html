{% load static %}
{% load util %}
{% load i18n %}
    {% with real_offer|get_offer_user_offer_id:user as role_offer %}
        {% with real_offer|get_offer_user_refactor:user as offer_user %}
        <li class="item-offer {% if offer_user == 'admin' %}offer-admin{% endif %} mscene mitem"
        data-offer="{{ real_offer.pk }}" data-type-offer="messenger_artist" data-offer-side="{%  if real_offer.offer_side == 1 %}admin{% else %}creator{% endif %}" data-reward="{{ real_offer.budget_or_reward }}" data-created="{{ real_offer.created|get_datetime }}">
            {% with role_offer|get_data_offer_by_role:offer as offer_color_text %}
                <div class="progress-offer {{offer_color_text.offer_id}}"
                    style="background-color: {% if offer.status == '1' %}#f0f0f0{% elif offer.status == '2' or offer.status == '3' %}#009ace{% elif offer.status == '4' %}#53565a{% else %}#f0f0f0{% endif %}"></div>
                <div class="offer-main offer-main-item-2">
                    {% with real_offer|get_other_member_in_offer_refactor:user as members %}
                        <div class="offer-main-content">
                            {% if offer_user == 'creator' %}
                                <div class="block-offer-top">
                                    <div class="offer-top-avatar">
                                        <div class="top-avatar"
                                            style="background-image: url('{{ members.0.user|get_avatar:'medium' }}');">
                                        </div>
                                    </div>
                                    <span class="text-bottom-offer">{{ members.0.user.get_display_name }}さんからのオファー</span>
                                </div>
                            {% endif %}

                            <div class="offer-content-bottom">
                                {% if offer_user == 'admin' %}
                                    <div class="main-avatar"
                                        style="background-image: url('{{ members.0.user|get_avatar:'medium' }}');">
                                    </div>
                                {% elif offer_user == 'creator' %}
                                    <div class="main-avatar"
                                        style="background-image: url('{{ user|get_avatar:'medium' }}');">
                                    </div>
                                {% endif %}
                                <div>
                                </div>
                                <div class="content-offer">
                                    <div class="block-offer">
                                        <div class="block-offer-1">
                                            <span class="title-offer {% if offer_user == 'creator' %}title-blue{% endif %}">{% if offer_user == 'creator' %}
                                                あなた{% else %}{{ offer.creator.get_display_name }}{% endif %}</span>
                                            <span class="time-offer">{{ real_offer.modified|get_weekday_new }}</span>
                                        </div>
                                        <div class="block-offer-2">
                                            <div class="block-text-description">
                                            <span class="text-des-1">{% if offer.contract %}
                                                {{ offer.contract }}{% endif %}</span>
                                                <span class="text-des-2">{% if offer.scenes %}（{{ offer.scenes }}）{% endif %}</span>
                                            </div>
                                            <span class="description-content-offer" style="color: {% if offer_color_text.color %}{{ offer_color_text.color }}{% else %}#a7a8a9{% endif %}">{% if offer_color_text %}{{ offer_color_text.text }}{% endif %}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endwith %}
                <div class="right-offer">
                    {% with real_offer|new_message_count_refactor as count_new %}
                        <div class="block-message-count">
                            <div class="number-offer notification notification--blue notification--round {% if count_new == 0 %}hide{% endif %}">
                                {% if count_new < 100 %}{{ count_new }}{% else %}99+{% endif %}</div>
                        </div>
                    {% endwith %}
                    <span class="next-offer-icon material-symbols-rounded">navigate_next</span>
                </div>
            {% endwith %}
        </li>
    {% endwith %}
{% endwith %}