{% load i18n %}
{% load util %}
<style>
  .modal-create-edit-offer .modal-body {
    max-height: 100% !important;
  }
</style>
<div class="smodal smodal--large modal fade modal-create-edit-offer"
     id="{% if page == 'search' %}modal-create-offer{% else %}modal-edit-offer{% endif %}" role="dialog"
     data-project="{{ project.pk }}">
  <div class="modal-dialog mscrollbar" role="document">
    <div class="modal-dialog__header">
      <div class="modal-dialog__header__container">
        <hr/>
        <div class="modal-dialog__header__text">
            <h1 class="heading-spacing-2140">オファーを作成</h1>
        </div>
      </div>
    </div>
    <div class="modal-content" style="margin-top: 45px;">
      <div class="create-offer mscrollbar">
        <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px">
          <div class="create-offer__heading sheading sheading--16"></div>
          <div class="create-offer__content">
            <div class="modal-offer-container">
              <div class="create-offer__form" style="margin-top: 0px;">
                <div class="form-block-container">
                  <div class="form-block-title">ロール</div>
                  <div class="form-block-content">
                    <div class="job-title-container">
                      {% with ''|get_all_skills as skills %}
                        <!-- <div class="psearch-filter"> -->
                          <ul class="nav tabs-skill-offer mscrollbar">
                            {% for group in skills %}
                              <li class="nav-item offer-form {% if forloop.counter0 == 0 %}active{% endif %}" data-index="{{forloop.counter0}}">
                                <a class="nav-link tab_{{ group.1.1.2 }}_offer" data-target="#tab_{{ group.1.1.2 }}_offer" data-toggle="tab">{{ group.0 }}</a>
                                <div class="notification notification--blue notification--round skill-selected hide"></div>
                              </li>
                            {% endfor %}
                          </ul>
                          <div class="tab-content-offer">
                            {% for group in skills %}
                              <div class="tab-pane-offer {% if forloop.counter0 == 0 %}active{% endif %}" id="tab_{{ group.1.1.2 }}_offer" data-index="{{forloop.counter0}}">
                                <div class="skills-list-offer-selected">
                                  {% for skill in group.1 %}
                                    <span class="skills-item-offer" data-id="{{ skill.0 }}">{{ skill.1 }}</span>
                                    <input type="checkbox" name="skills" value="" id="" hidden>
                                  {% endfor %}
                                </div>
                              </div>
                            {% endfor %}
                    
                          </div>
                        <!-- </div> -->
                      {% endwith %}
                    </div>
                    <div class="sform-row" style="margin-bottom: -1px;">
                      <div class="sform-group id_contract">
                        {% include 'input_box/component_input.html' with className='sform-control sform-control--input sform-control--full' placeholder="作編曲 / 譜面" attribute='id="id_contract" required' %}
                      </div>
                    </div>

                    <div class="sform-row" style="margin-bottom: -16px;">
                      <div class="sform-group">
                        <label class="sform-group__label" for="id_data_format" style="margin: 0;">
                          <span class="sform-group__label-text">納品物<span class="sform-group__label-hint not-required">{% trans "any" %}</span></span>
                        </label>
                          {% with '48k 24bit WAV,Nuendoプロジェクトファイル,スタジオにて実演,チェックシート及び著作物審査確認書,テキストファイル,PDFファイル,48k 24bit WAV 及び ProTools Session,48k 24bit WAV 及び ステムデータ,成果物をクライアントの指定する形式で納品'|create_option_component_select:'48k 24bit WAV,Nuendoプロジェクトファイル,スタジオにて実演,チェックシート及び著作物審査確認書,テキストファイル,PDFファイル,48k 24bit WAV 及び ProTools Session,48k 24bit WAV 及び ステムデータ,成果物をクライアントの指定する形式で納品' as options %}
                            {% include 'input_box/component_input.html' with value='' placeholder='48k 24bit WAVファイル' type='select-input' options=options value='48k 24bit WAV' data_type='48k 24bit WAV' data_selected_value='48k 24bit WAV' className='sform-control sform-control--input sform-control--full' attribute='id="id_data_format"' %}
                          {% endwith %}
                      </div>
                    </div>
      
                    <div class="form-check custom-switch switch-checkbox-public checked allow_subcontracting">
                      <label class="form-check-label">
                        <div class="form-check-group">
                          <input class="form-check-input switch-checkbox" type="checkbox" name="allow_subcontracting"
                            id="allow_subcontracting" checked="checked"><span class="switch-slider"></span>
                        </div>
                        <span class="switch-label label_public">再委託を認める</span>
                      </label>
                    </div>
                  </div>
                </div>
                <div class="form-block-container" style="margin-bottom: 16px;">
                  <div class="form-block-title">条件</div>
                  <div class="form-block-content">
                    <div class="sform-row" style="margin-bottom: 16px;">
                      <label class="sform-group__label" for="budget">
                        <span class="sform-group__label-text">対価<span class="sform-group__label-hint required">{% trans "required" %}</span></span>
                      </label>
                      <div class="input-block-container">
                        <div class="input-block-top">
                          <div class="input-block-top-left">
                            {% include 'input_box/component_input.html' with className='sform-control sform-control--input sform-control--full' placeholder=" " rightText="true" attribute='id="budget" required maxlength="15"' %}
                          </div>
                          <div class="input-unit-block-right"><div class="input-unit-content" style="width: 100%;">円（税抜）</div></div>
                        </div>
                        
                        <div class="input-block-bottom">
                          <div class="input-block-bottom-left">
                            <div class="create-offer__price">
                              <div class="create-offer__price-item">
                                <div class="create-offer__price-number"><span id="id_total_amount">0</span></div>
                              </div>
                            </div>
                          </div>
                          <div class="input-unit-block">円（税込）</div>
                        </div>
                      </div>
                    </div>
  
                    <label class="sform-group__label" for="deadline-date">
                      <span class="sform-group__label-text">納期</span>
                    </label>
                    <div class="sform-row sform-row--2-columns" style="margin-bottom: -8px; display: flex;">
                      <div class="sform-group">
                        <div class="sform-group__input-group">
                          {% include 'input_box/component_input.html' with className='sform-control sform-control--input sform-control--full' type='datetime' attribute='id="deadline-date"' placeholder="yyyy/mm/dd" %}
                        </div>
                      </div>
                      <div class="sform-group sform-group--select sform-group__select--white">
                        {% include 'input_box/component_input.html' with type='time' placeholder='10:00' className='select-deadline_time' attribute='' %}
                      </div>
                    </div>
  
                    <div class="sform-row">
                      <div class="sform-group">
                        <label class="sform-group__label" for="input-remarks">
                          <span class="sform-group__label-text">特記事項<span class="sform-group__label-hint not-required"> {% trans "any" %}</span></span>
                        </label>
                      {% if project.offer_product.exists %}
                        {% with project|get_contract_note as note_contract %}
                          {% include 'input_box/component_input.html' with value=note_contract placeholder=' ' type='text-area' attribute='id="input-remarks" rows="5"' %}
                        {% endwith %}
                      {% endif %}
                      </div>
                    </div>
                  </div>
                </div>
  
                <div class="form-block-container">
                  <div class="form-block-title">内容</div>
                  <div class="form-block-content">
                    <div class="sform-row" style="margin-bottom: -8px;">
                      <div class="sform-group">
                        <label class="sform-group__label" for="input-scenes">
                          <span class="sform-group__label-text">シーン<span class="sform-group__label-hint not-required">{% trans "any" %}</span></span>
                        </label>
  
                        {% with project|get_all_scene_name_by_project:user as list_select %}
                          {% with list_select|create_option_component_select:list_select as options %}
                            {% include 'input_box/component_input.html' with value='' placeholder=' ' type='select-input' options=options dataScroll='#modal-upload-contract-plan' data_type='' data_selected_value='' className='sform-control sform-control--input sform-control--full' attribute='id="input-scenes"' %}
                          {% endwith %}
                        {% endwith %}
                      </div>
                    </div>
  
                    <div class="sform-row" style="margin-bottom: 16px;">
                      <div class="sform-group">
                        <label class="sform-group__label" for="input-message">
                          <span class="sform-group__label-text">説明<span class="sform-group__label-hint not-required">{% trans "any" %}</span></span>
                        </label>
                        {% include 'input_box/component_input.html' with value='' placeholder=' ' type='text-area' attribute='id="input-message" rows="5"' %}
                      </div>
                    </div>
  
                    {% if not hide_upload %}
                    <label class="sform-group__label" for="create-offer-upload-form">
                      <span class="sform-group__label-text">資料<span class="sform-group__label-hint not-required">{% trans "any" %}</span></span>
                    </label>
                    <div class="mattach-info mattach-info-file hide" data-dz-thumbnail="">
                      <div class="mcommment-file">
                        <div class="mcommment-file__name mcommment-file__name-form" data-dz-name="">
                          <i class="icon icon--sicon-clip"></i><p class="file-name"></p>
                        </div>
                        <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                          <i class="icon icon--sicon-close"></i>
                        </div>
                      </div>
                    </div>
    
                    <div class="account_upload-file mattach mattach-form mattach-form-messenger-artist" style="margin-bottom: 0px;">
                      <div class="mcomment-attached">
                        <div class="mattach-preview-container mattach-preview-container-form">
                          <div class="mattach-previews mattach-previews-form collection">
                            <div class="mattach-template mattach-template-form collection-item item-template">
                              <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                  <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                  <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                  <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                          class="icon icon--sicon-close"></i>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="fallback dropzone" id="create-offer-upload-form">
                      </div>
                    </div>
                  {% endif %}
                  </div>
                </div>
                <div class="sform-group">
                  {% include 'expand/component_expand.html' with label='詳細' className='expand-detail-offer' filename='messenger/_tab_detail_form_offer.html' %}
                </div>
              </div>
            </div>

            <div class="button-scroll-top" >
              <div class="button-scroll-top-container">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M0 8L1.41 9.41L7 3.83V16H9V3.83L14.58 9.42L16 8L8 0L0 8Z" fill="#A7A8A9"/>
                </svg>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
    <div class="create-offer__action" style="text-align: right !important;">
      <div class="create-offer__action__container">
        {% include 'buttons/conponent_button.html' with className='btn--tertiary small close-modal close-modal-offer' value='キャンセル' attribute='data-dismiss="modal" aria-label="Close" style="font-family: A+mfCv-AXISラウンド 50 L StdN"' %}

        {% if page == 'search' %}
          {% include 'buttons/conponent_button.html' with className='btn--primary medium create-offer-submit' value='オファーを作成' icon_end="next" attribute='style="margin-left: 0px;"' %}
        {% else %}
          {% include 'buttons/conponent_button.html' with className='btn--primary medium edit-offer-submit' value='オファーを作成' icon_end="next"  attribute='style="margin-left: 0px;"' %}
        {% endif %}
      </div>
    </div>
  </div>
</div>
