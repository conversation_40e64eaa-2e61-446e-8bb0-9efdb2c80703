{% load static %}
{% load util %}
{% load i18n %}

<div class="mcolumn-header">
  <a class="mcolumn-back" href="#">
    <i class="icon icon--sicon-prev"></i>
  </a>
  <div class="sheading sheading--18">{{ thread_name }}</div>
  <a class="mcolumn-next" href="#">
    <i class="icon icon--sicon-menu"></i>
  </a>
</div>

<div class="mcontent">
  <div class="mattach" style="color: black;" id="comment-input-1-mcomment-attach" data-maxfile="2" data-maxsize="32">
    <div class="mattach-overlay"></div>
    <form class="mattach-file" action="upload.php" id="comment-input-1-mcomment-attach-form">
      <div class="mattach-drop">
        <div class="mattach-text"></div>
        <div class="border"></div>
      </div>
    </form>
  </div>
  <div class="mmessage-list mscrollbar mscrollbar--vertical mscrollbar--bottom {% if not is_seen %}not-seen {% endif %}" style="padding-bottom: 116px;">
    <!-- message for offer creator -->
    {% if type_comment != 'messenger_owner' %}
      {% include 'top/_item_messages.html' with messages=messages user=user type=type type_message='messenger' %}
        {% if offer.status == '1' and user == offer.creator %}
            {% with file_name=offer.get_contract_file_name file_attribute=offer|get_artist_contract_file_attribute text_bottom=offer|get_artist_contract_file_text_bottom %}
                {% include 'top/_item_message_for_artist_contract.html' %}
            {% endwith %}
        {% endif %}
      {% include 'top/_comment_container_offer.html'%}
    <!-- message for offer product -->
    {% else %}
      <!-- normal messages -->
      {% with offer_last_plan=offer.get_offer_last_plan offer_last_contract=offer.get_offer_last_contract offer_last_bill=offer.get_offer_last_bill %}
        {% include 'top/_item_messages.html' with messages=messages user=user type=type type_message='messenger_owner' %}
        {% comment %} {% include 'direct/_message_check_file.html' with offer=offer user=user type='messenger_owner' %} {% endcomment %}
      {% endwith %}
        {% include 'top/_comment_container_offer.html' with messages=messages user=user type=type type_message='messenger_owner' %}
    {% endif %}
    <div class="mlast__content"></div>

  </div>
{#  <div class="maction" data-offer="{{ real_offer.pk }}">#}
{#    <div class="mcommment" id="comment-input-1" style="position: relative;">#}
{#      <div class="floating-button-container" default-value-time-form="{{real_offer|get_default_deadline_from_offer}}">#}
{#        {% include 'direct/_floating_icon_DM.html' with offer=offer %}#}
{#      </div>#}
{#      <div class="mcomment-message">#}
{#        <div class="mcomment-top">#}
{#          <div class="mcomment-attached">#}
{#            <div class="mattach-preview-container">#}
{#              <div class="mattach-previews collection">#}
{#                <div class="mattach-template collection-item item-template">#}
{#                  <div class="mattach-info" data-dz-thumbnail="">#}
{#                    <div class="mcommment-file">#}
{#                      <div class="progress">#}
{#                        <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>#}
{#                      </div>#}
{#                      <div class="mcommment-file__name" data-dz-name=""></div>#}
{#                      <div class="mcommment-file__delete" href="#!" data-dz-remove="">#}
{#                        <span class="progress-text"></span>#}
{#                        <i class="icon icon--sicon-close"></i>#}
{#                      </div>#}
{#                    </div>#}
{#                  </div>#}
{#                </div>#}
{#              </div>#}
{#            </div>#}
{#          </div>#}
{#          <div class="mcomment-input">#}
{#            <textarea class="mcomment-input-text mcomment-autoExpand" name="mcomment-input" rows="1"#}
{#                      placeholder="Aa…"></textarea>#}
{#          </div>#}
{#            <div class="block-remove-msg-editing d-none">#}
{#                <div class="mcomment-input-close btn-remove-msg">#}
{#                    <i class="icon icon--sicon-close"></i>#}
{#                </div>#}
{#            </div>#}
{#        </div>#}
{#        <div class="mcomment-bottom">#}
{#          <div class="mcomment-action">#}
{#            <div class="mattach-label">#}
{#              <i class="icon icon--sicon-clip"></i>#}
{#            </div>#}
{#          </div>#}
{#          <div class="mcomment-input-placeholder">コメントを送信</div>#}
{#          <a class="mcomment-icon" href="javascript:void(0)">#}
{#            <i class="icon icon--sicon-stick"></i>#}
{#          </a>#}
{#          <a class="mcomment-send disabled" href="#">#}
{#            <i class="icon icon--sicon-gps"></i>#}
{#          </a>#}
{#        </div>#}
{##}
{#      </div>#}
{#    </div>#}
{#  </div>#}
</div>
{##}
{##}
{#{% load static %}#}
{#{% load util %}#}
{#{% load i18n %}#}
{##}
{#<div class="mcolumn-header">#}
{#  <a class="mcolumn-back" href="#">#}
{#    <i class="icon icon--sicon-prev"></i>#}
{#  </a>#}
{#  <div class="sheading sheading--18">{{ thread_name }}</div>#}
{#  <a class="mcolumn-next" href="#">#}
{#    <i class="icon icon--sicon-menu"></i>#}
{#  </a>#}
{#</div>#}
{##}
{#<div class="mcontent">#}
{#  <div class="mattach" style="color: black;" id="comment-input-1-mcomment-attach" data-maxfile="2" data-maxsize="32">#}
{#    <div class="mattach-overlay"></div>#}
{#    <form class="mattach-file" action="upload.php" id="comment-input-1-mcomment-attach-form">#}
{#      <div class="mattach-drop">#}
{#        <div class="mattach-text"></div>#}
{#        <div class="border"></div>#}
{#      </div>#}
{#    </form>#}
{#  </div>#}
{#  <div class="mmessage-list mscrollbar mscrollbar--vertical mscrollbar--bottom {% if not is_seen %}not-seen {% endif %}" style="padding-bottom: 96px;">#}
{#    <!-- message for offer creator -->#}
{#    {% if type_comment != 'messenger_owner' %}#}
{#      {% include 'top/_item_messages.html' with messages=messages user=user type=type type_message='messenger' %}#}
{#      {% if offer.status == '1' and user == offer.creator %}#}
{#        {% comment %} <div class="mmessage mmessage--received mmessage-confirm" data-offer='{{ real_offer.pk }}'>#}
{#          <div class="mmessage-main">#}
{#            <div class="avatar avatar--image avatar--32 avatar--round">#}
{#              <div class="avatar-image" style="background-image: url({{ offer.admin|get_avatar:'medium' }})"></div>#}
{#            </div>#}
{#            <div class="mmessage-content">#}
{#              <div class="minfo-contract">#}
{#                <a class="see-contract" href="#" data-toggle="modal" data-target="#contract-modal">#}
{#                  <div class="minfo-contract-icon">#}
{#                    <i class="icon icon--sicon-accept"></i>#}
{#                  </div>#}
{#                </a>#}
{#                <div class="minfo-accept minfo-confirm" style="padding-top: 10px;">#}
{#                  <div class="minfo-accept__policy">#}
{#                    <div class="form-check custom-checkbox {% if not offer.valid_date|compare_now_with_valid_date == 'False' %}disabled{% endif %}">#}
{#                      <input class="form-check-input" type="checkbox" name="minfo-accept-policy"#}
{#                             id="minfo-accept-policy2"/>#}
{#                      <label class="form-check-label" for="minfo-accept-policy2"></label>#}
{#                    </div>#}
{#                    <a class="minfo-accept__link see-contract" href="#" data-toggle="modal"#}
{#                       data-target="#contract-modal">契約書</a>を確認しました。#}
{#                  </div>#}
{#                  <div class="minfo-accept__action">#}
{#                    <a class="btn btn--primary btn--blue btn--lg mmessenger-director__item-action-accept disabled"#}
{#                       href="#">#}
{#                      <span class="btn-text">{% trans "Make an offer" %}</span>#}
{#                    </a>#}
{#                  </div>#}
{#                </div>#}
{#              </div>#}
{#            </div>#}
{#          </div>#}
{#        </div> {% endcomment %}#}
{#        {% with file_name=offer.get_contract_file_name file_attribute=offer|get_artist_contract_file_attribute text_bottom=offer|get_artist_contract_file_text_bottom %}#}
{#            {% include 'top/_item_message_for_artist_contract.html' %}#}
{#        {% endwith %}#}
{#      {% endif %}#}
{#    <!-- message for offer product -->#}
{#    {% else %}#}
{#      <!-- normal messages -->#}
{#      {% with offer_last_plan=offer.get_offer_last_plan offer_last_contract=offer.get_offer_last_contract offer_last_bill=offer.get_offer_last_bill %}#}
{#        {% include 'top/_item_messages.html' with messages=messages user=user type=type type_message='messenger_owner' %}#}
{#        {% comment %} {% include 'direct/_message_check_file.html' with offer=offer user=user type='messenger_owner' %} {% endcomment %}#}
{#      {% endwith %}#}
{#    {% endif %}#}
{##}
{#    <div class="mlast__content"></div>#}
{##}
{#  </div>#}
{##}
{#  <div class="maction" data-offer="{{ real_offer.pk }}">#}
{#    <div class="mcommment" id="comment-input-1" style="position: relative;">#}
{#      <div class="floating-button-container" default-value-time-form="{{real_offer|get_default_deadline_from_offer}}">#}
{#        {% include 'direct/_floating_icon_DM.html' with offer=offer %}#}
{#      </div>#}
{#      <div class="mcomment-message">#}
{#        <div class="mcomment-top">#}
{#          <div class="mcomment-attached">#}
{#            <div class="mattach-preview-container">#}
{#              <div class="mattach-previews collection">#}
{#                <div class="mattach-template collection-item item-template">#}
{#                  <div class="mattach-info" data-dz-thumbnail="">#}
{#                    <div class="mcommment-file">#}
{#                      <div class="progress">#}
{#                        <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>#}
{#                      </div>#}
{#                      <div class="mcommment-file__name" data-dz-name=""></div>#}
{#                      <div class="mcommment-file__delete" href="#!" data-dz-remove="">#}
{#                        <span class="progress-text"></span>#}
{#                        <i class="icon icon--sicon-close"></i>#}
{#                      </div>#}
{#                    </div>#}
{#                  </div>#}
{#                </div>#}
{#              </div>#}
{#            </div>#}
{#          </div>#}
{#          <div class="mcomment-input">#}
{#            <textarea class="mcomment-input-text mcomment-autoExpand" name="mcomment-input" rows="1"#}
{#                      placeholder="Aa…"></textarea>#}
{#          </div>#}
{#            <div class="block-remove-msg-editing d-none">#}
{#                <div class="mcomment-input-close btn-remove-msg">#}
{#                    <i class="icon icon--sicon-close"></i>#}
{#                </div>#}
{#            </div>#}
{#        </div>#}
{#        <div class="mcomment-bottom">#}
{#          <div class="mcomment-action">#}
{#            <div class="mattach-label">#}
{#              <i class="icon icon--sicon-clip"></i>#}
{#            </div>#}
{#          </div>#}
{#          <div class="mcomment-input-placeholder">コメントを送信</div>#}
{#          <a class="mcomment-icon" href="javascript:void(0)">#}
{#            <i class="icon icon--sicon-stick"></i>#}
{#          </a>#}
{#          <a class="mcomment-send disabled" href="#">#}
{#            <i class="icon icon--sicon-gps"></i>#}
{#          </a>#}
{#        </div>#}
{##}
{#      </div>#}
{#    </div>#}
{#  </div>#}
{#</div>#}




