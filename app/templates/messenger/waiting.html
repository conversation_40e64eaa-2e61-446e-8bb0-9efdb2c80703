{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load compress %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" />
    {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_contract.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message_file.css' %}"/>
    {% endcompress %}

{% endblock %}

{% block content %}
    {% compress css %}
  <style>

    @media (max-width: 992px) {
      .maction {
        position: fixed;
        bottom: 180px;
        width: 100%;
      }
    }
    .project-item__filter-item {
      width: auto;
      min-width: 50px;
      height: 50px;
      padding: 0;
    }

    .project-item__filter-item.active svg path {
      fill: #009ace;
    }

    .messenger__item--new {
      background-color: #fff;
    }

    .messenger-detail .messenger-detail-content {
      max-height: calc(100vh - 390px);
    }

    .messenger-director .messenger__list {
      max-height: calc(100vh - 390px);
    }

    .bootbox.modal {
      margin-top: 30vh;
    }

    .mrow {
      margin-top: 0;
    }

    .project-item__content {
      display: flex;
    }

    .messenger-detail {
      height: 0;
      margin: 0;
    }

    .custom-checkbox .form-check-label {
      margin-bottom: 0;
    }

    @media (max-width: 992px) {
      body {
        overflow: hidden;
        touch-action: none;
        -ms-touch-action: none;
      }
    }

    .icon--sicon-heart-o:hover:before {
      content: "";
      color: #0076a5;
    }

    .pbanner > div:last-child {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
  </style>
    {% endcompress %}
  <main>
    <div class="mcontainer">
      <div class="p-martist" data-page="{{ page }}">
        <div class="psearch-top">
          <a href="{% url 'app:messenger_project' %}{% if is_done_project %}?is_done=1{% endif %}">
            {% include 'top/_product_banner.html' with project=project user=user type_page='messenger' page_detail='messenger_detail'%}
          </a>
        </div>

        <div class="martist role_{{ user.role }}">
          <div class="mrow">
            <div class="mcolumn mcolumn--left">
              <div class="mcolumn-header">
                <div class="madd-new">
                  <div class="offer-filter offer-waiting" data-processing="{% url 'app:messenger_processing' %}?project_id={{ project.pk }}" data-waiting="{% url 'app:messenger_waiting' %}?project_id={{ project.pk }}&tab=1">{% if status == 'new' %}<i class="icon icon--sicon-heart-o" title="やることリスト"></i>やることリスト{% else %}<i class="icon icon--sicon-heart" title="すべてのスレッド"></i>すべてのスレッド{% endif %}</div>
                  {% if user.role == 'admin' %}
                    <div class="messenger-add"><a
                            href="{% url 'app:director_messenger_search' %}?project_id={{ project.pk }}&tab=1"
                            class="btn btn--text-blue">
                      <i class="icon icon--sicon-add-cirlce"></i>
                      <span class="btn-text">新規</span>
                    </a></div>
                  {% endif %}
                </div>
              </div>
              <div class="mcolumn-content">
                <div class="mlist-wrap mscrollbar mscrollbar--vertical">

                  <div class="msearch">
                    <div class="sform-group sform-group--required">
                      <div class="sform-group__input-group sform-group__append-before">
                        <input class="sform-control sform-control--input sform-control--full" id="m-search"
                               type="search"
                               placeholder="検索" required="required"/>
                        <i class="icon icon--sicon-eye-open search-delete"></i>
                        <label class="sform-group__append" for="m-search">
                          <i class="icon icon--sicon-search"></i>
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="mlist">
                    {% if user.role == 'admin' %}
                      {% for offer in offers %}
                        {% with seen=offer|is_seen:user creator=offer.creator %}
                          {% include 'messenger/_item_scene.html' with user=user offer=offer receiver=offer.creator receiver_name=creator.get_display_name %}
                        {% endwith %}
                      {% endfor %}
                    {% else %}
                      {% for offer in offers %}
                        {% with seen=offer|is_seen:user %}
                          {% include 'messenger/_item_scene.html' with receiver=offer.admin receiver_name=offer.admin.get_display_name %}
                        {% endwith %}
                      {% endfor %}
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
            <div class="mcolumn mcolumn--main">
            </div>

            <div class="mcolumn mcolumn--right">
            </div>
          </div>
        </div>

        <div class="upload-button-wrapper">
          <p>アップロード中</p>
          <div class="fill">
            <div class="process"></div>
          </div>
          <div class="fa fa-check"></div>
        </div>
      </div>
    </div>
  </main>

  {% include 'messenger/_modal_contract_offer.html' %}
  {% include 'messenger/_modal_open_file.html' %}
  {% include 'top/_modal_show_folder.html' %}

  {% if user.role == 'admin' %}

    {% include 'messenger/_modal_form_offer.html' with project=project %}

    {% include 'messenger/_modal_over_budget.html' %}
    {% include 'messenger/_modal_edit_offer.html' %}

  {% endif %}
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/init_socket_messenger.js' %}"></script>
  <script>
      let csrf = '{% csrf_token %}';
      let user_role = '{{ user.role }}';

      if (is_logged_in === 'True') {
          initSocket({{ user.id }});
      }

      let is_pc = '{{ request|is_pc }}';
  </script>
    {% endcompress %}
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/wavesurfer.js/3.3.3/plugin/wavesurfer.cursor.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/wavesurfer.js/3.3.3/plugin/wavesurfer.cursor.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/offer_modal.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/utils.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/soremo.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/action_banner.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/messenger_artist.js' %}"></script>
    {% endcompress %}
{% endblock %}
