{% load static %}
{% load util %}

<div class="contract-modal__party">
  <div class="contract-modal__text">
    <p>以上、コンテンツ本契約の成立を証するため、アーティストと当社はサービス利用規約所定の電磁的方法により手続を実施する。</p>
    {% if offer.accept_time %}
      <p id="modal-date__accept">
        <span>{{ offer.accept_time.year }} 年</span>
        <span>{{ offer.accept_time.month }} 月</span>
        <span>{{ offer.accept_time.day }} 日</span>
      </p>
    {% endif %}
    <p>契約当事者</p>
  </div>
  <div class="contract-modal__columns">
    <div class="contract-modal__column">
      <p>アーティスト</p>
      <ul>
        <li id="modal-address__creator">{{ creator|get_full_address }}</li>
        <li id="modal-fullname__creator">{{ creator.get_display_name }}</li>
      </ul>
    </div>
    <div class="contract-modal__column">
      <p>当社</p>
      <ul>
        <li>東京都江東区青海二丁目７番４号</li>
        <li>株式会社ソレモ</li>
        <li>代表取締役　善里 信哉</li>
      </ul>
    </div>
  </div>
  <div class="contract-modal__columns" style="margin-bottom: 15%">
    <div class="contract-modal__column">
      <p class="contract-modal__mark">印</p>
    </div>
    <div class="contract-modal__column">
      <p class="contract-modal__mark">印</p>
    </div>
  </div>
</div>

<div class="contract-modal__section">
  <div class="contract-modal__heading">職種</div>
  <div class="contract-modal__text">
    <p id="modal-contract">{{ offer.type_contract }}</p>
  </div>
</div>

<div class="contract-modal__section">
  <div class="contract-modal__heading">本仕様</div>
  <div class="contract-modal__text">
    <p>
      <span class="contract-modal__label contract-modal__label--spec">オーナー名：</span>
      <span class="contract-modal__value" id="modal-fullname_owner">{% if offer.project.client_name %}{{ offer.project.client_name }}{% endif %}</span>
    </p>
    <p>
      <span class="contract-modal__label contract-modal__label--spec">プロジェクト名：</span>
      <span class="contract-modal__value" id="modal-name_project">{{ offer.project.get_name_by_contract }}</span>
    </p>
    <p>
      <span class="contract-modal__label contract-modal__label--spec">シーン：</span>
      <span class="contract-modal__value" id="modal-sence">{{ offer.scenes }}</span>
    </p>
  </div>
</div>

<div class="contract-modal__section">
  <div class="contract-modal__heading">対価</div>
  <div class="contract-modal__text">
    <p id="modal-rewward_offer">{{ offer.reward|display_currency }} 円 （税込）</p>
  </div>
</div>

<div class="contract-modal__section">
  <div class="contract-modal__heading">納品物</div>
  <div class="contract-modal__text">
    <p>
      <span class="contract-modal__label contract-modal__label--deliver">詳細：</span>
      <span class="contract-modal__value" id="modal-message_offer"
            style="word-break: break-word;">{{ offer.message }}</span>
    </p>
    <p>
      <span class="contract-modal__label contract-modal__label--deliver">数量：</span>
      <span class="contract-modal__value" id="modal-quantity_offer"
            style="word-break: break-word;">{% if offer.quantity %}{{ offer.quantity }}{% endif %}</span>
    </p>
    <p>
      <span class="contract-modal__label contract-modal__label--deliver">形式：</span>
      <span class="contract-modal__value" id="modal-data-format_offer"
            style="word-break: break-word;">{% if offer.data_format %}{{ offer.data_format }}{% endif %}</span>
    </p>
  </div>
</div>

<div class="contract-modal__section">
  <div class="contract-modal__heading">予定納期</div>
  <div class="contract-modal__text contract-modal__text--date" id="modal-dealine_offer">
    <span>{{ offer.deadline.year }} 年</span>
    <span>{{ offer.deadline.month }} 月</span>
    <span>{{ offer.deadline.day }} 日</span>
    <span>{{ offer.deadline.hour }} 時</span>
  </div>
</div>

<div class="contract-modal__section">
  <div class="contract-modal__heading">納品方法</div>
  <div class="contract-modal__text">
    <p>
      <span class="contract-modal__bold">納品物データを本サービス上において当社所定の方法によりアップロードする。</span>
    </p>
  </div>
</div>

<div class="contract-modal__section">
  <div class="contract-modal__heading">連絡・通知先</div>
  <div class="contract-modal__text">
    <p>
      <span class="contract-modal__label">アーティスト</span>
    </p>
    <p>
      <span class="contract-modal__label">電子メールによる場合：</span>
      <span class="contract-modal__value" id="modal-mail_creator">{{ creator.email }}</span>
    </p>
    <p>
      <span class="contract-modal__label">当社</span>
    </p>
    <p>
      <span class="contract-modal__label">電子メールによる場合：</span>
      <span class="contract-modal__value"><EMAIL></span>
    </p>
  </div>
</div>
