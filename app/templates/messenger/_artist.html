{% load static %}
{% load util %}
{% load i18n %}
<div class="psearch-section psearch-artist-wrap c-group">
  <div class="psearch-content">
    {% if creators.count == 0 %}
      <div class="bodytext">該当するスペシャリストは、見つかりませんでした</div>
    {% else %}
      <div class="heading-spacing-1824 u-mb8 u-text-light-gray">{{ creators.count }}</div>
    {% endif %}
    <div class="psearch-content">
      <div class="psearch-artist-list mscrollbar">
        {% for creator in creators %}
          <div class="mcreator p-cards" data-creator="{{ creator.pk }}">
            <div class="u-row u-gap8">
              <div class="c-avatar40">
                <img src="{{ creator.user|get_avatar:'small' }}" alt width="40px" height="40px"></div>
              {% with creator|get_task_in_progress as task_progress %}
                <div class="notification notification--blue notification--round number-tasks {% if task_progress < 1 %}hide{% endif %}">{{ creator|get_task_in_progress }}</div>
              {% endwith %}
              <ul class="u-col u-gap4 u-mb0">
                <li class="heading-13-spacing">{{ creator.user.get_display_name }}</li>
                <li class="bodytext-11 u-line-height-150">{{ creator.user.type }}</li>
              </ul>
              <a class="mcreator__profile"
              href="{% if creator.slug %}{% url 'app:creator_info' slug=creator.slug %}{% else %}{% url 'accounts:accounts_creator' creator.user.pk %}{% endif %}"
              target="_blank"><span class="material-symbols-rounded">chevron_right</span></a>

            </div>
            <div class="mcreator__header-tradeoff">
              <div class="account__trade-slider">
                {% for i in '12345' %}
                  <div class="account__trade-item {% if i == creator.get_trading_display|stringformat:"i" %}active{% endif %}" data-option="{{ i }}"></div>
                {% endfor %}
              </div>
            </div>
              <div class="u-bg-background bodytext-11">{% if creator.policy %}{{ creator.policy }}{% endif %}</div>
              <hr>
              <div class="bodytext-11">{% if creator.note_schedule %}{{ creator.note_schedule }}{% endif %}</div>
              <div class="mcreator__body-schedule">
                <div class="mcalendar mcalendar--small">
                  <div class="datepicker datepicker-inline">
                    <div class="datepicker-days" style="display: block;">
                      <table class="table-condensed">
                        <tbody>
                          {% with creator|get_schedule_list_info as schedules %}
                            {% for week in schedules %}
                              <tr>
                                {% for day in week.1 %}
                                  <td class="day {% if day.1 == today %}today {% endif %}{% if day.0 == '2' %}day__maybe{% elif day.0 == '1'%}day__off{% endif %}">{{ day.1 }}</td>
                                {% endfor %}
                              </tr>
                            {% endfor %}
                          {% endwith %}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
          <div class="mcreator__footer">
            <button class="btn btn--primary btn-create-contact u-w100" data-toggle="modal"
                    data-target="#modal-create-offer">次へ</button>
          </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
