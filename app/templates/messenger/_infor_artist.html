{% load static %}
{% load util %}
<style>
  .mcalendar--black .datepicker-inline .day.today {
    color: #000 !important;
  }
</style>

<div class="psearch-artist">
  <div class="psearch-artist-top">
    <div class="psearch-artist-info">
      <div class="psearch-artist-avatar">
        <div class="avatar avatar--image avatar--40 avatar--round">
          <div class="avatar-image background-avt" style="background-image: url({{ creator.user|get_avatar:'small' }})"></div>
        </div>
      </div>
      <div class="psearch-artist-meta">
        <div class="psearch-artist-name">{{ creator.user.get_display_name }}</div>
        <div class="psearch-artist-role scaption">{{ creator.user.type }}</div>
      </div>
    </div>
    <div class="psearch-artist-btn">
      <input class="btn btn--blue btn--sm btn-create-contact" type="button" value="オファーする" data-toggle="modal"
             data-target="#modal-create-offer">
    </div>
  </div>
  <div class="psearch-artist-offer">
    <div class="artist-offer__wrap">
      <div class="artist-offer__slider" data-trading="{{ creator.get_trading_display }}">
        <div class="artist-offer__item" data-quantity="1" data-price="100"></div>
        <div class="artist-offer__item" data-quantity="2" data-price="75"></div>
        <div class="artist-offer__item" data-quantity="3" data-price="50"></div>
        <div class="artist-offer__item" data-quantity="4" data-price="25"></div>
        <div class="artist-offer__item" data-quantity="5" data-price="0"></div>
      </div>
      <div class="artist-offer__label">
        <div class="artist-offer__label-left scaption">仕事量を増やしたい</div>
        <div class="artist-offer__label-right scaption">条件を良くしたい</div>
      </div>
    </div>
    <div class="psearch-artist-contract">
      <div class="psearch-artist-title">案件受託についてのポリシー</div>
      <div class="psearch-artist-policy mscrollbar mscrollbar--dark" style="word-break: break-word">
        {{ creator.policy }}
      </div>
    </div>
  </div>
  <div class="psearch-artist-block">
    <div class="mcalendar mcalendar--black" data-dates-deadline="{{ list_deadline }}"
         data-dates-disable="{{ list_date }}"></div>
    <div class="psearch-artist-block-list mscrollbar mscrollbar--dark">
      {% for company in block_list %}
        <div class="block-company">
          <div class="block-company__avatar">
            <div class="avatar avatar--image avatar--40 avatar--square">
              <div class="avatar-image"></div>
            </div>
          </div>
          <div class="block-company__info">
            <div class="block-company__name sheading sheading--13"><a href="{{ company.company_url }}" target="_blank"
                                                                      style="color: #FFFFFF">{{ company.company_name }}</a>
            </div>
            <div class="block-company__business scaption"></div>
          </div>
          <div class="block-company__action">
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</div>
