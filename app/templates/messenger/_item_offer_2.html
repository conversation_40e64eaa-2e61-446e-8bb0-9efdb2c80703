{% load static %}
{% load util %}
{% load i18n %}
<li class="item-offer mscene mitem"
data-offer="{{ real_offer.pk }}" data-type-offer="messenger_owner" data-offer-side="0" data-reward="{{ real_offer.budget_or_reward }}" data-created="{{ real_offer.created|get_datetime }}">
{% with user.role|get_data_project_order:offer as product_offer_order %}
    <div class="progress-offer" style="background-color: {% if offer.condition == '1' or offer.condition == '8' %}#f0f0f0{% elif offer.condition == '2' or offer.condition == '3' or offer.condition == '9' %}#009ace{% elif offer.condition == '4' or offer.condition == '5' or offer.condition == '6' or offer.condition == '7' %}#53565a{% else %}#f0f0f0{% endif %}"></div>
    <div class="offer-main">
        <span class="material-symbols-rounded u-fontsize-24 grade-icon">grade</span>
        <div class="content-offer">
            <div class="block-offer">
                <div class="block-offer-1">
                    <span class="title-offer">このプロジェクトの契約</span>
                    <span class="time-offer">{{ real_offer.modified|get_weekday_new }}</span>
                </div>
                <div class="block-offer-2">
                    <div class="block-avatar-user">
                        {% if user.role == 'master_client' %}
                            <img class="avatar-user-offer "
                                 src="{{ owner|get_avatar:'medium' }}"
                                 alt="">
                            {% with project.get_director_producer_master_admin_in_project|check_owner_product:user as admins %}
                                {% if admins %}
                                    <img class="avatar-user-offer ml-8"
                                         src="{{ admins.0|get_avatar:'medium' }}"
                                         alt="">
                                {% endif %}
                            {% endwith %}
                        {% else %}
                            <img class="avatar-user-offer "
                                 src="{{ user|get_avatar:'medium' }}"
                                 alt="">
                            <img class="avatar-user-offer ml-8"
                                 src="{{ owner|get_avatar:'medium' }}"
                                 alt="">
                        {% endif %}
{#                       <img class="avatar-user-offer "#}
{#                                         src="{{ owner|get_avatar:'medium' }}"#}
{#                                         alt="">#}
{#                        {% with offer.project|get_other_member_in_project:user as members %}#}
{#                            {% for member in members %}#}
{#                                {% if forloop.counter0 < 5 %}#}
{#                                    <img class="avatar-user-offer ml-8"#}
{#                                         src="{{ user|get_avatar:'medium' }}"#}
{#                                         alt="">#}
{#                                {% endif %}#}
{#                            {% endfor %}#}
{#                        {% endwith %}#}
                    </div>
                    <span class="description-content-offer" style="color: {% if product_offer_order.color %}{{ product_offer_order.color }}{% else %}#a7a8a9{% endif %}">{% if product_offer_order %}{{ product_offer_order.text }}{% endif %}</span>
                </div>
            </div>
            <div class="right-offer">
                {% with offer|new_message_count:user as count_new %}
                    <div class="block-message-count">
                        <div class="number-offer notification notification--blue notification--round {% if count_new == 0 %}hide{% endif %}"> {% if count_new < 100 %}{{ count_new }}{% else %}99+{% endif %}</div>
                    </div>
                {% endwith %}
                <span class="next-offer-icon material-symbols-rounded">navigate_next</span>
            </div>
        </div>
    </div>
{% endwith %}
</li>