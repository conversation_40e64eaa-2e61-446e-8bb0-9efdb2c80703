{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load compress %}

{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css" />
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/messenger_project.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
    {% endcompress %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
    <main class="messenger-project">
        <div class="container">
            <div class="new-video-menu">
              <div class="project-list">
                {% for project in projects %}
                  {% with user|get_product_user:project as product_user %}
                  <div class="project-item-handle"
                       data-product-user-id="{{ product_user.pk }}">
                    {% if user.role == 'admin' %}
                      <a href="{% url 'app:director_messenger_search' %}?project_id={{ project.pk }}">
                    {% elif user.role == 'creator' %}
                      <a href="{% url 'app:messenger_waiting' %}?project_id={{ project.pk }}">
                    {% endif %}
                    <div class="project-item {% if not request|is_pc %} on-mobile{% endif %}"
                         data-project-id="{{ project.pk }}">
                      {% include 'top/_product_banner.html' with project=project user=user type_page='messenger_projects' is_pc=request|is_pc show_staff=True %}
                    </div>
                    </a>
                  </div>
                  {% endwith %}
                {% endfor %}
              </div>
            </div>
        </div>
    </main>
  {% if has_done %}
      <div class="project-progress-action" id="id-top-done-banner" style="z-index: 12">
          <div class="project-progress-action-content">
              <div class="project-progress-action-notice">
                {% if is_done %}
                    <div class="project-progress-action-message">←</div>
                {% else %}
                    <div class="project-progress-action-message">完了したプロジェクトはこちら。</div>
                {% endif %}
              </div>
              <div class="project-progress-action-btn">
                  <a class="button button--gradient button--gradient-primary button--round" href="
                        {% url 'app:messenger_project' %}{% if not is_done %}?is_done=1{% endif %}"
                   role="button">{% if is_done %}進行中プロジェクトに戻る{% else %}アーカイブを観る{% endif %}</a>
              </div>
          </div>
      </div>
  {% endif %}

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    <script src="{% url 'javascript-catalog' %}"></script>
    {% compress js inline %}
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/sort_project.js' %}"></script>
    {% endcompress %}
{% endblock %}
