{% load i18n %}
{% load util %}

{% with creators=real_offer.get_creator_in_offer_creator admins=real_offer.get_admin_in_offer_creator %}
  {% if offer.status == '1' and user in creators %}
    {% comment %} <a class="see-contract" href="#" data-toggle="modal" data-target="#contract-modal">
      <div class="minfo-contract-icon">
        <i class="icon icon--sicon-accept"></i>
      </div>
    </a>
    <div class="minfo-accept minfo-confirm" style="padding-top: 10px;">
      <div class="minfo-accept__policy">
        <div class="form-check custom-checkbox {% if not offer.valid_date|compare_now_with_valid_date == 'False' %}disabled{% endif %}">
          <input class="form-check-input" type="checkbox" name="minfo-accept-policy"
                 id="minfo-accept-policy"/>
          <label class="form-check-label" for="minfo-accept-policy"></label>
        </div>
        <a class="minfo-accept__link see-contract" href="#" data-toggle="modal" data-target="#contract-modal">契約書</a>を確認しました。
      </div>
      <div class="minfo-accept__action">
        <a class="btn btn--primary btn--blue btn--lg mmessenger-director__item-action-accept disabled" href="#">
          <span class="btn-text">{% trans "Make an offer" %}</span>
        </a>
      </div>
    </div> {% endcomment %}

  {% elif offer.status == '1' and user in admins %}

    <div class="minfo-contract-icon see-contract" data-toggle="modal" data-target="#contract-modal">
      <i class="icon icon--sicon-contract"></i>
    </div>
    <div class="minfo-accept minfo-confirm" style="padding-top: 10px;">

      <div class="minfo-accept__action">
        <a class="btn btn--secondary btn--blue btn--lg mmessenger-director__item-action-closed"  data-toggle="modal" data-target="#closed-offer">
          <span class="btn-text">オファーを閉じる</span>
        </a>
      </div>
    </div>

  {% elif offer.status in '2, 3' and user in admins %}
    <div class="minfo-contract-icon">
      <i class="icon icon--sicon-mark-done"></i>
    </div>
    <div class="minfo-contract-btn">
      <button class="btn btn--blue btn--lg accept_production_file">
        <span class="btn-text">検収する</span>
      </button>
    </div>

  {% elif offer.status == '4' and user in creator and not offer.review_admin or offer.status == '4' and user in admins and not offer.review %}
    <div class="minfo-contract-icon see-contract" data-toggle="modal" data-target="#contract-modal">
      <i class="icon icon--sicon-contract"></i>
    </div>
    <div class="minfo-contract-btn">
      <button class="btn btn--primary btn--blue btn--lg see-contract" data-toggle="modal" data-target="#contract-modal">
        <span class="btn-text">契約書を見る</span>
      </button>
    </div>

  {% else %}
    <div class="minfo-contract-icon see-contract" data-toggle="modal" data-target="#contract-modal">
      <i class="icon icon--sicon-contract"></i>
    </div>
    <div class="minfo-contract-btn">
      <button class="btn btn--primary btn--blue btn--lg see-contract" data-toggle="modal" data-target="#contract-modal">
        <span class="btn-text">契約書を見る</span>
      </button>
    </div>
  {% endif %}
{% endwith %}
