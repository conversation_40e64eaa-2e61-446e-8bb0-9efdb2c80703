{% load static %}
{% load util %}

<div class="mcolumn-header">
  <a class="mcolumn-back" href="#">
    <i class="icon icon--sicon-prev"></i>
  </a>
  <a class="mcolumn-toggle" href="#">
    <div class="mcolumn-header-toggle"><i class="icon icon--sicon-next"></i></div>
  </a>
</div>
{% with creators=real_offer.get_creator_in_offer_creator admins=real_offer.get_admin_in_offer_creator %}
  <div class="mcolumn-content" data-offer="{{ real_offer.pk }}"
       data-role-offer="{% if user in creators %}creator{% else %}admin{% endif %}">
    <div class="minfo-wrap mscrollbar mscrollbar--vertical">
      <div class="accordion accordion--normal" id="accordionInformation">
        <div class="accordion-item">
          <div class="accordion-header" id="heading1">
            <div class="accordion-heading sheading sheading--13">
              {% if user in admins %}
                <div class="scene-title__action active">
                {% if offer.status != '6' %}
                  <a class="button-edit_offer" data-toggle="modal" data-target="#modal-edit-offer">
                    <i class="icon icon--sicon-pencil"></i>
                  </a>
                  {% endif %}
                  {% if offer.status in '1,6' %}
                    <a class="button-delete_offer" data-toggle="modal" data-target="#delete-offer">
                      <i class="icon icon--sicon-trash"></i>
                    </a>
                  {% endif %}
                </div>
              {% endif %}
            </div>
          </div>
          <div class="accordion-content collapse in" id="collapse1" aria-labelledby="heading1"
               data-parent="#accordionInformation" aria-expanded="true">
            <div class="accordion-content-container mscrollbar mscrollbar--vertical">
              <div class="minfo-section">
                <div class="minfo-list">
                  <div class="infor-offer">
                    {% include 'messenger/_item_infor_offer.html' with offer=offer %}
                  </div>
                  <div class="minfo-contract">
                    {% include 'messenger/_item_show_icon.html' with offer=offer user=user %}
                  </div>

                  {#              review     #}
                  {% if offer.status == '4' %}
                    {% if user in creators and not offer.review_admin or user in admins and not offer.review %}
                      <div class="minfo-rating">
                        <button class="btn btn--black btn--sm btn-icon-unlike" data-value="3">
                          <i class="icon icon--sicon-unlike"></i>
                          <span class="btn-text"></span>
                        </button>
                        <button class="btn btn--blue btn--sm btn-icon-like" data-value="1">
                          <i class="icon icon--sicon-like"></i>
                          <span class="btn-text"></span>
                        </button>
                      </div>
                    {% endif %}
                  {% endif %}

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="minfo-section minfor-document">
        <div class="minfo-upload">
        </div>
        <div class="tfile">
          <div class="tfile-list">
            <div class="mlast__file"></div>
            {% for key in dict_files %}
              {% with key|get_message_file_folder:type_comment as object %}
                {% if object %}
                  {% if object.file_id %}
                    <div class="tfile-item">
                      <div class="tfile-item-time">{{ object.modified|get_weekday_new }}</div>
                      <div class="tfile-item-content">
                        {% include 'top/_file_infor.html' with file=object message=object.message type_comment='messenger' name=object.real_name %}
                      </div>
                    </div>
                  {% elif object.folder_id %}
                    <div class="tfile-item-time">{{ object.modified|get_weekday_new }}</div>
                    <div class="tfile-item-content">
                      <div class="tfile-file">
                        {% include 'top/_sfolder.html' with folder=object message=object.message type_comment='messenger' %}
                      </div>
                    </div>
                  {% endif %}
                {% endif %}
              {% endwith %}
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>
{% endwith %}
