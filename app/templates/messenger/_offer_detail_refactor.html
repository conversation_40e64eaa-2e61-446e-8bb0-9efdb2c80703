{% load static %}
{% load util %}
{% load i18n %}

{% if type_comment != 'messenger_owner' %}
  {% include 'top/_comment_container_offer_refactor.html'%}
{% else %}
  {% include 'top/_comment_container_offer_refactor.html' with messages=messages user=user type=type type_message='messenger_owner' %}
{% endif %}
<div class="mcolumn-header">
  <span class="mcolumn-back material-symbols-rounded u-text-blue">
    navigate_before
  </span>
<div> 
</div>
  <a class="mcolumn-next hide" href="#">
    <i class="icon icon--sicon-menu"></i>
  </a>
</div>

<div class="mcontent refactor">
  <div class="mattach" style="color: black;" id="comment-input-1-mcomment-attach" data-maxfile="2" data-maxsize="32">
    <div class="mattach-overlay"></div>
    <form class="mattach-file" action="upload.php" id="comment-input-1-mcomment-attach-form">
      <div class="mattach-drop">
        <div class="mattach-text"></div>
        <div class="border"></div>
      </div>
    </form>
  </div>
  <div class="mmessage-list mscrollbar mscrollbar--vertical mscrollbar--bottom {% if not is_seen %}not-seen {% endif %}">
    <div class="space-first-message"></div>
      <!-- message for offer creator -->
      {% if type_comment != 'messenger_owner' %}
        <div class="mmessage-container refactor">
          {% include 'messenger/offer/_block_offer_message.html' %}
        </div>
        {% include 'top/_item_messages.html' with messages=prev_messages user=user type=type type_message='messenger' %}
        <div class="mmessage-container refactor">
          {% if offer.status == "2" and user != offer.creator or offer.status == "3" and user != offer.creator %}
            {% include 'messenger/offer/_block_offer_after_accept.html' %}
          {% elif offer.status == "2" or offer.status == "3" %}
            {% include 'messenger/offer/_block_offer_after_artist_accept.html' %}
          {% elif offer.status == '4' and user != offer.creator%}
            {% include 'messenger/offer/_block_offer_after_accept.html' %}
          {% elif offer.status == '4'%}
            {% include 'messenger/offer/_block_offer_after_artist_accept.html' %}
          {% endif %}
        </div>
        {% for message_afterreview in list_messages_afterreview %}
          {% include 'top/_item_messages.html' with messages=message_afterreview user=user type=type type_message='messenger' %}
          {% if forloop.counter == 1 and list_review.0 %}
            {% include 'top/_item_message_review.html' with message=list_review.0 user_ids=id_review_offer user=user type=type type_message='messenger' %}
          {% elif forloop.counter == 2 and list_review.1 %}
            {% include 'top/_item_message_review.html' with message=list_review.1 user_ids=id_review_offer user=user type=type type_message='messenger' %}
          {% endif %}
        {% endfor %}

        {% if offer.status == '1' and user == offer.creator %}
          {% with file_name=offer.get_contract_file_name file_attribute=offer|get_artist_contract_file_attribute text_bottom=offer|get_artist_contract_file_text_bottom %}
              {% include 'top/_item_message_for_artist_contract.html' %}
          {% endwith %}
        {% endif %}
      <!-- message for offer product -->
      {% else %}
        <div class="mmessage-container refactor">
          {% if offer.status == '2' and user == offer.creator %}
              {% with file_name=offer.get_contract_file_name file_attribute=offer|get_artist_contract_file_attribute text_bottom=offer|get_artist_contract_file_text_bottom %}
                  {% include 'top/_item_message_for_artist_contract.html' %}
              {% endwith %}
          {% elif offer.status == '1' and user.role == 'admin'%}
            {% include 'messenger/offer/_block_offer_message.html' %}
          {% elif offer.status == '2' and user != offer.creator%}
            {% include 'messenger/offer/_block_offer_after_accept.html' %}
          {% elif offer.status == '4'%}
            {% include 'messenger/offer/_block_offer_after_artist_accept.html' %}
          {% endif %}
          <!-- normal messages -->
          {% with offer_last_plan=offer.get_offer_last_plan offer_last_contract=offer.get_offer_last_contract offer_last_bill=offer.get_offer_last_bill %}
            {% include 'top/_item_messages.html' with messages=messages user=user type=type type_message='messenger_owner' %}
            {% comment %} {% include 'direct/_message_check_file.html' with offer=offer user=user type='messenger_owner' %} {% endcomment %}
          {% endwith %}
        </div>
      {% endif %}
    <div class="mlast__content"></div>

  </div>
</div>



