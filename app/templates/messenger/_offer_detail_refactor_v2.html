{% load static %}
{% load util %}
{% load i18n %}

{% if type_comment != 'messenger_owner' %}
  {% include 'top/_comment_container_offer_refactor.html'%}
{% else %}
  {% include 'top/_comment_container_offer_refactor.html' with messages=messages user=user type=type type_message='messenger_owner' %}
{% endif %}
<div class="mcolumn-header">
  <span class="mcolumn-back material-symbols-rounded u-text-blue">
    navigate_before
  </span>
  <div></div>
  <a class="mcolumn-next hide" href="#">
    <i class="icon icon--sicon-menu"></i>
  </a>
</div>

<div class="mcontent refactor">
  <div class="mattach" style="color: black;" id="comment-input-1-mcomment-attach" data-maxfile="2" data-maxsize="32">
    <div class="mattach-overlay"></div>
    <form class="mattach-file" action="upload.php" id="comment-input-1-mcomment-attach-form">
      <div class="mattach-drop">
        <div class="mattach-text"></div>
        <div class="border"></div>
      </div>
    </form>
  </div>
  <div class="mmessage-list mscrollbar mscrollbar--vertical mscrollbar--bottom {% if not is_seen %}not-seen {% endif %}">
    <div class="space-first-message"></div>
    <div class="mmessage-container refactor">
      <!-- message for offer creator -->
      {% if type_comment != 'messenger_owner' %}
        {% include 'top/_item_messages_refactor.html' with messages=messages user=user type=type type_message='messenger' %}
          {% if offer.status == '1' and user == offer.creator %}
              {% with file_name=offer.get_contract_file_name file_attribute=offer|get_artist_contract_file_attribute text_bottom=offer|get_artist_contract_file_text_bottom %}
                  {% include 'top/_item_message_for_artist_contract.html' %}
              {% endwith %}
          {% endif %}
      <!-- message for offer product -->
      {% else %}
        <!-- normal messages -->
        {% with offer_last_plan=offer.get_offer_last_plan offer_last_contract=offer.get_offer_last_contract offer_last_bill=offer.get_offer_last_bill %}
          {% include 'top/_item_messages_refactor.html' with messages=messages user=user type=type type_message='messenger_owner' %}
          {% comment %} {% include 'direct/_message_check_file.html' with offer=offer user=user type='messenger_owner' %} {% endcomment %}
        {% endwith %}
          
      {% endif %}
    </div>
    <div class="mlast__content"></div>

  </div>
</div>



