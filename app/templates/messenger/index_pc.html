{% load static %}
{% load util %}
{% load compress %}
{% compress js inline %}
<script>
$(document).ready(function () {
    $('.room-'+chatroom_id).removeClass('messenger__item--not-selected').addClass('messenger__item--selected');
    $('.messenger-detail__button-send').on('click', function () {
        sendOfferMessage();
    });

    goToChatRoom = function (room_id, dom) {
        $('.messenger__item--selected').removeClass('messenger__item--selected').addClass('messenger__item--not-selected');
        $(dom).removeClass('messenger__item--not-selected messenger__item--new ').addClass('messenger__item--selected');
        $(dom).find('.messenger__tag').addClass('hide');
        $(dom).find('.messenger__time').removeClass('hide');
        $(dom).find('.messenger__seen').removeClass('hide');
        loading = true;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "{% url 'app:get_room_message' %}",
            data: {
                'chatroom_id': room_id,
                'change_room': true,
            },
            success: function (data) {
                loading = false;
                let chat_node = $('#mCSB_2_container');
                chat_node.empty();
                let message_list = data.message_list;
                chatroom_id = data.chatroom_id;
                scroll_page = 1;
                for (let i = message_list.length - 1; i >= 0; i--) {
                    let message = message_list[i].fields;
                    let seen_class = ' hide';
                    let file_url = message.file_url;
                    let has_file_class = 'hide';
                    if (message.file_url) {
                        has_file_class = '';
                    }
                    if(message.seen_date) {
                        seen_class = '';
                    }
                    let message_avatar;
                    if (data.avatar) {
                         message_avatar = data.avatar;
                    } else {
                        message_avatar = '{% static 'images/default-avt.png' %}';
                    }
                    if (message.owner === {{ user.pk }}) {
                        chat_node.append('<div class="messenger__item messenger__item--right">' +
                            '<div class="messenger__avatar">' +
                            '<img class="messenger__avatar-img"' +
                            'src="{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}" alt=""></div>' +
                            '<div class="messenger__info">' +
                            '<div class="messenger__mess">' +
                            '<div class="messenger__mess-item">' + message.content + 
                            '</div>' + '<br/>' +
                            '<a class="messenger__download--right '+has_file_class+'" href='+file_url+ '"><i class="fas fa-download"></i>&nbsp;'+message.file+'</a>' +
                            '<div class="messenger__seen'+ seen_class +'">' +
                            '<img class="messenger__seen-img"' +
                            'src="'+ message_avatar +'" alt=""></div></div></div></div>'
                        );
                    } else {
                        chat_node.append('<div class="messenger__item messenger__item--left">' +
                            ' <div class="messenger__avatar"><img class="messenger__avatar-img" src="'+ message_avatar +'" alt=""></div>' +
                            '<div class="messenger__info">' +
                            '<div class="messenger__mess-item">' + message.content + 
                            '</div>' + '<br/>' +
                            '<a class="messenger__download--left '+has_file_class+'" href='+file_url+ '"><i class="fas fa-download"></i>&nbsp;'+message.file+'</a>' +
                            '<div class="messenger__seen"><img class="messenger__seen-img" src="{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}" alt=""></div>' +
                            '</div>' +
                            '</div>' +
                            '</div>'
                        );
                    }
                }

                $('.messenger-detail-content').mCustomScrollbar('scrollTo', 'bottom', {
                    scrollInertia: 0
                })
            }
        })
    };
});

$(window).on('load', function () {
    console.log('onload');

    SoundcheckProject.mCustomScrollbar();
    //Observe chat rectangle for scroll ups
    loadPreviousMessages = function () {
        loading = true;
        $.ajax(
            {
                type: "GET",
                url: "{% url 'app:get_room_message' %}",
                data: {
                    'chatroom_id': chatroom_id,
                    'scroll_page': scroll_page,
                    'change_room': false
                },
                success: function (data) {
                    loading = false;
                    scroll_page = (parseInt(scroll_page) + 1).toString();
                    let message_list = data.message_list;
                    let des = $('#mCSB_2_container');
                    let current_top = des.children().first();
                    for (let i = 0; i < message_list.length; i++) {
                        let message = message_list[i].fields;
                        let message_avatar;
                        let file_url = message.file_url;
                        let has_file_class = 'hide';
                        if (message.file) {
                            has_file_class = '';
                        }
                        let seen_class = ' hide';
                        if(message.seen_date) {
                            seen_class = ''
                        }
                        if (data.avatar) {
                             message_avatar = data.avatar;
                        } else {
                            message_avatar = '{% static 'images/avatar-user.jpg' %}';
                        }
                        if (message.owner === {{ user.pk }}) {
                            des.prepend('<div class="messenger__item messenger__item--right">' +
                                '<div class="messenger__avatar">' +
                                '<img class="messenger__avatar-img"' +
                                'src="{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}" alt=""></div>' +
                                '<div class="messenger__info">' +
                                '<div class="messenger__mess">' +
                                '<div class="messenger__mess-item">' + message.content + 
                                '</div>' + '<br/>' +
                                `<a class="messenger__download--right ${has_file_class}" href="${file_url}"><i class="fas fa-download></i>&nbsp;${message.file}</a>` +
                                '<div class="messenger__seen'+ seen_class +'">' +
                                '<img class="messenger__seen-img"' +
                                'src="'+ message_avatar +'" alt=""></div></div></div></div>'
                            );
                        } else {
                            des.prepend('<div class="messenger__item messenger__item--left">' +
                                ' <div class="messenger__avatar"><img class="messenger__avatar-img" src="'+ message_avatar +'" alt=""></div>' +
                                '<div class="messenger__info">' +
                                '<div class="messenger__mess-item">' + message.content + 
                                '</div>' + '<br/>' +
                                '<a class="messenger__download--left '+has_file_class+'" href='+file_url+ '"><i class="fas fa-download"></i>&nbsp;'+message.file+'</a>' +
                                '<div class="messenger__seen"><img class="messenger__seen-img" src="{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}" alt=""></div>' +
                                '</div>' +
                                '</div>' +
                                '</div>'
                            );
                        }

                        let scrollTo = $('#mCSB_2_container').children().first().offset().top - current_top.offset().top;
                        $('.messenger-detail-content').mCustomScrollbar('scrollTo', '+=' + scrollTo, {
                            scrollInertia: 0,
                            scrollEasing: 'linear',
                            timeout: 0
                        })
                    }

                }
            }
        );
    };

    userScrollTop = function () {
        let leftover = $('.hidden-message');
        if(!leftover.length && !loading) {
            loadPreviousMessages();
        }
    };

    $('.messenger-chatroom').each(function(i, e) {
        initChatroom($(e).attr('data-chatroom'));
    })

});

$('.messenger-detail__input-text').keyup(function () {
    let comment_content = $(this).val();
    let btn_send = $(this).parents('.messenger-detail__input').siblings('.messenger-detail__button-send');
    let btn_link = btn_send.find('a');
        btn_link.css('color', "#258BCF");
        btn_link.removeClass('button--disabled');
        btn_send.addClass('button--actived');
        btn_send.css('box-shadow', '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)');
    if (!comment_content) {
        btn_link.css('color', "#b2b2b2");
        btn_link.addClass('button--disabled');
        btn_send.removeClass('button--actived');
        btn_send.css('box-shadow', 'none');
        return;
    }
});

$('.messenger-detail').on('click', function() {
    let unseen = $('.col-md-7 .messenger__item--left .messenger__seen.hide');
    if(unseen.length) {
        $.ajax({
            type: "POST",
            datatype: "json",
            url: "{% url 'app:update_seen_offer_message' %}",
            data: {
                'csrf_token': crsf,
                'chatroom_id': chatroom_id,
            },
            success: function () {
                console.log('update seen successful');
            }
        })
    }
});

initChatroom = function (chatroom) {
    let socket_url = 'ws://' + '{{ server_ip }}';
    if (window.location.host !== 'localhost:8000') {
        socket_url = 'wss://' + window.location.host;
    }
    let chatSocket = new WebSocket(
        socket_url + '/ws/messenger/' + chatroom);
    chatSocket.onmessage = function(e) {
        let data = JSON.parse(e.data);
        let room = e.target.url.replace(socket_url + '/ws/messenger/', '');
        let room_target = $('.room-' + room);
        let file_url = data.event.file_url;
        let has_file_class = 'hide'
        
        switch(data.event.action) {
            case 'new_message':
                let message = JSON.parse(data.event.message)[0].fields;
                if (message.file) {
                    has_file_class = '';
                }
                if(room === chatroom_id) {
                    let message_avatar;
                    if ('{{ user.role }}' === 'creator') {
                        if (data.event.admin_avatar) {
                             message_avatar = data.event.admin_avatar;
                        } else {
                            message_avatar = '{% static 'images/avatar-user.jpg' %}';
                        }
                    } else {
                        if (data.event.creator_avatar) {
                             message_avatar = data.event.creator_avatar;
                        } else {
                            message_avatar = '{% static 'images/avatar-user.jpg' %}';
                        }
                    }

                    if (message.owner === {{ user.pk }}) {
                        $('#mCSB_2_container').append('<div class="messenger__item messenger__item--right">' +
                            '<div class="messenger__avatar">' +
                            '<img class="messenger__avatar-img"' +
                            'src="{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}" alt=""></div>' +
                            '<div class="messenger__info">' +
                            '<div class="messenger__mess">' +
                            '<div class="messenger__mess-item">' + message.content + 
                            '</div>' + '<br/>' +
                            '<a class='+has_file_class+' href='+file_url+ '"><i class="fas fa-download"></i>&nbsp;'+message.file+'</a>' +
                            '<div class="messenger__seen hide">' +
                            '<img class="messenger__seen-img"' +
                            'src="'+ message_avatar +'" alt=""></div></div></div></div>'
                        );
                    } else {
                        $('#mCSB_2_container').append('<div class="messenger__item messenger__item--left">' +
                            ' <div class="messenger__avatar"><img class="messenger__avatar-img" src="'+ message_avatar +'" alt=""></div>' +
                            '<div class="messenger__info">' +
                            '<div class="messenger__mess-item">' + message.content + 
                            '</div>' + '<br/>' +
                            '<a class='+has_file_class+' href='+file_url+ '"><i class="fas fa-download"></i>&nbsp;'+message.file+'</a>' +
                            '<div class="messenger__seen hide"><img class="messenger__seen-img" src="{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}" alt=""></div>' +
                            '</div>' +
                            '</div>' +
                            '</div>'
                        );
                    }
                    $('.messenger-detail-content').mCustomScrollbar('scrollTo', 'last', {
                        scrollInertia: 300
                    })
                } else {
                    if(room_target.length) {
                        room_target.addClass('messenger__item--new');
                        room_target.find('.messenger__tag').removeClass('hide');
                        room_target.find('.messenger__time').addClass('hide')
                    }
                }
                room_target.find('.messenger__mess')[0].innerText = message.content;
                room_target.find('.messenger__time')[0].innerText = 'now'
                break;
            case 'seen':
                if(room === chatroom_id) {
                    $('.col-md-7 .messenger__seen.hide').removeClass('hide')
                }
                break;
            default:
                break;
        }
    };

    chatSocket.onclose = function(e) {
        console.error('Chat socket closed unexpectedly');
    };

    function sendMessage(message) {
        chatSocket.send(JSON.stringify({
            'message': message
        }));
    }
}
</script>
{% endcompress %}