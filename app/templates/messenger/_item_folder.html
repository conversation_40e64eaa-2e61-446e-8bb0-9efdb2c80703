{% load static %}
{% load util %}

<div class="minfo-file">
  <div class="sfolder" data-message-id="{{ folder.message.pk }}" data-folder-id="{{ folder.pk }}">
    <ul class="mfolder">
      <div class="parent-folder">
          <i class="icon icon--sicon-storage"></i>
          <span class="hasSub" data-folder-id="{{folder.pk}}">{{ folder.name }}</span>
          {% if folder.pk|check_child_folder:type_message %}
            <i class="icon icon--sicon-download icon--sicon-folder-download pointer"></i>
          {% endif %}
      </div>
      <ul>
        {% for child in folder.child_folders.all %}
          <li class="mfolder__sub"><i class="icon icon--sicon-storage"></i>
            <span class="hasSub" data-folder-id="{{child.pk}}">{{ child.name }}</span>
            {% if child.pk|check_child_folder:type_message %}
              <i class="icon icon--sicon-download icon--sicon-folder-download pointer"></i>
            {% endif %}
          </li>
        {% endfor %}
        {% for file in folder.children.all %}
          <li class="mfolder__sub" data-file-id="{{ file.pk }}"><i class="icon icon--sicon-clip"></i>
            <span>{{ file.real_name }}</span>
            <i class="icon icon--sicon-download pointer"></i>
            <div class="sview-user">
            </div>
          </li>
        {% endfor %}
      </ul>
    </ul>
  </div>
</div>



