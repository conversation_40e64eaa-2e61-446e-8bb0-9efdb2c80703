{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load compress %}
{% block extrahead %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/messenger.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
    <main class="messenger">
        <div class="messenger__content">
            <div class="container">
                <div class="messenger__main">
                    <div class="row messenger__column">
                        <div class="col-md-5 col-sm-5 messenger__column-left">
                            <div class="messenger__header"><a class="header-button header-button--back"
                                                              href="#">Project</a></div>
                            <div class="messenger__list custom-scrollbar">
                                {% if user.role == 'creator' %}
                                    {% for chatroom in chatroom_list %}
                                        {% with room_status=chatroom.last_admin_message last_message=chatroom.last_message %}
                                            <div class="messenger__item {% if room_status %}messenger__item--new {% endif %}
                                                        messenger-chatroom messenger__item--not-selected room-{{ chatroom.pk }}"
                                                 onclick="goToChatRoom('{{ chatroom.pk }}', this);"
                                                 data-chatroom="{{ chatroom.pk }}">
                                                <div class="messenger__avatar">
                                                    <img class="messenger__avatar-img"
                                                         src="{% if chatroom.admin.avatar %}{{ chatroom.admin|get_vatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                         alt=""></div>

                                                <div class="messenger__info">
                                                    <div class="messenger__name">{{ chatroom.admin.get_full_name }}</div>
                                                    <div class="messenger__work">{{ chatroom.admin.role.capitalize }}</div>
                                                    <div class="messenger__mess">{{ last_message.0.content }}
                                                        <div class="messenger__seen {% if room_status or not chatroom.last_message.0.seen_date %} hide{% endif %}">
                                                            <img class="messenger__seen-img"
                                                                 src="{% if message.owner == user %}{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}{% else %}{% if chatroom.admin.avatar %} {{ chatroom.admin|get_avatar:'medium' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}{% endif %}"
                                                                 alt=""></div>
                                                    </div>
                                                </div>
                                                <div class="messenger__status">
                                                    {% if room_status %}
                                                        <div class="messenger__tag">NEW</div>
                                                        <div class="messenger__time hide">{{ last_message.0.created_day_in_week }}</div>
                                                    {% else %}
                                                        <div class="messenger__tag hide">NEW</div>
                                                        <div class="messenger__time">{{ last_message.0.created_day_in_week }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endwith %}
                                    {% endfor %}
                                {% else %}
                                    {% for chatroom in chatroom_list %}
                                        {% with room_status=chatroom.last_creator_message last_message=chatroom.last_message %}
                                            <div class="messenger__item {% if room_status %}messenger__item--new {% endif %}
                                                        messenger-chatroom messenger__item--not-selected room-{{ chatroom.pk }}"
                                                 onclick="goToChatRoom('{{ chatroom.pk }}', this);"
                                                 data-chatroom="{{ chatroom.pk }}">
                                                <div class="messenger__avatar">
                                                    <img class="messenger__avatar-img"
                                                         src="{% if chatroom.creator.avatar %}{{ chatroom.creator|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                         alt=""></div>

                                                <div class="messenger__info">
                                                    <div class="messenger__name">{{ chatroom.creator.user_creator.all.first.stage_name }}</div>
                                                    <div class="messenger__work">{% if chatroom.creator.user_creator.all.first.type %}
                                                                                 {{chatroom.creator.user_creator.all.first.type}}
                                                                                 {% endif %}
                                                    </div>
                                                    <div class="messenger__mess">{{ last_message.0.content }}
                                                        <div class="messenger__seen {% if room_status or not chatroom.last_message.0.seen_date %} hide{% endif %}">
                                                            <img class="messenger__seen-img"
                                                                 src="{% if message.owner == user %}{% if user.avatar %}{{ user|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}{% else %}{% if chatroom.creator.avatar %} {{ chatroom.creator|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}{% endif %}"
                                                                 alt=""></div>
                                                    </div>
                                                </div>
                                                <div class="messenger__status">
                                                    {% if room_status %}
                                                        <div class="messenger__tag">NEW</div>
                                                        <div class="messenger__time hide">{{ last_message.0.created_day_in_week }}</div>
                                                    {% else %}
                                                        <div class="messenger__tag hide">NEW</div>
                                                        <div class="messenger__time">{{ last_message.0.created_day_in_week }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endwith %}
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-7 col-sm-7 messenger__column-right">
                            <div class="messenger__header">
                                <div class="messenger__header-button visible-mobile">
                                    <a class="header-button header-button--back" href="javascript:void(0)">Back</a>
                                </div>
                                <div class="messenger__header-icon">
                                    <a class="header-icon header-icon--phone" href="#">
                                        <img class="header-icon__img" src="{% static 'images/icon-phone.svg' %}"
                                             alt="">
                                    </a>
                                    <a class="header-icon header-icon--camera" href="#">
                                        <img class="header-icon__img" src="{% static 'images/icon-camera.svg' %}"
                                             alt="">
                                    </a>
                                </div>
                            </div>
                            <div class="messenger-detail">
                                <div class="messenger-detail-content custom-scrollbar" id='right-column'>
                                    {% for message in message_list reversed %}
                                        {% if message.offer_id %}
                                            <div class="messenger-detail__offer">
                                                <div class="messenger-detail__thumb">
                                                    {% with video_info=message.offer.scene_list.first.movie|get_video_url_with_fallback %}
                                                    <video class="messenger-detail__thumb-img"
                                                           src="{{ video_info.url }}"
                                                           controls
                                                                                                      data-video-src="{{ video_info.url }}"
                                           data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                                           data-fallback-src="{% if message.offer.scene_list.first.movie %}{{ message.offer.scene_list.first.movie.url }}{% endif %}">
                                                    </video>
                                                    {% endwith %}
                                                </div>
                                                <div class="messenger-detail__conditions">
                                                    {% if message.offer.status == '1' %}
                                                        <div class="messenger-detail__status messenger-detail__status_new">新規</div>
                                                    {% elif message.offer.status == '2' %}
                                                        <div class="messenger-detail__status messenger-detail__status_process">進行中</div>
                                                    {% elif message.offer.status == '3' %}
                                                        <div class="messenger-detail__status messenger-detail__status_done">完了</div>
                                                    {% else %}
                                                        <div class="messenger-detail__status messenger-detail__status_reject">拒否</div>
                                                    {% endif %}
                                                    <div class="messenger-detail__conditions-title">発注条件　▼</div>
                                                    <div class="messenger-detail__conditions-item">
                                                        <div class="messenger-detail__conditions-label">内容：</div>
                                                        <div class="messenger-detail__conditions-value">楽曲制作</div>
                                                    </div>
                                                    <div class="messenger-detail__conditions-item">
                                                        <div class="messenger-detail__conditions-label">報酬：</div>
                                                        <div class="messenger-detail__conditions-value">64,800円</div>
                                                    </div>
                                                    <div class="messenger-detail__conditions-item">
                                                        <div class="messenger-detail__conditions-label">納期：</div>
                                                        <div class="messenger-detail__conditions-value">2019年10月15日（水）
                                                            10:00
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="messenger-detail__boxes">
                                                    <div class="box-item">
                                                        <div class="box-item__icon"><img class="box-item__img"
                                                                                         src="images/icon-copyright.svg"
                                                                                         alt=""></div>
                                                        <div class="box-item__title">著作権</div>
                                                        <div class="box-item__desc">譲渡</div>
                                                    </div>
                                                    <div class="box-item">
                                                        <div class="box-item__icon"><img class="box-item__img"
                                                                                         src="images/icon-person.svg"
                                                                                         alt=""></div>
                                                        <div class="box-item__title">氏名表示権</div>
                                                        <div class="box-item__desc">任意</div>
                                                    </div>
                                                    <div class="box-item">
                                                        <div class="box-item__icon"><img class="box-item__img"
                                                                                         src="images/icon-earth.svg" alt="">
                                                        </div>
                                                        <div class="box-item__title">実績告知</div>
                                                        <div class="box-item__desc">可</div>
                                                    </div>
                                                </div>
                                                {% if user.role == 'creator' %}
                                                    {% if message.offer.status == 1 %}
                                                        <div class="messenger-detail__button">
                                                            <div class="messenger-detail__button-reject"><span>Reject</span></div>
                                                            <div class="messenger-detail__button-accept"><span>Accept</span></div>
                                                        </div>
                                                    {% endif %}
                                                {% else %}
                                                    {% if message.offer.status == 2 %}
                                                        <div class="messenger-detail__button">
                                                            <div class="messenger-detail__button-done"><span>Approve</span></div>
                                                        </div>
                                                    {% endif %}
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            {% if user.role == 'creator' %}
                                                <div class="messenger__item {% if message.owner == user %}messenger__item--right{% else %}messenger__item--left{% endif %}">
                                                    <div class="messenger__avatar">
                                                        <img class="messenger__avatar-img"
                                                             src="{% if message.owner.avatar %}{{ message.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}"
                                                             alt="">
                                                    </div>
                                                    <div class="messenger__info">
                                                        <div class="messenger__mess">
                                                            {% if message.owner == user %}
                                                                <div class="messenger__mess-item">{{ message.content }}</div>
                                                            {% else %}
                                                                {{ message.content }}
                                                            {% endif %}
                                                            {% if message.file %}
                                                                <br/>
                                                                <a href="{{ message.file.url }}" target="_blank"
                                                                   class="{% if message.owner == user %}messenger__download--right{% else %}messenger__download--left{% endif %}">
                                                                    <i class="fas fa-download">
                                                                    </i>
                                                                    &nbsp;{{ message.get_file_name }}
                                                                </a>
                                                            {% endif %}
                                                            <div class="messenger__seen {% if not message.seen_date %}hide{% endif %}">
                                                                <img class="messenger__seen-img"
                                                                     src="{% if message.chatroom.admin.avatar %}{{ message.chatroom.admin|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                     alt="">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <div class="messenger__item {% if message.owner == user %}messenger__item--right{% else %}messenger__item--left{% endif %}">
                                                    <div class="messenger__avatar">
                                                        <img class="messenger__avatar-img"
                                                             src="{% if message.owner.avatar %}{{ message.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %}{% endif %}"
                                                             alt="">
                                                    </div>
                                                    <div class="messenger__info">
                                                        <div class="messenger__mess">
                                                            {% if message.owner == user %}
                                                                <div class="messenger__mess-item">{{ message.content }}</div>
                                                            {% else %}
                                                                {{ message.content }}
                                                            {% endif %}
                                                            {% if message.file %}
                                                                <br/>
                                                                <a href="{{ message.file.url }}" target=="_blank"
                                                                   class="{% if message.owner == user %}messenger__download--right{% else %}messenger__download--left{% endif %}">
                                                                    <i class="fas fa-download"></i>
                                                                    &nbsp;{{ message.get_file_name }}
                                                                </a>
                                                            {% endif %}
                                                            <div class="messenger__seen {% if not message.seen_date %}hide{% endif %}">
                                                                <img class="messenger__seen-img"
                                                                     src="{% if message.chatroom.creator.avatar %}{{ message.chatroom.creator|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                     alt="">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    {% endfor %}

                                    {% if user.role in 'master_admin' %}
                                        <div class="add-offer hide"><label class="button-add-offer" for="messenger-scene-upload">+</label>
                                            <input id="messenger-scene-upload" type="file" accept="video/mp4">
                                        </div>
                                    {% endif %}
                                </div>

                                <form action="" enctype="multipart/form-data">
                                    <div class="messenger-detail__action messenger-detail__action-input">
                                        <div class="messenger-detail__input">
                                            <input type="hidden" value="{{csrf_token}}">
                                            <input class="messenger-detail__input-attach" type="file"
                                                   name="file" placeholder="" id="messenger-attach">
                                            <label for="messenger-attach"><img
                                                    src="{% static 'images/icon-file-attach.svg' %}" alt=""></label>
                                            <textarea class="messenger-detail__input-text cs-textarea"
                                                      name="message_content" placeholder="メッセージを入力…"></textarea>
                                        </div>
                                        <div class="messenger-detail__button-send"><a
                                                class="button button--text button--text-primary button--disabled" href="#"
                                                role="button">送信</a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>
    {% compress js inline %}
    <script>
        let chatroom_id = '{{ chatroom_id }}';
        let scroll_page = 1;
        let loading = false;
        let max = '{{ max_page }}';
        let creator = '{{ user.role }}' === 'creator';
        let csrf = '{{ csrf_token }}';
        $(document).ready(function() {
            $("#messenger-attach").change(function(e) {
                // previewImage(this);
                let fileName = e.target.files[0].name;
                let clear_file_dom = "clear_" + fileName;
                let message_box = $(this).closest('.messenger-detail__input');
                if (message_box.find('.messenger__textarea-file').length > 0) {
                    message_box.find('.messenger__textarea-file span').text(fileName);
                } else {
                    message_box.prepend('<div class="messenger__textarea-file">' +
                        '<div class="messenger__file_upload_temp"><span>' + fileName + '</span>' +
                        '<i class="fa fa-times-circle '+ clear_file_dom +'" id="clear_file"  aria-hidden="true"></i>' +
                        '</div></div>')
                }
            });

            $(document).on('click', '#clear_file', function () {
                $('.messenger-detail__input').find('.messenger__textarea-file').remove();
                $('#messenger-attach').val('');
            });
        });
        

        sendOfferMessage = function() {
            let messageContent = $('textarea.messenger-detail__input-text').val();
            if (messageContent !== '') {

                let form = $('.messenger-detail__input').parents('form');
                data = new FormData(form[0]);

                data.append('chatroom_id', chatroom_id);
                $.ajax({
                    type: "POST",
                    async: false,
                    contentType: false,
                    processData: false,
                    cache: false,
                    data: data,
                    url: '/offer_message/create',
                    success: function (data) {
                        console.log('success')
                        $('textarea.messenger-detail__input-text').val('');
                        $('.messenger-detail__input').find('.messenger__textarea-file').remove();
                        $('#messenger-attach').val('');
                        let btn_send = $('.messenger-detail__button-send');
                        let btn_link = btn_send.find('a');
                        btn_link.css('color', "#b2b2b2");
                        btn_link.addClass('button--disabled');
                        btn_send.removeClass('button--actived');
                        btn_send.css('box-shadow', 'none');
                    }
                });
            }
        }
    </script>
    {% endcompress %}
    {% if request.user_agent.is_pc is True %}
        {% include 'messenger/index_pc.html'%}
    {% else %}
        {% include 'messenger/index_mobile.html' %}
    {% endif %}
{% endblock %}
