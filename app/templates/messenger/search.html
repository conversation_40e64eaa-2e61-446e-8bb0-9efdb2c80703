{% extends 'base_nofooter.html' %}
{% load static %}
{% load util %}
{% load user_agents %}
{% load compress %}

{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
    {% compress css %}
    <style>
        .creator-item-component,
        .creator-item-component .text-center {
            margin: 10px;
            overflow: hidden;
        }
        .creator-item-component img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .creator-item-component img:hover{
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .messenger-director {
            margin-top: 75px;
        }

        .project-item__milestone {
            color: rgba(10, 7, 5, 0.93);
            margin-top: 20px;
            position: absolute;
            background: white;
            border: 1px solid gray;
            padding: 0 5px;
        }

        .project-item__info,
        .project-item__member-item img {
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .project-item__member-item img {
            border-radius: 50%;
        }

        .modal:before {
            height: 75px;
        }

        .project-item__title:before {
            padding-right: 5px;
        }

        .project-item__title {
            text-transform: uppercase;
        }

        .messenger-director__info {
            background: white;
            padding: 20px 10px;
            margin-bottom: 50px;
        }

        .upload_link {
            color: white !important;
        }

        .messenger-director__item-form {
            padding:0 40px;
        }

        .messenger-director__item-info {
            padding: 0;
            margin: auto;
        }
        .search-label {
            font-size: 1.5em;
            background: #e2e2e2;
            padding: 10px;
        }
        .messenger__avatar-img {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .messenger__creator-item.selected-creator,
        .messenger__audio-item.selected-creator {
            border: 2px solid #009ace;
        }

        .messenger__creator-item,
        .messenger__audio-item {
            transition: .1s;
            border: 2px solid transparent;
            margin-top: 5px;
        }

        .reward, #tax_amount {
            padding-left: 40px;
            text-align: right;
        }
        .currency {
            position: relative;
        }

        .currency span {
            position: absolute;
            top: 5px;
            right: 10px;
        }

        .form-group.textarea-large label {
            color: #009ace !important;
        }

        .tax-amount {
            display: flex;
            flex-wrap: nowrap;
            padding: 5px 10px;
            background: #f8f8f8;
        }

        .tax-amount-amount {
            flex-grow: 1;
            text-align: right;
        }

        .project-item__filter-item {
            margin: 10px 0;
            width: auto;
            min-width: 50px;
            height: 50px;
            padding: 0;
        }

        .project-item__filter-item.active svg path {
            fill: #009ace;
        }

        .messenger-popup__content {
          background-color: white;
          width: 35vw;
          margin: auto;
          padding: 28px;
          box-shadow: -2px -2px 6px 0 rgba(0, 0, 0, .1);
          border-radius: 20px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          min-width: 400px;
        }

        .messenger-popup__content input, .messenger-popup__content textarea, .messenger-popup__content_plan input, .messenger-popup__content_plan textarea {
          /* stylelint-disable-line */
          border: 1px solid #d6d6d6;
          margin-bottom: 15px;
        }

        .messenger-popup__content input::placeholder, .messenger-popup__content textarea::placeholder, .messenger-popup__content_plan input::placeholder, .messenger-popup__content_plan textarea::placeholder {
          color: #d0d1d4;
        }

        .messenger-popup__form {
          margin-bottom: 10px;
        }

    </style>
    {% endcompress %}
    <main class="messenger-director">
        <div class="container">
            <div class="new-video-menu">
                <div class="project-list">
                    <div class="project-item active{% if not request|is_pc %} on-mobile{% endif %}">
                        <a href="{% url 'app:messenger_project' %}{% if is_done_project %}?is_done=1{% endif %}">
                            {% include 'messenger/_project_banner.html' with project=project user=user is_pc=request|is_pc show_staff=False %}
                        </a>
                        <div class="project-item__content">
                            <div class="project-item__top">
                                <div class="project-item__filter">
                                    <div class="project-item__filter-item active" data-show="new">
                                      <svg width="42" height="36" viewBox="0 0 42 36" fill="none"
                                           xmlns="http://www.w3.org/2000/svg">
                                        <path
                                          d="M15.048 29.016H15.448C15.5707 28.7067 15.672 28.4027 15.752 28.104H14.824C14.9307 28.5093 15.0053 28.8133 15.048 29.016ZM17.12 29.992H15.784V30.344H17.016V31.352H15.808C16.1173 31.6133 16.4453 31.9067 16.792 32.232C17.048 31.7147 17.224 31.088 17.32 30.352C17.416 29.6107 17.464 28.4853 17.464 26.976C18.6427 26.88 19.6187 26.6933 20.392 26.416L20.704 27.52C20.0747 27.7227 19.3733 27.8747 18.6 27.976C18.5893 28.376 18.5787 28.6747 18.568 28.872H20.744V29.976H20.184V33.752H19V29.976H18.496C18.416 30.8827 18.2773 31.6213 18.08 32.192C17.888 32.7627 17.6133 33.2907 17.256 33.776L16.512 33L16.336 33.208C16.1867 33.0267 16.0027 32.8213 15.784 32.592V33.824H14.576V32.488C14.3253 32.8933 14.056 33.2453 13.768 33.544L13.16 32.592C13.5973 32.224 13.9573 31.8107 14.24 31.352H13.376V30.344H14.576V29.992H13.376V29.016H14.008C13.9333 28.712 13.8533 28.408 13.768 28.104H13.416V27.096H14.576V26.344H15.784V27.096H17.152V28.104H16.816C16.752 28.36 16.6533 28.664 16.52 29.016H17.12V29.992ZM25.448 30.136V30.544H27.176V30.136H25.448ZM25.448 28.88V29.296H27.176V28.88H25.448ZM25.448 28.04H27.176V27.624H25.448V28.04ZM21.304 30.456V29.448H22.096C22.1013 29.3467 22.104 29.1867 22.104 28.968V28.688H21.384V27.616H22.104V26.464H23.304V27.616H24.008V28.688H23.304V28.904C23.304 29.1493 23.3013 29.3307 23.296 29.448H24.024V30.456H23.176C23.5707 30.9467 23.8987 31.376 24.16 31.744L23.44 32.536C23.12 32.024 22.936 31.7307 22.888 31.656C22.6747 32.5147 22.3253 33.2053 21.84 33.728L21.16 32.648C21.6293 32.0507 21.9173 31.32 22.024 30.456H21.304ZM27.552 33.672H27.312H27.08C26.6587 33.6667 26.3973 33.6267 26.296 33.552C26.1947 33.4773 26.144 33.2933 26.144 33V31.52H25.936C25.7547 32.7253 24.944 33.4827 23.504 33.792L23.136 32.8C23.648 32.6987 24.04 32.544 24.312 32.336C24.584 32.128 24.7573 31.856 24.832 31.52H24.264V26.616H28.416V31.52H27.256V32.408C27.256 32.5307 27.2587 32.6027 27.264 32.624C27.2747 32.64 27.304 32.6507 27.352 32.656C27.3627 32.656 27.3813 32.6587 27.408 32.664C27.4347 32.664 27.4507 32.664 27.456 32.664C27.4667 32.664 27.4827 32.664 27.504 32.664C27.5307 32.6587 27.5493 32.656 27.56 32.656C27.64 32.6507 27.688 32.6107 27.704 32.536C27.72 32.456 27.7307 32.1973 27.736 31.76L28.8 31.896C28.7893 32.1467 28.7813 32.3387 28.776 32.472C28.7707 32.6053 28.7547 32.7467 28.728 32.896C28.7067 33.0453 28.688 33.152 28.672 33.216C28.6613 33.28 28.6267 33.3493 28.568 33.424C28.5147 33.4987 28.4667 33.544 28.424 33.56C28.3867 33.5813 28.3147 33.6027 28.208 33.624C28.1067 33.6507 28.0133 33.664 27.928 33.664C27.848 33.6693 27.7227 33.672 27.552 33.672Z"
                                          fill="#53565A"/>
                                        <path
                                          d="M21 23C26.5228 23 31 18.5228 31 13C31 7.47715 26.5228 3 21 3C15.4772 3 11 7.47715 11 13C11 18.5228 15.4772 23 21 23Z"
                                          fill="#53565A"/>
                                        <path d="M17 13.5H25" stroke="#FBFBFB" stroke-width="2.5"
                                              stroke-linecap="round"/>
                                        <path d="M21 9.5V17.5" stroke="#FBFBFB" stroke-width="2.5"
                                              stroke-linecap="round"/>
                                      </svg>
                                    </div>
                                    <a href="{% url 'app:messenger_waiting' %}?project_id={{ project.pk }}">
                                        <div class="project-item__filter-item deepgray" data-show="waiting">
                                          <svg width="64" height="36" viewBox="0 0 64 36" fill="none"
                                           xmlns="http://www.w3.org/2000/svg">
                                            <path
                                              d="M15.472 26.896L16.696 26.56C16.7173 26.6293 16.768 26.8 16.848 27.072C16.928 27.344 16.9893 27.5573 17.032 27.712C17.2773 27.664 17.5253 27.64 17.776 27.64C18.3467 27.64 18.808 27.816 19.16 28.168C19.5173 28.5147 19.696 28.9787 19.696 29.56C19.696 30.2107 19.5013 30.7227 19.112 31.096C18.728 31.464 18.2107 31.648 17.56 31.648C17.2133 31.648 16.872 31.616 16.536 31.552L16.64 30.408C16.896 30.4507 17.1573 30.472 17.424 30.472C17.712 30.472 17.9467 30.3947 18.128 30.24C18.3093 30.0853 18.4 29.88 18.4 29.624C18.4 29.0747 18.1253 28.8 17.576 28.8C17.5067 28.8 17.4293 28.808 17.344 28.824C17.4293 29.144 17.5093 29.448 17.584 29.736L16.424 30.08C16.3387 29.7547 16.2507 29.4453 16.16 29.152C16.1067 29.168 16.024 29.1973 15.912 29.24C15.8 29.2773 15.6907 29.3147 15.584 29.352C15.4827 29.3893 15.3813 29.4267 15.28 29.464C15.6427 30.6693 16 31.9573 16.352 33.328L15.096 33.64C14.8027 32.472 14.464 31.232 14.08 29.92C13.92 29.9787 13.672 30.0773 13.336 30.216C13 30.3493 12.776 30.4373 12.664 30.48L12.224 29.384C12.4427 29.2987 12.736 29.184 13.104 29.04C13.472 28.896 13.6827 28.8133 13.736 28.792C13.4427 27.8533 13.2827 27.344 13.256 27.264L14.48 26.888C14.5867 27.208 14.7413 27.6933 14.944 28.344C15.2427 28.2373 15.536 28.136 15.824 28.04C15.664 27.5173 15.5467 27.136 15.472 26.896ZM21.328 26.864H22.584V28.208H22.6C23.1707 27.4187 23.8827 27.024 24.736 27.024C25.4187 27.024 25.96 27.2587 26.36 27.728C26.76 28.1973 26.96 28.856 26.96 29.704C26.96 31.0213 26.5813 31.984 25.824 32.592C25.0667 33.2 23.8587 33.504 22.2 33.504L22.136 32.344C23.016 32.344 23.704 32.256 24.2 32.08C24.7013 31.904 25.0587 31.6293 25.272 31.256C25.4907 30.8773 25.6 30.36 25.6 29.704C25.6 28.7387 25.2667 28.256 24.6 28.256C24.072 28.256 23.6027 28.5147 23.192 29.032C22.7813 29.5493 22.568 30.184 22.552 30.936H21.328V26.864ZM29.776 26.896L31.096 26.688C31.224 27.4507 31.3733 28.184 31.544 28.888C32.376 28.5413 33.3813 28.2373 34.56 27.976L34.8 29.136C33.36 29.456 32.272 29.816 31.536 30.216C30.8053 30.6107 30.44 30.992 30.44 31.36C30.44 31.6693 30.568 31.9013 30.824 32.056C31.0853 32.2053 31.496 32.28 32.056 32.28C32.9093 32.28 33.792 32.2053 34.704 32.056L34.84 33.216C33.9493 33.3653 33.0213 33.44 32.056 33.44C30.072 33.44 29.08 32.768 29.08 31.424C29.08 30.72 29.4907 30.0907 30.312 29.536C30.0933 28.6187 29.9147 27.7387 29.776 26.896ZM37.328 26.864H38.584V28.208H38.6C39.1707 27.4187 39.8827 27.024 40.736 27.024C41.4187 27.024 41.96 27.2587 42.36 27.728C42.76 28.1973 42.96 28.856 42.96 29.704C42.96 31.0213 42.5813 31.984 41.824 32.592C41.0667 33.2 39.8587 33.504 38.2 33.504L38.136 32.344C39.016 32.344 39.704 32.256 40.2 32.08C40.7013 31.904 41.0587 31.6293 41.272 31.256C41.4907 30.8773 41.6 30.36 41.6 29.704C41.6 28.7387 41.2667 28.256 40.6 28.256C40.072 28.256 39.6027 28.5147 39.192 29.032C38.7813 29.5493 38.568 30.184 38.552 30.936H37.328V26.864ZM45.744 30.504H47.344V28.728H45.744V30.504ZM45.744 31.544V31.968H44.544V27.656H47.344V26.384H48.656V27.656H51.456V31.968H50.256V31.544H48.656V33.856H47.344V31.544H45.744ZM50.256 30.504V28.728H48.656V30.504H50.256Z"
                                              fill="#53565A"/>
                                            <path
                                              d="M23.9029 13.5336L28.1233 14.7216C28.2301 14.7499 28.3262 14.8088 28.3999 14.8911C28.4736 14.9733 28.5216 15.0754 28.5381 15.1846C28.5545 15.2938 28.5386 15.4054 28.4923 15.5057C28.4461 15.606 28.3715 15.6906 28.2777 15.749L26.5769 16.731C27.4429 18.2721 28.8849 19.4066 30.5866 19.8854C32.2882 20.3643 34.1103 20.1484 35.6529 19.2852C36.389 18.8633 37.0314 18.2959 37.5411 17.6177C37.6708 17.4541 37.847 17.3335 38.0464 17.2717C38.2458 17.2099 38.4593 17.2099 38.6588 17.2715C38.8371 17.3221 39.0004 17.4155 39.1345 17.5434C39.2686 17.6713 39.3696 17.83 39.4286 18.0057C39.4877 18.1814 39.503 18.3689 39.4733 18.5518C39.4436 18.7348 39.3698 18.9078 39.2582 19.0558C38.5836 19.9387 37.7406 20.679 36.7779 21.2338C35.7598 21.8049 34.6391 22.1698 33.4798 22.3077C32.3206 22.4457 31.1456 22.3539 30.0219 22.0376C28.8981 21.7213 27.8477 21.1868 26.9306 20.4645C26.0135 19.7422 25.2476 18.8463 24.6768 17.828L22.9759 18.81C22.8798 18.8634 22.77 18.8872 22.6604 18.8785C22.5508 18.8698 22.4461 18.8289 22.3596 18.7611C22.2731 18.6932 22.2085 18.6013 22.174 18.4969C22.1394 18.3925 22.1364 18.2802 22.1654 18.1741L23.2443 13.9208C23.261 13.8515 23.2915 13.7863 23.3339 13.729C23.3764 13.6717 23.4299 13.6236 23.4914 13.5874C23.5529 13.5513 23.621 13.5279 23.6917 13.5187C23.7624 13.5094 23.8342 13.5145 23.9029 13.5336ZM37.979 10.148C37.113 8.60697 35.6709 7.47248 33.9692 6.99362C32.2676 6.51475 30.4455 6.73062 28.9029 7.59386C28.1669 8.0158 27.5244 8.58316 27.0147 9.26137C26.8843 9.4247 26.7075 9.54476 26.5076 9.60577C26.3077 9.66679 26.094 9.66591 25.8946 9.60324C25.7162 9.5526 25.553 9.45928 25.4188 9.33135C25.2847 9.20342 25.1837 9.04474 25.1247 8.86903C25.0657 8.69332 25.0503 8.50587 25.08 8.32291C25.1097 8.13994 25.1835 7.96696 25.2951 7.81893C25.9708 6.93735 26.8147 6.19856 27.7779 5.6453C28.7961 5.0742 29.9168 4.70926 31.076 4.57134C32.2352 4.43341 33.4102 4.52521 34.5339 4.84147C35.6577 5.15773 36.7081 5.69227 37.6252 6.41456C38.5423 7.13684 39.3082 8.03272 39.879 9.05103L41.5799 8.06903C41.676 8.01567 41.7858 7.99185 41.8954 8.00056C42.0051 8.00927 42.1097 8.05012 42.1962 8.118C42.2827 8.18588 42.3473 8.27777 42.3819 8.38218C42.4164 8.48658 42.4194 8.59886 42.3904 8.70495L41.309 12.9539C41.2924 13.025 41.2614 13.0919 41.2179 13.1505C41.1744 13.2092 41.1193 13.2583 41.0561 13.2948C40.9928 13.3313 40.9228 13.3544 40.8503 13.3628C40.7778 13.3711 40.7043 13.3646 40.6344 13.3434L36.414 12.1555C36.3084 12.1253 36.2139 12.0649 36.1421 11.9817C36.0703 11.8985 36.0244 11.7962 36.0099 11.6873C35.9955 11.5784 36.0132 11.4676 36.0608 11.3686C36.1085 11.2696 36.184 11.1867 36.2781 11.13L37.979 10.148Z"
                                              fill="#53565A"/>
                                          </svg>
                                        </div>
                                    </a>
                                    <a href="{% url 'app:messenger_processing' %}?project_id={{ project.pk }}">
                                        <div class="project-item__filter-item deepgray" data-show="progress">
                                          <svg width="87" height="36" viewBox="0 0 87 36" fill="none"
                                           xmlns="http://www.w3.org/2000/svg">
                                            <path
                                              d="M11.504 27.256H15.16V26.456H16.384V27.256H18.496V28.28H16.384V29.792C16.5493 30.112 16.632 30.48 16.632 30.896C16.632 31.7227 16.3893 32.3413 15.904 32.752C15.424 33.1627 14.6133 33.424 13.472 33.536L13.264 32.432C14.032 32.3573 14.5493 32.2587 14.816 32.136C15.088 32.0133 15.2667 31.8213 15.352 31.56C15.352 31.5547 15.3547 31.5467 15.36 31.536C15.3653 31.52 15.368 31.5093 15.368 31.504L15.352 31.496C15.0747 31.6613 14.7627 31.744 14.416 31.744C13.9093 31.744 13.496 31.5973 13.176 31.304C12.8613 31.0107 12.704 30.6293 12.704 30.16C12.704 29.6907 12.864 29.3093 13.184 29.016C13.5093 28.7227 13.936 28.576 14.464 28.576C14.6827 28.576 14.9093 28.6213 15.144 28.712L15.16 28.704V28.28H11.504V27.256ZM14.096 29.728C13.9733 29.84 13.912 29.984 13.912 30.16C13.912 30.336 13.9733 30.48 14.096 30.592C14.224 30.704 14.3893 30.76 14.592 30.76C14.7947 30.76 14.9573 30.704 15.08 30.592C15.208 30.48 15.272 30.336 15.272 30.16C15.272 29.984 15.208 29.84 15.08 29.728C14.9573 29.616 14.7947 29.56 14.592 29.56C14.3893 29.56 14.224 29.616 14.096 29.728ZM25.184 28.248L24.28 28.68C24.0667 28.264 23.8187 27.8027 23.536 27.296L24.44 26.84C24.68 27.2773 24.928 27.7467 25.184 28.248ZM26.6 27.96L25.68 28.4C25.456 27.9573 25.2027 27.4853 24.92 26.984L25.84 26.52C26.1813 27.1493 26.4347 27.6293 26.6 27.96ZM20.088 31.944L19.136 31.056C19.3813 30.768 19.7787 30.2773 20.328 29.584C20.6213 29.2107 20.84 28.9387 20.984 28.768C21.1333 28.592 21.2827 28.448 21.432 28.336C21.5867 28.224 21.7173 28.1573 21.824 28.136C21.9307 28.1147 22.0827 28.104 22.28 28.104C22.4613 28.104 22.5973 28.112 22.688 28.128C22.784 28.144 22.92 28.2133 23.096 28.336C23.2773 28.4533 23.4667 28.6027 23.664 28.784C23.8613 28.9653 24.1733 29.2587 24.6 29.664C25.432 30.4587 26.1867 31.1627 26.864 31.776L25.976 32.8C25.0853 31.9893 24.1813 31.1413 23.264 30.256C22.8853 29.8933 22.6427 29.672 22.536 29.592C22.4293 29.5067 22.3333 29.464 22.248 29.464C22.1467 29.464 22.0507 29.5067 21.96 29.592C21.8747 29.6773 21.7013 29.888 21.44 30.224C21.008 30.8053 20.5573 31.3787 20.088 31.944ZM27.688 27.264C29.8533 27.264 32.0613 27.2107 34.312 27.104L34.352 28.264C33.1253 28.3493 32.2027 28.6027 31.584 29.024C30.9653 29.44 30.656 29.9547 30.656 30.568C30.656 31.0587 30.824 31.4507 31.16 31.744C31.496 32.032 31.936 32.176 32.48 32.176C32.7467 32.176 33.0693 32.1413 33.448 32.072L33.592 33.256C33.224 33.3307 32.832 33.368 32.416 33.368C31.4613 33.368 30.696 33.1253 30.12 32.64C29.544 32.1547 29.256 31.5067 29.256 30.696C29.256 30.2693 29.3867 29.848 29.648 29.432C29.9093 29.016 30.272 28.664 30.736 28.376V28.36C29.616 28.4027 28.6 28.424 27.688 28.424V27.264ZM39.616 32.184C40.752 32.024 41.32 31.336 41.32 30.12C41.32 29.624 41.176 29.2027 40.888 28.856C40.6053 28.504 40.232 28.2827 39.768 28.192C39.6293 29.1893 39.472 30.024 39.296 30.696C39.1253 31.3627 38.9307 31.8747 38.712 32.232C38.4987 32.584 38.28 32.8293 38.056 32.968C37.832 33.1067 37.5733 33.176 37.28 33.176C36.8267 33.176 36.408 32.92 36.024 32.408C35.6453 31.896 35.456 31.272 35.456 30.536C35.456 29.4853 35.8107 28.632 36.52 27.976C37.2293 27.3147 38.1627 26.984 39.32 26.984C40.2533 26.984 41.024 27.28 41.632 27.872C42.24 28.4587 42.544 29.208 42.544 30.12C42.544 31.0693 42.304 31.8267 41.824 32.392C41.344 32.9573 40.696 33.2747 39.88 33.344L39.616 32.184ZM38.48 28.24C37.9147 28.3787 37.472 28.6507 37.152 29.056C36.8373 29.4613 36.68 29.9547 36.68 30.536C36.68 30.8933 36.7493 31.2187 36.888 31.512C37.032 31.8 37.1627 31.944 37.28 31.944C37.3387 31.944 37.4027 31.9067 37.472 31.832C37.5467 31.7573 37.6267 31.6293 37.712 31.448C37.7973 31.2667 37.88 31.04 37.96 30.768C38.0453 30.496 38.1333 30.144 38.224 29.712C38.3147 29.2747 38.4 28.784 38.48 28.24ZM44.4 28.264V27.064H49.6V28.264C49.2427 29.032 48.7573 29.768 48.144 30.472C48.8533 31.1013 49.5733 31.768 50.304 32.472L49.416 33.352C48.6053 32.5627 47.8933 31.896 47.28 31.352C46.3787 32.168 45.3733 32.8267 44.264 33.328L43.688 32.272C45.7147 31.2907 47.2 29.9547 48.144 28.264H44.4ZM53.688 32.072C54.632 31.9547 55.376 31.6587 55.92 31.184C56.464 30.7093 56.912 29.976 57.264 28.984L58.456 29.456C57.944 30.864 57.2267 31.8667 56.304 32.464C55.3867 33.0613 54.12 33.36 52.504 33.36H52.296V26.896H53.688V32.072ZM65.656 28.384C65.6453 29.5307 65.496 30.4507 65.208 31.144C64.9253 31.832 64.4853 32.3493 63.888 32.696C63.296 33.0427 62.4907 33.264 61.472 33.36L61.248 32.312C62.096 32.2107 62.7493 32.0293 63.208 31.768C63.6667 31.5067 64 31.1093 64.208 30.576C64.416 30.0373 64.5227 29.288 64.528 28.328L65.656 28.384ZM60.272 28.72L61.32 28.472C61.5333 29.2187 61.7253 29.9307 61.896 30.608L60.816 30.864C60.6507 30.144 60.4693 29.4293 60.272 28.72ZM61.976 28.52L63.056 28.272C63.28 29.072 63.472 29.8 63.632 30.456L62.552 30.704C62.3387 29.856 62.1467 29.128 61.976 28.52ZM72.744 28.408L71.84 28.84C71.6267 28.4293 71.3787 27.968 71.096 27.456L72 27C72.24 27.4373 72.488 27.9067 72.744 28.408ZM74.16 28.12L73.24 28.56C73.016 28.1173 72.7627 27.6453 72.48 27.144L73.4 26.68C73.7413 27.3093 73.9947 27.7893 74.16 28.12ZM68.544 26.864H69.904V29.072C71.3493 29.424 72.784 29.8587 74.208 30.376L73.792 31.6C72.4427 31.1147 71.1467 30.7147 69.904 30.4V33.536H68.544V26.864Z"
                                              fill="#53565A"/>
                                            <path
                                              d="M35 9H39V5H35V9ZM41 21H45V17H41V21ZM35 21H39V17H35V21ZM35 15H39V11H35V15ZM41 15H45V11H41V15ZM47 5V9H51V5H47ZM41 9H45V5H41V9ZM47 15H51V11H47V15ZM47 21H51V17H47V21Z"
                                              fill="#53565A"/>
                                          </svg>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <div class="project-tab project-tab-new active">
                                <div class="messenger-director-new">
                                    <div class="messenger-director__container">
                                        <div class="messenger-director__info">
                                            <div class="messenger-director__form-group">
                                                <form method="post"
                                                      action="{% url 'app:director_messenger_search' %}?project_id={{ project.pk }}">
                                                    {% csrf_token %}
                                                    {{ form.errors }}
                                                    <div class="messenger-director__group">
                                                        <div class="form-group form-group-search">
                                                            <button type="submit">
                                                                <i class="fa fa-search"></i>
                                                            </button>
                                                            {{ form.keyword }}
                                                        </div>
                                                    </div>
                                                    <div class="messenger-director__group">
                                                        <div class="form-group select-container select_role sumo-select">
                                                            <label>職種</label>
                                                            {{ form.role }}
                                                        </div>
                                                    </div>
                                                    <div class="messenger-director__group">
                                                        <div class="messenger-director__column-2">
                                                            <div class="form-group form-datepicker from_date_datepicker">
                                                                <label for="start-date">開始日時</label>
                                                                {{ form.from_date }}
                                                            </div>
                                                            <div class="form-group form-datepicker to_date_datepicker">
                                                                <label for="end-date">期限</label>
                                                                {{ form.to_date }}
                                                            </div>
                                                        </div>
                                                        <div class="messenger-director__column-2">
                                                            <div class="form-group"></div>
                                                            <div class="form-group form-timepicker">
                                                                <label for="start-time">時間</label>
                                                                <input class="form-control form-control" type="text"
                                                                       placeholder="10:00" value="10:00"
                                                                       id="start-time">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="messenger-director__action">
                                                        <div class="messenger-director__action">
                                                            <div class="messenger-director__form-group">
                                                                <input type="submit" value="検索する" class="button
                                                            button--gradient button--gradient-primary button--round"
                                                                       role="button">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </form>
                                                {% if creators.count > 0 %}
                                                    <div class="messenger-director__group">
                                                        <div class="messenger__creator-list">
                                                            <div class="search-label">アーティスト({{ creators.count }}件)</div>
                                                            <div class="container-fluid">
                                                                {% for creator in creators %}
                                                                    <div class="messenger__creator-item {% if forloop.counter0 > 3 %}hide{% endif %} col-sm-3"
                                                                         title="{{ creator.user.get_display_name }}">
                                                                        <div class="creator-item-component">
                                                                            <div class="creator-item-content">
                                                                                <div class="messenger__avatar background-avt">
                                                                                    <img src="{{ creator.user|get_avatar:'medium' }}" alt="">
                                                                                </div>
                                                                                <div class="text-center">{{ creator.user.get_display_name }}</div>
                                                                                <div class="creator-info"
                                                                                     data-user-id="{{ creator.user_id }}"
                                                                                     data-avatar-url="{{ creator.user|get_avatar:'medium' }}"
                                                                                     data-creator-name="{{ creator.user.get_display_name }}"
                                                                                     data-creator-role="{{ creator.user.type }}"
                                                                                     data-policy="{{ creator.policy }}"
                                                                                     data-detail-url="{% url 'accounts:accounts_creator' creator.user_id %}"
                                                                                     data-trading="{{ creator.get_trading_display }}">
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                        {% if creators.count > 4 %}
                                                            <div class="messenger__audio-button">
                                                                <a class="button button--background button--background-gray button--round button--small show-more-creator"
                                                                   href="javascript:;" role="button">もっと観る</a>
                                                            </div>
                                                        {%  endif %}
                                                    </div>
                                                {% endif %}
                                              {% if sale_contents.count > 0 %}
                                                <div class="messenger-director__group">
                                                  <div class="messenger__audio-list">
                                                    <div class="search-label">サンプル({{ sale_contents.count }}件)</div>
                                                    {% for sale_content  in sale_contents %}
                                                      {% with sale_content|get_public_audios as audios %}
                                                          {% for audio in audios %}
                                                            <div class="messenger__audio-item {% if forloop.parentloop.counter0 > 2 %}hide{% endif %}">
                                                              <div class="audio-player-component">
                                                                <div class="audio-player-content">
                                                                  <div class="audio-player-controls">
                                                                    <div class="audio-player-control">
                                                                      <div class="audio-player-control-playpause"></div>
                                                                    </div>
                                                                    <div class="audio-player-time">
                                                                      <div class="audio-player-current">
                                                                        00:00
                                                                      </div>
                                                                      <div class="audio-player-duration"></div>
                                                                    </div>
                                                                  </div>
                                                                    <div class="audio-player-data"
                                                                         data-link="{{ audio }}"
                                                                    >
                                                                      <div class="audio-player-waveform"></div>
                                                                    </div>
                                                                    <div class="audio-player-title"></div>
                                                                    {% with sale_content.profile.creator as owner %}
                                                                      <div class="audio-player-owner">
                                                                        <div class="creator-info"
                                                                             data-user-id="{{ owner.user.pk }}"
                                                                             data-avatar-url="{{ owner.user|get_avatar:'medium' }}"
                                                                             data-creator-name="{{ owner.user.get_display_name }}"
                                                                             data-creator-role="{{ owner.user.type }}"
                                                                             data-policy="{{ owner.policy }}"
                                                                             data-detail-url="{% url 'accounts:accounts_creator' owner.user.pk %}"
                                                                             data-trading="{{ owner.get_trading_display }}">
                                                                        </div>
                                                                      </div>
                                                                    {% endwith %}
                                                                </div>
                                                              </div>
                                                            </div>
                                                          {% endfor %}
                                                      {% endwith %}
                                                    {% endfor %}
                                                  </div>
                                                  {% if sale_contents.count > 3 %}
                                                    <div class="messenger__audio-button">
                                                      <a class="button button--background button--background-gray button--round button--small show-more-audio"
                                                         href="javascript:;" role="button">もっと観る</a>
                                                    </div>
                                                  {% endif %}
                                                </div>
                                              {% endif %}
                                            </div>
                                            <div class="selected_creator hide">
                                                <div class="messenger__item">
                                                    <div class="messenger__avatar background-avt">
                                                        <img class="messenger__avatar-img" src=""
                                                             alt="">
                                                        <div class="messenger__user-active"></div>
                                                    </div>
                                                    <div class="messenger__info">
                                                        <a class="messenger__info-url" href="#">
                                                            <div class="messenger__name"></div>
                                                        </a>
                                                        <div class="messenger__work"></div>
                                                        <div class="messenger__mess hide">
                                                            <div class="messenger__like">
                                                                <i class="fas fa-thumbs-up"></i>
                                                                <span>18</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="account__tradeoff">
                                                <div class="account__trade-slider">
                                                  {% for i in '12345' %}
                                                    <div class="account__trade-item"
                                                    data-value={{ i }}>
                                                    </div>
                                                  {% endfor %}
                                                </div>
                                                <div class="account__trade-label">
                                                    <div class="account__label-left">仕事量を増やしたい</div>
                                                    <div class="account__label-right">条件を良くしたい</div>
                                                </div>
                                              </div>
                                                <div class="messenger-director__group">
                                                    <div class="form-group form-customize textarea-large" rows="5">
                                                        <label for="offer-policy">案件受託についてのポリシー</label>
                                                        <div class="form-textarea" id="offer-policy">
                                                            <textarea class="form-control" readonly>
                                                            </textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="messenger-director__action">
                                                    <div class="messenger-director__form-group">
                                                        <a class="button button--gradient button--gradient-primary
                                                           button--round btn-offer-creator"
                                                           role="button" data-toggle="modal" data-target="#offerModal">このアーティストに相談</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="project-tab project-tab-waiting">
                                <p>waiting</p>
                            </div>
                            <div class="project-tab project-tab-doing">
                                <p>doing</p>
                            </div>
                        </div>
                        <div class="project-member-setting-modal modal fade" id="offerModal"
                             role="dialog">
                            <div class="messenger-director-offer" id="messenger-director-offer">
                                <div class="messenger-director__container">
                                    <div class="messenger-director__info">
                                        <div class="messenger-director__form-group">
                                            <div class="messenger__item">
                                                <div class="messenger__avatar background-avt">
                                                    <img class="messenger__avatar-img" src=""
                                                         alt="">
                                                    <div class="messenger__user-active"></div>
                                                </div>
                                                <div class="messenger__info">
                                                    <a class="messenger__info-url" href="#">
                                                        <div class="messenger__name"></div>
                                                    </a>
                                                    <div class="messenger__work"></div>
                                                    <div class="messenger__mess">
                                                        <div class="messenger__like">
                                                            <i class="fas fa-thumbs-up"></i>
                                                            <span>18</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="messenger-director__group" style="padding-bottom: 0">
                                                <div class="form-group form-customize" rows="5">
                                                    <label for="offer-policy">案件受託についてのポリシー</label>
                                                    <div class="form-textarea" id="offer-policy">
                                                        <textarea class="form-control" readonly>
                                                            {{ creator.policy }}
                                                        </textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="messenger-director__list">
                                                <div class="messenger-director__item">
                                                    <form method="post" action="{% url 'app:creator_offer' %}"
                                                          enctype="multipart/form-data">
                                                        {% csrf_token %}
                                                        {{ offer_form.errors }}
                                                        <div class="messenger-director__item-content">
                                                            <div class="messenger-director__item-info">
                                                                <div class="messenger-director__item-form">
                                                                    {{ offer_form.project }}
                                                                    {{ offer_form.creator }}
                                                                    <div class="messenger-director__column-2">
                                                                        <div class="form-group">
                                                                            <label for="id_date">期限</label>
                                                                            <input id="id_date" type="text"
                                                                                   class="form-control form-control"
                                                                                   placeholder="yyyy/mm/dd"
                                                                                   autocomplete="off" readonly/>
                                                                            {{ offer_form.start_time }}
                                                                            {{ offer_form.deadline }}
                                                                        </div>
                                                                        <div class="form-group">
                                                                            <label for="id_time">時間</label>
                                                                            <input class="form-control form-control"
                                                                                   type="text" value="10:00"
                                                                                   id="id_time"
                                                                                   readonly>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group select-container select_contract sumo-select">
                                                                        <label for="id_contract">業務内容</label>
                                                                        {{ offer_form.contract }}
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label for="id_type_contract">契約書の種類</label>
                                                                        {{ offer_form.type_contract }}
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label for="id_scenes">シーン</label>
                                                                        {{ offer_form.scenes }}
                                                                    </div>

                                                                  <div class="form-group">
                                                                    <label for="id_quantity">数量</label>
                                                                    {{ offer_form.quantity }}
                                                                  </div>

                                                                  <div class="form-group">
                                                                    <label for="id_data_format">データ形式</label>
                                                                    {{ offer_form.data_format }}
                                                                  </div>

                                                                    <div class="form-group form-customize textarea-large"
                                                                         rows="5">
                                                                        <label for="right-messenger">詳細</label>
                                                                        <div class="form-textarea"
                                                                             id="right-messenger">
                                                                            {{ offer_form.message }}
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group upload__file">
                                                                        {{ offer_form.file }}
                                                                        <label class="button button--background
                                                                               button--background-primary button--round
                                                                               button--small button-upload upload_link"
                                                                               for="id_file"
                                                                               role="button">ファイルを添付</label>
                                                                        <div class="selected_file"></div>
                                                                    </div>

                                                                     <div class="form-group">
                                                                        <label for="price">報酬（税抜）</label>
                                                                        <div class="currency">
                                                                            <input type="number" class="form-control reward" id="id_tmp_reward" required/>
                                                                            <span>&nbsp円</span>
                                                                            <input type="hidden" name="reward" id="id_reward">
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group tax-amount">
                                                                        <span class="tax-amount-label">消費税</span>
                                                                        <span class="tax-amount-amount" id="id_tax_amount"></span><span>&nbsp円</span>
                                                                    </div>

                                                                    <div class="form-group tax-amount">
                                                                        <span class="tax-amount-label">会計（税込）</span>
                                                                        <span class="tax-amount-amount" id="id_total_amount"></span><span>&nbsp円</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="messenger-director__action">
                                                            <div class="messenger-director__form-group">
                                                                <button class="button button--gradient button--gradient-primary button--round"
                                                                        type="submit"
                                                                        role="button">この条件でオファー
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </form>
                                                    <div class="messenger-director__action">
                                                        <div class="messenger-director__form-group">
                                                            <div type="button" class="button--background
                                                                    button--background-secondary button--round
                                                                    button--small"
                                                                    data-dismiss="modal">
                                                                キャンセル
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="upload-button-wrapper" style="position: fixed;top: 0; left: 0;">
                                <p>アップロード中</p>
                                <div class="fill">
                                    <div class="process"></div>
                                </div>
                                <div class="fa fa-check"></div>
                            </div>
                        </div>

                      <div class="messenger-popup modal fade animate" id="over-budget" role="dialog">
                        <div class="messenger-popup__content">
                          <div class="messenger-popup__form">
                            <div class="messenger-popup__form-text"> 予算上限を超えています。
                            </div>
                            <div class="messenger-popup__form-text"> 調整が難しい場合、プロジェクトオーナーと折衝が必要ですので、管理者に御相談ください。
                            </div>
                          </div>
                          <div class="messenger-popup__action">
                            <div class="messenger-popup__action-button">
                              <a class="gradient button--gradient button--gradient-primary button--round"
                                 data-dismiss="modal">OK</a>
                              <br>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <script>
        let project_id = '{{ project.pk }}';
    </script>
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.form/4.3.0/jquery.form.min.js" crossorigin="anonymous"></script>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
    <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/messenger_search.js' %}"></script>
    {% endcompress %}
{% endblock %}
