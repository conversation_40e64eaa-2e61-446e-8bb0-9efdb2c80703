{% load util %}
{% load static %}

<div class="project-item__general">
    {% with done_count=project.current_heart scene_count=project.current_scene %}
        <div class="project-item__info-bottom">
            <div class="project-item__more">
                <div class="project-item__more-left">

                    <div class="project-item__more-left-top">
                        <ul class="project-item__category">
                        </ul>
                        {% if project.information and not is_pc and show_staff %}
                            <div class="project-item__more-right">
                                <div class="button button--text button--text-primary button-staff"
                                     role="button"></div>
                            </div>
                        {% endif %}

                        <div class="project-item__progress-percent">
                        　　{% if user.role == 'admin' %}
                              <div class="project-item__progress-total" title="検収済みスレッド合計金額" style="margin-right: 5px;">
                                <span style=" color: #0f9ca9">￥{{ user|get_current_paid_budget_admin:project }}</span> |
                              </div>
                              <div class="project-item__progress-total"
                                  title="発注済みスレッド残り金額" style="margin-right: 5px;">
                                  <span style=" color: #53565a">￥{{ user|get_current_spending_budget_admin:project }}</span> |
                              </div>
                              <div class="project-item__progress-total" title="予算上限">￥{{ user|get_admin_budget:project }}
                              </div>
                            {% else %}
                                <div class="project-item__progress-success"
                                     title="完了">{{ done_count|floatformat:"0" }}</div>
                                <div class="project-item__progress-warning"
                                     title="完了また進行中">{{ scene_count|minus:done_count|floatformat:"0" }}</div>
                                <div class="project-item__progress-total" title="残る"
                                     data-max-scene="{{ project.max_scene }}">
                                    <span>{{ project.max_scene }}</span>
                                    {% if user.role in 'master_admin,admin' or user.role == 'master_client' and user in project|get_owners_project %}
                                        <div class="max_scene_edit_btn max_scene_edit"></div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="project-item__bar">
                            <div class="progressbar-new">
                              {% if user.role == 'admin' %}
                                {% with budget_done=user|get_budget_admin_offer_done:project budget_offer=user|get_budget_admin_offer:project %}
                                  <div class="progress">
                                      <div class="progress-bar bg-success"
                                           style="width: {{ budget_done }}%"></div>
                                      <div class="progress-bar bg-warning"
                                           style="width: {{ budget_offer|minus:budget_done }}%"></div>
                                  </div>
                                {% endwith %}
                              {% else %}
                                  <div class="progress">
                                      <div class="progress-bar bg-success"
                                           style="width: {{ project.get_current_heart_rate }}%"></div>
                                      <div class="progress-bar bg-warning"
                                           style="width: {{ project.get_current_scene_rate }}%"></div>
                                  </div>
                              {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% if project.information and is_pc and show_staff %}
                    <div class="project-item__more-right">
                        <div class="button button--text button--text-primary button-staff"
                             role="button"></div>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endwith %}
    <div class="project-item__info" style="background-image: url({{ project|get_image }});">
    </div>
</div>
