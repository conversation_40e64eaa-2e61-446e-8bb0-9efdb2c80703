{% load i18n %}
<!-- Modal confirm -->
<div class="modal popup-container" id="modal-confirm-step" role="dialog" style="z-index: 9000">
  <div class="modal-dialog popup-dialog modal-lg">
    <div class="modal-content popup-content">
      <div class="popup-header">
        <h4 class="popup-title">{% trans "I will send it with the following contents. please confirm." %}</h4>
        <hr/>
      </div>

      {% if page == 'gallery' %}
        <div class="popup-body">
          <div class="popup-body__wrap">
            <div class="popup-body__heading heading--18" id="id_project_type_value"></div>
            <div class="text-title heading--13">作品名</div>
            <div class="msg-content bodytext--13 break-line" id="id_album_name"></div>
            <div class="popup-body__list popup-body-selection">
            </div>
          </div>

          <div class="popup-body__wrap">

            <div class="popup-body__list">
              <div class="popup-body__item">
                <div class="text-title heading--13">メッセージ</div>
                <div class="msg-content bodytext--13 break-line" id="id_description_value"></div>
              </div>

              <div class="popup-body__item">
                <div class="text-title heading--13">対価</div>
                <div class="bodytext--13"><span id="id_budget_value"></span> {% trans "yen without tax" %}
                </div>
              </div>
            </div>
          </div>
        </div>
      {% else %}
      {% if not add_by_sale %}
        <div class="popup-body">
          <div class="popup-body__wrap">
            <div class="popup-body__heading heading--18" id="id_project_type_value"></div>
            {#                    <div class="text-title heading--13">{% trans "Production items" %}</div>#}
            <div class="popup-body__list popup-body-selection">

            </div>
          </div>

          <div class="popup-body__wrap">
            <div class="popup-body__heading heading--18">{% trans "Contents" %}</div>
            <div class="popup-body__list">
              <div class="popup-body__item">
                <div class="msg-content bodytext--13 break-line" id="id_description_value"></div>
              </div>
              <div class="popup-body__item" id="user-file">
                <div class="order-step__upload-file">
                </div>
              </div>
              <div class="popup-body__item">
                <div class="text-title heading--13">{% trans "Hope budget" %}</div>
                <div class="bodytext--13"><span id="id_budget_value"></span> {% trans "yen without tax" %}
                </div>
              </div>
            </div>
          </div>
        </div>

      {% elif contact_artist_profile %}
        <div class="popup-body">
          <div class="popup-body__wrap">
            <div class="popup-body__heading heading--18" id="id_project_type_value"></div>
            <div class="text-title heading--13">アーティスト名</div>
            <div class="msg-content bodytext--13 break-line" id="id_album_name"></div>
            <div class="popup-body__list popup-body-selection">
            </div>
          </div>

          <div class="popup-body__wrap">

            <div class="popup-body__list">
              <div class="popup-body__item">
                <div class="text-title heading--13 break-line">メッセージ</div>
                <div class="msg-content bodytext--13" id="id_description_value"
                     style="word-break: break-word; white-space: pre-line;"></div>
              </div>
              <div class="text-title heading--13 break-line">資料</div>
              <div class="popup-body__item" id="user-file">
                <div class="order-step__upload-file">
                </div>
              </div>

            </div>
          </div>
        </div>

      {% else %}
        <div class="popup-body">
          <div class="popup-body__wrap">
            <div class="popup-body__heading heading--18" id="id_project_type_value"></div>
            <div class="text-title heading--13">作品名</div>
            <div class="msg-content bodytext--13 break-line" id="id_album_name"></div>
            <div class="popup-body__list popup-body-selection">
            </div>
          </div>

          <div class="popup-body__wrap">

            <div class="popup-body__list">
              <div class="popup-body__item">
                <div class="text-title heading--13 break-line">メッセージ</div>
                <div class="msg-content bodytext--13" id="id_description_value" style="word-break: break-word; white-space: pre-line;"></div>
              </div>

              <div class="popup-body__item">
                <div class="text-title heading--13">対価</div>
                <div class="bodytext--13"><span id="id_budget_value"></span> {% trans "yen without tax" %}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="popup-body__wrap">
      </div>

      {% endif %}
      {#        Step 3#}
    {% if not hide_step_3 %}
      <div class="popup-body__wrap">
        <div class="popup-body__heading heading--18">{% trans "Conditions" %}</div>
        <div class="popup-body__list">
          <div class="popup-body__item">
            <div class="text-title heading--13">{% trans "Production period" %}</div>
            <div class="bodytext--13" id="id_schedule_time_value"></div>
          </div>
          <div class="popup-body__item">
            <div class="text-title heading--13">{% trans "deadline" %}</div>
            <div class="bodytext--13" id="id_deadline_value"></div>
          </div>
          <div class="popup-body__item">
            <div class="text-title heading--13">{% trans "Handling of rights" %}</div>
            <div class="bodytext--13" id="id_contract_type_value"></div>
          </div>
          <div class="popup-body__item">
            <div class="text-title heading--13">{% trans "contract" %}</div>
            <div class="bodytext--13" id="id_ownership_type_value"></div>
          </div>

          <div class="popup-body__item">
            <div class="text-title heading--13">{% trans "Achievements released" %}</div>
            <div class="bodytext--13" id="id_disclosure_rule_value"></div>
          </div>
        </div>
      </div>
    {% endif %}

      {% if not user.is_authenticated %}
        <div class="popup-body__wrap">
          <div class="popup-body__heading heading--18">{% trans "Contact information" %}</div>
          <div class="popup-body__list">
            <div class="popup-body__item">
              <div class="text-title heading--13 break-line">{% trans "Full name" %}</div>
              <div class="bodytext--13" id="id_fullname_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13 break-line">{% trans "Email address" %}</div>
              <div class="bodytext--13" id="id_email_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13 break-line">{% trans "job type" %}</div>
              <div class="bodytext--13" id="id_job_type_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13 break-line">{% trans "company name" %}</div>
              <div class="bodytext--13" id="id_enterprise_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13 break-line">{% trans "website" %}</div>
              <div class="bodytext--13" id="id_company_url_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13 break-line">{% trans "phone number" %}</div>
              <div class="bodytext--13" id="id_phone_value"></div>
            </div>
            <div class="popup-body__item">
              <div class="text-title heading--13">{% trans "Your preferred contact method" %}</div>
              <div class="bodytext--13" id="id_contact_channel_value"></div>
            </div>
          </div>
        </div>
      {% endif %}

    {% endif %}

      <div class="popup-footer" style="text-align: right !important;">
        <button type="button" class="btn btn--tertiary" data-dismiss="modal">{% trans "return" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send"
                id="submit-confirm">{% trans "Consult with this content" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal confirm -->

<!-- Modal create success -->
<div class="modal popup-container fade" id="modal-create-success" role="dialog">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-header">
      </div>
      <div class="popup-body">
        <p class="popup-text bodytext--13"
           style="text-align: left !important;">{% trans "We received a consultation." %}</p>
        <p class="popup-text bodytext--13"
           style="text-align: left !important;">{% trans 'We have emailed you the details, so please check.' %}</p>
      </div>
      <div class="popup-footer" style="text-align: right !important;">
        <a href="#" style="color: #f0f0f0">
          <button type="button" class="btn btn--primary btn-create-success" id="submit-ok">OK</button>
        </a>
      </div>
    </div>
  </div>
</div>
<!-- End modal create success -->


{% if not user.is_authenticated or user.is_authenticated and user.role == 'master_client' %}
  <!-- Modal contract artist -->
  <div class="modal popup-container fade" id="modal-contact-artist" role="dialog" style="z-index: 2000">
    <div class="modal-dialog popup-dialog">
      <div class="modal-content popup-content">
        <div class="popup-body">

          <div class="list-new-works__sub-item" style="margin-bottom: 0; padding-bottom: 16px; border-bottom: 1px solid #f0f0f0">
          </div>

          <form id='contact-artist' action='' class="form-group" style="margin-top: 24px; margin-bottom: 0;">
            <label class="label_field label-budget" for="id_description">
              <div class="contact__field-label" style="font-weight: 400;">{% trans "message" %}<span
                      class="contact__jp-astarisk">[必須]</span></div>
              <textarea name="description" rows="4" required maxlength="1000" class="form-textarea form-control"
                        id="id_description"
                        placeholder="" style="resize: none;"></textarea>
            </label>
            <label class="label_field label-budget" for="id_budget">
              <div class="contact__field-label" style="font-weight: 400;">{% trans "Consideration" %}<span
                      class="contact__jp-astarisk-op">[任意]</span></div>
              <div class="budget-input-container">
                <input class="form-control" placeholder="30,000" type='text' name='budget' maxlength="19" id="id_budget"
                       min="0"/>
                <span class="budget-unit">円（税抜）</span>
              </div>
            </label>

          </form>

        </div>

        <div class="popup-footer" style="text-align: right;">
          <button type="button"
                  class="btn btn--primary btn-popup-send btn-update-project-setting">{% trans "to the next" %}</button>
        </div>
      </div>
    </div>
  </div>
  <!-- End modal contract artist -->


  <div class="modal popup-container fade" id="modal-contact-artist-profile" role="dialog" style="z-index: 2000">
    <div class="modal-dialog popup-dialog">
      <div class="modal-content popup-content">
        <div class="popup-body">

          <form id='contact-artist' action='' class="form-group" style="margin-bottom: 0;">
            <label class="label_field label-budget" for="id_description">
              <div class="contact__field-label" style="font-weight: 400;">{% trans "message" %}<span
                      class="contact__jp-astarisk">[必須]</span></div>
              <textarea name="description" rows="4" required maxlength="1000" class="form-textarea form-control"
                        id="id_description"
                        placeholder="" style="resize: none;"></textarea>
            </label>
            <label class="label_field label-budget" for="id_budget">
              <div class="contact__field-label" style="font-weight: 400;">資料<span
                      class="contact__jp-astarisk-op">[任意]</span></div>

              <div class="account_upload-file mattach mattach-form">
                <div class="mcomment-attached">
                  <div class="mattach-preview-container mattach-preview-container-form">
                    <div class="mattach-previews mattach-previews-form collection">
                      <div class="mattach-template mattach-template-form collection-item item-template">
                        <div class="mattach-info" data-dz-thumbnail="">
                          <div class="mcommment-file">
                            <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                            <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                            <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                    class="icon icon--sicon-close"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="fallback dropzone" id="uploadFile">
                </div>

              </div>

              <div class="caption--11 hint-text--modal">※フォルダごとドラッグ＆ドロップでお送りいただけます。</div>


              {# upload file #}


            </label>

          </form>

        </div>

        <div class="popup-footer" style="text-align: right;">
          <button type="button"
                  class="btn btn--primary btn-popup-send btn-update-project-setting">{% trans "to the next" %}</button>
        </div>
      </div>
    </div>
  </div>

{% endif %}
