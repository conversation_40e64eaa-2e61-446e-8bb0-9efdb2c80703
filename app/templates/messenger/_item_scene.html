{% load static %}
{% load util %}

{% with offer|get_offer_user:user as offer_user %}
  <div class="mscene mitem type_item_scene {% if offer.status in '4, 6' %}mchecked{% elif offer.status in '2,3' %}mprogress{% endif %} {% if offer_user == 'admin' %}offer-admin{% endif %}"
       data-offer="{{ real_offer.pk }}" data-type-offer="messenger_artist" data-offer-side="{%  if real_offer.offer_side == 1 %}admin{% else %}creator{% endif %}" data-reward="{{ real_offer.budget_or_reward }}" data-created="{{ real_offer.created|get_datetime }}">
    <div class="mscene__top" style="align-items: center;">
      {% if offer_user == 'admin' %}
        <i class="icon icon--sicon-drop-next" style="margin-right: 5px;"></i>
      {% endif %}
      <div class="mmessage-user">
        {% with offer|get_other_member_in_offer:user as members %}
          {% for member in members %}
            {% if forloop.counter0 < 5 %}
              <div class="mmessage-user-seen seen--owner">
                <div class="avatar avatar--image avatar--32 avatar--round stooltip background-avt" data-toggle="tooltip"
                     data-placement="bottom" data-html="true" title="{{ member.get_display_name }}">
                  <img class="avatar-image" src="{{ member|get_avatar:'medium' }}" style="padding-bottom: 0">
                </div>
              </div>
            {% endif %}
          {% endfor %}
          {% with members.count|minus:5 as member_count %}
            <div class="mmessage-user-count {% if member_count <= 0 %}hide{% endif %}">
              {% if member_count >= 100 %}
                +99+
              {% else %}
                +{{ member_count }}
              {% endif %}</div>
          {% endwith %}
        {% endwith %}
        <div class="user-status-icon offline"></div>
      </div>
      {% if offer_user == 'creator' %}
        <i class="icon icon--sicon-drop-next" style="margin-left: 5px;"></i>
      {% endif %}

      <div class="mscene__label">
        <i class="icon {{ offer|get_icon_offer:user }}"></i>
      </div>

    </div>
    <div class="mscene__bottom guide-popup__wrap-info">
      <div class="mscene__name">
        <div class="thread-name">{{ offer.scenes }}</div>
        {% with offer|new_message_count:user as count_new %}
          <div class="notification notification--blue notification--round {% if count_new == 0 %}hide{% endif %}">
            {% if count_new < 100 %}{{ count_new }}{% else %}99+{% endif %}</div>
        {% endwith %}
      </div>
      <div class="mscene__date">{{ real_offer.modified|get_weekday_new }}</div>
    </div>
  </div>

{% endwith %}
