{% load i18n %}
{% load util %}

<div class="{% if offer_creator.creator_id != user.pk %} mmessage mmessage--sent clicked load-lasted-message {% else %} mmessage mmessage--received clicked {% endif %}">
  <div class="mmessage-main u-gap4">
    {% if offer_creator.creator_id == user.pk %}
      <div class="avatar avatar--image" title="{{offer_creator.admin_id|get_full_name}}" >
        <div class="avatar-image c-avatar32" style="background-image: url({{ offer_creator.admin_id|get_avatar:'medium' }})"></div>
      </div>
    {% endif %}
    <div class="contract-block u-row-end u-wrapper-reading u-bg-white"  data-offer="{{ offer.offer.pk }}">
      {% if user.pk != offer_creator.creator_id%}
        <div class="u-relative">
          <div class="contract-block-action">
            <div class="contract-block-action-button" onclick="setUpToggleOffer(event)">
              <span class="material-symbols-rounded u-text-light-gray c-icon-more-horiz">more_horiz</span>
            </div>
            <div class="contract-block-action-dropdown hide">
              <div class="contract-block-action-trigger d-flex" data-toggle="modal" data-target="#modal-edit-offer" onclick="fillValueToModal('{{offer.offer.pk}}')">
                <div class="contract-block-action-trigger-text">
                  契約書（案）を編集
                </div>
                <div class="contract-block-action-trigger-icon">
                  <span class="material-symbols-rounded">contract_edit</span>
                </div>
              </div>
              <div class="contract-block-action-trigger d-flex top-border" data-toggle="modal" data-target="#delete-offer">
                <div class="contract-block-action-trigger-text">
                  スレッドを削除
                </div>
                <div class="contract-block-action-trigger-icon">
                  <span class="material-symbols-rounded">delete_forever</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
      <div class="contract-block-content u-col {% if offer_creator.creator_id == user.pk %} c-message-their {% else %} c-message-ours {% endif %}">
        <div class="contract-block-title u-line-height-150 bodytext-13"><span class="contract-block-detail-line-message">{% if offer_creator.message %}{{ offer_creator.message|linebreaksbr }}{% endif %}</span></div>
        <div class="contract-block-detail c-group u-ptb8 u-w100">
          <div class="contract-block-detail-line">
            <p class="contract-block-detail-line-content"><span class="u-text-light-gray">納品形式:</span> <span class="contract-block-detail-line-value">{{ offer_creator.data_format }}</span></p>
            <p class="contract-block-detail-line-content"><span class="u-text-light-gray">納期:</span> <span class="contract-block-detail-line-value">{{ offer_creator.deadline|format_deadline_with_weekday }}</span></p>
            <p class="contract-block-detail-line-content"><span class="u-text-light-gray">対価:</span> <span class="contract-block-detail-line-value">{{ offer_creator.reward|display_currency }}円 (税込)</span></p>
            {% if offer_creator.allow_subcontracting %}
            <p class="contract-block-detail-line-content"><span class="u-text-light-gray">再委託:</span> <span class="contract-block-detail-line-value">可</span></p>
            {% endif %}
            {% if offer_creator.note %}
            <p class="contract-block-detail-line-content"><span class="u-text-light-gray">特記事項:</span> <span class="contract-block-detail-line-value">{{ offer_creator.note }}</span></p>
            {% endif %}
          </div>
        </div>
        <div class="contract-block-contract u-row d-flex u-w100 u-border-top u-border-light-gray u-pt8 u-mt8" onclick="get_content_modal_contract('{{offer.offer.pk}}')">
          <div class="contract-block-contract-icon u-line-height-100"><span class="material-symbols-rounded u-pr8 icon-offer">contract</span></div>
          <div class="contract-block-contract-file-name bodytext-13">{{ offer_creator.get_contract_file_name }}</div>
          <div class="contract-block-contract-download-button u-line-height-100">
            <span class="u-pl8 material-symbols-rounded">download</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>