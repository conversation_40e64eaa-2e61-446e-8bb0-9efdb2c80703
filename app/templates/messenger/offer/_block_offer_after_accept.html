{% load i18n %}
{% load util %}

<div class="contract-block c-btn-contract-done u-row-center u-mx-auto u-relative" data-offer="{{ offer.offer.pk }}">
  <div class="contract-block-button d-flex accept_production_file u-row u-gap16">
    <div class="u-col-center u-gap4">
      <div class="contract-block-button-child-top text-center">
        <span class="material-symbols-rounded u-text-blue">
          assignment_turned_in
        </span>
        <p class="label8">
          {{ offer_creator.get_contract_file_name }}
        </p>
      </div>
    </div>
    <div class="contract-block-button-child u-col-center u-gap8">
      <p class="heading-18-spacing u-mb8">
        締結完了
      </p>
      <p class="label8">締結日: {{ offer_creator.deadline|get_updated_full_time }}</p>
    </div>
  </div>
  <div class="contract-block-action u-absolute">
    <div class="contract-block-action-button" onclick="setUpToggleOffer(event)">
      <span class="material-symbols-rounded u-text-blue c-icon-more-horiz">more_horiz</span>
    </div>
    <div class="contract-block-action-dropdown u-absolute hide">
      <div class="contract-block-action-trigger d-flex" onclick="downloadFileOffer('{{offer.offer.pk}}')">
        <div class="contract-block-action-trigger-text">
          PDFをダウンロード
        </div>
        <div class="contract-block-action-trigger-icon">
          <div class="contract-block-action-trigger-icon">
            <span class="material-symbols-rounded">download</span>
          </div>
        </div>
      </div>
      <div class="contract-block-action-trigger d-flex top-border" data-toggle="modal" data-target="#modal-edit-offer" onclick="fillValueToModal('{{offer.offer.pk}}')">
        <div class="contract-block-action-trigger-text">
          条件を調整
        </div>
        <div class="contract-block-action-trigger-icon">
          <span class="material-symbols-rounded">contract_edit</span>
        </div>
      </div>
      <div class="contract-block-action-trigger d-flex top-border" data-toggle="modal" data-target="#closed-offer">
        <div class="contract-block-action-trigger-text">
          オファーを終了
        </div>
        <div class="contract-block-action-trigger-icon">
          <span class="material-symbols-rounded">contract_delete</span>
        </div>
      </div>
    </div>
  </div>
</div>
