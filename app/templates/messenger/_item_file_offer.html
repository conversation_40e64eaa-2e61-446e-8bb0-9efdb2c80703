{% load static %}
{% load util %}

{% with message|get_message_dict_files as dict_files %}
  {% for file in dict_files %}
    {% with file|get_message_file_folder as object %}
      {% if object %}
        {% if object.file_id %}
          {% with object.is_audio_file as type_file %}

            <div class="minfo-file_info minfo-document" data-file-id="{{ object.pk }}" data-toggle="modal"
                 data-target="#modal-{{ type_file }}-popup" data-link="{{ object.file.url }}"
                 data-name="{{ object.real_name }}" data-type="{{ type_file }}" data-message-id="{{ message.pk }}">
              {% include 'messenger/_item_file.html' with file=object %}
            </div>
          {% endwith %}
        {% elif object.folder_id %}
          <div class="minfo-file_info minfo-document" data-file-id="{{ object.pk }}" data-toggle="modal"
               data-target="#modal-folder-popup" data-link="{{ object.file.url }}"
               data-name="{{ object.name }}" data-type="folder" data-message-id="{{ message.pk }}">
            {% include 'messenger/_item_folder.html' with folder=object type_message='message_artist' %}
          </div>
        {% endif %}
      {% endif %}
    {% endwith %}
  {% endfor %}
{% endwith %}
