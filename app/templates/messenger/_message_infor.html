{% load static %}
{% load util %}

<div class="message-actions-container {% if type_infor == 'received' %} actions-received {% endif %}">
  {% if user == message.owner %}
    {% if first_message %}
      <div class="mmessage-action hide">
        <a href="#" class="button-edit_offer message-first__message" data-toggle="modal"
          data-target="#modal-edit-offer">
          <i class="icon icon--sicon-pencil"></i>
        </a>
      </div>
    {% else %}
      <div class="mmessage-action hide">
        <a class="mmessage-delete" href="#">
          <i class="icon icon--sicon-trash"></i>
        </a>
        <a class="mmessage-edit" href="#">
          <i class="icon icon--sicon-pencil"></i>
        </a>
      </div>
    {% endif %}
  {% endif %}
</div>

<div class="message-info-container {% if type_infor == 'received' %} actions-received {% endif %}">
  <div class="mmessage-status">
    <div class="mmessage-time">{{ message.created|get_weekday_new }}</div>
    {% with message.seen_date as is_seen %}
      <div class="mmessage-user">
        <div class="mmessage-user-seen {% if not is_seen %}hide{% endif %}">
          <div class="avatar avatar--image avatar--14 avatar-seen avatar--square">
            {% if message.owner == offer.admin %}
              <div class="avatar-image"
                  style="background-image: url({{ message.offer.creator|get_avatar:'small' }})"></div>
            {% else %}
              <div class="avatar-image"
                  style="background-image: url({{ message.offer.admin|get_avatar:'small' }})"></div>
            {% endif %}
          </div>
        </div>
      </div>
    {% endwith %}
  </div>
</div>

