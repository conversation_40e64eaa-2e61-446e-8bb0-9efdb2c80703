{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util %}
{% load compress %}
{% block extrahead %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css"
  integrity="sha512-aOG0c6nPNzGk+5zjwyJaoRUgCdOrfSDhmMID2u4+OIslr0GjpLKo7Xm0Ao3xmpM4T8AmIouRkqwj1nrdVsLKEQ=="
  crossorigin="anonymous" />
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
{% compress css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}" />
<link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
{% endcompress %}
{% compress css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}" />
<link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}" />
{% endcompress %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" />
{% compress css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}" />
<link href="{% static 'css/order_step.css' %}" rel="stylesheet">
{% endcompress %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
{% endblock %}

{% block content %}
<div class="container-wrap">
  <div class="container">
    <div class="order-step__main">
      <div class="order-step__heading">
        {% comment %} <h3 class="heading-24">新規プロジェクトを相談 </h3>
        <div class="order-step__heading-description">
          <p class="caption--11">新しいプロジェクトの概要を３ステップで伝えましょう。無料で最適なお見積もりプランの提案が届きます。</p>
          <p class="caption--11"><span class="text--blue">03-6457-1780 </span>へ電話で相談もできます。</p>
        </div> {% endcomment %}
      </div>

      <div class="order-step__content">
        <form class="order-step__form row" id="form-order-step" method="post" action="" enctype="multipart/form-data">
          {% csrf_token %}
          <!-- Step 1 -->
          <div class="order-step__form-tab active">
            <div class="order-step__form-heading">
              <h3 class="heading--18">プロジェクト
                {% comment %} <span class="caption--11">(1/3)</span> {% endcomment %}
              </h3>
              {% comment %} <p class="caption--11">目的物の種類を選びましょう。</p> {% endcomment %}
            </div>
            <div class="order-step__form-group">
              <div class="order-step__option">
                <ul class="order-step__option-list">
                  <li class="col-sm-3 order-step__option-item">
                    <label for="">
                      <input type="radio" class="order-step__option-radio" name="" id="">
                      <a href="">
                        <div class="order-step__option-wrap">
                          <div class="order-step__option-header">
                            <div class="order-step__option-img">
                              <img src="../../static/images/branding.png" alt="">
                            </div>
                          </div>
                          <div class="order-step__option-body">
                            <div class="order-step__option-heading heading--16">ソニックブランディング</div>
                            <div class="order-step__option-description bodytext--13">サウンドロゴ、番組OPED、アプリ起動音、UIサウンドなど。様々な検証を重ねて制作。レギュレーションマニュアルと共に納品します。</div>
                          </div>
                          <div class="order-step__option-footer">
                            <div class="caption--11">ラボ開発スタイルの場合</div>
                            <div class="order-step__option-price">
                              <div class="heading--16">800,000円</div>
                              <div class="caption--11">/人月</div>
                            </div>
                          </div>
                        </div>
                      </a>
                    </label>
                  </li>
                  <li class="col-sm-3 order-step__option-item active">
                    <label for="">
                      <input type="radio" class="order-step__option-radio" name="" id="">
                      <a href="">
                        <div class="order-step__option-wrap">
                          <div class="order-step__option-header">
                            <div class="order-step__option-img">
                              <img src="../../static/images/entertainment.png" alt="">
                            </div>
                          </div>
                          <div class="order-step__option-body">
                            <div class="order-step__option-heading heading--16">エンタメ</div>
                            <div class="order-step__option-description bodytext--13">ゲーム、アニメ、遊技機、イベントなど。必要な音を0→1で制作。 インタラクティブな作品では、仕様にあわせてアセットを組み立て、実装します。</div>
                          </div>
                          <div class="order-step__option-footer">
                            <div class="caption--11">期間契約の場合</div>
                            <div class="order-step__option-price">
                              <div class="heading--16">40,000円</div>
                              <div class="caption--11">/人日</div>
                            </div>
                          </div>
                        </div>
                      </a>
                    </label>
                  </li>
                  <li class="col-sm-3 order-step__option-item">
                    <label for="">
                      <input type="radio" class="order-step__option-radio" name="" id="">
                      <a href="">
                        <div class="order-step__option-wrap">
                          <div class="order-step__option-header">
                            <div class="order-step__option-img">
                              <img src="../../static/images/program.png" alt="">
                            </div>
                          </div>
                          <div class="order-step__option-body">
                            <div class="order-step__option-heading heading--16">番組</div>
                            <div class="order-step__option-description bodytext--13">vlog、ウェビナー、チュートリアルなど。撮影で収録した素材も活用しながら、必要な音を追加し制作。</div>
                          </div>
                          <div class="order-step__option-footer">
                            <div class="caption--11">年間サブスクリプション契約（48本）の場合</div>
                            <div class="order-step__option-price">
                              <div class="heading--16">40,000円 -</div>
                              <div class="caption--11">/週</div>
                            </div>
                          </div>
                        </div>
                      </a>
                    </label>
                  </li>
                </ul>
              </div>
            </div>
            <div class="order-step__form-heading">
              <h3 class="heading--18">制作項目</h3>
              <p class="caption--11">わかる範囲で制作依頼したい項目を選びましょう。</p>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group">
                  <div class="order-step__form-label heading--13">BGM<span class="grey-label--8">[任意]</span></div>
                  <div class="order-step__sub-group">
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="bgm" checked="" value="" index="0" required="True"
                            id="" />新規制作したい
                          <div class="check-mark"></div>
                      </label>
                    </div>
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="bgm" value="" index="1" required="True"
                            id="" />利用権（非独占）ライセンスで用意したい
                          <div class="check-mark"></div>
                      </label>
                    </div>
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="bgm" value="" index="2" required="True"
                          id="" />なし
                        <div class="check-mark"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group">
                  <div class="order-step__form-label heading--13">SE<span class="grey-label--8">[任意]</span></div>
                  <div class="order-step__sub-group">
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="se" checked="" value="" index="0" required="True"
                            id="" />新規制作したい
                          <div class="check-mark"></div>
                      </label>
                    </div>
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="se" value="" index="1" required="True"
                            id="" />なし
                          <div class="check-mark"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-row row">
              <div class="col-sm-12 form-group">
                <div class="order-step__form-label heading--13">VOICE<span class="grey-label--8">[任意]</span></div>
                <div class="order-step__sub-group">
                  <div class="order-step__form-multi">
                    <label class="input-radio">
                      <input type="radio" name="voice" checked="" value="" index="0" required="True"
                          id="" />新規収録したい
                        <div class="check-mark"></div>
                    </label>
                  </div>
                  <div class="order-step__form-multi">
                    <label class="input-radio">
                      <input type="radio" name="voice" value="" index="1" required="True"
                          id="" />収録したものを整音したい
                        <div class="check-mark"></div>
                    </label>
                  </div>
                  <div class="order-step__form-multi">
                    <label class="input-radio">
                      <input type="radio" name="voice" value="" index="2" required="True"
                        id="" />なし
                      <div class="check-mark"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__submit order-step__action">
              {% buttons %}
              <button class="btn btn--primary btn-next" id="btn__next_1">次へ</button>
              {% endbuttons %}
            </div>
          </div>
          <!-- End step 1 -->

          <!-- Step 2 -->
          <div class="order-step__form-tab">
            <div class="order-step__form-heading">
              <h3 class="heading--18">内容
                {% comment %} <span class="caption--11">(2/3)</span> {% endcomment %}
              </h3>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group">
                  <label for="id_message">
                    <div class="order-step__form-label heading--13">メッセージ<span class="blue-label--8">[任意]</span></div>
                  </label>
                  <div class="form-textarea">
                    <textarea name="message" cols="40" rows="10" class="form-textarea"
                      placeholder="添付プロジェクトのサウンド制作をお願いしたいです。資料を確認の上、お見積もりプランをお願いします。折り返しをお待ちしておリます。" maxlength="1000"
                      id="id_message"></textarea>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group">
                  <label for="id_message">
                    <div class="order-step__form-label heading--13">資料<span class="grey-label--8">[任意]</span></div>
                  </label>
                  <div class="order-step_upload-file mattach mattach-form">
                    <div class="mcomment-attached">
                      <div class="mattach-preview-container mattach-preview-container-form">
                        <div class="mattach-previews mattach-previews-form collection">
                          <div class="mattach-template mattach-template-form collection-item item-template">
                            <div class="mattach-info" data-dz-thumbnail="">
                              <div class="mcommment-file">
                                <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                    class="icon icon--sicon-close"></i>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="uploadFile" class="fallback dropzone">
                    </div>
                    <p class="order-step__field-text">※フォルダごとドラッグ＆ドロップでお送りいただけます。</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_budget">
                    <div class="order-step__form-label heading--13">希望バジェット<span class="grey-label--8">[任意]</span></div>
                  </label>
                  <div class="order-step__form-budget">
                    <input type="text" name="budget" placeholder="1,000,000" class="form-control order-step__input-text"
                      maxlength="10" id="id_budget" min="0">
                    <span class="bodytext--13">円（税抜）</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__submit order-step__action">
              {% buttons %}
              <button class="btn btn--tertiary btn-previous" id="btn__previous_2">戻る</button>
              <button class="btn btn--primary btn-next" id="btn__next_2">次へ</button>
              {% endbuttons %}
            </div>
          </div>
          <!-- End step 2 -->

          <!-- Step 3 -->
          <div class="order-step__form-tab">
            <div class="order-step__form-heading">
              <h3 class="heading--18">条件
                {% comment %} <span class="caption--11">(3/3)</span> {% endcomment %}
              </h3>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="deadline_date">
                    <div class="order-step__form-label heading--13">制作期間<span class="grey-label--8">[任意]</span></div>
                  </label>
                  <div class="sform-group__input-group">
                    <input class="sform-control sform-control--input sform-control--full js-daterangepicker"
                      id="deadline_date" type="text" placeholder="yyyy/mm/dd　~　yyyy/mm/dd" autocomplete="off">
                    <label class="sform-group__append" for="deadline_date">
                      <i class="icon icon--undefined"></i>
                      <i class="icon icon--sicon-calendar"></i>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_dob">
                    <div class="order-step__form-label heading--13">最終期限<span class="grey-label--8">[任意]</span></div>
                  </label>
                  <div class="form-row row form-row__mobile">
                    <div class="col-sm-8 form-row__mobile-date">
                      <div class="sform-group__input-group">
                        <input type="text" name="dob" value="2021-10-29 00:00:00" placeholder="yyyy/mm/dd" class="mcalendar mcalendar--small form-control" id="id_dob">
                        <label class="sform-group__append" for="id_dob">
                          <i class="icon icon--undefined"></i>
                          <i class="icon icon--sicon-calendar"></i>
                        </label>
                      </div>
                    </div>
                    <div class="col-sm-4">
                      <select class="input-time" name="hours" id="id_time" value="10:00">
                        <option value="00:00">00:00</option>
                        <option value="01:00">01:00</option>
                        <option value="02:00">02:00</option>
                        <option value="03:00">03:00</option>
                        <option value="04:00">04:00</option>
                        <option value="05:00">05:00</option>
                        <option value="06:00">06:00</option>
                        <option value="07:00">07:00</option>
                        <option value="08:00">08:00</option>
                        <option value="09:00">09:00</option>
                        <option value="10:00">10:00</option>
                        <option value="11:00">11:00</option>
                        <option value="12:00">12:00</option>
                        <option value="13:00">13:00</option>
                        <option value="14:00">14:00</option>
                        <option value="15:00">15:00</option>
                        <option value="16:00">16:00</option>
                        <option value="17:00">17:00</option>
                        <option value="18:00">18:00</option>
                        <option value="19:00">19:00</option>
                        <option value="20:00">20:00</option>
                        <option value="21:00">21:00</option>
                        <option value="22:00">22:00</option>
                        <option value="23:00">23:00</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group">
                  <div class="order-step__form-label heading--13">権利の取り扱い<span class="blue-label--8">[必須]</span></div>
                  <div class="order-step__sub-group">
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="handling" checked="" value="" index="0" required="True"
                            id="" />バイアウト（著作権譲渡）
                          <div class="check-mark"></div>
                      </label>
                    </div>
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="handling" value="" index="1" required="True"
                            id="" />利用権（非独占）
                          <div class="check-mark"></div>
                      </label>
                    </div>
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="handling" value="" index="2" required="True"
                          id="" />その他
                        <div class="check-mark"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-12 form-group">
                  <div class="order-step__form-label heading--13">契約書<span class="blue-label--8">[必須]</span></div>
                  <div class="order-step__sub-group">
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="contract" checked="" value="" index="0"
                          required="True" id="" />ドラフトを提示してほしい
                        <div class="check-mark"></div>
                      </label>
                    </div>
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="contract" value="" index="1" required="True"
                          id="" />ドラフトを提示するので、内容を検討してほしい
                        <div class="check-mark"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__submit order-step__action">
              {% buttons %}
              <button class="btn btn--tertiary btn-previous" id="btn__previous_3">戻る</button>
              <button class="btn btn--primary btn-next" id="btn__next_3">次へ</button>
              {% endbuttons %}
            </div>
          </div>
          <!-- End step 3 -->

          <!-- Step 4 -->
          <div class="order-step__form-tab">
            <div class="order-step__form-heading">
              <h3 class="heading--18">ご連絡先</h3>
            </div>
            <div class="order-step__form-group">
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_fullname">
                    <div class="order-step__form-label heading--13">氏名<span class="blue-label--8">[必須]</span></div>
                  </label>
                  <input type="email" class="form-control" id="id_fullname" placeholder="善里 信哉">
                </div>
              </div>
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_email">
                    <div class="order-step__form-label heading--13">メールアドレス<span class="blue-label--8">[必須]</span></div>
                  </label>
                  <input type="email" class="form-control" id="id_email" placeholder="<EMAIL>">
                </div>
              </div>
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_email_confirm">
                    <div class="order-step__form-label heading--13">メールアドレス（確認）<span class="blue-label--8">[必須]</span>
                    </div>
                  </label>
                  <input type="email" class="form-control" id="id_email_confirm" placeholder="<EMAIL>">
                </div>
              </div>
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_position">
                    <div class="order-step__form-label heading--13">役職・ポジション<span class="grey-label--8">[任意]</span>
                    </div>
                  </label>
                  <input type="email" class="form-control" id="id_position" placeholder="ディレクター">
                </div>
              </div>
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_company_name">
                    <div class="order-step__form-label heading--13">会社名<span class="grey-label--8">[任意]</span></div>
                  </label>
                  <input type="email" class="form-control" id="id_company_name" placeholder="株式会社ソレモ">
                </div>
              </div>
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_website">
                    <div class="order-step__form-label heading--13">WEBサイト<span class="grey-label--8">[任意]</span></div>
                  </label>
                  <input type="email" class="form-control" id="id_website" placeholder="https://soremo.jp">
                </div>
              </div>
              <div class="form-row row">
                <div class="col-sm-4 form-group">
                  <label for="id_phone">
                    <div class="order-step__form-label heading--13">電話番号（日中連絡がとれる番号）<span
                        class="grey-label--8">[任意]</span></div>
                  </label>
                  <input type="email" class="form-control" id="id_phone" placeholder="03-6457-1780">
                </div>
              </div>
              <div class="form-row row">
                <div class="col-sm-12 form-group">
                  <div class="order-step__form-label heading--13">ご希望の連絡方法<span class="grey-label--8">[任意]</span></div>
                  <div class="order-step__sub-group">
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="contact_method" checked="" value="option1" index="0" required="True"
                          id="id_contact_method_1" />メール
                        <div class="check-mark"></div>
                      </label>
                    </div>
                    <div class="order-step__form-multi">
                      <label class="input-radio">
                        <input type="radio" name="contact_method" value="option2" index="1" required="True"
                          id="id_contact_method_2" />電話
                        <div class="check-mark"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="order-step__submit order-step__action">
              {% buttons %}
                <button class="btn btn--tertiary btn-previous" id="btn__previous_4">戻る</button>
                <button class="btn btn--primary btn-next" id="submit-contact">次へ（内容の確認）</button>
              {% endbuttons %}
            </div>
          </div>
          <!-- End step 4 -->
        </form>
      </div>
    </div>
  </div>
</div>

<div class="upload-final-product-file upload-button-wrapper">
  <p>アップロード中</p>
  <div class="fill">
    <div class="process"></div>
  </div>
  <div class="fa fa-check"></div>
</div>

{% include 'messenger/_modal_confirm_step.html' %}

{% endblock content %}
{% block extra_script %}
<script>
  let csrf = '{% csrf_token %}';
</script>
<script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}";</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
  integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="{% url 'javascript-catalog' %}"></script>
    {% compress js inline %}
<script src="{% static 'js/isInViewport.min.js' %}"></script>
<script src="{% static 'js/main.js' %}"></script>
<script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
<script src="{% static 'js/common_variable.js' %}"></script>
<script src="{% static 'js/order_step.js' %}"></script>
    {% endcompress %}
{% endblock %}