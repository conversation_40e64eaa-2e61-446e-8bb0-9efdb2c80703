{% load static %}
{% load util %}
{% load i18n %}
<style>
  .go__to-messenger {
    margin-left: auto;
    cursor: pointer;
    color: #0f9ca9;
  }
</style>

{% block extrahead %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_form_offer.css' %}"/>
{% endblock %}

{% if page == 'search' %}
  <div class="psearch-main">

    <div class="psearch-section psearch-form-wrap">
      <div class="psearch-title">
        <h3 class="sheading sheading--18">送付先</h3>
        <p class="caption--11">ロールやアーティスト名で検索</p>
      </div>
      <div class="psearch-content psearch-form">
        <div class="psearch-filter">
          <div class="tab-content">
            {% for group in skills %}
              <div class="tab-pane {% if forloop.counter0 == 0 %}active{% endif %}" id="tab_{{ group.1.1.2 }}">
                <div class="skills-list-selected">
                  {% for skill in group.1 %}
                    <span class="skills-item" data-id="{{ skill.0 }}">{{ skill.1 }}</span>
                    <input type="checkbox" name="skills" value="" id="" hidden>
                  {% endfor %}
                </div>
              </div>
            {% endfor %}
          <ul class="nav tabs-skill">
            {% for group in skills %}
              <li class="nav-item {% if forloop.counter0 == 0 %}active{% endif %}">
                <a class="nav-link" data-target="#tab_{{ group.1.1.2 }}" data-toggle="tab">{{ group.0 }}</a>
                <div class="notification notification--blue notification--round skill-selected hide"></div>
              </li>
            {% endfor %}
          </ul>
          </div>
        </div>
        <div class="sform-row psearch-keyword">
          <div class="sform-group sform-group--required">
            <div class="sform-group__input-group sform-group__append-before">
              <input class="sform-control sform-control--input sform-control--full" id="pm-search-creator" type="search"
                     placeholder="アーティスト名で検索" required="required"/>
              <i class="icon icon--sicon-close search-delete"></i>
              <label class="sform-group__append" for="pm-search-creator">
                <i class="icon icon--sicon-search"></i>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    {% if user.role == 'admin' %}
      {% include 'messenger/_modal_form_offer.html' with page=page project=project list_scenes=list_scenes list_data_format=list_data_format list_quantity=list_quantity %}
    {% endif %}
    {% include 'direct/_model_confirm_contract.html' %}
  </div>
{% else %}
  <div class="martist role_{{ user.role }}">
    <div class="mrow mrow-custom">
      <div class="mcolumn--wrap" id="mColumnWrap">
        <div class="mcolumn mcolumn--left column-list-offer resize-drag">
{#          <div class="mcolumn-header mheader-toggle">#}
{#            <div class="madd-new">#}
{#              <div class="form-check custom-switch">#}
{#                <label class="form-check-label">#}
{#                  <div class="form-check-group">#}
{#                    <input class="form-check-input switch-checkbox"#}
{#                           {% if offer_status == 'processing' %}checked{% endif %} type="checkbox" name=""#}
{#                           id="offer-filter" data-offers-status="{{ offer_status }}"><span#}
{#                          class="switch-slider"></span>#}
{#                  </div>#}
{#                  <span class="switch-label">{% trans "See also accepted" %}</span>#}
{#                </label>#}
{#              </div>#}
{##}
              {#              {% if user.role == 'admin' %}#}
              {#                <div class="messenger-help" data-toggle="modal" data-target="#modal-guide">#}
              {#                  <i class="icon icon&#45;&#45;sicon-guide"></i>#}
              {#                </div>#}
              {#              {% endif %}#}
{#            </div>#}
{#              <div id="mainSidebar">#}
{#                  {% if user.role != 'master_client' %}#}
{#                      <a href="javascript:void(0)" class="open-close-nav nav-close" onclick="openCloseNav(this)">#}
{#                          <span class="material-symbols-rounded icon-balance-wallet">account_balance_wallet</span>#}
{#                      </a>#}
{#                  {% endif %}#}
{##}
{#              </div>#}
{##}
{#          </div>#}
          <div class="mcolumn-content">
            <div class="mlist-wrap mscrollbar mscrollbar--vertical custom-list-wrap">
{#                <div class="pd-search">#}
{#              <div class="pd-search-keyword">#}
{#                <div class="sform-group sform-group--required">#}
{#                  <div class="sform-group__icon">#}
{#                    <i class="icon icon--sicon-search"></i>#}
{#                  </div>#}
{#                  <div class="sform-group__input-group sform-group__append-before">#}
{#                    <div class="sform-group__icon">#}
{#                      <i class="icon icon--sicon-search"></i>#}
{#                    </div>#}
{#                    <input class="sform-control sform-control--input sform-control--full" id="pm-search"#}
{#                           type="search" placeholder="検索" required="required"><i#}
{#                          class="icon icon--sicon-close search-delete"></i>#}
{#                    <label class="sform-group__append" for="pm-search"><i#}
{#                            class="icon icon--sicon-search"></i>#}
{#                    </label>#}
{#                  </div>#}
{#                </div>#}
{#              </div>#}
{#            </div>#}
                <div class="block-search-offer">
                    <span class="material-symbols-rounded c-icon-search">search</span>
                    <input class="input-search-offer" type="search" id="pm-search" placeholder="スレッドを検索">
                    <span class="material-symbols-rounded c-icon-close-small search-delete-icon hide">close_small</span>
                </div>
              <div class="mlist">

{#                {% if user.role == 'admin' or user.role == 'master_admin' %}#}
{#                  <div class="messenger-add">#}
{#                    <div class="messenger-add-row">#}
{#                      <div class="messenger-add-icon">#}
{#                        <i class="icon icon--sicon-add-cirlce"></i>#}
{#                        <p>{% trans "Deliver an offer" %}</p>#}
{#                      </div>#}
{#                    </div>#}
{#                  </div>#}
{#                {% endif %}#}
                <div class="list--offers-project">
{#                  {% include 'messenger/_list_offers.html' with user=user offers=offers %}#}
                  {% include 'messenger/_list_offers_new.html' with user=user offers=offers %}
                </div>
                <div class="list--offers-search">

                </div>
              </div>
            </div>
          </div>
              <div class="resize-handle"></div>
        </div>
        <div class="mcolumn mcolumn--main DM-box-container dm-block-message">
        </div>
      </div>


      <div class="mcolumn mcolumn--right">
      </div>
        {% if user.role == 'admin' or user.role == 'master_admin' %}
            {% include 'top/_comment_container_offer_2.html' %}
        {% endif %}
      {% include 'messenger/_modal_guide.html' %}
      {% include 'messenger/_modal_delete_offer.html' %}
      {% include 'direct/_model_confirm_contract.html' %}
    </div>
  </div>

{% endif %}

{% if user.role == 'admin' or user.role == 'master_admin' %}
  {% include 'messenger/_modal_form_offer.html' with page=page project=project %}
  {% include 'messenger/_modal_over_budget.html' %}
  {% include 'messenger/_modal_over_budget.html' %}
  {% include 'messenger/_modal_edit_offer.html' %}
{% endif %}

{% block extra_script %}
    <script type="text/javascript" src="{% static 'js/component_input.js' %}"></script>
{% endblock %}
