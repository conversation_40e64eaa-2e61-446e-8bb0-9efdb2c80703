{% load static %}
{% load util %}
  {% with title.last_version as scene %}
    {% if scene %}
      <div class="cvideo">
        {% with scene|get_authorization_user:user as view_only %}
        <div class="project-delivery-item-content" data-scene-id="{{ scene.pk }}" data-scene-title-id="{{ title.pk }}">
          <div class="cvideo__thumb" data-link="{{ scene|get_url_scene }}"  data-file-type="{{scene.is_audio_file}}">
            {% with video_info=scene.movie|get_video_url_with_fallback %}
            <video width="100%" height="128px" poster="{{ scene|get_thumbnail }}" preload="auto"
                   data-video-src="{{ video_info.url }}"
                   data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                   data-fallback-src="{% if scene.movie %}{{ scene.movie.url }}{% endif %}">
              <source src="{{ video_info.url }}" type="video/mp4"/>
            </video>
            {% endwith %}
          </div>
          <div class="cvideo__heading">
            <div class="cvideo__title" data-scene-title-id="{{ title.pk }}">{{ title.title }}</div>
             <div class="scene-title__action">
               <a class="scene-title-button unbookmark_item" href="javascript:void(0)" data-toggle="modal" data-target="#unbookmark-scene">
                 <i class="icon icon--sicon-trash"></i>
               </a>
            </div>
          </div>
          <div class="cvideo__meta">
            <div class="cvideo__rating">
              {% with title|get_rating:user.role as rating %}
                <div class="stars cannot-check selected"
                     data-rating="{{ rating }}"><span><a
                        class="star-1" href="javascript:void(0)" data-value="1">1</a><a class="star-2"
                                                                                        href="javascript:void(0)"
                                                                                        data-value="2">2</a><a
                        class="star-3" href="javascript:void(0)" data-value="3">3</a><a class="star-4"
                                                                                        href="javascript:void(0)"
                                                                                        data-value="4">4</a><a
                        class="star-5"
                        href="javascript:void(0)"
                        data-value="5">5</a></span>
                </div>
              {% endwith %}
            </div>
            <div class="cvideo__date-time">
              <div class="cvideo__date">{{ title.updated_at|get_updated_time }}</div>
              <div class="cvideo__time">{{ title.updated_at|date:"H:i" }}</div>
            </div>
          </div>
        </div>
        {% endwith %}
      </div>
    {% endif %}
  {% endwith %}
