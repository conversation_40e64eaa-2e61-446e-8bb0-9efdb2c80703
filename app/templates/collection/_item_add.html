{% load static %}
{% load util %}
{% if bookmark.type_bookmark == 'scene' %}
  {% with bookmark.title as title %}
    {% with title.last_version as scene %}
      {% if scene %}
        <div class="cvideo">
          {% with scene|get_authorization_user:user as view_only %}
            <div
                    class="project-delivery-item-content"
                    data-scene-title-id="{{ title.pk }}"
                    data-scene-id="{{ scene.pk }}">
              <div class="cvideo__thumb" data-file-type="{{ scene.is_audio_file }}">
                {% with video_info=scene.movie|get_video_url_with_fallback %}
                <video width="100%" height="128px" poster="{{ scene|get_thumbnail }}" preload="metadata"
                       data-video-src="{{ video_info.url }}"
                       data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                       data-fallback-src="{% if scene.movie %}{{ scene.movie.url }}{% endif %}">
                  <source src="{{ video_info.url }}"
                          type="video/mp4"/>
                </video>
                {% endwith %}
                <div class="cvideo__bookmark">
                  <span class="icon--bookmark"></span>
                </div>
              </div>

              <div class="cvideo__heading">
                <div data-scene-title-id="{{ title.pk }}">{{ title.title }}</div>
              </div>

              <div class="cvideo__meta">
                <div class="cvideo__rating">
                  {% with title|get_rating:user.role as rating %}
                    <div class="stars cannot-check selected"
                         data-rating="{{ rating }}"><span><a
                            class="star-1" href="javascript:void(0)" data-value="1">1</a><a class="star-2"
                                                                                            href="javascript:void(0)"
                                                                                            data-value="2">2</a><a
                            class="star-3" href="javascript:void(0)" data-value="3">3</a><a class="star-4"
                                                                                            href="javascript:void(0)"
                                                                                            data-value="4">4</a><a
                            class="star-5"
                            href="javascript:void(0)"
                            data-value="5">5</a></span>
                    </div>
                  {% endwith %}
                </div>
                <div class="cvideo__date-time">
                  <div class="cvideo__date">{{ title.updated_at|get_updated_time }}</div>
                  <div class="cvideo__time">{{ title.updated_at|date:"H:i" }}</div>
                </div>
              </div>
            </div>
          {% endwith %}
        </div>
      {% endif %}
    {% endwith %}
  {% endwith %}
{% else %}
  {% include 'collection/_sale.html' with sale_content=bookmark.sale user=user modal_add='modal_add' %}
{% endif %}
