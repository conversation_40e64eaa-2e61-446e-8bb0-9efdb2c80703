{#  html for sale content#}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}
{% with sale_content|get_artist_of_sale_content as artist %}
{% with artist.get_link_profile as profile_url %}
    <div class="list-new-works__item-container" data-id="{{ sale_item.pk }}" data-order="{{ sale_item.order }}"
        data-sale-id="{% if sale_content.child.first %}{{ sale_content.child.first.pk }}{% else %}{{ sale_content.pk }}{% endif %}" data-artist="{{ artist.pk }}">

    {% for album_variation in sale_content|get_audios:user %}
    {% with album_variation|get_file_type:user as file_type %}
        <div class="list-new-works__media gallery__item" style="{{ sale_content|get_thumbnail_sale_content:user }}"
            data-type="{{ sale_content.last_published_version.content_type }}" data-artist="{{ artist.get_display_name }}"
            data-file-type="{{ file_type }}">
            <div class="list-search__item-playpause {% if file_type != 'audio' %}btn-preview-album{% endif%}"></div>
            <audio
                preload="none"
                class="gallery__item-banner"
                src="{{ album_variation|get_audio:user }}"
                data-album="{{ album_variation.pk }}"
                data-name="{{ sale_content|get_title_sale_content:user }}"
                data-file-type="{{ file_type }}"></audio>
            {% if file_type == 'movie' %}
                {% with video_url=album_variation|get_audio:user %}
                {% with video_info=video_url|get_video_url_with_fallback %}
                <video preload="none" style="width: 100%;height: 100%; max-height: 100%; background: white; display: none; cursor: pointer"
                                                   data-video-src="{{ video_info.url }}"
                            data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                            data-fallback-src="{% if video_url %}{{ video_url }}{% endif %}">
                    <source src="{{ video_info.url }}">
                </video>
                {% endwith %}
                {% endwith %}
            {% endif %}
            <div class="list-new-works__content_hover hide">
                <div class="list-new-works__heading"><div class="list-new-works__title heading--18">{{ sale_content|get_title_sale_content:user }}</div><span class="list-new-works__title__created-year label--8">{{ sale_content.last_published_version.created_year }}</span></div>
                <div class="list-new-works__artist bodytext--13">{{ artist.get_display_name }}</div>
                <div class="list-new-works__credit caption--11 break-line">{{ sale_content.last_published_version.credit }}</div>
                <div class="list-new-works__desc bodytext--13 break-line">{{ sale_content.last_published_version.desc }}</div>
            </div>
        </div>

    {% endwith %}
    {% endfor %}


    <div class="list-new-works__content"
        data-description="{{ sale_content.last_published_version.desc }}"
        data-created-year="{{ sale_content.last_published_version.created_year }}"
        data-sale-type="{{sale_content.last_published_version.sale_type}}"
        data-max-price="{{ sale_content.last_published_version.max_price }}"
        data-min-price="{{ sale_content.last_published_version.price }}"
        data-credit="{{ sale_content.last_published_version.credit }}"
        data-customizable-sale-setting = "{{ sale_content.last_published_version.customizable_sale_setting }}">
        <div class="list-new-works__title">
            <span>{{ sale_content|get_title_sale_content:user }}</span>
            <div class="scene-title__action">
                <a class="scene-title-button scene-title__move" href="javascript:void(0)">
                <i class="fas fa-arrows-alt"></i>
                </a>
                <a class="scene-title-button item_bookmark_delete" href="javascript:void(0)" data-toggle="modal" data-target="#delete-scene-bookmark">
                <i class="icon icon--sicon-trash"></i>
                </a>
            </div>
        </div>
        <div class="list-new-works__sub-title" data-url="{{ profile_url }}">{{ artist.get_display_name }}</div>
        <div class="list-new-works__hint">{{ sale_content|get_sale_type_text:user }}</div>
    </div>

    {% if modal_add == 'modal_add' %}
        <div class="cvideo__bookmark">
            <span class="icon--bookmark"></span>
        </div>
    {% endif%}

    </div>
{% endwith %}
{% endwith %}

