{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load i18n %}
{% load compress %}

{% block extrahead %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/creator_style.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/collection.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/components/utils.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
  <main class="owner-top">
    <div class="container">
      <div class="collection-wrap">
        <div class="tab-nav">
          <div class="tab-header">
            <h3 class="tab-header__heading heading--wrap">コレクション</h3>
            <p class="tab-header__description account__field-hint">あなたのお気に入りを集めて、次に活かしましょう</p>
          </div>
          <div class="tab-section">
            <div class="tab-section__wrap">
              <div class="tab-container">
                <div class="collapse-header">
                  <div class="pd-chapter__toggle active"></div>
                </div>
                <div class="tab-section__list-root">
{#                  {% include 'collection/_list_all_bookmark.html' with bookmark_scenes=bookmark_scenes user=user %}#}
                  <div class="tab-section__list ">
                    {% for list_bookmark in list_bookmarks %}
                      {% include 'collection/list_bookmark.html' with list_bookmark=list_bookmark user=user %}
                    {% endfor %}
                  </div>

                  <div class="pd-add-chapter" style="display: none">
                    <a class="pd-add-chapter__content button-upload-video-scene-title" href="#create-chapter">
                      <div class="pd-add-chapter__icon" data-toggle="modal" data-target="#create-new-modal">
                        <i class="icon icon--sicon-plus"></i>
                      </div>
                      <div class="pd-add-chapter__text account__field-hint">テーマを追加</div>
                    </a>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Modal create new list -->
  <div class="modal" id="create-new-modal" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header create-modal__heading">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <i class="icon icon--sicon-close"></i>
          </button>
        </div>
        <div class="modal-body create-modal__wrap">
          <div class="form-group">
            <input class="form-control input-title-name" type="text" placeholder="テーマを追加" required/>
          </div>
        </div>
        <div class="modal-footer create-modal__action">
          <a class="btn btn--tertiary btn-modal-cancel btn-new-cancel" href="javascript:void(0)" role="button" data-dismiss="modal">{% trans "cancel" %}</a>
          <a class="btn btn--primary btn-modal-accept btn-new-create button--gradient disabled" href="javascript:void(0)" role="button">OK</a>
        </div>
      </div>
    </div>
  </div>
  <!-- End modal create new list -->

  <!-- Modal edit list -->
  <div class="modal" id="edit-list-name-modal" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header create-modal__heading">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <i class="icon icon--sicon-close"></i>
          </button>
        </div>
        <div class="modal-body create-modal__wrap">
          <div class="form-group">
            <input class="form-control input-title-name" type="text" placeholder="テーマ名" required/>
          </div>
        </div>
        <div class="modal-footer create-modal__action">
          <a class="btn btn--tertiary btn-modal-cancel btn-edit-cancel" href="javascript:void(0)" role="button" data-dismiss="modal">{% trans "cancel" %}</a>
          <a class="btn btn--primary bootbox-edit-accept btn-modal-accept btn-edit-change" href="javascript:void(0)" role="button">OK</a>
        </div>
      </div>
    </div>
  </div>
<!-- End modal edit list -->

<!-- Modal delete list -->
  <div class="modal modal-bookmark-delete" id="delete-list-bookmark" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <p class="modal-title-down">本当にテーマを丸ごと削除しますか？</p>
        </div>
        <div class="modal-footer delete-modal__action">
          <a class="btn btn--primary btn-modal-accept btn-cancel-delete-list" href="javascript:void(0)" data-dismiss="modal" role="button">{% trans "cancel" %}</a>
          <a class="btn btn--tertiary btn-modal-cancel btn-delete-list" href="javascript:void(0)" role="button">削除</a>
        </div>
      </div>
    </div>
  </div>
<!-- End delete  list -->

<!-- Modal delete scene -->
  <div class="modal modal-bookmark-delete" id="delete-scene-bookmark" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <p class="modal-title-down">テーマからこのシーンを削除しますか？</p>
        </div>
        <div class="modal-footer delete-modal__action">
          <a class="btn btn--primary btn-modal-accept btn-cancel-delete-scene" data-dismiss="modal" href="javascript:void(0)" role="button">{% trans "cancel" %}</a>
          <a class="btn btn--tertiary btn-modal-cancel btn-delete-scene" href="javascript:void(0)" role="button">OK</a>
        </div>
      </div>
    </div>
  </div>
<!-- End delete  list -->

<!-- Modal unbookmark scene -->
  <div class="modal modal-bookmark-delete" id="unbookmark-scene" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <p class="modal-title-down">お気に入りからこのシーンを削除しますか？<br/> シーンそのものは、プロジェクトから削除されずに残っています</p>
        </div>
        <div class="modal-footer delete-modal__action">
          <a class="btn btn--primary btn-modal-accept btn-cancel-unbookmark-scene" data-dismiss="modal" href="javascript:void(0)" role="button">{% trans "cancel" %}</a>
          <a class="btn btn--tertiary btn-modal-cancel btn-unbookmark-scene" href="javascript:void(0)" role="button">OK</a>
        </div>
      </div>
    </div>
  </div>
<!-- End delete  list -->

<div class="modal fade" id="add-item-modal" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header add-modal__heading">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="icon icon--sicon-close"></i>
        </button>
      </div>
      <div class="modal-body add-modal__wrap">
        <div class="form-group add-modal__search">
          <span class="icon icon--sicon-search"></span>
          <input class="form-control search-input" type="text" placeholder="シーンを検索" id="search-input"/>
        </div>
        <div class="pd-chapter__list ui-sortable">

        </div>
        <div class="cvideo__not-found">Video not found。</div>
        <div class="cvideo__existed">Video does not exist。</div>
      </div>
      <div class="modal-footer add-modal__action">
        <a class="btn btn--tertiary btn-new-cancel" href="#" role="button" data-dismiss="modal">{% trans "cancel" %}</a>
        <a class="btn btn--primary button--gradient btn-new-add disabled" href="#" role="button">OK</a>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_script %}
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
    {% compress js inline %}
  <script src="{% static 'js/main.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/top_page.js' %}"></script>
  <script src="{% static 'js/utils.js' %}"></script>
  <script src="{% static 'js/project_detail.js' %}"></script>
  <script src="{% static 'js/collection.js' %}"></script>
    {% endcompress %}
{% endblock %}
