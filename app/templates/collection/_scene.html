{% load static %}
{% load util %}
{% with title.last_version as scene %}
  {% if scene %}
    <div class="cvideo">
      {% with scene|get_authorization_user:user as view_only %}
      <div class="project-delivery-item-content" data-scene-id="{{ scene.pk }}" data-scene-title-id="{{ title.pk }}">
          <div class="video-ratting-container">
            <div class="cvideo__rating" style="width: 100%; height: 24px; display: flex; justify-content: flex-end; align-items: center;">
              {% with title|get_rating:user.role as rating %}
                <div class="stars cannot-check selected"
                     data-rating="{{ rating }}"><span><a
                        class="star-1" href="javascript:void(0)" data-value="1">1</a><a class="star-2"
                                                                                        href="javascript:void(0)"
                                                                                        data-value="2">2</a><a
                        class="star-3" href="javascript:void(0)" data-value="3">3</a><a class="star-4"
                                                                                        href="javascript:void(0)"
                                                                                        data-value="4">4</a><a
                        class="star-5"
                        href="javascript:void(0)"
                        data-value="5">5</a></span>
                </div>
              {% endwith %}
            </div>
          </div>
          <div class="cvideo__thumb" data-link="{{ scene|get_url_scene }}"  data-file-type="{{scene.is_audio_file}}" style="margin: 0;">
            <!-- <video width="100%" height="144px" poster="{{ scene|get_thumbnail }}" preload="auto">
              <source src="{{ scene.movie.url }}" type="video/mp4"/>
            </video> -->

            {% if not scene|get_schedule_out_of_date %}
              {% with video_info=scene.movie|get_video_url_with_fallback %}
              <video width="100%" height="144px" poster="{{ scene|get_thumbnail }}" preload="auto" style="border-radius: 6px;"
                     data-video-src="{{ video_info.url }}"
                     data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                     data-fallback-src="{% if scene.movie.name %}{{ scene.movie.url }}{% endif %}">
                <source src="{{ video_info.url }}" type="video/mp4">
              </video>
              {% endwith %}
            {% else %}
              <div class="thumb-schedule-video" style="{% if scene.movie.name %}background-image: url({{ scene|get_thumbnail }});mix-blend-mode: luminosity;opacity: 0.5;{% endif %}">
              </div>
            {% endif %}
          </div>
          <div class="cvideo__heading" style="margin: 0; line-height: 200%;">
            <div class="cvideo__title" data-scene-title-id="{{ title.pk }}" style="line-height: 200%;">{{ title.title }}</div>
             <div class="scene-title__action">
              <a class="scene-title-button scene-title__move" href="javascript:void(0)">
                <i class="fas fa-arrows-alt"></i>
              </a>
               <a class="scene-title-button item_bookmark_delete" href="javascript:void(0)" data-toggle="modal" data-target="#delete-scene-bookmark">
                <i class="icon icon--sicon-trash"></i>
              </a>
            </div>
          </div>

          <div class="cvideo__meta">
            <div class="cvideo__date-time" style="padding: 0; margin: 0;">
              {% if scene.movie.name %}
                {% if scene|get_schedule_out_of_date %}
                  <div class="cvideo__date">配信予定: {{scene|get_schedule_out_of_date}}</div>
                {% else %}
                  <div class="cvideo__date">{{ title|get_schedule_out_of_date:'last-update' }}</div>
                {% endif %}
              {% else %}
                {% if not scene|get_schedule_out_of_date or scene|get_schedule_out_of_date == 'まもなくリリース' %}
                  <div class="release-soon">まもなくリリース</div>
                {% else %}
                  <div class="cvideo__date">配信予定: {{scene|get_schedule_out_of_date}}</div>
                {% endif %}
              {% endif %}
              
            </div>
          </div>
        </div>
      {% endwith %}
    </div>
  {% endif %}
{% endwith %}

