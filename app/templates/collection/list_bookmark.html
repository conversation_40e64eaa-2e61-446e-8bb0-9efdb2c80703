{% load util %}
{% load static %}

<div class="tab-section__item" data-item-id="{{list_bookmark.pk}}">
  <div class="tab-section__header can-not-drag">
    <div class="tab-section__title account__form-heading">{{list_bookmark.title}}</div>
    <div class="tab-section__action">
      <button class="btn--edit" data-toggle="modal" data-target="#edit-list-name-modal">
        <i class="icon icon--sicon-pencil"></i>
      </button>
      <button class="btn--delete" data-toggle="modal" data-target="#delete-list-bookmark">
        <i class="icon icon--sicon-trash"></i>
      </button>
    </div>
    <div class="pd-chapter__line"></div>
  </div>
  <div class="tab-section__main">
    <div class="pd-chapter__list ui-sortable nice-scroll">
      <a class="pd-chapter__add" data-toggle="modal" data-target="#add-item-modal">
          <div class="pd-chapter__add-content">
              <div class="pd-chapter__add-icon"><i class="icon icon--sicon-plus"></i></div>
              <div class="account__field-hint pd-chapter__add-text">シーンを追加</div>
          </div>
      </a>
      {% for book_mark in list_bookmark.item_ids.all %}
        {% include 'collection/_item_bookmarked.html' with book_mark=book_mark user=user %}
      {% endfor %}
    </div>
  </div>
</div>
