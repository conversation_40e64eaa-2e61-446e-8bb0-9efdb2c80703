{% extends "base_nofooter_refactor.html" %}
{% load static %}

{% block extrahead %}
  <script>
    $('html').attr('prefix', 'og: http://ogp.me/ns# fb: http://ogp.me/ns/ fb# website: http://ogp.me/ns/website#')
  </script>
  <meta property="og:url" content="https://soremo.jp/" />
  <meta property="og:type" content="website" />
  <meta property="og:title" content="SOREMO" />
  <meta property="og:description" content="動画を送るとぴったりの音を返してくれるWEBサービス" />
  <meta property="og:site_name" content="SOREMO" />
  <meta property="og:image" content="{{ request.scheme }}://{{ request.META.HTTP_HOST }}{% static 'images/OGP_logo.png' %}" />
  <meta property="og:image:width" content="400" />
  <meta property="og:image:height" content="230" />
{% endblock extrahead %}

{% block content %}
    <link href="https://fonts.googleapis.com/css?family=Cabin:400,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Montserrat:900" rel="stylesheet">

    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>

    <div id="notfound">
        <div class="notfound">
            <div class="notfound-404">
                <h3>お探しのページは見つかりません。</h3>
                <h1><span>4</span><span>0</span><span>4</span></h1>
            </div>
            <h2>お探しのページはアクセスできないにあるか、もしくはURLにタイプミスがないか再度ご確認ください。</h2>
        </div>
    </div>

    <style>
    * {
      -webkit-box-sizing: border-box;
              box-sizing: border-box;
    }

    #notfound {
      position: relative;
      height: 92vh;
    }

    #notfound .notfound {
      position: absolute;
      left: 50%;
      top: 50%;
      -webkit-transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%);
    }

    .notfound {
      max-width: 520px;
      width: 100%;
      line-height: 1.4;
      text-align: center;
    }

    .notfound .notfound-404 {
      position: relative;
      height: 240px;
    }

    .notfound .notfound-404 h1 {
      font-family: 'A+mfCv-AXISラウンド 50 L StdN', "Noto Sans Japanese", 'Montserrat', sans-serif;
      position: absolute;
      left: 50%;
      top: 50%;
      -webkit-transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%);
      font-size: 240px;
      font-weight: 900;
      color: #262626;
      text-transform: uppercase;
      letter-spacing: -40px;
      margin: 12px 10px 10px -20px;
    }

    .notfound .notfound-404 h1>span {
      text-shadow: -8px 0px 0px #fff;
    }

    .notfound .notfound-404 h3 {
      font-family: 'Cabin', sans-serif;
      position: relative;
      font-size: 16px;
      font-weight: 700;
      text-transform: uppercase;
      color: #262626;
      margin: 0px;
      letter-spacing: 3px;
      padding-left: 6px;
    }

    .notfound h2 {
      font-family: 'Cabin', sans-serif;
      font-size: 20px;
      font-weight: 400;
      text-transform: uppercase;
      color: #000;
      margin-top: 0px;
      margin-bottom: 25px;
    }

    @media only screen and (max-width: 767px) {
      .notfound .notfound-404 {
        height: 200px;
      }
      .notfound .notfound-404 h1 {
        font-size: 200px;
      }
    }

    @media only screen and (max-width: 480px) {
      .notfound .notfound-404 {
        height: 162px;
      }
      .notfound .notfound-404 h1 {
        font-size: 162px;
        height: 150px;
        line-height: 162px;
      }
      .notfound h2 {
        font-size: 16px;
      }
    }
    </style>
{% endblock %}
