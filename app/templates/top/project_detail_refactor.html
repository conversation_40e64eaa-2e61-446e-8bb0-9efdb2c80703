{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load i18n %}
{% load compress %}
{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.css" />
    {% comment %} <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" /> {% endcomment %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css" />
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/components/utils.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_list.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/top_admin.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/video_modal.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/message.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/message_file.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal_contract.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/upload_contract.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}"/>
    {% comment %} <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/> {% endcomment %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/input_component.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/offer_search.css' %}"/>
    {% comment %} <link rel="stylesheet" type="text/css" href="{% static 'css/button_components.css' %}"/> {% endcomment %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/comment_project_new.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_budget_log.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/schedule_calendar.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail_new.css' %}"/>
    <link rel="stylesheet" href="{% static 'css/flatpickr_soremo.css' %}">
    {% endcompress %}

{% endblock %}

{% block content %}

    {% with project.start_time|get_range_date_format:project.end_time as date_project %}
        <main class="owner-top prdt {% if not request|is_pc %} on-mobile{% endif %} {% if user.role == 'admin' %}is-admin{% endif %}"
              data-title-page="{{ title_page }}" {% if date_project %}data-start-date="{{ date_project.to_date }}" data-end-date="{{ date_project.from_date }}"{% endif %} id="main-container">
            
        <div class="container">
            <div class="new-video-menu">

                <div class="project-list">
                    <div class="project-item {% if not request|is_pc %} on-mobile{% endif %}"
                         data-project-id="{{ project.pk }}" data-user-role="{{ role }}"
                         data-done-scene="{{ project.current_heart }}"
                         data-unresolved-comment="{{ project.product_owner_comments.count }}" data-page="top_page"  data-max-scene="{{ project.max_scene }}" data-project-endtime="{{ project.end_time|get_updated_datetime }}"
                          {% if neareast_milestone %} data-milestone-name="{{ neareast_milestone.name }}" data-milestone-time="{{ neareast_milestone.time|get_updated_datetime }}"{% endif %}>
{#                        <a href="{% url 'app:top_page' %}?force=true{% if is_done_project %}?is_done=1&force=true{% endif %}" style="display: none">#}
{#                            {% include 'top/_product_banner.html' with project=project user=user type_page='top_page' project_list=False is_pc=request|is_pc show_staff=True %}#}
{#                        </a>#}
                    {% include 'top/_top_app_bar_refactor.html' with project=project %}
                    {% include 'top/_navigation_bar_refactor.html' with project=project user=user type_page='top_page' project_list=False is_pc=request|is_pc show_staff=True %}
                      <div class="popover-overlay"></div>
                      <div class="popover project-video-item">
                      </div>
                        <div class="project-item__content refactor" id="projectItemDetail">
                            {% include 'top/_left_sidebar_refactor.html' with product_scenes=product_scenes project=project%}
                            <!-- <div class='loader' style='display: none;'>
                                <img src="{% static 'images/icon-loading-outline-w.svg' %}" alt="">
                            </div> -->
                            <div class="project-tab project-tab-new active" data-tab="new">
                                <div class="project-item__product-comment">


                                    </div>
                                </div>

                                <div class="project-tab project-tab-product-comment" data-tab="product-comment">
                                    <div class="project-item__video-list">

                                    </div>
                                </div>

                                <div class="project-tab project-tab-progress {% if view_only %} view_only cannot-check{% endif %}"
                                     data-tab="progress">
                                    <div class="tab--video-progress tab--video-all">
                                        {% if user.role  == 'admin' %}
                                            <div class="tab--video-watting_checkback processing-list-item d-none-chapter chapter-block"></div>
                                        {% endif %}
                                    </div>

                                </div>

                          <div class="project-tab project-tab-messenger refactor"
                                id="projectTabMessenger"
                               data-tab="messenger">
                            <div class="tab--messenger-artist refactor">
                            </div>
                          </div>

                                <div class="project-setting">
                                    <div class="project-setting__filter">
                                        <div class="project-setting__filter-item active" data-show="owner">詳細</div>
                                        <div class="project-setting__filter-item" data-show="director">詳細</div>
                                        <div class="project-setting__filter-item" data-show="staff">スタッフ</div>
                                    </div>
                                    <div class="setting-content-tab">
                                        <!-- ajax will append data here -->
                                    </div>
                                </div>
                                {% include 'product/project_budget_info.html' %}
                            </div>
                        </div>
                        <div class="project-member-setting-modal modal fade" id="project-member-setting"
                             role="dialog" style="display: none;">
                            <!-- ajax will append data here -->
                        </div>
                    </div>
                </div>
            </div>

            {% include 'top/_modal_show_folder.html' %}

            <div class="modal fade share-modal" role="dialog" id="shareModal" style="z-index: 9999;">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h6 class="modal-title"></h6>
                            <button class="close" data-dismiss="modal"
                                    type="button">閉じる
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="video-time-slider-item">
                                <div class="video-time-slider-content">
                                    <div class="video-time-slider-start">00:00
                                    </div>
                                    <div class="video-time-slider-bar"></div>
                                    <div class="video-time-slider-end hide">00:00
                                    </div>
                                </div>
                                <div class="video-time-slider-label">
                                    <div class="video-time-slider-label-start">
                                        開始位置を指定
                                    </div>
                                    <div class="video-time-slider-label-end hide">
                                        終了位置も指定
                                    </div>
                                </div>
                            </div>
                            <div class="modal-share-link">
                                <div class="video-item-share-input">
                                    <input class="video-share-link"
                                           id="video-share-link" type="text"
                                           name="video-share-link"
                                           placeholder=""
                                           value="">
                                </div>
                                <a class="button button--text button--text-primary video-item-share-btn"
                                   href="javascript:;" role="button">URLをコピー</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" role="dialog" id="processingSceneModal">
                <div class="modal-dialog modal-lg" role="document" style="width: 100%">
                    <div class="modal-content">
                        <div class="modal-body container">
                        </div>
                    </div>
                </div>
            </div>

            {% include 'messenger/_modal_open_file.html' %}

            {% include 'top/_modal_ACR_check.html' %}

            {% include 'messenger/_modal_contract_offer.html' %}

            <div class="modal fade" id="searchModal" role="dialog" style="overflow: hidden;">
                <div class="modal-content" style="height: 100vh">
                    <div class="modal-body container" style="max-height: 80vh; background: white">
                        <h2>検索結果</h2>
                    </div>
                </div>
            </div>
            {% include 'top/_edit_chapter_name.html' with csrf=csrf_token project=project %}
            {% if role == 'admin' %}
                {% include 'top/_create_new_chapter.html' with csrf=csrf_token project=project %}
                {% include 'top/_upload_form.html' with csrf=csrf_token user=user titles=titles project=project %}
                {% include 'top/_modal_create_edit_scene.html' with csrf=csrf_token user=user titles=titles project=project %}
            {% endif %}
            <div class="modal fade video-modal" id="video-modal" role="dialog" style="z-index: 1001;">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-body">
                        </div>
                    </div>

                    <div class="upload-button-wrapper">
                        <div class="fill">
                            <div class="process"></div>
                        </div>
                        <p>アップロード中...</p>
                    </div>
                </div>
            </div>
            <div class="upload-final-product-file upload-button-wrapper">
                <p>アップロード中...</p>
                <div class="fill">
                    <div class="process"></div>
                </div>
                <div class="fa fa-check"></div>
            </div>

        </main>
    {% endwith %}
    {% include 'top/_modal_setting_setting.html' with project_list=False %}
    {% compress js inline %}
    <!-- <script type="text/javascript" src="{% static 'js/init_socket_messenger_refactor.js' %}"></script> -->

    <script>
        {% comment %} let csrf = '{% csrf_token %}'; {% endcomment %}
        let default_thumb = "{% static 'images/messenger-thumb.png' %}";
        let user = {{ user.id }};
        let is_pc = '{{ request|is_pc }}';
        let user_role = '{{ role }}';
        let project_position = '{{ project_position }}';
        let isOwner = {{ is_owner|yesno:"true,false" }};
        let canBeDone = {{ can_be_done|yesno:"true,false" }};
        let from_refer = '{{ from_refer }}' === 'true'; 
        if (is_logged_in === 'True') {
            initSocket({{ user.id }});
        }
    </script>
    <script src="{% static 'js/isInViewport.min.js' %}"></script>
    <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
    <script src="{% static 'js/top_page_admin.js' %}"></script>
    <script src="{% static 'js/top_page_member.js' %}"></script>
    <script src="{% static 'js/utils.js' %}"></script>
    <script src="{% static 'js/sortable.js' %}"></script>
    <script src="{% static 'js/video_modal.js' %}"></script>
    {% endcompress %}
{% endblock %}

{% block extra_script %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.2.5/moment-timezone-with-data.min.js" integrity="sha512-OvYMp/zgYHU6ojgDxmtCLsgHBtSrX34Cx/01Tv2bFfbJnlVMbIv5TVVTQ+0wU4GHVDIlHAMMsjFBpTzDnSqIiA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script  src="https://cdnjs.cloudflare.com/ajax/libs/jsmediatags/3.9.5/jsmediatags.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>

  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.4/gsap.min.js"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
    {% endcompress %}
  <script src="{% url 'javascript-catalog' %}"></script>
        {% compress js inline %}
  <script src="{% static 'js/common_variable.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/offer_modal.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/utils.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/soremo_refactor.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/upload_contract.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/soremo.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/action_banner.js' %}"></script>
  {% comment %} <script type="text/javascript" src="{% static 'js/main.js' %}"></script> {% endcomment %}
  <script type="module" src="{% static 'js/project_screen_performance.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/interactjs@1.10.11/dist/interact.min.js"></script>
    <script type="text/javascript">
    interact('.resize-drag')
                    .resizable({
                        edges: {left: false, right: true, bottom: false, top: false},
                        listeners: {
                            move(event) {
                                let {x, y} = event.target.dataset;

                                x = (parseFloat(x) || 0) + event.deltaRect.left;
                                y = (parseFloat(y) || 0) + event.deltaRect.top;

                                Object.assign(event.target.style, {
                                    width: `${event.rect.width}px`,
                                    height: `${event.rect.height}px`,
                                    transform: `translate(${x}px, ${y}px)`
                                });

                                Object.assign(event.target.dataset, {x, y});
                            }
                        },
                        modifiers: [
                            // 以下のコードはリサイズの最小/最大サイズを制限します
                            interact.modifiers.restrictSize({
                                min: {width: 320},
                                max: {width: 640}
                            })
                        ]
                    });
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.6.347/pdf.min.js" integrity="sha512-Z8CqofpIcnJN80feS2uccz+pXWgZzeKxDsDNMD/dJ6997/LSRY+W4NmEt9acwR+Gt9OHN0kkI1CTianCwoqcjQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.6.347/pdf.worker.min.js" integrity="sha512-lHibs5XrZL9hXP3Dhr/d2xJgPy91f2mhVAasrSbMkbmoTSm2Kz8DuSWszBLUg31v+BM6tSiHSqT72xwjaNvl0g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/project_detail_refactor.js' %}"></script>
  <script src="{% static 'js/messenger_owner.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/messenger_artist.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/component_input.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/component_expand.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/search_creator.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/component_tab.js' %}"></script>
  <script src="{% static 'js/load_more_message_refactor.js' %}"></script>
  <script src="{% static 'js/top_page_refactor.js' %}"></script>
  <script src="{% static 'js/project_budget_log.js' %}"></script>
  <script type="text/javascript">
     let imageAudioPath = window.location.origin + "{% static 'images/audio-item-thumbnail.png' %}";
     let imagePDFPath = window.location.origin + "{% static 'images/pdf-item-thumbnail.png' %}";
  </script>
  <script src="{% static 'js/schedule_calendar.js' %}"></script>
  <script src="{% static 'js/project_detail_new_refactor.js' %}"></script>
  <script src="{% static 'js/flatpickr_soremo.js' %}"></script>
    {% endcompress %}
{% endblock %}
