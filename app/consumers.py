from channels.generic.websocket import AsyncWebsocketConsumer
import json

class MessengerConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_group_name = self.scope['path'].replace('/ws/messenger/', '')

        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()
        self.create_status_user()

    async def disconnect(self, close_code):
        # Leave room group
        await self.update_status_disconnect()
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'offer_message',
                'message': message
            }
        )

    # Receive message from room group
    async def chat_message(self, event):

        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'event': event
        }))

    # Receive message from room group
    async def offer_message(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'event': event
        }))


    async def update_rating(self, event):
        await self.send(text_data=json.dumps({
            'event': event
        }))


    async def scene_message(self, event):
        await self.send(text_data=json.dumps({
            'event': event
        }))

    def create_status_user(self):
        try:
            from app.models import AuthUser, UserOnlineStatus
            user_id = self.scope['path'].split('/')[len(self.scope['path'].split('/')) - 1]
            user = AuthUser.objects.filter(pk=user_id).first()
            UserOnlineStatus.objects.create(user=user, status=True)
        except Exception as e:
            return

    async def update_status_disconnect(self):
        try:
            from app.models import AuthUser, UserOnlineStatus
            user_id = self.scope['path'].split('/')[len(self.scope['path'].split('/')) - 1]
            user = AuthUser.objects.filter(pk=user_id).first()
            online_status = UserOnlineStatus.objects.filter(user=user, status=True).last()
            if online_status:
                online_status.status = False
                online_status.save()
        except Exception as e:
            return print(e)