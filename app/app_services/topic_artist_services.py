from django.db.models import Prefetch
from django.urls import reverse, reverse_lazy

from accounts.models import C<PERSON>, AuthUser, CreatorProfile
from app.models import SelectionGallery, SaleContent
LOAD_MORE_LIST = 3


def prefetch_topics(topics):
    topics = topics.prefetch_related(
        Prefetch('selections',
                 queryset=SelectionGallery.objects.filter()
                 .prefetch_related(
                     Prefetch('sale_contents',
                              queryset=SaleContent.objects.filter().prefetch_related(
                                  Prefetch('profile',
                                           queryset=CreatorProfile.objects.filter().prefetch_related(
                                               Prefetch('creator',
                                                        queryset=Creator.objects.filter().prefetch_related(
                                                            Prefetch('user',
                                                                     queryset=AuthUser.objects.filter()))))))))))
    return topics
