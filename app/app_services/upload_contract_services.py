import io
import os
import base64
import re

import json
import requests
import datetime
import pdfkit
from django.conf import settings
from django.db.models import Q
from pyvirtualdisplay import Display
from django.urls import reverse_lazy
from asgiref.sync import async_to_sync
from django.template.loader import get_template
from django.template.loader import render_to_string
from django.core.files.uploadedfile import InMemoryUploadedFile

from app import tasks
from accounts.models import AuthUser
from channels.layers import get_channel_layer
from app.models import FormContractAndPlan, OfferProduct, ProductMessageFile
# from app.services import generate_jwt_token_invite_message
from voice.utils import pdfkit_generate_pdf

UNIT_MAPPING = {
    '1': '人月',
    '2': '曲',
    '3': '演出',
    '4': '名',
    '5': '時間',
    '6': '式',
    '7': '人日',
}

WORK_TYPE_MAPPING = {
    '1': '進行管理',
    '2': '開発',
    '3': 'ディレクター',
    '4': 'QA',
    '5': '楽曲',
    '6': '効果音',
    '7': '実演',
    '8': 'スタジオ',
    '9': 'デザイン',
    '10': '撮影',
    '11': '動画編集',
    '12': '作編曲',
    '13': '編曲',
    '14': '作詞',
    '15': '歌唱',
    '16': '演奏',
    '17': 'エンジニア',
}
DATE_FORMAT = '%Y/%-m/%-d'
DATE_TIME_FORMAT = '%Y/%-m/%-d %H:%M'


def sanitize_filename(filename):
    """
    ファイル名として使用できない文字を安全な文字に置換する
    
    Args:
        filename (str): サニタイズするファイル名
        
    Returns:
        str: サニタイズされたファイル名
    """
    # Windowsで使用できない文字
    invalid_chars = r'[<>:"/\\|?*]'
    
    # 無効な文字をアンダースコアに置換
    sanitized = re.sub(invalid_chars, '_', filename)
    
    # 先頭・末尾の空白とピリオドを削除
    sanitized = sanitized.strip(' .')
    
    # 連続するアンダースコアを1つにまとめる
    sanitized = re.sub(r'_{2,}', '_', sanitized)
    
    # ファイル名が空になった場合のフォールバック
    if not sanitized:
        sanitized = 'document'
    
    # ファイル名の長さ制限（拡張子を除いて200文字まで）
    max_length = 200
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized


def render_to_quotation_pdf(context_dict={}, code_name=''):
    sanitized_code_name = sanitize_filename(code_name) if code_name else ''
    file_name = f'御見積書{sanitized_code_name}_{context_dict["instance"].contract_code}.pdf'

    api_url = settings.VIVLIOSTYLE_SERVER + "/generate-pdf"
    payload = {
        "config": f"module.exports = {{title: 'Quotation', entry: ['index.html']}};",
        "html_input": {
            "template_info": {
                "name": "quotation"
            },
            "data_info": {
                "contract": {
                    "contract_code": code_name,
                    "pre_deadline": context_dict["pre_deadline"],
                    "valid_date": context_dict["valid_date"],
                    "release_time": context_dict["release_time"],
                    "subject": context_dict["subject"],
                    "total_without_tax": context_dict["total_without_tax"],
                    "tax": context_dict["tax"],
                    "total_money": context_dict["total_money"],
                    "note": context_dict["note"],
                    "work_content": [
                        {
                            "work_type_dsp": item["work_type_dsp"],
                            "price": item["price"],
                            "quantity": item["quantity"],
                            "unit_dsp": item["unit_dsp"],
                            "amount_money": item["amount_money"],
                            "note": item["note"]
                        } for item in context_dict["work_content"]
                    ],
                    "producer_info": {
                        "company_name": context_dict["producer_info"]["company_name"],
                        "address":  context_dict["producer_info"]["address"],
                        "job_title":  context_dict["producer_info"]["job_title"],
                        "fullname":  context_dict["producer_info"]["fullname"]
                    },
                    "instance": {
                        "owner_info": context_dict["instance"].owner_infor,
                        "contract_code": context_dict["instance"].contract_code,
                        "delivery_place": context_dict["instance"].delivery_place,
                        "pick_up_method": context_dict["instance"].pick_up_method
                    }
                }
            }
        }
    }


    # Make the POST request to the Vivliostyle API
    response = requests.post(api_url, json=payload)
    if response.status_code == 200:
        with open(file_name, 'wb') as file:
            file.write(response.content)

    return file_name


def render_to_contract_pdf(context_dict={}, code_name=''):
    print("context_dict:", context_dict)
    sanitized_code_name = sanitize_filename(code_name) if code_name else ''
    file_name = f'契約書{sanitized_code_name}_{context_dict["instance"].contract_code}.pdf'

    api_url = settings.VIVLIOSTYLE_SERVER + "/generate-pdf"
    payload = {
        "config": f"module.exports = {{title: 'Contract', entry: ['index.html']}};",
        "html_input": {
            "template_info": {
                "name": "contract"
            },
            "data_info": {
                "contract": {
                    "contract_code": context_dict["instance"].contract_code,
                    "producer_name": context_dict["producer_name"],
                    "owner_name": context_dict["owner_name"],
                    "subject": context_dict["subject"],
                    "deadline": context_dict["deadline"],
                    "start_schedule": context_dict["start_schedule"],
                    "end_schedule": context_dict["end_schedule"],
                    "total_money": context_dict["total_money"],
                    "note": context_dict["note"],
                    "semi_delegate": context_dict["semi_delegate"],
                    "work_content": [
                        {
                            "work_type_dsp": item["work_type_dsp"],
                            "quantity": item["quantity"],
                            "unit_dsp": item["unit_dsp"],
                            "note": item["note"]
                        } for item in context_dict["work_content"]
                    ],
                    "instance": {
                        "semi_delegate": context_dict["instance"].semi_delegate,
                        "allow_public_contract": context_dict["instance"].allow_public_contract,
                        "delivery_format": context_dict["instance"].delivery_format,
                        "delivery_place": context_dict["instance"].delivery_place,
                        "pick_up_method": context_dict["instance"].pick_up_method
                    },
                    "owner_info": {
                        "address": context_dict["owner_info"]["address"],
                        "company_name": context_dict["owner_info"]["company_name"],
                        "job_title": context_dict["owner_info"]["job_title"],
                        "fullname": context_dict["owner_info"]["fullname"]
                    },
                    "producer_info": {
                        "address": context_dict["producer_info"]["address"],
                        "company_name": context_dict["producer_info"]["company_name"],
                        "job_title": context_dict["producer_info"]["job_title"],
                        "fullname": context_dict["producer_info"]["fullname"]
                    }
                }
            }
        }
    }

    # Make the POST request to the Vivliostyle API
    response = requests.post(api_url, json=payload)
    if response.status_code == 200:
        with open(file_name, 'wb') as file:
            file.write(response.content)

    return file_name


def render_to_offer_contract_pdf(template_src, contract_time, context_dict={}, code_name=''):
    date_now = contract_time.strftime("%y%m%d") 
    sanitized_code_name = sanitize_filename(code_name) if code_name else ''
    file_name = f'契約書{sanitized_code_name}_{date_now}.pdf'

    api_url = settings.VIVLIOSTYLE_SERVER + "/generate-pdf"
    payload = {
        "config": f"module.exports = {{title: 'Creator Subcontract', entry: ['index.html']}};",
        "html_input": {
            "template_info": {
                "name": "creator_subcontract"
            },
            "data_info": {
                "contract": {
                    "contract_code": code_name,
                    "producer_name": context_dict["producer_name"],
                    "artist_name": context_dict["artist_name"],
                    "owner_name": context_dict["owner_name"],
                    "approved": context_dict["approved"]
                },
                "offer": {
                    "producer": {
                        "enterprise": context_dict["producer"].enterprise,
                        "position": context_dict["producer"].position,
                        "display_name": context_dict["producer"].get_display_name()
                    } ,
                    "artist": {
                        "enterprise": context_dict["artist"].enterprise,
                        "position": context_dict["artist"].position,
                        "display_name": context_dict["artist"].get_display_name()
                    },
                    "contract_time": context_dict["offer"].contract_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "contract_display": context_dict["offer"].get_contract_display(),
                    "scenes": context_dict["offer"].scenes,
                    "message": context_dict["offer"].message,
                    "data_format": context_dict["offer"].data_format,
                    "delivery_place": context_dict["offer"].delivery_place,
                    "pick_up_method": context_dict["offer"].pick_up_method,
                    "deadline": context_dict["deadline"],
                    "start_time": context_dict["start_time"],
                    "end_time": context_dict["end_time"],
                    "reward": context_dict["offer"].reward,
                    "note": context_dict["offer"].note,
                    "allow_sub_contracting": context_dict["offer"].allow_subcontracting,
                    "project": {
                        "name": context_dict['project'].name if context_dict['project'].name else context_dict['project'].code_name
                    }
                }
            }
        }
    }

    # Make the POST request to the Vivliostyle API
    response = requests.post(api_url, json=payload)
    if response.status_code == 200:
        with open(file_name, 'wb') as file:
            file.write(response.content)

    return file_name


def generate_plan(request, offer, action_check, real_name, contract_plan_form, key_file, form_contract_and_plan_id):
    mapping_params = {
        'plan': {
            'type_file': ProductMessageFile.PLAN,
            'action': 'upload_file_offer',
            'is_contract': False,
            'call_function': render_to_quotation_pdf,
        },
    }
    actions = []
    real_names = []
    key_files = []
    offer_files = []
    form_type_values = ['plan']

    context = contract_plan_form.cleaned_data
    context.update(work_content=contract_plan_form.instance.get_value_attr_json('work_content'),
                   producer_info=contract_plan_form.instance.get_value_attr_json('producer_info'),
                   instance=contract_plan_form.instance)
    format_date(context)
    total_without_tax = calculator(context['work_content'])
    producer = offer.project.get_master_producer_is_artist()
    producer_name = '株式会社ソレモ'
    producer_info = contract_plan_form.instance.get_value_attr_json('producer_info')
    if producer_info['company_name']:
        producer_name = producer_info['company_name']
    else:
        if producer_info['fullname']:
            producer_name = producer_info['fullname']
    
    producer_name = (producer_name[:30] + '..') if len(producer_name) > 30 else producer_name
    context.update(
        producer_name=producer_name, 
        total_without_tax=total_without_tax, 
        tax=total_without_tax * 0.1, 
        total_money=total_without_tax * 1.1
    )

    for form_type in form_type_values:
        actions.append(mapping_params[form_type]['action'])
        offer_file = ProductMessageFile(real_name=real_name, type_file=mapping_params[form_type]['type_file'],
                                               offer=offer, user=request.user, form_object=contract_plan_form.instance)
        if contract_plan_form.cleaned_data['creation_method'] == FormContractAndPlan.GENERATE_METHOD:
            code_name = f'_{offer.project.code_name}' if offer.project.code_name and offer.project.code_name != '' else ''
            sanitized_code_name = sanitize_filename(code_name) if code_name else ''
            pdf_file = mapping_params[form_type]['call_function'](context, sanitized_code_name)
            real_names, key_files = set_file_from_local(offer_file, pdf_file, real_names, key_files)
        else:
            set_file_upload(offer_file, key_file, form_contract_and_plan_id)
            key_files.append(key_file)
            real_names.append(real_name)

        offer.condition = OfferProduct.STATUS_UPLOADED_PLAN
        action_check = 'upload_plan'

        offer_files.append(offer_file)

    return action_check, actions, real_names, key_files, offer_files


def generate_contract(request, offer, action_check, real_name, contract_plan_form, key_file, form_contract_and_plan_id):
    mapping_params = {
        'contract': {
            'type_file': ProductMessageFile.CONTRACT,
            'action': 'upload_file_offer',
            'is_contract': True,
            'call_function': render_to_contract_pdf,
        }
    }
    actions = []
    real_names = []
    key_files = []
    offer_files = []
    form_type_values = ['contract']

    context = contract_plan_form.cleaned_data
    context.update(work_content=contract_plan_form.instance.get_value_attr_json('work_content'),
                   producer_info=contract_plan_form.instance.get_value_attr_json('producer_info'),
                   owner_info=contract_plan_form.instance.get_value_attr_json('owner_info'),
                   instance=contract_plan_form.instance)
    format_date(context)
    total_without_tax = calculator(context['work_content'])
    producer = offer.project.get_master_producer_is_artist()
    producer_name = '株式会社ソレモ'
    semi_delegate = False
    if contract_plan_form.instance.semi_delegate:
        semi_delegate = contract_plan_form.instance.semi_delegate
    
    producer_info = contract_plan_form.instance.get_value_attr_json('producer_info')
    if producer_info['company_name']:
        producer_name = producer_info['company_name']
    else:
        if producer_info['fullname']:
            producer_name = producer_info['fullname']
    owner = contract_plan_form.instance.get_value_attr_json('owner_info')
    owner_name = ''
    if owner['company_name']:
        owner_name = owner['company_name']
    else:
        owner_name = owner['fullname']
    producer_name = (producer_name[:30] + '..') if len(producer_name) > 30 else producer_name
    owner_name = (owner_name[:30] + '..') if len(owner_name) > 30 else owner_name
    context.update(
        semi_delegate=semi_delegate, 
        producer_name=producer_name, 
        owner_name=owner_name, 
        total_without_tax=total_without_tax, 
        tax=total_without_tax * 0.1, 
        total_money=total_without_tax * 1.1,
    )
    for form_type in form_type_values:
        actions.append(mapping_params[form_type]['action'])
        offer_file = ProductMessageFile(real_name=real_name, type_file=mapping_params[form_type]['type_file'],
                                               offer=offer, user=request.user, form_object=contract_plan_form.instance)
        if contract_plan_form.cleaned_data['creation_method'] == FormContractAndPlan.GENERATE_METHOD:
            code_name = f'_{offer.project.code_name}' if offer.project.code_name and offer.project.code_name != '' else ''
            sanitized_code_name = sanitize_filename(code_name) if code_name else ''
            pdf_file = mapping_params[form_type]['call_function'](context, sanitized_code_name)
            real_names, key_files = set_file_from_local(offer_file, pdf_file, real_names, key_files)
        else:
            set_file_upload(offer_file, key_file, form_contract_and_plan_id)
            key_files.append(key_file)
            real_names.append(real_name)

        if mapping_params[form_type]['is_contract']:
            offer.condition = OfferProduct.STATUS_CONTRACT
            action_check = 'upload_contract'

        offer_files.append(offer_file)

    return action_check, actions, real_names, key_files, offer_files


def render_confirming_artist_contract(offer, project, approved=False, update_time=False):
    try:
        context = {}

        if not offer.contract_time and approved and offer.accept_time:
            offer.contract_time = offer.accept_time

        if not offer.contract_time or update_time:
            offer.contract_time = datetime.datetime.today()

        start_time = offer.start_time.strftime(DATE_FORMAT) if offer.start_time else ''
        end_time = offer.deadline.strftime(DATE_FORMAT) if offer.deadline else ''
        deadline = offer.deadline.strftime(DATE_FORMAT + ' %H:%M') if offer.deadline else ''

        if offer.range_deadline:
            dates = offer.range_deadline.split(' - ')
            if len(dates) == 2:
                start_time = dates[0]
                end_time = dates[1]
            

        producer_name = ''
        producer = offer.admin
        if producer.role == AuthUser.MASTERADMIN:
            if producer.enterprise:
                producer_name = producer.enterprise
            else:
                producer_name = producer.fullname
        else:
            if producer.stage_name and producer.stage_name != '':
                producer_name = producer.stage_name
            elif producer.stage_name_en and producer.stage_name_en != '':
                producer_name = producer.stage_name_en
            else:
                producer_name = producer.fullname
        
        
        artist_name = ''
        artist = offer.creator
        if artist.stage_name and artist.stage_name != '':
            artist_name = artist.stage_name
        elif artist.stage_name_en and artist.stage_name_en != '':
            artist_name = artist.stage_name_en
        else:
            artist_name = artist.fullname

        owner = project.get_owner()
        owner_name = '株式会社ソレモ'
        offer_product = project.offer_product.first()
        if offer_product:
            last_contract = offer_product.get_offer_last_contract()
            last_plan = offer_product.get_offer_last_plan()
            if project.client_name and project.client_name != '':
                owner_name = project.client_name
            elif last_contract and last_contract.is_approved:
                contract_owner_info = last_contract.get_value_attr_json('owner_info')
                if contract_owner_info.get('company_name'):
                    owner_name = contract_owner_info.get('company_name')
                elif contract_owner_info.get('fullname'):
                    owner_name = contract_owner_info.get('fullname')
                elif last_plan and last_plan.is_approved and last_plan.owner_infor:
                    owner_name = last_plan.owner_infor
                elif owner:
                    if owner.enterprise:
                        owner_name = owner.enterprise
                    else:
                        owner_name = owner.get_display_name()
            elif last_plan and last_plan.is_approved and last_plan.owner_infor:
                owner_name = last_plan.owner_infor
            elif owner:
                if owner.enterprise:
                    owner_name = owner.enterprise
                else:
                    owner_name = owner.get_display_name()
        
        producer_name = (producer_name[:30] + '..') if len(producer_name) > 30 else producer_name
        owner_name = (owner_name[:30] + '..') if len(owner_name) > 30 else owner_name
        artist_name = artist_name = (artist_name[:30] + '..') if len(artist_name) > 30 else artist_name

        offer_product = project.offer_product.all()
        note = None
        if offer_product.exists():
            offer_product = offer_product.first()
            file_contract_plan = offer_product.get_latest_form_contract_file()
            if file_contract_plan.exists():
                form_contract_plan = file_contract_plan.first().form_object
                if form_contract_plan and form_contract_plan.note:
                    note = form_contract_plan.note

        context.update(approved=approved, start_time=start_time, end_time=end_time, deadline=deadline,
                       producer_name=producer_name, owner_name=owner_name, artist_name=artist_name,
                       producer=producer, artist=artist, owner=owner, offer=offer, project=project, note=note)

        code_name = f'_{project.code_name}' if project.code_name and project.code_name != '' else ''
        sanitized_code_name = sanitize_filename(code_name) if code_name else ''
        pdf_file = render_to_offer_contract_pdf('pdf/artist_contract.html', offer.contract_time, context, sanitized_code_name)

        with open(pdf_file, 'rb') as f:
            output = io.BytesIO(f.read())
            offer.contract_file_raw = InMemoryUploadedFile(output, 'FileField', pdf_file, 'application/pdf', output.getbuffer().nbytes, None)

            offer.save()
        os.remove(os.path.join(settings.BASE_DIR, pdf_file))
    except:
        return False
    return True


def render_artist_contract(offer, approved=False, update_time=False):
    try:
        context = {}

        if not offer.contract_time and approved and offer.accept_time:
            offer.contract_time = offer.accept_time

        if not offer.contract_time or update_time:
            offer.contract_time = datetime.datetime.today()

        start_time = offer.start_time.strftime(DATE_FORMAT) if offer.start_time else ''
        end_time = offer.deadline.strftime(DATE_FORMAT) if offer.deadline else ''
        deadline = offer.deadline.strftime(DATE_FORMAT + ' %H:%M') if offer.deadline else ''

        if offer.range_deadline:
            dates = offer.range_deadline.split(' - ')
            if len(dates) == 2:
                start_time = dates[0]
                end_time = dates[1]

        project = offer.project
        producer_name = ''
        producer = offer.admin
        if producer.role == AuthUser.MASTERADMIN:
            if producer.enterprise:
                producer_name = producer.enterprise
            else:
                producer_name = producer.fullname
        else:
            if producer.stage_name and producer.stage_name != '':
                producer_name = producer.stage_name
            elif producer.stage_name_en and producer.stage_name_en != '':
                producer_name = producer.stage_name_en
            else:
                producer_name = producer.fullname
        
        
        artist_name = ''
        artist = offer.creator
        if artist.stage_name and artist.stage_name != '':
            artist_name = artist.stage_name
        elif artist.stage_name_en and artist.stage_name_en != '':
            artist_name = artist.stage_name_en
        else:
            artist_name = artist.fullname

        owner = project.get_owner()
        owner_name = '株式会社ソレモ'
        offer_product = project.offer_product.first()
        last_contract = offer_product.get_offer_last_contract() if offer_product and hasattr(offer_product, "get_offer_last_contract") else None
        last_plan = offer_product.get_offer_last_plan() if offer_product and hasattr(offer_product, "get_offer_last_plan") else None
        if project.client_name and project.client_name != '':
            owner_name = project.client_name
        elif last_contract and last_contract.is_approved:
            contract_owner_info = last_contract.get_value_attr_json('owner_info')
            if contract_owner_info.get('company_name'):
                owner_name = contract_owner_info.get('company_name')
            elif contract_owner_info.get('fullname'):
                owner_name = contract_owner_info.get('fullname')
            elif last_plan and last_plan.is_approved and last_plan.owner_infor:
                owner_name = last_plan.owner_infor
            elif owner:
                if owner.enterprise:
                    owner_name = owner.enterprise
                else:
                    owner_name = owner.get_display_name()
        elif last_plan and last_plan.is_approved and last_plan.owner_infor:
                owner_name = last_plan.owner_infor
        elif owner:
            if owner.enterprise:
                owner_name = owner.enterprise
            else:
                owner_name = owner.get_display_name()

        producer_name = (producer_name[:30] + '..') if len(producer_name) > 30 else producer_name
        owner_name = (owner_name[:30] + '..') if len(owner_name) > 30 else owner_name
        artist_name = artist_name = (artist_name[:30] + '..') if len(artist_name) > 30 else artist_name

        offer_product = project.offer_product.all()
        note = None
        if offer_product.exists():
            offer_product = offer_product.first()
            file_contract_plan = offer_product.get_latest_form_contract_file()
            if file_contract_plan.exists():
                form_contract_plan = file_contract_plan.first().form_object
                if form_contract_plan and form_contract_plan.note:
                    note = form_contract_plan.note

        context.update(approved=approved, start_time=start_time, end_time=end_time, deadline=deadline,
                       producer_name=producer_name, owner_name=owner_name, artist_name=artist_name,
                       producer=producer, artist=artist, owner=owner, offer=offer, project=project, note=note)

        code_name = f'_{project.code_name}' if project.code_name and project.code_name != '' else ''
        sanitized_code_name = sanitize_filename(code_name) if code_name else ''
        pdf_file = render_to_offer_contract_pdf('pdf/artist_contract.html', offer.contract_time, context, sanitized_code_name)

        with open(pdf_file, 'rb') as f:
            output = io.BytesIO(f.read())
            offer.contract_file = InMemoryUploadedFile(output, 'FileField', pdf_file, 'application/pdf', output.getbuffer().nbytes, None)

            offer.save()
        os.remove(os.path.join(settings.BASE_DIR, pdf_file))
    except:
        return False
    return True


def regen_plan(request, offer, action_check, real_name, key_file, form_contract_and_plan):
    mapping_params = {
        'plan': {
            'type_file': ProductMessageFile.PLAN,
            'action': 'new_message',
            'is_contract': False,
            'call_function': render_to_quotation_pdf,
        }
    }
    actions = []
    real_names = []
    key_files = []
    offer_files = []
    form_type_values = ['plan']

    context = form_contract_and_plan.__dict__
    context.update(work_content=form_contract_and_plan.get_value_attr_json('work_content'),
                   producer_info=form_contract_and_plan.get_value_attr_json('producer_info'),
                   owner_info=form_contract_and_plan.get_value_attr_json('owner_info'),
                   instance=form_contract_and_plan,
                   owner_approved=True)
    format_date(context)
    total_without_tax = calculator(context['work_content'])
    producer = offer.project.get_master_producer_is_artist()
    producer_name = '株式会社ソレモ'
    semi_delegate = False
    if form_contract_and_plan.semi_delegate:
        semi_delegate = form_contract_and_plan.semi_delegate
    
    producer_info = context['producer_info']
    if producer_info['company_name']:
        producer_name = producer_info['company_name']
    else:
        if producer:
            producer_name = producer_info['fullname']
    
    producer_name = (producer_name[:30] + '..') if len(producer_name) > 30 else producer_name
    context.update(
        semi_delegate=semi_delegate, 
        producer_name=producer_name, total_without_tax=total_without_tax, tax=total_without_tax * 0.1, total_money=total_without_tax * 1.1,
    )
    for form_type in form_type_values:
        actions.append(mapping_params[form_type]['action'])
        offer_file = form_contract_and_plan.product_message_files.filter(type_file='5')[0]
        code_name = f'_{offer.project.code_name}' if offer.project.code_name and offer.project.code_name and offer.project.code_name != '' else ''
        sanitized_code_name = sanitize_filename(code_name) if code_name else ''
        pdf_file = mapping_params[form_type]['call_function'](context, sanitized_code_name)
        real_names, key_files = set_file_from_local(offer_file, pdf_file, real_names, key_files)

        offer_file.file = key_files[0]
        offer_file.save()

        offer_files.append(offer_file)

    return action_check, actions, real_names, key_files, offer_files


def regen_contract_and_plan(request, offer, action_check, real_name, key_file, form_contract_and_plan):
    mapping_params = {
        'plan': {
            'type_file': ProductMessageFile.PLAN,
            'action': 'new_message',
            'is_contract': False,
            'call_function': render_to_quotation_pdf,
        },
        'contract': {
            'type_file': ProductMessageFile.CONTRACT,
            'action': 'upload_file_offer',
            'is_contract': True,
            'call_function': render_to_contract_pdf,
        }
    }
    actions = []
    real_names = []
    key_files = []
    offer_files = []
    form_type_values = ['contract']
    context = form_contract_and_plan.__dict__
    context.update(work_content=form_contract_and_plan.get_value_attr_json('work_content'),
                   producer_info=form_contract_and_plan.get_value_attr_json('producer_info'),
                   owner_info=form_contract_and_plan.get_value_attr_json('owner_info'),
                   instance=form_contract_and_plan,
                   owner_approved=True)
    format_date(context)
    total_without_tax = calculator(context['work_content'])
    producer = offer.project.get_master_producer_is_artist()
    producer_name = '株式会社ソレモ'
    semi_delegate = False
    if form_contract_and_plan.semi_delegate:
        semi_delegate = form_contract_and_plan.semi_delegate
    
    producer_info = context['producer_info']
    if producer_info['company_name']:
        producer_name = producer_info['company_name']
    else:
        if producer:
            producer_name = producer_info['fullname']
    owner = context['owner_info']
    owner_name = ''
    if owner['company_name']:
        owner_name = owner['company_name']
    else:
        owner_name = owner['fullname']
    producer_name = (producer_name[:30] + '..') if len(producer_name) > 30 else producer_name
    owner_name = (owner_name[:30] + '..') if len(owner_name) > 30 else owner_name
    context.update(
        semi_delegate=semi_delegate, producer_name=producer_name, owner_name=owner_name,
        total_without_tax=total_without_tax, tax=total_without_tax * 0.1, total_money=total_without_tax * 1.1,
    )
    for form_type in form_type_values:
        actions.append(mapping_params[form_type]['action'])
        offer_file = form_contract_and_plan.product_message_files.filter(type_file='2')[0]
        code_name = f'_{offer.project.code_name}' if offer.project.code_name and offer.project.code_name and offer.project.code_name != '' else ''
        sanitized_code_name = sanitize_filename(code_name) if code_name else ''
        pdf_file = mapping_params[form_type]['call_function'](context, sanitized_code_name)
        real_names, key_files = set_file_from_local(offer_file, pdf_file, real_names, key_files)

        offer_file.file = key_files[0]
        offer_file.save()

        offer_files.append(offer_file)

    return action_check, actions, real_names, key_files, offer_files


def set_file_from_local(offer_file, file_path, real_names, key_files):
    real_names.append(file_path)
    offer_file.real_name = file_path
    with open(file_path, 'rb') as f:
        output = io.BytesIO(f.read())
        offer_file.file = InMemoryUploadedFile(output, 'FileField', file_path, 'application/pdf', output.getbuffer().nbytes, None)
        offer_file.save()
    key_files.append(offer_file.file)
    os.remove(os.path.join(settings.BASE_DIR, file_path))
    return real_names, key_files


def set_file_upload(offer_file, key_file, form_contract_and_plan_id):
    if (not key_file or key_file == 'undefined') and form_contract_and_plan_id:
        # get last file
        form_contract_and_plan = FormContractAndPlan.objects.get(pk=form_contract_and_plan_id)
        last_offer_file = form_contract_and_plan.product_message_files.first()
        offer_file.file = last_offer_file.file
        offer_file.real_name = last_offer_file.real_name
    else:
        offer_file.file = key_file
    offer_file.save()


def calculator(work_content):
    total_without_tax = 0
    for item in work_content:
        item['amount_money'] = item['price'] * item['quantity']
        item['work_type_dsp'] = WORK_TYPE_MAPPING[item['work_type']] if item['work_type'] in WORK_TYPE_MAPPING.keys() else item['work_type_dsp']
        item['unit_dsp'] = UNIT_MAPPING[item['unit']] if item['unit'] in UNIT_MAPPING.keys() else item['unit_dsp']
        total_without_tax += item['amount_money']
    return total_without_tax


def get_image_file_as_base64_data(filepath):
    with open(filepath, 'rb') as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def format_date(context_dict):
    context_dict['deadline_without_time'] = context_dict['deadline'].strftime(DATE_FORMAT) if context_dict['deadline'] else ''
    context_dict['deadline'] = context_dict['deadline'].strftime(DATE_TIME_FORMAT) if context_dict['deadline'] else ''
    context_dict['start_schedule'] = context_dict['start_schedule'].strftime(DATE_FORMAT) if context_dict['start_schedule'] else ''
    context_dict['end_schedule'] = context_dict['end_schedule'].strftime(DATE_FORMAT) if context_dict['end_schedule'] else ''
    context_dict['release_time'] = context_dict['release_time'].strftime(DATE_FORMAT)
    context_dict['valid_date'] = context_dict['valid_date'].strftime(DATE_FORMAT) if context_dict['valid_date'] else ''
    context_dict['pre_deadline'] = context_dict['pre_deadline'].strftime(DATE_FORMAT) if context_dict['pre_deadline'] else ''


def send_mail_service(actions, request, offer, real_offer, project, action_check):
    receiver_ids = list(project.authuser_set.filter(Q(productuser__is_owner='1') &
                                                    (Q(is_active=True) |
                                                     (Q(is_active=False) &
                                                      Q(is_verify=False) &
                                                      Q(last_login__isnull=True)))).values_list('id', flat=True))
    if not receiver_ids:
        return

    scheme = request.scheme
    host = settings.HOST
    url = reverse_lazy("app:top_project_detail", kwargs={'pk': project.pk}) + f'?tab=messenger&offer={real_offer.pk}'

    # upload only contract or bill
    if len(actions) == 1 and action_check in ['upload_contract', 'upload_bill']:
        tasks.send_email_when_upload_contract.delay(str(offer.pk), receiver_ids, scheme, host, url, action_check)
    # upload only plan
    elif len(actions) == 1 and action_check == 'upload_plan':
        tasks.send_email_when_upload_plan.delay(str(offer.pk), receiver_ids, scheme, host, url)
    # upload both - plan and contract
    elif len(actions) == 2 and action_check == 'upload_contract':
        tasks.send_email_when_upload_plan_and_contract.delay(str(offer.pk), receiver_ids, scheme, host, url)


def get_current_form_contract_and_plan_service(product):
    offer_product = product.offer_product.first()
    if offer_product:
        last_file = offer_product.files.filter(type_file__in=[ProductMessageFile.CONTRACT, ProductMessageFile.PLAN]).order_by('-created').first()
        if last_file and last_file.form_object:
            return last_file.form_object, last_file

    return get_default_form_contract_and_plan(product), None


def get_default_form_contract_and_plan(product):
    owner = product.get_owner()
    offer_product =  product.offer_product.all()

    default_owner_info = {
        'address': owner.full_address if owner and owner.full_address else '',
        'company_name': owner.enterprise if owner and owner.enterprise else '',
        'job_title': owner.position if owner and owner.position else '',
        'fullname': owner.fullname if owner and owner.fullname else '',
    }

    default_producer_info = {
        'address': '東京都江東区青海二丁目7番4号',
        'company_name': '株式会社ソレモ',
        'job_title': '代表取締役',
        'fullname': '善里 信哉',
    }

    master_producer = product.get_master_producer_is_artist()
    if master_producer:
        master_producer = master_producer.user
        default_producer_info = {
            'address': master_producer.province + master_producer.city + master_producer.mansion,
            'company_name': master_producer.enterprise,
            'job_title': master_producer.position,
            'fullname': master_producer.get_display_name(),
        }
    allow_public_contract = False
    if offer_product.exists() and offer_product.first().disclosure_rule == 'can_public':
        allow_public_contract = True
    form = FormContractAndPlan(release_time=datetime.datetime.today(),
                               owner_info=json.dumps(default_owner_info),
                               producer_info=json.dumps(default_producer_info),
                               allow_public_contract=allow_public_contract)
    
    if offer_product.exists():
        form.subject = get_default_project_subject(offer_product.first())
    return form


def get_default_project_subject(offer):
    if offer.topic:
        return offer.topic.title
    elif offer.sale:
        name = offer.sale.last_published_version.title
        sale_type = offer.sale.last_published_version.sale_type
        sale_action = 'テイストで依頼'
        if sale_type == '1':
            sale_action = '購入'
        elif sale_type == '2':
            if offer.sale.last_published_version.customizable_sale_setting:
                sale_action = offer.sale.last_published_version.customizable_sale_setting
            else:
                sale_action = 'セミオーダー'
        elif sale_type == '3':
            sale_action =  '利用権を購入'
        return name + 'の' + sale_action
    return ""


def push_offer_message(request, project, actions, offer_files, real_offer, offer, new_budget):
    channel_layer = get_channel_layer()
    list_user = project.get_member_offer_project()
    for data in zip(actions, offer_files):
        for identity in list_user:
            new_offer_message = {
                'type': 'offer_message',
                'action': data[0],
                'sender_id': request.user.pk,
                'offer_id': str(real_offer.pk),
                'project_id': str(project.pk),
                'new_budget': new_budget,
                'action_check': 'upload_file_plan_contract_offer',
                'is_not_approved': offer.is_not_approved()
            }
            new_offer_message['file_html'] = render_to_string('direct/_item_file.html',
                                                              {'file': data[1], 'user': identity, 'type_comment': 'messenger_owner'})
            new_offer_message['infor_html'] = render_to_string('direct/_floating_icon_DM.html',
                                                               {'offer': offer, 'real_offer': real_offer, 'user': identity})
            # if identity.role == AuthUser.MASTERCLIENT:
                # new_offer_message['message_infor_html'] = 'render_to_string('direct/_message_check_file.html',
                                                                        #    {'offer': offer, 'real_offer': real_offer, 'user': identity})'
                # new_offer_message['message_infor_html'] = ''
            async_to_sync(channel_layer.group_send)('{}'.format(identity.pk), new_offer_message)


def get_budget_in_form(form_contract_and_plan):
    contract_work_content = form_contract_and_plan.get_work_content()
    total_with_tax = int(calculator(contract_work_content) * 1.1)
    return total_with_tax
