# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 15/12/2021
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.db.models import Prefetch, Subquery, Q
from django.utils.translation import gettext as _
from django.template.loader import render_to_string

from accounts.models import AuthUser
from app.models import OfferProject, ProductMessageFile, OfferProduct, ProductComment, ProductCommentFile, MessageFile, SceneComment, \
    SceneCommentFile, FormContractAndPlan, ProductMessage
from app.services import create_system_message
from landingPage.models import ContactFile

MESSAGE_PER_PAGE = 40

def removeFilePerformance(file_id, mess_type):
    try:
        if file_id:
            mapping_folder_class = {
                'message': models.MessageFile,
                'message_owner': models.ProductMessageFile,
                'scene_comment': models.SceneCommentFile,
                'contact': ContactFile,
                'offer': models.ProductMessageFile,
                'product_comment': models.ProductCommentFile,
                'messenger_owner': models.ProductMessageFile,
            }
            file_model = mapping_folder_class.get(mess_type)
            file = file_model.objects.filter(pk=file_id).first()
            removed_folder_id = ""
            if file and (not hasattr(file, 'form_object') or not file.form_object):
                if mess_type in ['messenger_owner', 'message_owner'] and file.type_file == models.ProductMessageFile.BILL:
                    revert_offer_condition(file)
                    file.delete()
                else:
                    folder = file.folder
                    file.message = None
                    file.folder = None
                    file.save()
                    if folder:
                        while not folder.children.exists() and not folder.child_folders.exists():
                            parent_folder = folder.parent
                            removed_folder_id += "," + str(folder.pk)
                            folder.delete()
                            folder = parent_folder
                            if not parent_folder:
                                break

                        if mess_type in ['contact', 'offer']:
                            if folder:
                                folder.delete()
            elif mess_type in ['messenger_owner', 'message_owner'] and file and hasattr(file, 'form_object') and file.form_object:
                revert_offer_condition(file)
                remove_file(file)
            return {'status': "success", 'removed_folder_id': removed_folder_id.strip(",")}
    except:
        pass
    return {'status': "error"}

def remove_old_file(message):
    deleted_file_ids = []
    for file in message.files.all():
        old_file = ProductMessageFile.objects.filter(file=file.file, message__isnull=True, real_name=file.real_name, user=message.user).first()
        if old_file:
            revert_offer_condition(old_file)
            deleted_file_ids.append(str(old_file.pk))
            old_file.delete()
        else:
            revert_offer_condition(file)

        remove_file(file)
        deleted_file_ids.append(str(file.pk))
    return deleted_file_ids


def remove_file(file_obj):
    form_contract_and_plan = file_obj.form_object
    file_obj.delete()
    if form_contract_and_plan and form_contract_and_plan.product_message_files.count() == 0:
        form_contract_and_plan.delete()


def revert_offer_condition(file):
    offer = file.offer
    if not offer:
        return

    if offer.condition == OfferProduct.STATUS_UPLOADED_PLAN and file == offer.get_plan_in_offer() and \
         offer.files.filter(type_file=ProductMessageFile.PLAN).count() == 1:
         offer.condition = OfferProduct.STATUS_NEW
    elif offer.condition == OfferProduct.STATUS_CONTRACT and file == offer.get_contract_in_offer() and \
            offer.files.filter(type_file=ProductMessageFile.CONTRACT).count() == 1:
            last_plan = offer.get_offer_last_plan()
            if not last_plan:
                offer.condition = OfferProduct.STATUS_NEW
            elif last_plan.is_approved:
                offer.condition = OfferProduct.STATUS_APPROVED_PLAN
    elif offer.condition == OfferProduct.STATUS_BILL and file == offer.get_bill_in_offer() and \
            offer.files.filter(type_file=ProductMessageFile.BILL).count() == 1:
        offer.condition = OfferProduct.STATUS_DONE
    offer.save()


def get_comments(product=None, offset=0, last_message=None, all_scenes=None, scene_title=None):
    comments = None
    if product:
        if last_message:
            comments = ProductComment.objects.filter(project=product, created__lte=last_message.created).order_by(
                "-created")[MESSAGE_PER_PAGE * offset:][
                       :MESSAGE_PER_PAGE]
        else:
            comments = ProductComment.objects.filter(project=product).order_by("-created")[MESSAGE_PER_PAGE * offset:][
                       :MESSAGE_PER_PAGE]
        if comments:
            ids = list(comments.values_list('pk', flat=True))
            comments = ProductComment.objects.filter(pk__in=ids).select_related('user').prefetch_related(
                Prefetch('files', queryset=ProductCommentFile.objects.order_by('created'))).order_by("created")
    else:
        if last_message:
            comments = SceneComment.objects.filter((Q(scene_id__in=all_scenes.values_list("scene_id", flat=True)) | Q(
                scene_title_id=scene_title.pk)) & Q(created__lte=last_message.created)).order_by("-created")[
                       MESSAGE_PER_PAGE * offset:][:MESSAGE_PER_PAGE]
        else:
            comments = SceneComment.objects.filter(
                Q(scene_id__in=all_scenes.values_list("scene_id", flat=True)) | Q(
                    scene_title_id=scene_title.pk)).order_by("-created")[MESSAGE_PER_PAGE * offset:][
                       :MESSAGE_PER_PAGE]
        if comments:
            ids = list(comments.values_list('pk', flat=True))
            comments = SceneComment.objects.filter(pk__in=ids).select_related('user').prefetch_related(
                Prefetch('files', queryset=SceneCommentFile.objects.order_by('created'))).order_by("created")

    return comments

def getAllComments(product=None, offset=0, last_message=None, all_scenes=None, scene_title=None):
    comments = None
    if product:
        if last_message:
            comments = ProductComment.objects.filter(project=product, created__lte=last_message.created).order_by(
                "-created")
        else:
            comments = ProductComment.objects.filter(project=product).order_by("-created")
        if comments:
            ids = list(comments.values_list('pk', flat=True))
            comments = ProductComment.objects.filter(pk__in=ids).select_related('user').prefetch_related(
                Prefetch('files', queryset=ProductCommentFile.objects.order_by('created'))).order_by("created")
    else:
        if last_message:
            comments = SceneComment.objects.filter((Q(scene_id__in=all_scenes.values_list("scene_id", flat=True)) | Q(
                scene_title_id=scene_title.pk)) & Q(created__lte=last_message.created)).order_by("-created")
        else:
            comments = SceneComment.objects.filter(
                Q(scene_id__in=all_scenes.values_list("scene_id", flat=True)) | Q(
                    scene_title_id=scene_title.pk)).order_by("-created")
        if comments:
            ids = list(comments.values_list('pk', flat=True))
            comments = SceneComment.objects.filter(pk__in=ids).select_related('user').prefetch_related(
                Prefetch('files', queryset=SceneCommentFile.objects.order_by('created'))).order_by("created")
    return comments

def get_messages(real_offer, offset=0, last_message=None):
    messages = None
    if real_offer.offer_product:
        offer = real_offer.offer_product
        if last_message:
            messages = offer.message_product.filter(created__lte=last_message.created).order_by(
                "-created")[MESSAGE_PER_PAGE * offset:][:MESSAGE_PER_PAGE]
        else:
            messages = offer.message_product.order_by("-created")[MESSAGE_PER_PAGE * offset:][:MESSAGE_PER_PAGE]
        if messages:
            ids = list(messages.values_list('pk', flat=True))
            messages = offer.message_product.filter(pk__in=ids).select_related('user').prefetch_related(
                Prefetch('files', queryset=ProductMessageFile.objects.order_by('created'))).order_by("created")
    else:
        offer = real_offer.offer_creator
        if last_message:
            messages = offer.message_offer.filter(created__lte=last_message.created).order_by(
                "-created")[MESSAGE_PER_PAGE * offset:][:MESSAGE_PER_PAGE]
        else:
            messages = offer.message_offer.order_by("-created")[MESSAGE_PER_PAGE * offset:][:MESSAGE_PER_PAGE]
        if messages:
            ids = list(messages.values_list('pk', flat=True))
            messages = offer.message_offer.filter(pk__in=ids).select_related('user').prefetch_related(
                Prefetch('files', queryset=MessageFile.objects.order_by('created'))).order_by("created")
    return messages


def create_system_message_plan(offer):
    plan_contract_files = FormContractAndPlan.objects.filter(product_message_files__offer=offer)
    if not plan_contract_files.exists():
        message_content = _('Creating a quote ... Please wait a moment now.')
        create_system_message(None, 'messenger_owner', str(offer.offer.pk), message_content,
                              system_message=None, type_system=ProductMessage.OWNER_SYSTEM_MESSAGE)
    return


def delete_system_message_project_done(project, offer, message_content):
    system_message = offer.offer_product.message_product.filter(type_message=ProductMessage.OWNER_SYSTEM_MESSAGE,
                                                                comment=message_content)
    if system_message.exists():
        system_message.delete()
        list_user = project.get_owners_project()
        data = {
            'type': 'offer_message',
            'type_message': 'system_message',
            'action': 'delete_system_message',
            'offer_id': str(offer.pk),
            'project_id': str(project.pk)
        }
        channel_layer = get_channel_layer()
        for user in list_user:
            data['infor_html'] = render_to_string('direct/_floating_icon_DM.html', {'offer': OfferProject.objects.filter(pk=offer.pk).first().offer_product, 'user': user}),
            async_to_sync(channel_layer.group_send)(
                '{}'.format(user.pk), data)
