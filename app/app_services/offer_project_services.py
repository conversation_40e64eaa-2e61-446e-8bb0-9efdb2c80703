import datetime
import logging
import re

from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.core import serializers
from django.db import transaction
from django.db.models import Q, Sum, Case, F, When
from django.http import JsonResponse
from django.template.loader import render_to_string
from django.utils.translation import gettext as _

from accounts.models import AuthUser, ProductUser
from app.app_services.upload_contract_services import render_artist_contract
from app.models import OfferCreator, OfferProduct, MessageFile, SceneTitle, SceneTitleBookmark
from app.services import check_budget_admin, create_system_message
from payments.services import master_admin_get_payment_products


def delete_offer_creator_service(request, offer):
    # update balance
    product_admin = ProductUser.objects.filter(user=offer.admin, product=offer.project,
                                               user__role=AuthUser.CREATOR).first()
    product_creator = ProductUser.objects.filter(user=offer.creator, product=offer.project,
                                                 user__role=AuthUser.CREATOR).first()
    if product_admin:
        # update budget can offer
        ProductUser.update_available_budget(product_admin, offer.reward)
        if offer.status == OfferCreator.STATUS_COMPLETED:
            AuthUser.change_balance(offer.admin.pk,
                                    [('balance_available', offer.reward), ('balance_reward', offer.reward),
                                     ('total_point', offer.reward)])
        else:
            AuthUser.change_balance(offer.admin.pk, [('balance_expected', offer.reward)])
    if product_creator and offer.status != OfferCreator.STATUS_NEW:
        ProductUser.update_available_budget(product_creator, -1 * offer.reward)
        ProductUser.update_rewarded(product_creator, -1 * offer.reward)
        if offer.status in OfferCreator.STATUS_PROGRESS:
            AuthUser.change_balance(offer.creator.pk, [('balance_expected', -1 * offer.reward)])
        elif offer.status == OfferCreator.STATUS_COMPLETED:
            AuthUser.change_balance(offer.creator.pk,
                                    [('balance_available', -1 * offer.reward), ('balance_reward', -1 * offer.reward),
                                     ('total_point', -1 * offer.reward)])

    # update offer status
    offer.status = OfferCreator.STATUS_REJECT
    offer.reject_time = datetime.datetime.now()
    offer.save()

    # send to admin/creator in task
    project = offer.project
    channel_layer = get_channel_layer()
    for user in offer.get_member_offer():
        new_offer_reject = {
            'type': 'offer_message',
            'action': 'offer_reject',
            'sender_id': str(user.pk),
            'offer_id': str(offer.offer.pk),
            'thread_name': str(offer.scenes),
            'project_id': str(project.pk)
        }
        new_offer_reject["unread_message_count"] = project.count_unread_offer_message_admin(user)
        async_to_sync(channel_layer.group_send)(
            '{}'.format(user.pk), new_offer_reject)
    # append html
    with_paid = True if request.POST.get('with_paid') == '1' else False
    projects = master_admin_get_payment_products(request.user, with_paid, 0, project)
    data_payment = request.POST.get('data_payment', None)
    html = render_to_string('_master_admin_payment_info_project_item.html',
                            {'projects': projects, 'current_user': request.user, 'data_loop': data_payment})
    return html


def delete_project_service(project):
    try:
        offer_creators = OfferCreator.original_objects.filter(project=project,
                                                              status__in=OfferCreator.STATUS_OFFER_ACTIVE)
        admins = offer_creators.filter(admin__role=AuthUser.CREATOR).values_list('admin__pk')
        creators = offer_creators.filter(creator__role=AuthUser.CREATOR).values_list('creator__pk')
        users = AuthUser.objects.filter(Q(role=AuthUser.CREATOR) & Q(is_active=True) & (
                Q(pk__in=admins.values_list('admin__pk', flat=True)) | Q(
            pk__in=creators.values_list('creator__pk', flat=True)))).distinct()

        for user in users:
            # update balance_expected = offer nhận in progress (2, 3) - offer đi in progress (1,2,3)
            offers_inprogress = offer_creators.filter(
                (Q(status__in=['1', '2', '3']) & Q(admin=user)) | (
                        Q(status__in=['2', '3']) & Q(creator=user))).exclude(payment_status=True)
            if offers_inprogress.exists():
                balance_expected = float(user.balance_expected)
                balance_expected_project = offers_inprogress.aggregate(total_reward=Sum(
                    Case(When(admin=user, then=-1 * F('reward')),
                         default='reward'))).get('total_reward', 0) if offers_inprogress else 0
                balance_expected -= balance_expected_project
                user.balance_expected = balance_expected
                user.save()

            # update balance_available = tổng các task checked hiện thị khi toggle OFF
            current_user = user
            offers = OfferCreator.original_objects.filter(project=project).filter(
                (Q(status=OfferCreator.STATUS_COMPLETED) & Q(admin=current_user)) | (
                        Q(status=OfferCreator.STATUS_COMPLETED) & Q(creator=current_user))).exclude(
                (Q(payment_status=True) & Q(creator_payment_request_id__isnull=True) & Q(
                    admin_payment_request_id__isnull=True)) | (Q(payment_status=True) & Q(creator=current_user)) | \
                (Q(admin=current_user) & Q(admin_payment_request_id__isnull=False)) | \
                (Q(creator=current_user) & Q(creator_payment_request_id__isnull=False)))
            if offers.exists():
                balance_available = float(user.balance_available)
                budget_offer = offers.aggregate(total_reward=Sum(
                    Case(When(admin=current_user, then=-1 * F('reward')),
                         default='reward'))).get('total_reward', 0) if offers else 0
                balance_available -= budget_offer
                user.balance_available = balance_available
                user.save()

            # update total_point: show in screen curator setting
            offers_done = offer_creators.filter(
                Q(status='4') & (Q(admin=user) | Q(creator=user)))
            if offers_done:
                offer_admins = offers.filter(admin=user)
                total_offer_admins = offer_admins.aggregate(Sum('reward'))
                total_offer_admins = total_offer_admins['reward__sum'] if total_offer_admins['reward__sum'] else 0
                offer_creators_1 = offers.filter(creator=user)
                total_offer_creators = offer_creators_1.aggregate(Sum('reward'))
                total_offer_creators = total_offer_creators['reward__sum'] if total_offer_creators[
                    'reward__sum'] else 0
                user.balance_reward = total_offer_creators - total_offer_admins
                user.total_point = user.total_point - total_offer_creators + total_offer_admins
                user.save()

        # update status
        project.is_active = False
        project.save()
        product_scenes = project.scene_list.all()
        titles = SceneTitle.original_objects.filter(product_scene__in=product_scenes)
        SceneTitleBookmark.objects.filter(type_bookmark=SceneTitleBookmark.BOOKMARK_SCENE, title__in=titles).delete()

        offer_product = project.offer_product.first()
        if offer_product:
            offer_product.condition = OfferProduct.STATUS_DELETED
            offer_product.save()


        # send to member in project
        list_user = project.get_all_member_project_detail()
        new_offer_message = {
            'type': 'offer_message',
            'action': 'delete_offer_product',
            'project_id': str(project.pk)
        }
        channel_layer = get_channel_layer()
        for identity in list_user:
            async_to_sync(channel_layer.group_send)(
                '{}'.format(identity.pk),
                new_offer_message
            )
        return True
    except Exception as e:
        logging.error(e)
    return False


def update_info_offer_creator_service(request, target):
    html = ''
    try:
        reward = request.POST.get('reward', '0')
        contract = request.POST.get('contract', None)
        range_deadline = request.POST.get('range_deadline', None)
        scenes = request.POST.get('scenes', None)
        data_format = request.POST.get('data_format', '')
        message = request.POST.get('message', '')
        quantity = request.POST.get('quantity', '')
        time_deadline = request.POST.get('time_deadline', '')
        date_deadline = request.POST.get('deadline', '')
        start_time = datetime.datetime.strptime(range_deadline.split('-')[0].strip(), '%Y/%m/%d')
        deadline = datetime.datetime.strptime(date_deadline + ' ' + time_deadline,
                                              '%Y/%m/%d %H:%M')
        key_file = request.POST.get('key_file')
        real_name = request.POST.get('real_name')
        is_delete_file = request.POST.get('is_delete_file')
        reward = float(re.sub('[^0-9]', '', reward))

        valid_date = datetime.datetime.strptime(request.POST.get('valid_date').strip(), '%Y/%m/%d %H:%M')
        note = request.POST.get('note')
        pick_up_method = request.POST.get('pick_up_method')
        delivery_place = request.POST.get('delivery_place')
        allow_subcontracting = request.POST.get('allow_subcontracting', 1)
        selected_job_type = request.POST.get('selected_job_type', '')

        real_offer = target.offer
        project = target.project
        offer = OfferCreator.objects.filter(pk=target.pk)

        first_message = target.message_offer.first()
        current_reward = target.reward

        # check real edit offer
        offer_edit = False
        array_values = {
            'contract': contract,
            'deadline': deadline,
            'start_time': start_time,
            'message': message,
            'quantity': quantity,
            'scenes': scenes,
            'data_format': data_format,
            'reward': reward,
            'range_deadline': range_deadline,
            'valid_date': valid_date,
            'note':note,
            'pick_up_method':pick_up_method,
            'delivery_place':delivery_place,
            'allow_subcontracting':allow_subcontracting,
            'selected_job_type': selected_job_type,

        }
        for (key, value) in array_values.items():
            current_value = getattr(target, key)
            if current_value != value:
                offer_edit = True
                break

        if message != target.message:
            offer_edit = True
            first_message.content = message
            first_message.comment = message
            first_message.save()
        if key_file:
            offer_edit = True
            first_message.has_file = True
            first_message.save()
            first_message.files.all().update(message_id=None)
            MessageFile.objects.create(message=first_message, file=key_file, real_name=real_name)
            offer.update(file=key_file)
        else:
            if is_delete_file == "true":
                offer_edit = True
                first_message.file = ""
                first_message.real_name = ""
                first_message.has_file = True
                first_message.save()
                first_message.files.all().update(message_id=None)
                offer.update(file=None)
        if offer_edit:
            offer.update(contract=contract,
                         deadline=deadline,
                         start_time=start_time,
                         message=message,
                         reward=reward,
                         scenes=scenes,
                         project=project,
                         quantity=quantity,
                         data_format=data_format,
                         type_contract='コンポーザー',
                         range_deadline= range_deadline,
                         valid_date= valid_date,
                         note=note,
                         pick_up_method=pick_up_method,
                         delivery_place=delivery_place,
                         allow_subcontracting=allow_subcontracting,
                         selected_job_type=selected_job_type)

            contract = offer[0].contract
            if contract == '4':
                offer[0].type_contract = 'サウンドデザイナー'
            elif contract == '5':
                offer[0].type_contract = 'オーディオエンジニア'
            elif contract == '6':
                offer[0].type_contract = '声優・ナレーター'
            elif contract == '7':
                offer[0].type_contract = 'ボーカリスト'
            elif contract == '8':
                offer[0].type_contract = '演奏家'
            elif contract == '9':
                offer[0].type_contract = 'ディレクター'
            offer[0].save()
            try:
                approved = False
                if offer[0].status in ['2', '3']:
                    approved = True
                render_artist_contract(offer[0], approved)
            except:
                pass

            product_admin = ProductUser.objects.filter(user=target.admin, product=target.project,
                                                       user__role=AuthUser.CREATOR).first()
            product_creator = ProductUser.objects.filter(user=target.creator, product=target.project,
                                                         user__role=AuthUser.CREATOR).first()

            if current_reward != reward:
                if product_admin:
                    ProductUser.update_available_budget(product_admin, current_reward - reward)
                    if target.status == OfferCreator.STATUS_COMPLETED:
                        AuthUser.change_balance(target.admin.pk, [('balance_available', current_reward - reward),
                                                                  ('balance_reward', current_reward - reward),
                                                                  ('total_point', current_reward - reward)])
                    else:
                        AuthUser.change_balance(target.admin.pk, [('balance_expected', current_reward - reward)])
                if product_creator and target.status != OfferCreator.STATUS_NEW:
                    ProductUser.update_available_budget(product_creator, reward - current_reward)
                    ProductUser.update_rewarded(product_creator, reward - current_reward)
                    if target.status in OfferCreator.STATUS_PROGRESS:
                        AuthUser.change_balance(target.creator.pk, [('balance_expected', reward - current_reward)])
                    elif target.status == OfferCreator.STATUS_COMPLETED:
                        AuthUser.change_balance(target.creator.pk, [('balance_available', reward - current_reward),
                                                                    ('balance_reward', reward - current_reward),
                                                                    ('total_point', reward - current_reward)])

            target.refresh_from_db()
            file_infor_html = ''

            infor_offer = render_to_string('messenger/_item_infor_offer.html', {'offer': target})

            receiver = target.creator
            message_received_html = render_to_string('top/_item_message_received.html',
                                                     {'message': first_message, "user": receiver,
                                                      'receiver': target.admin, 'type': 'messenger'})

            data = list()
            data.append(first_message)
            channel_layer = get_channel_layer()
            for identity in real_offer.offer_creator.get_member_offer():
                new_offer_message = {
                    'type': 'offer_message',
                    'message': serializers.serialize('json', data),
                    'message_id': str(first_message.pk),
                    'action': 'edit_offer',
                    'sender_id': target.admin.pk,
                    'offer_id': str(real_offer.pk),
                    'file_infor_html': file_infor_html,
                    'message_received_html': message_received_html,
                    'infor_offer': infor_offer,
                    'has_file': first_message.has_file,
                    'thread_name': target.scenes
                }
                if first_message.has_file:
                    new_offer_message['file_infor_html'] = render_to_string('top/_comment_file_infor.html',
                                                                            {'message': first_message, 'user': identity,
                                                                             'type_comment': 'messenger'})
                new_offer_message['message_send_html'] = render_to_string('top/_item_message_send.html',
                                                                          {'message': first_message, "user": identity,
                                                                           'first_message': True, 'type': 'messenger'})

                async_to_sync(channel_layer.group_send)(
                    '{}'.format(identity.pk), new_offer_message)

            # append html
            with_paid = True if request.POST.get('with_paid') == '1' else False
            projects = master_admin_get_payment_products(request.user, with_paid, 0, project)
            data_payment = request.POST.get('data_payment', None)
            html = render_to_string('_master_admin_payment_info_project_item.html',
                                    {'projects': projects, 'current_user': request.user, 'data_loop': data_payment})
            return html
    except:
        pass
    return html


def get_information_offer_creator_service(offer, user):
    context = {}
    reward = offer.reward
    contract = offer.contract
    scenes = offer.scenes
    data_format = offer.data_format
    message = offer.message
    quantity = offer.quantity
    start_time = offer.start_time
    deadline = offer.deadline
    time_deadline = deadline.strftime("%-H:%M")
    range_deadline = offer.range_deadline
    file_name = ''
    if offer.file:
        file_name = offer.get_file_name()
    offer_status = offer.status
    list_value = get_value_exists(offer.project, user)

    list_scenes = list_value[0]
    list_quantity = list_value[1]
    list_data_format = list_value[2]
    valid_date = offer.valid_date
    note = offer.note
    pick_up_method = offer.pick_up_method
    delivery_place = offer.delivery_place
    allow_subcontracting = offer.allow_subcontracting
    selected_job_type = offer.selected_job_type
    form_html = render_to_string('messenger/_modal_form_offer.html', {'page': 'edit', 'project': offer.project, 'hide_upload': True, 'user':user})

    context.update({
        'range_deadline': range_deadline,
        'time_deadline': time_deadline,
        'start_time': start_time,
        'deadline': deadline,
        'contract': contract,
        'quantity': quantity,
        'data_format': data_format,
        'scenes': scenes,
        'message': message,
        'file_name': file_name,
        'key_file': offer.file.name,
        'reward': reward,
        'offer_status': offer_status,
        'list_scenes': list_scenes,
        'list_data_format': list_data_format,
        'list_quantity': list_quantity,
        'can_edit_reward': True,
        'form_html': form_html,
        'note':note,
        'pick_up_method':pick_up_method,
        'delivery_place':delivery_place,
        'allow_subcontracting':allow_subcontracting,
        'valid_date': valid_date,
        'selected_job_type': selected_job_type,
    })

    return context


def get_value_exists(project, user):
    options = project.product_offers.filter(status__in=OfferCreator.STATUS_OFFER_ACTIVE)
    if user:
        options = options.filter(admin=user)
    offer_scenes = list(options.values_list('scenes', flat=True))
    offer_quantity = list(options.filter(quantity__isnull=False).values_list('quantity', flat=True))
    offer_format = list(options.filter(data_format__isnull=False).values_list('data_format', flat=True))

    list_scenes = list(set([i.strip() for i in offer_scenes]))
    list_quantity = list(set([i.strip() for i in offer_quantity]))
    list_data_format = list(set([i.strip() for i in offer_format]))
    list_value = []
    list_value.append(list_scenes)
    list_value.append(list_quantity)
    list_value.append(list_data_format)
    return list_value