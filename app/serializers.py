from rest_framework import serializers

from .app_services.upload_contract_services import get_budget_in_form
from .models import Product, OfferCreator, OfferProduct, Scene, SceneTitle, OfferProject, SceneComment, ProductScene, SectionCredit
from accounts.models import AuthUser, ScheduleCreator
from django.conf import settings
from app.templatetags.util import (
	count_batch_number_project
)

ROLE_DEFAULT_AVATAR = {
    AuthUser.CREATOR: '/static/images/default-avatar-creator.png',
    AuthUser.MASTERADMIN: '/static/images/default-avatar-master-admin.png',
    AuthUser.MASTERCLIENT: '/static/images/default-avatar-client.png',
}
DEFAULT_AVATAR = '/static/images/default-avt.png'
SOREMO_AVATAR = '/static/images/soremo-favi2_01.png'
class GroupOfferCreatorSerializer(serializers.ModelSerializer):
    custom_contract = serializers.SerializerMethodField()
    accept_time = serializers.SerializerMethodField()
    user_name = serializers.SerializerMethodField()
    class Meta:
        model = OfferCreator
        fields = ('id', 'reward', 'contract', 'accept_time', 'check_time', 'scenes', 'custom_contract', 'payment_status',
                  'admin_payment_request', 'admin_payment_request',  'status', 'type', 'created', 'user_name')

    def get_custom_contract(self, obj):
        return obj.custom_contract_dsp()

    def get_accept_time(self, obj):
        return obj.created

    def get_user_name(self, obj):
        if obj.show_admin:
            user = obj.admin
        else:
            user = obj.creator
        return 'SOREMO' if not obj.show_admin and obj.type == '2' else user.get_display_name() or ''

class AuthUserGroupOfferSerializer(serializers.ModelSerializer):
    avatar_url = serializers.SerializerMethodField()
    grouped_offer = GroupOfferCreatorSerializer(many=True)
    total_reward = serializers.FloatField()
    display_name = serializers.SerializerMethodField()
    class Meta:
        model = AuthUser
        fields = ('username', 'email', 'role', 'id', 'avatar_url', 'grouped_offer', 'total_reward', 'display_name')

    def get_avatar_url(self, user):
        host = settings.HOST
        try:
            if getattr(user, 'is_fee', None):
                return f'{host}{SOREMO_AVATAR}'
            else:
                if user.avatar:
                    return user.avatar.url
                return f'{host}{ROLE_DEFAULT_AVATAR.get(user.role, DEFAULT_AVATAR)}'
        except:
            return f'{host}{DEFAULT_AVATAR}'

    def get_display_name(self, user):
        return 'SOREMO' if getattr(user, 'is_fee', None) else user.get_display_name() or ''

class GroupOfferProductSerializer(serializers.ModelSerializer):
    custom_contract = serializers.SerializerMethodField()
    accept_time = serializers.SerializerMethodField()
    reward = serializers.SerializerMethodField()
    user_name = serializers.SerializerMethodField()
    class Meta:
        model = OfferProduct
        fields = ('status', 'type', 'accept_time', 'custom_contract', 'reward', 'user_name')

    def get_accept_time(self, offer_product):
        return offer_product.created
    def get_custom_contract(self, offer_product):
        return 'プロジェクトオーナー'

    def get_reward(selfself, offer_product):
        return offer_product.project.total_budget

    def get_user_name(self, offer_product):
        return getattr(offer_product, 'user_name', None)

class MasterAdminProductOfferSerializer(serializers.ModelSerializer):
    avatar_url = serializers.SerializerMethodField()
    grouped_offer = serializers.SerializerMethodField()
    total_reward = serializers.SerializerMethodField()
    display_name = serializers.SerializerMethodField()
    class Meta:
        model = AuthUser
        fields = ('username', 'email', 'role', 'avatar_url', 'grouped_offer', 'total_reward', 'display_name')

    def get_avatar_url(self, user):
        host = settings.HOST
        try:
            if user.avatar:
                return user.avatar.url
            return f'{host}{ROLE_DEFAULT_AVATAR.get(user.role, DEFAULT_AVATAR)}'
        except:
            return f'{host}{DEFAULT_AVATAR}'

    def get_display_name(self, user):
        return user.get_display_name() or ''

    def get_grouped_offer(self, obj):
        offer_projects = obj.offeruser_set.all().values_list('offer', flat=True)
        offer_products = OfferProduct.objects.filter(offer__in=offer_projects)
        return GroupOfferProductSerializer(offer_products, many=True).data

    def get_total_reward(self, user):
        offer_projects = user.offeruser_set.all().values_list('offer', flat=True)
        offer_product = OfferProduct.objects.filter(offer__in=offer_projects, ).first()
        project = offer_product.project
        total_budget = project.total_budget
        return total_budget


class SceneScheduleSerializer(serializers.ModelSerializer):
    file_type = serializers.SerializerMethodField()
    class Meta:
        model = Scene
        fields = ('scene_id', 'movie', 'schedule_date', 'created', 'thumbnail', 'file_type')

    def get_file_type(self, scene):
        return scene.is_audio_file()


class SceneTitleScheduleSerializer(serializers.ModelSerializer):
    last_version = serializers.SerializerMethodField()
    chapter = serializers.SerializerMethodField()
    take_number = serializers.SerializerMethodField()
    class Meta:
        model = SceneTitle
        fields = ('title_id', 'title', 'last_version', 'is_done', 'chapter', 'take_number')

    def get_last_version(self, obj):
        return SceneScheduleSerializer(obj.last_version).data

    def get_chapter(self, obj):
        return obj.product_scene.name

    def get_take_number(self, obj):
        related_scenes = Scene.original_objects.select_related("title").filter(
            title_id=obj.pk, version__isnull=True).order_by("order")
        if related_scenes.count() > 1:
            for i, scene in enumerate(related_scenes):
                if scene.scene_id == obj.last_version.scene_id:
                    return i + 1
        else:
            return 0
        return 0


class OfferCreatorScheduleSerializer(serializers.ModelSerializer):
    admin_avatar_url = serializers.SerializerMethodField()
    creator_avatar_url = serializers.SerializerMethodField()
    custom_contract = serializers.SerializerMethodField()
    creator_name = serializers.SerializerMethodField()
    admin_name = serializers.SerializerMethodField()
    
    class Meta:
        model = OfferCreator
        fields = ('id', 'status', 'type', 'deadline', 'custom_contract', 'admin_avatar_url', 'admin', 'creator', 'scenes', 'creator_avatar_url', 'creator_name', 'contract', 'admin_name')

    def get_custom_contract(self, obj):
        return obj.custom_contract_dsp()

    def get_admin_avatar_url(self, obj):
        host = settings.HOST
        user = obj.admin
        try:
            if getattr(user, 'is_fee', None):
                return f'{host}{SOREMO_AVATAR}'
            else:
                if user.avatar:
                    return user.avatar.url
                return f'{host}{ROLE_DEFAULT_AVATAR.get(user.role, DEFAULT_AVATAR)}'
        except:
            return f'{host}{DEFAULT_AVATAR}'

    def get_creator_avatar_url(self, obj):
        host = settings.HOST
        user = obj.creator
        try:
            if getattr(user, 'is_fee', None):
                return f'{host}{SOREMO_AVATAR}'
            else:
                if user.avatar:
                    return user.avatar.url
                return f'{host}{ROLE_DEFAULT_AVATAR.get(user.role, DEFAULT_AVATAR)}'
        except:
            return f'{host}{DEFAULT_AVATAR}'
    
    def get_creator_name(self, obj):
        user = obj.creator
        return user.get_display_name()
    
    def get_admin_name(self, obj):
        user = obj.admin
        return user.get_display_name()
        


class OfferProjectScheduleSerializer(serializers.ModelSerializer):
    offer_creator = OfferCreatorScheduleSerializer()
    offer_side = serializers.CharField()
    
    offer_product_deadline = serializers.SerializerMethodField()
    offer_product_condition = serializers.SerializerMethodField()
    unread_message_count = serializers.SerializerMethodField()
    class Meta:
        model = OfferProject
        fields = ('offer_id', 'project', 'offer_creator', 'offer_side', 'type_offer', 'modified', 'offer_product_deadline', 'offer_product_condition', 'unread_message_count')
    
    def get_offer_product_deadline(self, obj):
        if obj.offer_product:
            return obj.offer_product.deadline
        return None  # Or return a default value like '' if desired

    def get_offer_product_condition(self, obj):
        if obj.offer_product:
            return obj.offer_product.condition
        return None  # Or return a default value like '' if desired

    def get_unread_message_count(self, obj):
        if hasattr(obj, 'unread_message_count') and obj.unread_message_count is not None:
            return obj.unread_message_count
        return None  # Or return 0 if preferred


class ProductSerializer(serializers.ModelSerializer):
    count_batch_number_project = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'product_id', 'name', 'description', 'is_active', 'modified', 
            'max_scene', 'current_heart', 'image_resized', 'image', 
            'get_name_in_page_title', 'font_setting', 'color_setting', 
            'count_batch_number_project', 'start_time', 'end_time'
        ]

    def get_count_batch_number_project(self, obj):
        user = self.context.get('user')
        if user is None:
            return 0  # or handle this case as needed
        return count_batch_number_project(obj, user)
    
class OfferProjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferProject
        fields = '__all__'

class SceneTitleSerializer(serializers.ModelSerializer):
    class Meta:
        model = SceneTitle
        fields = '__all__'

class SceneCommentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SceneComment
        fields = '__all__'

class ProductSceneSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductScene
        fields = '__all__'
class SectionCreditSerializer(serializers.ModelSerializer):
    class Meta:
        model = SectionCredit
        fields = '__all__'

class ProjectsSerializer(serializers.ModelSerializer):

    class Meta:
        model = Product
        fields = [
            'project_id',
            'updated_at',
            'code_name',
            'final_work',
            'rating',
            'overview',
            'total_deliverables_count',
            'current_deliverables_count',
            'approval_deliverables_count',
            'banner_image_optimized',
            'banner_font',
            'banner_color',
        ]
    project_id = serializers.UUIDField(source='product_id', read_only=True)
    updated_at = serializers.DateTimeField(source='last_update')
    final_work = serializers.CharField(source='name')
    overview = serializers.CharField(source='description')
    total_deliverables_count = serializers.IntegerField(source='max_scene')
    current_deliverables_count = serializers.IntegerField(source='current_scene')
    approval_deliverables_count = serializers.IntegerField(source='current_heart')
    banner_image_optimized = serializers.FileField(source='image')
