from django.core.management.base import BaseCommand

from django.db.models import Q, Avg

from app.models import ProductScene


def update_average_rating():
    try:
        product_scenes = ProductScene.objects.all()
        for product_scene in product_scenes:
            product_scene.update_rating()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "Update average rating product scene"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_average_rating()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
