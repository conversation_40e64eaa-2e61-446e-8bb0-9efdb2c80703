import json

from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import TopicGallery, SelectionGallery


def update_current_topic():
    try:
        # Update topic 3 with 3 selection
        topic_3 = TopicGallery.objects.create(title='ソニックブランディング',
                                              description='サウンドロゴ、アプリUI、IoT。繰り返し耳にする音を、試作検証を重ねて制作。レギュレーションマニュアルと共に納品します。',
                                              order=3, thumbnail='', thumbnail_real_name='', is_deleted=True)

        SelectionGallery.objects.create(topic=topic_3, title='BGM', order=3,
                                        selection_content=json.dumps([{"detail": "新規制作したい", "status": "off"},
                                                                      {"detail": "利用権（非独占）ライセンスで用意したい",
                                                                       "status": "off"},
                                                                      {"detail": "なし", "status": "on"}]))

        SelectionGallery.objects.create(topic=topic_3, title='SE', order=2,
                                        selection_content=json.dumps([{"detail": "新規制作したい", "status": "on"},
                                                                      {"detail": "なし", "status": "off"}]))

        SelectionGallery.objects.create(topic=topic_3, title='VOICE', order=1,
                                        selection_content=json.dumps([{"detail": "新規収録したい", "status": "off"},
                                                                      {"detail": "収録したものを整音したい", "status": "off"},
                                                                      {"detail": "なし", "status": "on"}]))

        # Update topic 2 with 3 selection
        topic_2 = TopicGallery.objects.create(title='エンタメ',
                                              description='ゲーム、アニメ、遊技機、イベント。必要な音を0→1で制作。インタラクティブな作品では、仕様にあわせてアセットを組み立て、実装します。',
                                              order=2, thumbnail='', thumbnail_real_name='', is_deleted=True)

        SelectionGallery.objects.create(topic=topic_2, title='BGM', order=3,
                                        selection_content=json.dumps([{"detail": "新規制作したい", "status": "on"},
                                                                      {"detail": "利用権（非独占）ライセンスで用意したい",
                                                                       "status": "off"},
                                                                      {"detail": "なし", "status": "off"}]))
        SelectionGallery.objects.create(topic=topic_2, title='SE', order=2,
                                        selection_content=json.dumps([{"detail": "新規制作したい", "status": "on"},
                                                                      {"detail": "なし", "status": "off"}]))

        SelectionGallery.objects.create(topic=topic_2, title='VOICE', order=1,
                                        selection_content=json.dumps([{"detail": "新規収録したい", "status": "on"},
                                                                      {"detail": "収録したものを整音したい", "status": "off"},
                                                                      {"detail": "なし", "status": "off"}]))

        # Update topic 1 with 3 selection
        topic_1 = TopicGallery.objects.create(title='番組',
                                              description='vlog、ウェビナー、チュートリアルなど。撮影で収録した音素材も活用しながら、必要な項目を追加し制作。ボイスオーバーを収録する場合は、原稿作成から相談できます。',
                                              order=1, thumbnail='', thumbnail_real_name='', is_deleted=True)

        SelectionGallery.objects.create(topic=topic_1, title='BGM', order=3,
                                        selection_content=json.dumps([{"detail": "新規制作したい", "status": "off"},
                                                                      {"detail": "利用権（非独占）ライセンスで用意したい", "status": "on"},
                                                                      {"detail": "なし", "status": "off"}]))
        SelectionGallery.objects.create(topic=topic_1, title='SE', order=2,
                                        selection_content=json.dumps([{"detail": "新規制作したい", "status": "on"},
                                                                      {"detail": "なし", "status": "off"}]))

        SelectionGallery.objects.create(topic=topic_1, title='VOICE', order=1,
                                        selection_content=json.dumps([{"detail": "新規収録したい", "status": "off"},
                                                                      {"detail": "収録したものを整音したい", "status": "on"},
                                                                      {"detail": "なし", "status": "off"}]))

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update default topic"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_current_topic()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
