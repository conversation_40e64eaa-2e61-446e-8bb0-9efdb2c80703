from django.core.management.base import BaseCommand

from app.models import Product
from app.tasks import update_image_resized


def resize_image_for_product():
    product_ids = Product.objects.all().values_list('product_id', flat=True)
    for product_id in product_ids:
        update_image_resized.delay(product_id)
    return len(product_ids)


class Command(BaseCommand):
    help = 'Set back job to resize image for products'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start set back job to resize__________')
        num_products = resize_image_for_product()
        self.stdout.write(f'=============> Set {num_products} done for {Product.__name__}')
        self.stdout.write(self.style.SUCCESS('__________set done!__________'))
