from django.core.management.base import BaseCommand
from app.models import ProductCommentFile
from concurrent.futures import ThreadPoolExecutor
import time
from app.util import get_info_file, save_logging_update_info_file
from datetime import datetime
import logging
list_id_upload_failed = []
num_threads = 2


def update_info_file(item):
    try:
        result = get_info_file(str(item.file), str(item.real_name))
        item.type_file_name = result['type_file']
        item.file_info = result['file_info']
        item.save()
    except Exception as e:
        file_update_failed = {
            'table': item.__class__.__name__,
            'file_id': item.pk,
            'file': item.file,
            'reason': e
        }
        list_id_upload_failed.append(file_update_failed)
        object_log = {
            'table': item.__class__.__name__,
            'file_id_failed': item.pk,
            'file': item.file
        }
        msg_log = 'update info file failed'
        save_logging_update_info_file(msg_log, e, object_log)


def setup_thread_get_info_comment():
    start_time = time.time()
    # ProductCommentFile.objects.all().update(file_info=None, type_file_name=None)
    list_product_comment_file = ProductCommentFile.objects.filter(
        file_info=None,
        type_file_name=None
    ).all()
    total_record = list_product_comment_file.count()
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        executor.map(update_info_file, list_product_comment_file)
        pass
    end_time = time.time()
    elapsed_time = end_time - start_time
    formatted_time = datetime.utcfromtimestamp(elapsed_time).strftime('%H:%M:%S')
    object_log = {
        'workers': num_threads,
        'start_time': start_time,
        'end_time': end_time,
        'run_time': elapsed_time,
        'run_time_formatted': formatted_time,
        'list_id_upload_failed': str(list_id_upload_failed)
    }
    total_record_failed = len(list_id_upload_failed)
    save_logging_update_info_file(
        f'UPDATE INFO PRODUCT COMMENT FILE done,'
        f' total: {total_record}'
        f', success: {total_record - total_record_failed}'
        f', failed: {total_record_failed}',
        None,
        object_log)
    print('UPDATE INFO PRODUCT COMMENT FILE done!')
    return True


class Command(BaseCommand):
    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        msg_start_cmd = "__________START PROCESS COMMAND: 'update_info_product_comment_file'__________"
        msg_end_cmd = "__________END PROCESS COMMAND: 'update_info_product_comment_file'__________"
        logging.info(msg_start_cmd)
        self.stdout.write(msg_start_cmd)
        setup_thread_get_info_comment()
        logging.info(msg_end_cmd)
        self.stdout.write(self.style.SUCCESS(msg_end_cmd))
