from django.core.management.base import BaseCommand

from app.models import Product


def update_number_of_banner():
    try:
        for product in Product.objects.filter(is_active=True):
            product.update_current_and_heart_scene()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update number of banner for all products"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_number_of_banner()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
