
from django.core.management.base import BaseCommand

from app.models import Product
from accounts.models import ProductUser, AuthUser


def update_authuser_fullname():
    for user in AuthUser.objects.all():
        try:
            user.fullname = user.get_full_name()
            user.save()
        except:
            print("Error! Try again!")


class Command(BaseCommand):
    help = "update fullname to new field"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_authuser_fullname()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
