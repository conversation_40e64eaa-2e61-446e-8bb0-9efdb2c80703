
from django.core.management.base import BaseCommand

from app.models import ProductMessage


def before_update_user_upload_plan():
    try:
        messengers = ProductMessage.objects.filter(status='2')
        for messenger in messengers:
            owner = messenger.owner
            offer = messenger.offer_product
            if owner != offer.master_admin:
                offer.master_admin = owner
                offer.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update master admin upload plan for offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        before_update_user_upload_plan()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
