
from django.core.management.base import BaseCommand

from app.models import Product
from accounts.models import ProductUser, AuthUser


def update_product_owner():
    try:
        products = Product.objects.filter(is_active=True)
        for product in products:
            if product.offer_product.exists() and product.offer_product.first().master_client:
                product_user = ProductUser.objects.filter(product=product,
                                                          user=product.offer_product.first().master_client)
                product_user.update(is_owner='1')
            if product.owner:
                product_user = ProductUser.objects.filter(product=product, user=product.owner)
                product_user.update(is_owner='1')
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update product owner into table ProductUser"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_product_owner()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
