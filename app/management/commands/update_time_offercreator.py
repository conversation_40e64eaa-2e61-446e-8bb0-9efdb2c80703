
from django.core.management.base import BaseCommand

from app.models import OfferCreator


def update_time_offercreator():
    try:
        offers = OfferCreator.objects.all()
        for offer in offers:
            if offer.status in ('2', '3', '4'):
                offer.accept_time = offer.created
            if offer.status == '4':
                offer.check_time = offer.modified
            if offer.status == '5':
                if offer.message_offer and offer.message_offer.filter(file__isnull=False).exists():
                    offer.accept_time = offer.created
                offer.reject_time = offer.modified
            offer.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update time change status for offer creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_time_offercreator()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
