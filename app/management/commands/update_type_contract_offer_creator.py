
from django.core.management.base import BaseCommand

from app.models import OfferCreator


def update_type_contract_offer_creator():
    try:
        offers = OfferCreator.objects.filter(creator__is_active=True, project__is_active=True)
        if offers.exists():

            for offer in offers:
                contract = offer.contract

                if contract in ('1', '2', '3'):
                    offer.type_contract = 'コンポーザー'
                elif contract == '4':
                    offer.type_contract = 'サウンドデザイナー'
                elif contract == '5':
                    offer.type_contract = 'オーディオエンジニア'
                elif contract == '6':
                    offer.type_contract = '声優・ナレーター'
                elif contract == '7':
                    offer.type_contract = 'ボーカリスト'
                elif contract == '8':
                    offer.type_contract = '演奏家'
                offer.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update type contract depend on contract for offer creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_type_contract_offer_creator()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
