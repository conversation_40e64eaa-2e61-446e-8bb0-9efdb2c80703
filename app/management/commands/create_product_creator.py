
from django.core.management.base import BaseCommand
from accounts.models import ProductUser, Creator, AuthUser
import sys


def create_product_creator():
    try:
        ProductUser.objects.filter(user__role='creator').delete()
        creators = Creator.objects.filter(user__is_active=True)
        for creator in creators:
            user = creator.user
            offers = user.offer_to_creator.all().order_by('created')
            product_ids = []
            index = 0
            for offer in offers:
                product_user, created = ProductUser.objects.update_or_create(user=user, product=offer.project)
                if offer.project.pk not in product_ids:
                    product_ids.append(offer.project.pk)
                    product_user.order = index
                    product_user.save()
                    index += 1
    except:
        print(sys.exc_info()[0])


class Command(BaseCommand):
    help = "create product_user for role creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        create_product_creator()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
