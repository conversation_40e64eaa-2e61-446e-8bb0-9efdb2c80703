import json

from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import TopicGallery, SelectionGallery, OfferProduct, SelectionOffer, Product


def update_data_offer_product():
    try:
        topic_branding = TopicGallery.objects.filter(title='ソニックブランディング').first()
        topic_entertainment = TopicGallery.objects.filter(title='エンタメ').first()
        topic_program = TopicGallery.objects.filter(title='番組').first()
        offer_products = OfferProduct.objects.filter(project__isnull=False)
        for offer in offer_products:
            topic = None
            offers = OfferProduct.objects.filter(pk=offer.pk)
            project_type = offer.project_type
            if project_type == 'branding':
                topic = topic_branding
            elif project_type == 'entertainment':
                topic = topic_entertainment
            else:
                topic = topic_program

            bgm = offer.get_bgm_display()
            se = offer.get_se_display()
            voice = offer.get_voice_display()
            selection_bgm = topic.selections.filter(title='BGM').first()
            selection_se = topic.selections.filter(title='SE').first()
            selection_voice = topic.selections.filter(title='VOICE').first()
            SelectionOffer.objects.create(offer=offer, selection=selection_bgm,
                                          selection_content=bgm)
            SelectionOffer.objects.create(offer=offer, selection=selection_se,
                                          selection_content=se)
            SelectionOffer.objects.create(offer=offer, selection=selection_voice,
                                          selection_content=voice)

            offers.update(topic=topic)
            if offer.condition in ['1', '2']:
                projects = Product.objects.filter(pk=offer.project.pk)
                projects.update(name=offers[0].topic.title)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update data for offer product"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_data_offer_product()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
