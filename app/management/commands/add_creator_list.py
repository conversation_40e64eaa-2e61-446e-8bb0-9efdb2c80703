from django.core.management.base import BaseCommand

from accounts.models import CreatorList


def add_creator_list():
    list_creator_new = CreatorList.objects.create(title='NEW', description='NEW', is_default=True)
    list_creator_new.order = CreatorList.objects.order_by('-order').first().order + 1
    list_creator_new.save()

    list_creator_confirm = CreatorList.objects.create(title='承認待ち', description='承認待ち', is_default=True)
    list_creator_confirm.order = CreatorList.objects.order_by('-order').first().order + 1
    list_creator_confirm.save()


class Command(BaseCommand):
    help = "creator list creator NEW and 承認待ち"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("start convert!")
        add_creator_list()
        self.stdout.write(self.style.SUCCESS("convert successed!"))
