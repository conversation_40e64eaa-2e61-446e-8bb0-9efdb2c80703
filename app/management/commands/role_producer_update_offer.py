import datetime

from dateutil.relativedelta import relativedelta
from django.core.management.base import BaseCommand
from django.db import transaction

from accounts.models import AuthUser, ProductUser
from app.models import OfferCreator, OfferProduct, OfferProject, OfferUser, OfferMessage, OfferMessageReceiver, Product
from app.tasks import add_member_into_offer_creator


def role_producer_update_offer():
    try:
        with transaction.atomic():
            # update offer creator, offer product into table offer project
            # offer user: all member in offer
            print('----START UPDATE OFFER CREATOR----')

            offer_creators = OfferCreator.objects.filter(project__isnull=False)
            for offer_creator in offer_creators:
                if offer_creator.status == OfferCreator.STATUS_REJECT:
                    offer_status = '3'
                elif offer_creator.status in OfferCreator.STATUS_IN_PROGRESS:
                    offer_status = '1'
                else:
                    offer_status = '2'
                offer, created = OfferProject.objects.get_or_create(offer_creator=offer_creator,
                                                                    project=offer_creator.project,
                                                                    type_offer=OfferProject.OFFER_CREATOR,
                                                                    offer_status=offer_status)


                OfferUser.objects.get_or_create(offer=offer, user=offer_creator.admin, position=OfferUser.ADMIN)
                OfferUser.objects.get_or_create(offer=offer, user=offer_creator.creator, position=OfferUser.CREATOR)

            print('----END UPDATE OFFER CREATOR----')

            print('----START UPDATE OFFER PRODUCT----')

            offer_products = OfferProduct.objects.filter(project__isnull=False)
            for offer_product in offer_products:

                if offer_product.condition == OfferProduct.STATUS_DELETED:
                    offer_status = '3'
                elif offer_product.condition in OfferProduct.STATUS_IN_PROGRESS:
                    offer_status = '1'
                else:
                    offer_status = '2'
                offer, created = OfferProject.objects.get_or_create(offer_product=offer_product,
                                                                    project=offer_product.project,
                                                                    type_offer=OfferProject.OFFER_PRODUCT,
                                                                    offer_status=offer_status)

                master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN)
                for master_admin in master_admins:
                    OfferUser.objects.get_or_create(offer=offer, user=master_admin, position=OfferUser.MASTER_ADMIN)
                owners = offer_product.project.get_member_offer_project().filter(role=AuthUser.MASTERCLIENT)
                for owner in owners:
                    OfferUser.objects.get_or_create(offer=offer, user=owner, position=OfferUser.OWNER)

            print('----END UPDATE OFFER PRODUCT----')

            print('----START UPDATE OFFER MESSAGE----')
            # update offer creator form 1-1 to 1-n
            messages = OfferMessage.objects.filter(offer__isnull=False, owner__isnull=False, offer__admin__isnull=False,
                                                   offer__creator__isnull=False)
            for message in messages:
                offer = message.offer
                message.user = message.owner
                message.comment = message.content
                message.save()
                admin = offer.admin
                creator = offer.creator
                user = admin if message.owner == creator else admin
                if message.type_message != '2':
                    OfferMessageReceiver.objects.get_or_create(user=user, message=message, seen_date=message.seen_date)

            print('----END UPDATE OFFER MESSAGE----')

            # create offer creator for director
            message_content = '下記の全サウンド開発業務をお願い致します。\n ・SOREMO WEB サービス上での顧客対応\n・SOREMO WEB サービス上での納品\n・Box ストレージサービス上でのPremiere、Nuendo プロジェクトの納品'
            scenes = '委任契約'
            data_format = '顧客の指定する形式'
            data_quantity = '一式'

            print('----START UPDATE OFFER FOR DIRECTOR----')
            master_admin = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).first()
            projects = Product.original_objects.filter()
            for project in projects:
                pus = ProductUser.objects.filter(product=project, position=ProductUser.DIRECTOR)
                directors = AuthUser.objects.filter(pk__in=pus.values_list('user_id'))
                today = datetime.datetime.today().replace(hour=0, minute=0, second=0, microsecond=0)
                start_time = project.start_time or today
                end_time = project.end_time or today + relativedelta(months=1)
                if start_time > end_time:
                    temp = start_time
                    start_time = end_time
                    end_time = temp
                for director in directors:
                    product_user = ProductUser.objects.filter(product=project, user=director).first()
                    if not product_user:
                        continue
                    budget = 0
                    if product_user and product_user.budget:
                        budget = product_user.budget

                    offer, created = OfferCreator.objects.get_or_create(admin=master_admin, creator=director,
                                                                        reward=budget, message=message_content,
                                                                        scenes=scenes, project=project, status='2',
                                                                        quantity=data_quantity, data_format=data_format,
                                                                        start_time=start_time, deadline=end_time,
                                                                        contract='9', type_contract='ディレクター')
                    OfferMessage.objects.get_or_create(offer=offer, owner=master_admin,
                                                       user=master_admin,
                                                       content=offer.message,
                                                       comment=offer.message)
                    add_member_into_offer_creator(offer)

            print('----END UPDATE OFFER FOR DIRECTOR----')
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update offer creator, offer product into table offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        role_producer_update_offer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
