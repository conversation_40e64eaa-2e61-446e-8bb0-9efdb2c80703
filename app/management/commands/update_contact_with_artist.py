from django.core.management.base import BaseCommand
from accounts.models import AuthUser, ProductUser
from app.models import Product


def update_contact_with_artist():
    projects = Product.objects.filter()
    try:
        for project in projects:
            if project.productuser_set.filter(user__role=AuthUser.MASTERADMIN, user__is_active=True).exists():
                continue
            if project.productuser_set.filter(user__role=AuthUser.CREATOR, user__is_active=True,
                                              position=ProductUser.PRODUCER, is_super_producer=True).exists():
                Product.objects.filter(pk=project.pk).update(contact_artist=True)

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update project contact direct with artist"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_contact_with_artist()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
