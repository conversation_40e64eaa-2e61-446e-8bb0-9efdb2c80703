
from django.core.management.base import BaseCommand

from app.models import SceneTitle


def update_status_scene_title():
    try:
        scene_titles = SceneTitle.objects.filter(status='1')
        for scene_title in scene_titles:
            version_scenes = scene_title.scene_title.filter(product_scene__isnull=False, version__isnull=False).exists()
            if version_scenes:
                scene_title.status = '2'
                scene_title.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update status = 2 for scene_title has scene with version"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_status_scene_title()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
