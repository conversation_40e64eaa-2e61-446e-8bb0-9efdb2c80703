
from django.core.management.base import BaseCommand

from app.models import Product, ProductScene


def update_order_product_scene():
    try:
        products = Product.objects.all()
        for product in products:
            product_scenes = ProductScene.original_objects.filter(product_scene=product).order_by('-order')
            if product_scenes.exists():
                for index, ps in enumerate(product_scenes):
                    ps.order = index + 1
                    ps.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update order product scene"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_order_product_scene()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
