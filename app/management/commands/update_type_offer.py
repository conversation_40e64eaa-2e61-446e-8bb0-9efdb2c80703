
from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import OfferProduct, PlanOffer


def update_type_offer():
    try:
        offers = OfferProduct.objects.filter(
            Q(project__isnull=False) & (Q(plan_offer__isnull=True) | Q(contact__isnull=True)))
        if offers.exists():
            offers.update(type='2')
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update master admin upload plan for offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_type_offer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
