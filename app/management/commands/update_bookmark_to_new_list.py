from django.core.management.base import BaseCommand

from django.db.models import Q, Avg

from app.models import ListBookMark, SceneTitleBookmark, BookmarkListBookMarks
from accounts.models import AuthUser


def update_bookmark_to_new_list():
    try:
        all_users = AuthUser.objects.all()
        for user in all_users:
            bookmarks = SceneTitleBookmark.objects.filter(user=user)
            collection = ListBookMark.objects.filter(user=user).order_by("created").first()
            if not collection:
                collection = ListBookMark.objects.create(user=user, title=user.fullname)
            order = 1
            for bookmark in bookmarks:
                BookmarkListBookMarks.objects.create(scenetitlebookmark=bookmark, listbookmark=collection, order=order)
                order += 1
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "Update bookmark to new list collection"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_bookmark_to_new_list()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
