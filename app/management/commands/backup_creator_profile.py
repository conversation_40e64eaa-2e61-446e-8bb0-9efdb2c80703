
from django.core.management.base import BaseCommand

from accounts.models import Creator, CreatorProfile, AuthUser
from app.models import Sale<PERSON>ontent, ContentSale, SaleContentVersion, AlbumVariation, AlbumVersion, Audio


def backup_creator_profile():
    try:
        creators = Creator.objects.all()
        for creator in creators:
            creator_profile, created = CreatorProfile.objects. \
                get_or_create(creator=creator,
                              status='1',
                              banner=creator.banner,
                              official_site=creator.official_site,
                              twitter_link=creator.twitter_link,
                              instagram_link=creator.instagram_link,
                              stage_name=creator.stage_name,
                              stage_name_en=creator.stage_name_en,
                              type=creator.type,
                              theme_quote=creator.theme_quote,
                              profile_quote=creator.profile_quote,
                              x_banner=creator.x_banner,
                              y_banner=creator.y_banner,
                              width_banner=creator.width_banner,
                              height_banner=creator.height_banner,
                              avatar=creator.user.avatar,
                              x=creator.user.x,
                              y=creator.user.y,
                              width=creator.user.width,
                              height=creator.user.height,
                              owner=creator.user)
            creator.last_published_version = creator_profile
            creator.save()

        content_sales = ContentSale.objects.all()
        for content_sale in content_sales:
            creator = content_sale.owner
            image_sale = content_sale.image_sale.first()
            profile = creator.last_published_version
            sale_content, created = SaleContent.objects.get_or_create(profile=profile)
            if image_sale:
                sale_content_version = SaleContentVersion.objects.create(sale=sale_content,
                                                                         title=content_sale.title,
                                                                         price=content_sale.price,
                                                                         desc=content_sale.desc,
                                                                         status=content_sale.status,
                                                                         version_status='1',
                                                                         image=image_sale.image,
                                                                         x=image_sale.x,
                                                                         y=image_sale.y,
                                                                         width=image_sale.width,
                                                                         height=image_sale.height)
            else:
                sale_content_version = SaleContentVersion.objects.create(sale=sale_content,
                                                                         title=content_sale.title,
                                                                         price=content_sale.price,
                                                                         desc=content_sale.desc,
                                                                         status=content_sale.status,
                                                                         version_status='1')
            if sale_content_version:
                sale_content.last_published_version = sale_content_version
                sale_content.save()
                audio_sales = content_sale.audio_sale.all()
                for audio_sale in audio_sales:
                    album_variation = AlbumVariation.objects.create(sale_content=sale_content_version)
                    album_version = AlbumVersion.objects.create(album=album_variation,
                                                                file=audio_sale.file,
                                                                status='1')
                    if album_version:
                        album_variation.last_published_version = album_version
                        album_variation.save()

        audios = Audio.objects.all()
        for audio in audios:
            creator = audio.user
            profile = creator.last_published_version
            sale_content = SaleContent.objects.create(profile=profile)
            sale_content_version = SaleContentVersion.objects.create(sale=sale_content, version_status='1')
            if sale_content_version:
                sale_content.last_published_version = sale_content_version
                sale_content.save()
                album_variation = AlbumVariation.objects.create(sale_content=sale_content_version)
                album_version = AlbumVersion.objects.create(album=album_variation, file=audio.audio, status='1')
                if album_version:
                    album_variation.last_published_version = album_version
                    album_variation.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "backup information for creator profile"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        backup_creator_profile()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
