from django.core.management.base import BaseCommand
from django.db import transaction

from accounts.models import <PERSON><PERSON>, Auth<PERSON><PERSON>, CreatorPro<PERSON>le, ProductUser
from app.models import Product


def create_creator_for_admin():
    try:
        with transaction.atomic():
            directors = AuthUser.objects.filter(role='admin')
            for director in directors:
                creator, created = Creator.objects.update_or_create(user=director)
                if not creator.last_published_version:
                    creator_profile = CreatorProfile.objects.create(creator=creator, status='1')
                    creator.last_published_version = creator_profile
                    creator.save()

            products = Product.objects.all()
            for product in products:
                product_users = product.productuser_set.all()
                update_position_depend_on_role(product_users, AuthUser.MASTERADMIN, ProductUser.MASTERADMIN)
                update_position_depend_on_role(product_users, AuthUser.MASTERCLIENT, ProductUser.OWNER)
                update_position_depend_on_role(product_users, AuthUser.CREATOR, ProductUser.DIRECTOR)
                update_position_depend_on_role(product_users, AuthUser.CREATOR, ProductUser.STAFF)

            creators = AuthUser.objects.filter(role=AuthUser.CREATOR)
            if creators:
                creators.update(role=AuthUser.CREATOR)

    except:
        print("Error! Try again!")


def update_position_depend_on_role(product_users, role, position):
    pu = product_users.filter(user__role=role)
    if role == AuthUser.MASTERCLIENT:
        pu = pu.filter(is_owner='1')
    if pu.exists():
        pu.update(position=position)


class Command(BaseCommand):
    help = "create data in model Creator, CreatorProfile for admin"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        create_creator_for_admin()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
