from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import SceneTitle, SceneComment, Scene


def update_modified_scene_title():
    try:
        scene_titles = SceneTitle.original_objects.all()

        for st in scene_titles:
            date_time = None
            scenes = Scene.original_objects.filter(title=st)
            comment = SceneComment.objects.filter(Q(scene__in=scenes) | Q(scene_title=st)).order_by('-modified').first()
            if comment or scenes.exists():
                if comment and scenes.exists():
                    last_scene = scenes.order_by('-modified').first()
                    date_time = last_scene.modified
                    if comment and comment.modified > date_time:
                        date_time = comment.modified
                elif comment:
                    date_time = comment.modified
                elif scenes.exists():
                    last_scene = scenes.order_by('-modified').first()
                    date_time = last_scene.modified
            else:
                date_time = st.created

            if date_time:
                st.updated_at = date_time
                st.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update real updated_at for scene title"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_modified_scene_title()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
