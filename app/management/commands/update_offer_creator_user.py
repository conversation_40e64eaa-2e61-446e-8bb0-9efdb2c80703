
from django.core.management.base import BaseCommand
from django.db import transaction

from app.models import Product, OfferUser, OfferProject, OfferCreator
from accounts.models import ProductUser, AuthUser
from app.tasks import add_remove_member_into_offer_product


def update_offer_creator_user():
    try:
        offers = OfferCreator.objects.filter(admin__role=AuthUser.MASTERADMIN)
        with transaction.atomic():
            for offer in offers:
                real_offer = offer.offer
                if real_offer:
                    OfferUser.objects.filter(user__role=AuthUser.CREATOR, position=AuthUser.CREATOR,
                                             offer=real_offer).delete()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update offer user for offer creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_offer_creator_user()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
