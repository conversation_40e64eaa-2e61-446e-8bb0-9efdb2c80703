from django.core.management.base import BaseCommand
from django.db.models import Q
import re

from app.models import SceneComment


def update_pin_video_scene_comment():
    try:
        comments = SceneComment.objects.exclude(Q(pin_time__isnull=False) | Q(pin_time=''))
        for comment in comments:
            if comment.pin_time and comment.scene:
                comment.pin_video = comment.scene_id
                scene = comment.scene
                name = scene.movie.name
                file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
                file_extension = file_extension.lower()
                if file_extension in '.pdf, .PDF':
                    comment.pin_time = None
                comment.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update field has pin for comment scene"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_pin_video_scene_comment()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
