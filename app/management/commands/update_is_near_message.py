
from django.core.management.base import BaseCommand
import datetime

from app.models import OfferMessage, OfferCreator


def update_is_near_message():
    offers = OfferCreator.objects.filter(creator__is_active=True, admin__is_active=True)

    for offer in offers:
        if offer.message_offer.exists():
            admin_messages = offer.message_offer.filter(owner__role='admin').order_by('created')
            creator_messages = offer.message_offer.filter(owner__role='creator').order_by('created')

            for count, item in enumerate(admin_messages):
                if count > 0:
                    time = item.created.timestamp()
                    before_message = admin_messages[count-1]
                    before_time = before_message.created.timestamp()
                    if time - before_time < 30 and not OfferMessage.objects.filter(offer=offer, owner__role='creator', created__lte=item.created, created__gte=before_message.created).exists():
                        before_message.is_near = True
                        before_message.save()

            for count, item in enumerate(creator_messages):
                if count > 0:
                    time = item.created.timestamp()
                    before_message = creator_messages[count-1]
                    before_time = before_message.created.timestamp()
                    if time - before_time < 30 and not OfferMessage.objects.filter(offer=offer, owner__role='admin', created__lte=item.created, created__gte=before_message.created).exists():
                        before_message.is_near = True
                        before_message.save()


class Command(BaseCommand):
    help = "update is_near into offer_message to show with small margin"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_is_near_message()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
