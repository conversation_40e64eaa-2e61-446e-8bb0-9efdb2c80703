
from django.core.management.base import BaseCommand

from accounts.models import AuthUser, CreatorFile


def update_artist_file():
    artists = AuthUser.objects.filter(role=AuthUser.CREATOR, user_file__isnull=False).exclude(user_file='')
    for artist in artists:
        creator = artist.user_creator.first()
        creator.creator_file_status = '1'
        creator.save()
        file = artist.user_file
        real_name = artist.user_file_name
        CreatorFile.objects.create(file=file, real_name=real_name, owner=artist, creator=creator)


class Command(BaseCommand):
    help = "update file artist upload"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_artist_file()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
