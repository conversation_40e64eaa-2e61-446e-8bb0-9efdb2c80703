from django.core.management.base import BaseCommand

from app.models import Offer<PERSON><PERSON>
from app.tasks import *


def send_mail_when_admin_upload_file():
    offers = OfferCreator.objects.filter(admin__is_active=True, creator__is_active=True)
    for offer in offers:
        if offer.message_offer.exists():
            last_message = offer.last_message()
            if last_message.real_name and not last_message.seen_date and last_message.owner.role == 'admin':
                recipient = offer.creator
                if recipient.user_creator.first().notification == 'immediately':
                    type = 'new_message'
                    offer_id = offer.pk
                    sender_id = last_message.owner.pk
                    recipient_id = recipient.pk
                    scheme = ''
                    host = settings.HOST
                    if offer.status in ['1', '2']:
                        url = reverse(
                            "app:messenger_waiting") + f'?project_id={offer.project.pk}&offer={offer.pk}'
                    else:
                        url = reverse(
                            "app:messenger_processing") + f'?project_id={offer.project.pk}&offer={offer.pk}'
                    path = "{host}{url}".format(host=host, url=url)
                    send_mail_offer_creator.delay(type, offer_id, recipient_id, sender_id, scheme, host, path)


class Command(BaseCommand):
    help = "fix send file when admin upload file"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        send_mail_when_admin_upload_file()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
