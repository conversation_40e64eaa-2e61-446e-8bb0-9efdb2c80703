
from django.core.management.base import BaseCommand

from app.models import Product, OfferProduct
from accounts.models import AuthUser


def create_offer_for_old_product():
    try:
        new_products = OfferProduct.objects.filter(status='6').values_list('project')
        old_products = Product.objects.filter(is_active=True).exclude(pk__in=new_products)
        master_admin = AuthUser.objects.filter(role='master_admin').first()
        for product in old_products:
            offer = OfferProduct.objects.create(master_admin=master_admin, status=6, project=product)
            if product.owner and offer:
                offer.master_client = product.owner
                offer.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "create offer for old product to payment"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        create_offer_for_old_product()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
