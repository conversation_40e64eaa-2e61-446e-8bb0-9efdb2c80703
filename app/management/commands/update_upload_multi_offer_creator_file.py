
from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import OfferCreator, CreatorOfferFile

class Command(BaseCommand):
    help = "Migrate data from OfferCreator to CreatorOfferFile"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start migration data__________")
        offers = OfferCreator.objects.filter(Q(file__isnull=False) & ~Q(file='')).values('id', 'file')
        for offer in offers:
            offer_file = CreatorOfferFile(offer_id=offer['id'], file=offer['file'])
            offer_file.save()
        self.stdout.write(self.style.SUCCESS("__________Data migration complete'__________"))
