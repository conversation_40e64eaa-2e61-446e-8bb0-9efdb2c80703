from django.core.management.base import BaseCommand
from accounts.models import ProductUser


def update_view_only():
    try:
        #ProductUser.objects.filter(is_owner='0', is_rating=False, is_favorite=False).update(view_only=True)
        ProductUser.objects.filter(user__role__in=['master_admin', 'admin']).update(view_only=False)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update view_ply=False for master admin, admin, owner project"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_view_only()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
