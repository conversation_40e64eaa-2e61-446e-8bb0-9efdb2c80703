
from django.core.management.base import BaseCommand
from django.db import transaction

from accounts.models import Creator


def update_artist_info():
    with transaction.atomic():
        try:
            creators = Creator.objects.filter(user__is_active=True)
            for creator in creators:
                print(creator.pk)
                user = creator.user
                profile = None
                last_version = creator.last_version
                last_published_version = creator.last_published_version
                profile_avatar = None

                if last_version:
                    profile = last_version
                elif last_published_version:
                    profile = last_published_version
                if last_version and last_version.avatar:
                    profile_avatar = last_version
                else:
                    profile_avatar = last_published_version
                if profile_avatar:
                    user.avatar = profile_avatar.avatar
                    if profile_avatar.x > 0:
                        user.x = profile_avatar.x
                        user.y = profile_avatar.y
                        user.height = profile_avatar.height
                        user.width = profile_avatar.width
                if profile:
                    user.stage_name = profile.stage_name
                    user.stage_name_en = profile.stage_name_en
                    user.type = profile.type
                user.dob = creator.dob
                user.post_number = creator.post_number
                user.province = creator.province
                user.city = creator.city
                user.mansion = creator.mansion
                user.phone = creator.phone
                user.bank = creator.bank
                user.bank_branch = creator.bank_branch
                user.bank_branch_number = creator.bank_branch_number
                user.account_type = creator.account_type
                user.account_number = creator.account_number
                user.save()

            creators = Creator.objects.filter(last_version__isnull=False, last_published_version__isnull=False)

            list_key = ["banner", "official_site", "twitter_link", "facebook_link", "instagram_link", "youtube_link",
                        "theme_quote", "profile_quote", "x_banner", "y_banner", "width_banner", "height_banner"]
            for creator in creators:
                last_profile = creator.last_version
                public_profile = creator.last_published_version
                real_change = False
                for key in list_key:
                    current_value = getattr(public_profile, key)
                    new_value = getattr(last_profile, key)
                    if current_value != new_value:
                        real_change = True
                        break

                last_sale_contents = last_profile.content_profile.all()
                public_sale_contents = last_profile.content_profile.all()
                if last_sale_contents.count() != public_sale_contents.count():
                    real_change = True
                else:
                    for sale_content in public_sale_contents:
                        if sale_content.last_version:
                            real_change = True
                            break
                        audios = sale_content.last_published_version.album.all()
                        for audio in audios:
                            if audio.last_version:
                                real_change = True
                                break

                if real_change is False:
                    last_profile.status = '3'
                    last_profile.save()
                    creator.last_version = None

            print(creator.pk)

        except:
            print("Error! Try again!")


class Command(BaseCommand):
    help = "update data for aritst"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_artist_info()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
