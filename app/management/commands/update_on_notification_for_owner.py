from django.core.management.base import BaseCommand

from accounts.models import ProductUser


def update_on_notification_for_owner():
    try:
        ProductUser.objects.filter(position__in=[ProductUser.OWNER, ProductUser.DIRECTOR],
                                   notification='off').update(notification='on')
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "set notification for owner is ON"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_on_notification_for_owner()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
