from django.core.management.base import BaseCommand

from app.models import OfferMessage


def rename_file_offer_message():
    messages = OfferMessage.objects.filter(real_name__contains='file/')
    for message in messages:
        real_name = message.real_name
        message.real_name = real_name.replace('file/', '')
        message.save()


class Command(BaseCommand):
    help = "rename for offer_message has /file"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        rename_file_offer_message()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
