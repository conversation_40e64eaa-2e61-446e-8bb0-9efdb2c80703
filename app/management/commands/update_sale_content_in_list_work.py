
from django.core.management.base import BaseCommand

from app.models import SaleContent


def update_sale_content_in_list_work():
    try:
        sale_contents = SaleContent.objects.filter(profile__status='1')
        for sale_content in sale_contents:
            sale_last_version = sale_content.last_version
            if sale_last_version:
                sale_content.last_published_version = sale_last_version
                sale_content.last_version = None
            sale_content.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update content type from 1 to music"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_sale_content_in_list_work()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
