from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import Scene, SceneCommentFile


def reset_acr_result():
    number_scenes = Scene.objects.filter(~Q(movie__endswith='.pdf')).update(acr_result='チェッキング')
    number_scene_comment_files = SceneCommentFile.objects.filter(Q(file__endswith='.mp3') | Q(file__endswith='.wav') |
                                                                 Q(file__endswith='.mp4') | Q(file__endswith='.x-m4v') | Q(file__endswith='.avi') |
                                                                 Q(file__endswith='.webm') | Q(file__endswith='.mov')).update(acr_result='チェッキング')
    return number_scenes, number_scene_comment_files


class Command(BaseCommand):
    help = 'reset old acr result to processing'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start__________')
        number_scenes, number_scene_comment_files = reset_acr_result()
        self.stdout.write(f'Scene number updated: {number_scenes}.\nScene Comment File number updated: {number_scene_comment_files}.')
        self.stdout.write(self.style.SUCCESS('__________successed!__________'))
