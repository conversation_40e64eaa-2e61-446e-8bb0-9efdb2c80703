
from django.core.management.base import BaseCommand

from app.models import Product


def update_staff_credit_session_and_item():
    try:
        products = Product.objects.all()
        for project in products:
            sections = project.sections.order_by('order')
            index = 0
            for section in sections:
                section.order = index
                section.save()
                index += 1
                items = section.items.order_by('order')
                for item in items:
                    item.project = section.project
                    item.order = index
                    item.save()
                    index += 1
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update to unlink session with it item"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_staff_credit_session_and_item()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
