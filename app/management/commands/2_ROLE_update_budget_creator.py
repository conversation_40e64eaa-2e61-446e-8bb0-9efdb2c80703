from django.core.management.base import BaseCommand
from accounts.models import ProductUser


def update_budget_creator():
    try:
        product_users = ProductUser.objects.filter()
        for pu in product_users:
            if pu.position == ProductUser.STAFF:
                offers = pu.product.product_offers.filter(creator=pu.user).exclude(status='5')
                if not offers.exists():
                    pu.delete()
                    continue
            pu.budget_offer = pu.budget
            pu.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update total budget = budget master admin + budget offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_budget_creator()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
