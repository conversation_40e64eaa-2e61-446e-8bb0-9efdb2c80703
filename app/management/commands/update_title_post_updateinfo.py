from django.core.management.base import BaseCommand

from app.models import Post


def update_title_post_updateinfo():
    try:
        posts = Post.objects.all()

        for post in posts:
            start_time = post.start_time
            end_time = post.end_time
            text_range = start_time.strftime('%y/%-m/%-d') + ' - ' + end_time.strftime('%y/%-m/%-d')
            post.text_range = text_range
            post.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update text range for post in updateinfo"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_title_post_updateinfo()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
