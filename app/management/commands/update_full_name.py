
from django.core.management.base import BaseCommand

from app.models import Product
from accounts.models import ProductUser, AuthUser


def update_full_name():
    for user in AuthUser.objects.filter(is_active=True):
        try:
            user.save()
        except:
            print("Error! Try again!")


class Command(BaseCommand):
    help = "update fullname when edited firstname/lastname"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_full_name()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
