# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 16/12/2021
from django.core.management.base import BaseCommand

from app.models import FormContractAndPlan


def update_offer_product_for_form_contract_and_plan():
    counter = 0
    for form in FormContractAndPlan.objects.all():
        product_message_file = form.product_message_files.last()
        if product_message_file:
            form.offer_product = product_message_file.offer
            form.save()
            counter += 1

    return counter


class Command(BaseCommand):
    help = 'Update value for offer product field of form contract and plans'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start update offer product for form contract and plans__________')
        num_records = update_offer_product_for_form_contract_and_plan()
        self.stdout.write(f'=============> Updated {num_records} records.')
        self.stdout.write(self.style.SUCCESS('__________done!__________'))
