from django.core.management.base import BaseCommand

from app.models import Scene, SceneComment
from custom_acr.customrecognize import ERROR_READFILE_MESSAGE


def recheck_acr_for_error_objects():
    try:
        scenes = Scene.objects.filter(acr_result__contains=ERROR_READFILE_MESSAGE).update(acr_result=None)
        comments = SceneComment.objects.filter(acr_result__contains=ERROR_READFILE_MESSAGE).update(acr_result=None)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "get all records error when check with ACR and reset value as None"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        recheck_acr_for_error_objects()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
