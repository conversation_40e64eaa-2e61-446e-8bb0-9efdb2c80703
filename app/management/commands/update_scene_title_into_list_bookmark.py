
from django.core.management.base import BaseCommand

from app.models import BookmarkListBookMarks, ListBookMark, SceneTitle, SceneTitleBookmark
from accounts.models import AuthUser


def update_scene_title_into_list_bookmark():
    try:
        # delete bookmark when product scene  deleted
        title_deleted = SceneTitle.original_objects.filter(product_scene__deleted_on__isnull=False)
        SceneTitleBookmark.objects.filter(title__in=title_deleted).delete()

        # update scene into first list
        users = AuthUser.objects.filter(is_active=True)
        for user in users:
            if not user.bookmarks.exists():
                continue
            list_bookmark = user.list_bookmarks.all().first()
            if not list_bookmark:
                list_bookmark = ListBookMark.objects.create(title='お気に入り', user=user)

            if list_bookmark:
                scene_marks = user.bookmarks.all()
                for scene_mark in scene_marks:
                    if BookmarkListBookMarks.objects.filter(scenetitlebookmark=scene_mark,
                                                            listbookmark=list_bookmark).exists():
                        continue
                    if not list_bookmark.item_ids.count():
                        order = 1
                    else:
                        order = max(list_bookmark.item_ids.values_list("order", flat=True)) + 1
                    BookmarkListBookMarks.objects.create(scenetitlebookmark=scene_mark,
                                                         listbookmark=list_bookmark, order=order)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "Update scene title into first list bookmark"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_scene_title_into_list_bookmark()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
