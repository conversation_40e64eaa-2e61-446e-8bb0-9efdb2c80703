
from django.core.management.base import BaseCommand

from app.models import Product, ProductOrderUpload, OfferProduct
from accounts.models import ProductUser, AuthUser


def update_receipt_id():
    try:
        product_order = ProductOrderUpload.objects.filter(tag='3', type_file_pdf='2')
        complete_project = Product.objects.filter(order_upload_product__in=product_order)
        offer_done = OfferProduct.objects.filter(project__in=complete_project)
        offer_done.update(receipt_id='done')
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update receipt_id for old product"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_receipt_id()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
