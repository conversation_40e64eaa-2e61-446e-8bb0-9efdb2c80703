from django.core.management.base import BaseCommand

from accounts.models import AuthUser, ProductUser


def turn_on_top_notification_for_admin():
    try:
        admin_productuser_count = ProductUser.objects.filter(user__role__in=['master_admin', 'admin'], notification='off')\
            .update(notification='on')
        print("updated {} records!".format(admin_productuser_count))
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "turn on top notification for admin/master_admin"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        turn_on_top_notification_for_admin()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
