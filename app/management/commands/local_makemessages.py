import os

from django.core.management.commands.makemessages import Command as BaseCommand
from django.core.management.commands.makemessages import BuildFile as BaseBuildFile
from django.utils.functional import cached_property


class BuildFile(BaseBuildFile):
    @cached_property
    def is_templatized(self):
        if self.domain == 'djangojs':
            return True
            # return self.command.gettext_version < (0, 18, 3)
        elif self.domain == 'django':
            file_ext = os.path.splitext(self.translatable.file)[1]
            return file_ext != '.py'
        return False


class Command(BaseCommand):
    build_file_class = BuildFile
