
from django.core.management.base import BaseCommand

from app.models import Product, SaleContent
from accounts.models import <PERSON><PERSON><PERSON>, <PERSON>th<PERSON><PERSON>, CreatorProfile, Creator


def update_order_sale_content():
    try:
        creators = Creator.objects.filter(user__is_active=True)
        for creator in creators:
            creator_profiles = creator.creator_profile.filter(status__in=('1', '2'))
            for profile in creator_profiles:
                sale_contents = profile.content_profile.all().order_by('modified')
                if sale_contents.exists():
                    for index, sale_content in enumerate(sale_contents):
                        sale_content.order = index
                        sale_content.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update order sale content by modififed"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_order_sale_content()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
