from django.core.management.base import BaseCommand

from app.models import Scene


def convert_version_to_uuid():
    children = Scene.objects.filter(version__isnull=False)
    for child in children:
        try:
            child.version = str(child.version).replace('-', '')
            child.save()
        except:
            pass


class Command(BaseCommand):
    help = "convert version of scene to uuid before migrate"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("start convert!")
        convert_version_to_uuid()
        self.stdout.write(self.style.SUCCESS("convert successed!"))
