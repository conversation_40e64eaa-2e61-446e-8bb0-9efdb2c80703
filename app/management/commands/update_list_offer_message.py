from django.core.management.base import BaseCommand
from app.models import OfferMessage, MessageFile


class Command(BaseCommand):
    help = "Move all old file in message to MessageFile"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("start move")
        messages = OfferMessage.objects.all()
        for message in messages:
            if message.file:
                MessageFile.objects.create(message=message, file=message.file, peaks=message.peaks,
                                           real_name=message.real_name)
        self.stdout.write(self.style.SUCCESS("convert successed!"))
