from django.core.management.base import BaseCommand

from app.models import Product, SceneTitle


def update_rating_product():
    try:
        products = Product.objects.filter(is_active=True)
        for product in products:
            rating = 0
            product_scenes = product.scene_list.all()
            rating_scene = SceneTitle.objects.filter(product_scene__in=product_scenes, rating__gt=0.0).exclude(
                last_version__isnull=True)
            if rating_scene:
                rating_sum = 0
                for r in rating_scene:
                    rating_sum += r.rating
                rating = rating_sum / rating_scene.count()
                rating = round(rating, 2)
            if product.rating != rating:
                Product.objects.filter(pk=product.pk).update(rating=rating)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update rating for product"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_rating_product()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
