
from django.core.management.base import BaseCommand

from accounts.models import AuthUser


def update_order_list_bookmark():
    try:
        users = AuthUser.objects.all().prefetch_related("list_bookmarks")
        for user in users:
            list_bookmarks = user.list_bookmarks.all()
            for order, list_bookmark in enumerate(list_bookmarks):
                list_bookmark.order = order + 1
                list_bookmark.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_order_list_bookmark()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
