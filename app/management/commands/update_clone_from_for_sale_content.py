
from django.core.management.base import BaseCommand

from app.models import Sale<PERSON>ontent


def update_clone_from_for_sale_content():
    try:
        sale_contents = SaleContent.objects.all()
        for sale_content in sale_contents:
            last_published_version = sale_content.last_published_version
            last_version = sale_content.last_version
            if last_version and last_published_version and last_published_version != last_version:
                old_sale_content = last_published_version.sale
                sale_content.parent = old_sale_content
                sale_content.save()
            elif last_published_version:
                album_variations = sale_content.last_published_version.album.all()
                for album_variation in album_variations:
                    public_version = album_variation.last_published_version
                    new_version = album_variation.last_version
                    if new_version and public_version and new_version != public_version:
                        old_sale_content = public_version.sale_content.sale
                        sale_content.parent = old_sale_content
                        sale_content.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update field clone from ~ parent for sale_content"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_clone_from_for_sale_content()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
