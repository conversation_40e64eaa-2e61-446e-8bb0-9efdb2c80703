from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import ProductMessage, OfferProduct


def update_system_message_offer_product():
    try:
        ProductMessage.objects.filter(type_message='2', comment='見積もり作成中..。今しばらくお待ちください。').update(type_message='3')
        offers = OfferProduct.objects.filter(condition__in=OfferProduct.STATUS_IN_DONE)
        ProductMessage.objects.filter(type_message='3', comment='全てのシーンの納品が完了しました。検収をお願いします。',
                                      offer__pk__in=offers.values_list('pk', flat=True)).delete()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update type message for system message offer product"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_system_message_offer_product()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
