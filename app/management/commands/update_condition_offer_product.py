from django.core.management.base import BaseCommand
from app.models import OfferProduct


def update_condition_offer_product():
    try:
        OfferProduct.objects.filter(receipt_id__isnull=False).update(condition=OfferProduct.STATUS_PAYMENTED)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update condition for offer product payment"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_condition_offer_product()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
