
from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from app.models import OfferCreator, Product, OfferUser
from accounts.models import ProductUser, AuthUser
from app.services import get_budget_creator_can_assign


def update_super_producer():
    try:
        with transaction.atomic():
            product_producer = ProductUser.objects.filter(position=ProductUser.PRODUCER,
                                                          user__is_active=True).values_list('product_id')
            projects = Product.objects.filter(pk__in=product_producer)
            for project in projects:
                product_user = project.productuser_set.filter(position=ProductUser.PRODUCER,
                                                              user__is_active=True).order_by('pk').first()
                project.productuser_set.filter(position=ProductUser.PRODUCER).exclude(pk=product_user.pk).update(
                    current_budget=0, is_super_producer=False)
                if not product_user:
                    continue
                producer = AuthUser.objects.filter(pk=product_user.user_id).first()
                if not producer:
                    continue

                creator = producer.user_creator.first()
                current_budget = get_budget_creator_can_assign(producer, project)
                product_user.current_budget = current_budget
                product_user.usage_fee = creator.usage_fee
                product_user.is_super_producer = True
                product_user.save()

                # reward offer master admin with producer
                OfferCreator.objects.filter(Q(admin__role=AuthUser.MASTERADMIN) & Q(creator=producer)).update(reward=0)


    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update producer to super producer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_super_producer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
