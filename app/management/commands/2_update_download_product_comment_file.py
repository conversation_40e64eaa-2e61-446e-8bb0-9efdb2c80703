from django.core.management.base import BaseCommand

from app.models import DownloadedProductComment, ProductCommentDownloaded


def update_download_product_comment_file():
    try:
        file_downloaded = ProductCommentDownloaded.objects.all()
        for file in file_downloaded:
            comment = file.comment
            owner = file.owner
            if comment.files.exists():
                DownloadedProductComment.objects.get_or_create(file=comment.files.first(), user=owner)

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update model product comment similar offer message"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_download_product_comment_file()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
