from django.core.management.base import BaseCommand
from django.db.models import Q, F
from django.db import transaction

from accounts.models import AuthUser, ProductUser
from accounts.tasks import update_product_user_order
from app.models import VariationOffer, OfferProduct, Product, ProductMessageFile, ProductMessage


# OLD: create offer (1) -> upload variation plan (2) -> checked plan (3) -> update contract (4) -> checked contract  (5) -> create project  (6) -> upload bill -> payment
# NEW: create offer  (1)-> upload contract (2) -> checked contract (3) -> check done offer (4) -> upload bill  (5)  -> payment  (6) -> deleted (7)
# UPDATE STATUS:


def update_offer_product():
    try:
        with transaction.atomic():
            offers = OfferProduct.objects.all()
            # UPDATE OFFER STATUS
            update_offer_status(offers)

            # CREATE PROJECT FOR OFFER 1,2,3,4,5
            create_project_for_offer(offers)

            # UPDATE FILE PLAN, CONTRACT, BILL
            update_variation_file()

            # UPDATE MESSAGE
            ProductMessage.objects.all().update(comment=F('content'))

            # Create offer for project
            projects = Product.objects.filter(offer_product__isnull=True)
            for project in projects:
                OfferProduct.objects.create(budget=project.total_budget, amount=project.total_budget,
                                            start_time=project.start_time, end_time=project.end_time,
                                            deadline=project.end_time, project=project)

            # update default budget
            OfferProduct.objects.filter(budget__isnull=True).update(budget=0)

    except:
        print("Error! Try again!")


def update_offer_status(offers):
    # type = 3: offer is deleted
    offers.filter(type__in=['3']).update(condition='7')

    # type = 4 | receipt_id != null: offer done
    offers.filter(Q(receipt_id__isnull=False) | Q(type='4')).update(condition='6')
    offers = offers.exclude(Q(receipt_id__isnull=False))

    # type = 1: normal flow
    offers_flow = offers.filter(type='1')
    offers_flow.filter(status__in=['2', '3', '4']).update(condition='2')
    offers_flow.filter(status__in=['5', '6']).update(condition='3')
    offers_flow.filter(status='6', variation_offer__bill_name__isnull=False).update(condition='5')

    # type = 2: other flow
    offers_unflow = offers.filter(type='2')
    offers_unflow.filter(variation_offer__isnull=True).update(condition='1')
    offer_uploaded = offers_unflow.filter(variation_offer__isnull=False, variation_offer__is_chosen='2')
    if offer_uploaded.exists():
        offer_uploaded.update(condition='2')
    offers_unflow.filter(Q(variation_offer__bill_name__isnull=False)).update(condition='5')
    offers_unflow.filter(Q(variation_offer__is_chosen='2'), Q(variation_offer__contract_name__isnull=False),
                         Q(variation_offer__bill_name__isnull=True)).exclude(
        pk__in=offer_uploaded.values_list('pk')).update(condition='3')


def create_project_for_offer(offers):
    offer_update = offers.filter(type='1', status__in=['1', '2', '3', '4', '5'], project__isnull=True)
    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN)
    for offer in offer_update:
        project = Product.objects.create(name='新規プロジェクト')
        for master_admin in master_admins:
            user_index = update_product_user_order(master_admin)
            ProductUser.objects.create(user=master_admin, product=project, order=user_index,
                                       position=ProductUser.MASTERADMIN)
        if offer.master_client:
            user_index = update_product_user_order(offer.master_client)
            ProductUser.objects.create(user=offer.master_client, product=project, order=user_index,
                                       position=ProductUser.OWNER, is_owner='1')
        offer.project = project
        offer.save()


def update_variation_file():
    for offer in OfferProduct.objects.filter(Q(file__isnull=False) & ~Q(file='')):
        ProductMessageFile.objects.create(file=offer.file, real_name=offer.real_name, offer=offer,
                                          type_file=ProductMessageFile.OFFER, user=offer.master_client)
    variation_offers = VariationOffer.objects.all()
    variation_old = variation_offers.filter(is_chosen='1')
    variation_new = variation_offers.filter(is_chosen='2')
    for variation in variation_old:
        update_variation(variation)
    for variation in variation_new:
        update_variation(variation)


def update_variation(variation):
    master_admin = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).first()
    offer = variation.offer
    if variation.plan and variation.plan != '':
        ProductMessageFile.objects.create(file=variation.plan, real_name=variation.plan_name, offer=offer,
                                          type_file=ProductMessageFile.CONTRACT, user=master_admin)
    if variation.contract and variation.contract != '':
        ProductMessageFile.objects.create(file=variation.contract, real_name=variation.contract_name, offer=offer,
                                          type_file=ProductMessageFile.CONTRACT, user=master_admin)
    if variation.bill and variation.bill != '':
        ProductMessageFile.objects.create(file=variation.bill, real_name=variation.bill_name, offer=offer,
                                          type_file=ProductMessageFile.BILL, user=master_admin)


class Command(BaseCommand):
    help = "update model for offer product"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_offer_product()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
