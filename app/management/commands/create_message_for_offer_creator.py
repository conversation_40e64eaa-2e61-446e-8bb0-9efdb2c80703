
from django.core.management.base import BaseCommand

from app.models import OfferCreator, OfferMessage


def create_message_for_offer_creator():
    try:
        offers = OfferCreator.objects.filter(admin__is_active=True, creator__is_active=True)
        for offer in offers:
            message_content = offer.message
            file = offer.file
            if message_content or file:
                message = OfferMessage.objects.create(content=message_content, owner=offer.admin, offer=offer)
                if file:
                    message.file = file
                message.created = offer.created
                message.save()
                if offer.message_offer.filter(owner=offer.creator).exists():
                    message.seen_date = offer.message_offer.filter(owner=offer.creator).first().created
                    message.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "create message when create offer creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        create_message_for_offer_creator()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
