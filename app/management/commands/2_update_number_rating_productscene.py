from django.core.management.base import BaseCommand
from django.db.models import Prefetch, Count

from app.models import ProductScene, SceneTitle


def update_number_rating_product_scene():
    try:
        product_scenes = ProductScene.objects.all().prefetch_related(
            Prefetch('title_product_scene',
                     queryset=SceneTitle.objects.filter(last_version__isnull=False).annotate(num_rate=Count("owner_rating_title")), to_attr='scene_titles'
                     ))
        for product_scene in product_scenes:
            num_rate = 0
            for scene_title in product_scene.title_product_scene.all():
                if not scene_title.last_version:
                    continue
                for rating in scene_title.owner_rating_title.all():
                    num_rate += 1
            product_scene.number_rating = num_rate
            product_scene.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update number ratings of product scene"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_number_rating_product_scene()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
