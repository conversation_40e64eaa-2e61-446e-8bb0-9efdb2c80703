
from django.core.management.base import BaseCommand

from accounts.models import Skill


def before_update_account_skill():
    try:
        # list_skill = ['1', '3', '4', '5', '6', '7', '13', '15', '16', '25', '32', '33']
        list_skill = ['58']
        Skill.objects.filter(pk__in=list_skill).delete()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "delete skill in account_skill"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        before_update_account_skill()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
