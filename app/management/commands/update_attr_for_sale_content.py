
from django.core.management.base import BaseCommand

from app.models import Sale<PERSON>ontentVersion


def update_authuser_fullname():
    sale_versions = SaleContentVersion.objects.filter()
    for sale in sale_versions:
        try:
            sale.save()
        except:
            print("Error! Try again!")


class Command(BaseCommand):
    help = "update song attr for sale content"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_authuser_fullname()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
