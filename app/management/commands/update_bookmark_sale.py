
from django.core.management.base import BaseCommand

from app.models import SceneTitleBookmark, ListBookMark, SaleContent, BookmarkListBookMarks


def update_bookmark_sale():
    items = SceneTitleBookmark.objects.filter(type_bookmark=SceneTitleBookmark.BOOKMARK_SALE, sale__isnull=False)
    for item in items:
        sale = item.sale
        if sale.child.exists():
            item.sale = sale.child.first()
            item.save()

    sale_bookmarked = SaleContent.objects.filter(bookmarks__isnull=False)
    sales = sale_bookmarked.filter(profile__status='3')
    SceneTitleBookmark.objects.filter(pk__in=sales.values_list('pk')).delete()
    list_bookmarks = ListBookMark.objects.filter()
    for list_bookmark in list_bookmarks:
        book_mark_sales = list_bookmark.item_ids.filter(scenetitlebookmark__type_bookmark='sale').values_list(
            'scenetitlebookmark__pk', flat=True)
        for sale_marked in book_mark_sales:
            if list_bookmark.item_ids.filter(scenetitlebookmark=sale_marked).count() > 1:
                list_bookmark.item_ids.filter(scenetitlebookmark=sale_marked).first().delete()


class Command(BaseCommand):
    help = "update fullname to new field"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_bookmark_sale()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
