
from django.core.management.base import BaseCommand
from django.db.models import Q
from app.models import SceneTitle, SceneCommentReceiver, Product, ProductScene, SceneComment, Scene, ProductComment


def update_seen_scene_comment():
    try:
        products = Product.objects.filter(is_active=True)
        for product in products:
            users = product.authuser_set.filter(role__in=['master_client', 'admin'])
            product_comments = ProductComment.objects.filter(project=product, receivers__isnull=True)
            if product_comments.exists():
                for user in users:
                    if not product_comments.filter(user=user).exists():
                        for after_comment in product_comments:
                            SceneCommentReceiver.objects.create(user=user, product_comment=after_comment,
                                                                seen_date=None)

            product_scenes = ProductScene.original_objects.filter(product_scene=product)
            scene_titles = SceneTitle.original_objects.filter(product_scene__in=product_scenes)
            for st in scene_titles:
                scenes = Scene.original_objects.filter(title=st)
                comments = SceneComment.objects.filter(Q(scene_title=st) | Q(scene__in=scenes), receivers__isnull=True)
                if comments.exists():
                    for user in users:
                        if not comments.filter(user=user).exists():
                            for after_comment in comments:
                                SceneCommentReceiver.objects.create(user=user, message=after_comment, seen_date=None)

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update table user seen scene comment"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_seen_scene_comment()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
