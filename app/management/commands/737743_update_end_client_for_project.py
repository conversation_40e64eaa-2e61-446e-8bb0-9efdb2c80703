from django.core.management.base import BaseCommand

from django.db.models import Q, Avg

from accounts.models import ProductUser
from app.models import Product


def update_end_client_for_project():
    try:
        projects = Product.objects.filter(Q(client_name__isnull=True) | Q(client_name=''))
        for project in projects:
            owners = project.productuser_set.filter(position=ProductUser.OWNER)
            if owners.count() == 1:
                project.end_client = owners.first().user.enterprise
                project.save()

        projects = Product.objects.filter(Q(information__isnull=False), ~Q(information=''))
        for project in projects:
            project.real_name = project.information.name
            project.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "Update initial for project by company name owner "

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_end_client_for_project()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
