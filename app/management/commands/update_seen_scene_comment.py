
from django.core.management.base import BaseCommand
from django.db.models import Q
from app.models import SceneTitle, SceneCommentReceiver, Product, ProductScene, SceneComment, Scene, ProductComment


def update_seen_scene_comment():
    try:
        products = Product.objects.filter(is_active=True)
        for product in products:
            users = product.authuser_set.filter(role__in=['master_client', 'admin'])
            product_comments = ProductComment.objects.filter(project=product)
            if product_comments.exists():
                for user in users:
                    last_comment = product_comments.filter(user=user).order_by('-created').first()
                    if last_comment:
                        time = last_comment.created
                        before_comments = product_comments.filter(created__lte=time).exclude(user=user)
                        after_comments = product_comments.filter(created__gte=time).exclude(user=user)
                        for before_comment in before_comments:
                            SceneCommentReceiver.objects.create(user=user, product_comment=before_comment,
                                                                seen_date=time)
                        for after_comment in after_comments:
                            SceneCommentReceiver.objects.create(user=user, product_comment=after_comment,
                                                                seen_date=None)

            product_scenes = ProductScene.original_objects.filter(product_scene=product)
            scene_titles = SceneTitle.original_objects.filter(product_scene__in=product_scenes)
            for st in scene_titles:
                scenes = Scene.original_objects.filter(title=st)
                comments = SceneComment.objects.filter(Q(scene_title=st) | Q(scene__in=scenes))
                for user in users:
                    last_comment = comments.filter(user=user).order_by('-created').first()
                    if last_comment:
                        time = last_comment.created
                        before_comments = comments.filter(created__lte=time).exclude(user=user)
                        after_comments = comments.filter(created__gte=time).exclude(user=user)
                        for before_comment in before_comments:
                            SceneCommentReceiver.objects.create(user=user, message=before_comment, seen_date=time)
                        for after_comment in after_comments:
                            SceneCommentReceiver.objects.create(user=user, message=after_comment, seen_date=None)

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update table user seen scene comment"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_seen_scene_comment()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
