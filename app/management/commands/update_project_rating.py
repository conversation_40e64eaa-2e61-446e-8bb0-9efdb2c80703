from django.core.management.base import BaseCommand
from django.db.models import Sum

from app.models import RatingSceneTitle, Product


def update_project_rating():
    try:
        products = Product.objects.filter(is_active=True)
        for product in products:
            product.update_product_rating()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update rating for project"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_project_rating()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
