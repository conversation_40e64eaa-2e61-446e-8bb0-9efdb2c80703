
from django.core.management.base import BaseCommand
from django.db import transaction

from app.models import Product, OfferUser, OfferProject
from accounts.models import ProductUser, AuthUser
from app.tasks import add_remove_member_into_offer_product


def update_product_owner():
    try:
        products = Product.objects.all()
        with transaction.atomic():
            for product in products:
                print(product.pk)
                if product.offer_product.exists():
                    offer = product.offer_product.first()
                    real_offer = OfferProject.objects.filter(offer_product=offer).first()
                    if not real_offer:
                        add_remove_member_into_offer_product(offer)
                    else:
                        pus = product.productuser_set.filter(is_invited=False, position=ProductUser.OWNER)
                        owners = AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))
                        for owner in owners:
                            offer_user = OfferUser.objects.filter(offer=real_offer, user=owner)
                            if offer_user.count() > 1:
                                offer_user.delete()
                            offer_user, created = OfferUser.objects.get_or_create(offer=real_offer, user=owner)
                            offer_user.position = OfferUser.OWNER
                            offer_user.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update offer user for user"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_product_owner()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
