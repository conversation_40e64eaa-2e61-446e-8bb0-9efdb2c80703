from django.core.management.base import BaseCommand
from app.models import FormContractAndPlan, OfferProduct, Product
import sys

def update_offer_product_condition():
    try:
        projects = Product.objects.filter(offer_product__isnull=False)
        count = 0
        for project in projects:
            count = count + 1
            offer = project.offer_product.first()
            if offer.condition == '1':
                last_plan = offer.get_offer_last_plan()
                if last_plan:
                    offers = OfferProduct.objects.filter(pk=offer.pk)
                    offers.update(condition='8')
            elif offer.condition == '3':
                last_file = offer.get_latest_form_contract_file()
                if last_file and last_file.exists():
                    if last_file.first().form_object:
                        contract = last_file.first().form_object
                        contracts = FormContractAndPlan.objects.filter(pk=contract.pk)
                        contracts.update(is_approved=True)
    except:
        print("Error! Try again!" + str(offer.pk))
        print(sys.exc_info()[1])


class Command(BaseCommand):
    help = "update model for offer product"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_offer_product_condition()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
