from django.core.management.base import BaseCommand
from django.db.models import Sum, Prefetch, Q

from app.models import OfferCreator
from django.utils.translation import gettext as _


def update_pick_up_method():
    try:
        offers = OfferCreator.original_objects.all()
        for offer in offers:
            if offer.pick_up_method and len(offer.pick_up_method) == 1 and offer.pick_up_method in '12':
                temp_pick_up_method = 'SOREMO WEBサービス内' if offer.pick_up_method == '1' else '御社指定'
                OfferCreator.original_objects.filter(pk=offer.pk).update(pick_up_method=temp_pick_up_method)
    except:
        print("Error update pick up method! Try again!")

def update_delivery_place():
    try:
        offers = OfferCreator.original_objects.all()
        for offer in offers:
            if offer.delivery_place and len(offer.delivery_place) == 1 and offer.delivery_place in '12':
                temp_delivery_place = 'SOREMO WEBサービス内' if offer.delivery_place == '1' else '御社指定'
                OfferCreator.original_objects.filter(pk=offer.pk).update(delivery_place=temp_delivery_place)
    except:
        print("Error update delivery place! Try again!")


class Command(BaseCommand):
    help = "update value for pickup method and delivery place!"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_pick_up_method()
        update_delivery_place()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
