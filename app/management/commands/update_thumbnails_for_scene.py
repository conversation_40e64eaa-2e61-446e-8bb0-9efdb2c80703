import tempfile, subprocess, os
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.core.files.uploadedfile import SimpleUploadedFile
from app.models import Scene


def update_thumbnails_for_scenes():
    OUTPUT_IMAGE_EXT = 'png'
    OUTPUT_IMAGE_CONTENT_TYPE = 'image/png'
    scenes = Scene.objects.filter(Q(movie__isnull=False), ~Q(movie='')).exclude(
        ~Q(thumbnail='') | Q(thumbnail__isnull=True))

    for scene in scenes:
        f_out = tempfile.NamedTemporaryFile(suffix=".%s" % OUTPUT_IMAGE_EXT, delete=False)
        movie_url = scene.movie.url
        tmp_img_out = f_out.name
        ffmpeg_command = "ffmpeg -y -i '%s' -vframes 1 -ss 00:00:00 -an -vcodec png -f rawvideo '%s'" % (movie_url, tmp_img_out)
        try:
            ffmpeg_result = subprocess.call(ffmpeg_command, shell=True)
        except Exception as e:
            ffmpeg_result = None
            print("Failed to extract thumbnail from %s to %s" % (movie_url, tmp_img_out))
            print("Error: %s" % e)

        if ffmpeg_result is not None:
            print("#############################START UPDATE################################")
            imagefilename = "%s.%s" % (scene.movie.name.split(".")[0].replace("movie/", ""), OUTPUT_IMAGE_EXT)
            suf = SimpleUploadedFile(imagefilename, f_out.read(), content_type=OUTPUT_IMAGE_CONTENT_TYPE)
            # upload converted image to S3 and set the name.
            # save set to False to avoid infinite loop
            scene.thumbnail.save(imagefilename, suf, save=False)
            print(scene.thumbnail.url)
            #remove temp thumbnail
            print("[ffmpeg_image] removing temporary file: %s" % tmp_img_out)
            os.remove(tmp_img_out)
            # add do_conversion=False to avoid infinite loop
            scene.save(update_fields=['thumbnail'])
            print("#############################UPDATE SUCCESS################################")


class Command(BaseCommand):
    help = "Get thumbnail from scene.movie and update"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_thumbnails_for_scenes()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
