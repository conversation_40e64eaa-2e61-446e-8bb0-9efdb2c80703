
from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import SceneComment, SceneTitle


def update_field_tag_scene_title():
    titles = SceneTitle.objects.all()
    try:
        titles.update(tag=False)
        for title in titles:
            scenes = title.scene_title.all()
            comment = SceneComment.objects.filter((Q(scene__in=scenes) | Q(scene_title=title)) & Q(user__is_active=True)).order_by(
                '-created').first()
            if comment and comment.user.role == 'admin':
                title.tag = True
                title.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "field tag in model scene title to update scene title not reply"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_field_tag_scene_title()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
