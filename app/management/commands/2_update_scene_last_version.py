from django.core.management.base import BaseCommand
from django.db.models import Q, Prefetch

from app.models import SceneTitle, Scene


def update_scene_last_version():
    try:
        scene_titles = SceneTitle.original_objects.filter(scene_title__isnull=False)

        scene_titles = scene_titles.prefetch_related(
            Prefetch('scene_title', queryset=Scene.objects \
                     .filter(version=None) \
                     .order_by('-variation__order') \
                     .prefetch_related(Prefetch('other_versions',
                                                queryset=Scene.objects.order_by('-created')))))

        for st in scene_titles:
            if not st.last_version:
                last_scene = None
                if st.scene_title.exists():
                    if st.scene_title.all().first().other_versions.all().exists():
                        last_scene = st.scene_title.all().first().other_versions.all().first()
                    else:
                        last_scene = st.scene_title.all().first()
                    if last_scene:
                        st.last_version = last_scene
                        st.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update last version scene for scene title"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_scene_last_version()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
