from django.core.management.base import BaseCommand

from app.models import ProductComment, ProductCommentFile


def update_product_comment_has_file():
    messages = ProductComment.objects.filter(real_name__isnull=False)
    try:
        for message in messages:
            if message.file:
                message.has_file = True
                message.save()
                ProductCommentFile.objects.create(message=message, file=message.file, peaks=message.peaks,
                                                  real_name=message.real_name)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update model product comment similar offer message"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_product_comment_has_file()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
