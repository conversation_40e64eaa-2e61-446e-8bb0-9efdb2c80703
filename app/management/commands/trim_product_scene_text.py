from django.core.management.base import BaseCommand

from app.models import ProductScene


def trim_product_scene_text():
    try:
        ProductScene.objects.filter(name__isnull=False)
        for productscene in ProductScene.objects.filter(name__isnull=False):
            productscene.name = productscene.name.strip()
            print("_____", productscene.name, "_____")
            productscene.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "trim name of product_scene"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        trim_product_scene_text()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
