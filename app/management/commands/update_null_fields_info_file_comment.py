import logging

from django.core.management.base import BaseCommand
from app.models import MessageFile, ProductCommentFile, ProductMessageFile, SceneCommentFile
from app.util import save_logging_update_info_file


class Command(BaseCommand):
    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        msg_start_cmd = "__________START PROCESS COMMAND: 'update_null_fields_info_file_comment'__________"
        msg_end_cmd = "__________END PROCESS COMMAND: 'update_null_fields_info_file_comment'__________"
        logging.info(msg_start_cmd)
        self.stdout.write(msg_start_cmd)
        update_fields_file_comment()
        logging.info(msg_end_cmd)
        self.stdout.write(self.style.SUCCESS(msg_end_cmd))


def update_fields_file_comment():
    msg_success = 'Update fields file comments successfully!'
    msg_failed = 'Update fields file comments failed!'
    try:
        MessageFile.objects.all().update(file_info=None, type_file_name=None)
        ProductCommentFile.objects.all().update(file_info=None, type_file_name=None)
        ProductMessageFile.objects.all().update(file_info=None, type_file_name=None)
        SceneCommentFile.objects.all().update(file_info=None, type_file_name=None)
        logging.info(msg_success)
        print(msg_success)
    except Exception as e:
        save_logging_update_info_file(msg_failed, e, None)
        print(msg_failed)
    return True
