
from django.core.management.base import BaseCommand
from django.db.models import Sum

from app.models import Product, OfferCreator
from accounts.models import ProductUser, AuthUser


def update_budget_for_admin():
    try:
        products = Product.objects.filter(is_active=True)
        for product in products:
            total_budget = 0
            offers = OfferCreator.objects.filter(project=product, admin__is_active=True,
                                                 creator__is_active=True).exclude(status='5')
            if offers.exists():
                admins = AuthUser.objects.filter(role='admin',
                                                 pk__in=offers.values_list('admin_id', flat=True)).distinct()
                for admin in admins:
                    offer_creator = offers.filter(admin=admin)
                    total = offer_creator.aggregate(Sum('reward'))
                    total = total.get('reward__sum')
                    if not total:
                        total = 0
                    pu = ProductUser.objects.filter(user=admin, product=product)
                    if pu.exists():
                        total_budget += total
                        pu = pu[0]
                        pu.budget = total
                        pu.save()
                product.total_budget = total_budget
                product.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update budget for admin"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_budget_for_admin()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
