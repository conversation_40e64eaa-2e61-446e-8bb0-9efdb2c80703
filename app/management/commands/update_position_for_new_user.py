from django.core.management.base import BaseCommand

from accounts.models import ProductUser, AuthUser
from app.models import Product


def update_position_for_new_user():
    try:
        ProductUser.objects.filter(user__role=AuthUser.MASTERADMIN).update(position=ProductUser.MASTERADMIN)
        ProductUser.objects.filter(user__role=AuthUser.CREATOR, position=ProductUser.REVIEWER).update(
            position=ProductUser.DIRECTOR)
        ProductUser.objects.filter(user__role=AuthUser.CURATOR).delete()
        products = Product.objects.all()
        master_admins = AuthUser.objects.filter(is_active=True, role=AuthUser.MASTERADMIN)
        for product in products:
            for master_admin in master_admins:
                product_user = ProductUser.objects.filter(user=master_admin, product=product).first()
                if not product_user:
                    ProductUser.objects.create(user=master_admin, product=product, position=ProductUser.MASTERADMIN)
        users = AuthUser.objects.all()
        for user in users:
            product_users = user.productuser_set.all().order_by('order', 'product__created')
            for index, pu in enumerate(product_users):
                pu.order = index
                pu.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update product user for new user"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_position_for_new_user()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
