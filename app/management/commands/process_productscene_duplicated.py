from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Count

from app.models import Product, ProductScene, SceneTitle, Scene


def process_productscene_duplicated():

    scenes = Scene.original_objects.all()
    for scene in scenes:
        title = scene.title
        product_scene = scene.product_scene
        if title and title.product_scene != product_scene:
            scene.product_scene = title.product_scene
            scene.save()

    for product in Product.objects.all():
        with transaction.atomic():
            print("start processing ", product.pk)
            duplicated_names = list(
                product.scene_list.filter(deleted_on__isnull=False).values_list('name', flat=True).annotate(
                    num_productscene=Count('product_scene_id')).order_by().filter(num_productscene__gt=1))
            for dup_name in duplicated_names:
                dup_productscenes = product.scene_list.filter(name=dup_name).order_by('created')
                scenetitles = SceneTitle.objects.filter(product_scene__in=dup_productscenes)\
                    .select_related('product_scene')
                if scenetitles:
                    print('___scene title___')
                    print(scenetitles.values('product_scene__name', 'scene_title__title'))
                    product_scene = scenetitles.filter(scene_title__isnull=False).first().product_scene
                    scenetitles.update(product_scene=product_scene)
                    scenes = Scene.objects.filter(product_scene__in=dup_productscenes)
                    print('___scenes___')
                    print(scenes.values('pk', 'movie'))
                    scenes.update(product_scene=product_scene)
                    dup_productscenes.exclude(pk=product_scene.pk).super_delete()
                else:
                    product_scene = dup_productscenes.first()
                    dup_productscenes.exclude(pk=product_scene.pk).super_delete()
            print("Done!")

            deleted_product_scene = ProductScene.original_objects.filter(deleted_on__isnull=False)
            for ps in deleted_product_scene:
                scene_titles = SceneTitle.original_objects.filter(product_scene=ps).exists()
                if not scene_titles:
                    ps.super_delete()
    pass


class Command(BaseCommand):
    help = "processing productscene duplicated"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("start processing!")
        process_productscene_duplicated()
        self.stdout.write(self.style.SUCCESS("successed!"))
