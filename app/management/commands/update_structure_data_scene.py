from django.core.management.base import BaseCommand
from django.db import transaction

from app.models import Scene, SceneTitle
from django.db.models import Prefetch
from django.utils.translation import gettext as _


def update_structure_data_scene():
    try:
        scene_titles = SceneTitle.original_objects.filter()
        with transaction.atomic(): 
            for title in scene_titles:         
                scenes = Scene.original_objects.filter(version__isnull=True, title__pk=title.pk).order_by("-variation__order").prefetch_related(
                        Prefetch('other_versions', queryset=Scene.original_objects.order_by("-created")))
                # scenes.update(version_order=1)
                max_version = 0
                for scene in scenes:
                    len_other_version = len(scene.other_versions.all())
                    if(len_other_version + 1 > max_version):
                        max_version = len_other_version + 1

                for i in range(max_version):
                    main_parent = None
                    for scene in scenes:
                        other_version = scene.other_versions.all()
                        if i < len(other_version) and other_version[i]:
                            main_parent = other_version[i]
                            break
                        elif i == len(other_version):
                            main_parent = scene
                            break

                    for index, scene in enumerate(scenes):
                        other_version = scene.other_versions.all()
                        if i == len(other_version):
                            if main_parent != scene:
                                scene.version = main_parent
                                scene.order = max_version - i
                                scene.version_order = index + 1
                                scene.take_uploaded = scene.created
                            elif main_parent == scene:
                                scene.version = None
                                scene.order = max_version - i
                                scene.version_order = 1
                                scene.take_uploaded = scene.created
                            scene.save()
                        elif i < len(other_version):
                            if other_version[i] and other_version[i] != main_parent:
                                other_version[i].version = main_parent
                                other_version[i].order = max_version - i
                                other_version[i].version_order = index + 1
                                other_version[i].take_uploaded = other_version[i].created
                            elif other_version[i] and other_version[i] == main_parent:
                                other_version[i].version = None
                                other_version[i].order = max_version - i
                                other_version[i].version_order = 1
                                other_version[i].take_uploaded = other_version[i].created
                            other_version[i].save()
        with transaction.atomic():
            for st in scene_titles:
                st.update_last_version()

    except Exception as e:
        print("Error to update version order! Try again!: " + str(e))

def update_schedule_date_scene():
    try:
        with transaction.atomic():
            scenes = Scene.original_objects.all()
            for scene in scenes:
                if not scene.schedule_date:
                    scene.schedule_date = scene.modified
                    scene.save()
    except Exception as e:
        print("Error to update scheduledate scene! Try again!: " + str(e))

class Command(BaseCommand):
    help = "check and update version order of scene!"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start update structure!__________")
        update_structure_data_scene()
        self.stdout.write(self.style.SUCCESS("__________update version order successed!__________"))
        self.stdout.write("__________start update schedule!__________")
        update_schedule_date_scene()
        self.stdout.write(self.style.SUCCESS("__________update schedule date successed!__________"))
