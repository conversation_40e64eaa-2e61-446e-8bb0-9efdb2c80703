
from django.core.management.base import BaseCommand

from app.models import Product, OfferProduct


def update_foreignkey_project_into_offer():
    try:
        offers = OfferProduct.objects.all()
        for offer in offers:
            if offer.product:
                offer.project = Product.objects.filter(pk=offer.product).first()
                offer.save()

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update foreignkey project for offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_foreignkey_project_into_offer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
