# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 16/12/2021
from django.core.management.base import BaseCommand
from django.db.models import Prefetch

from app.models import OfferProduct, ProductMessageFile


def update_amount_product_message_files():
    offers = OfferProduct.objects.filter(condition__in=['5', '6'], amount__isnull=False)\
        .prefetch_related(Prefetch('files', queryset=ProductMessageFile.objects.order_by('created')))
    counter = 0
    for offer in offers:
        counter += offer.files.update(amount=offer.amount)
    return counter


class Command(BaseCommand):
    help = 'Update value for amount field of product message files'

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write('__________start update amount for product message files__________')
        num_records = update_amount_product_message_files()
        self.stdout.write(f'=============> Updated {num_records} records.')
        self.stdout.write(self.style.SUCCESS('__________done!__________'))
