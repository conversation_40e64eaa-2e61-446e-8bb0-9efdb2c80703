
from django.core.management.base import BaseCommand

from app.models import SaleContentVersion


def update_sale_content_type():
    try:
        sale_contents = SaleContentVersion.objects.filter(content_type='1')
        sale_contents.update(content_type='music')
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update content type from 1 to music"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_sale_content_type()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
