
from django.core.management.base import BaseCommand

from app.app_services.upload_contract_services import get_budget_in_form
from app.models import OfferProduct, FormContractAndPlan, Product


def update_type_contract_offer_creator():
    try:
        offers = OfferProduct.objects.filter(project__isnull=False)
        for offer in offers:
            project = offer.project
            projects = Product.objects.filter(pk=project.pk)
            if offer.condition not in ['1', '2']:
                form_contract = FormContractAndPlan.objects.filter(product_message_files__offer=offer).order_by(
                '-created', '-id').first()
                if form_contract:
                    projects.update(name=form_contract.subject, total_budget=get_budget_in_form(form_contract))
                    continue
            if project.master_admin_get_spent_budget() <= offer.budget:
                projects.update(total_budget=offer.budget)

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update type contract depend on contract for offer creator"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_type_contract_offer_creator()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
