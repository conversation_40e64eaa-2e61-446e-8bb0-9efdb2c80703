
from django.core.management.base import BaseCommand
from django.db.models import Q

from app.models import OfferCreator
from django.db import transaction


def update_offer_creator_status():
    try:
        with transaction.atomic():
            OfferCreator.objects.filter(status='4').update(status='5')
            OfferCreator.objects.filter(status='3').update(status='4')
            OfferCreator.objects.filter(status='2').filter(
                Q(message_offer__file__isnull=False) | ~Q(message_offer__file='')).update(status='3')
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update order product scene"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_offer_creator_status()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
