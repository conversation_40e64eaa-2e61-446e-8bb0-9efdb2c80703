from django.core.management.base import BaseCommand
from django.db.models import Sum

from app.models import SceneTitle, RatingScene, Scene, RatingSceneTitle, Product


def update_rating_scene_title():
    try:
        scene_titles = SceneTitle.objects.all()
        for scene_title in scene_titles:
            scene_title.update_rating()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update rating for scene title and product from rating scene"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_rating_scene_title()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
