
from django.core.management.base import BaseCommand

from accounts.models import Creator, CreatorPro<PERSON>le


def before_profile_last_version():
    try:
        creators = Creator.objects.filter(user__is_active=True)
        for creator in creators:
            last_published_version = creator.last_published_version
            last_version = creator.last_version
            if last_version:
                if not is_new_profile(last_published_version, last_version):
                    creator.last_version = None
                    creator.save()
                    last_version.status = '3'
                    last_version.save()
    except:
        print("Error! Try again!")


def is_new_profile(last_published_version, last_version):
    attribute_profile = ["official_site", "twitter_link", "facebook_link", "instagram_link", "stage_name", "type",
                         "theme_quote", "profile_quote", "avatar", "banner"]
    for item in attribute_profile:
        if item not in ("avatar", "banner"):
            if getattr(last_published_version, item) != getattr(last_version, item):
                return True
        else:
            edit_value = getattr(last_version, item)
            if edit_value:
                return True
    public_sales = last_published_version.content_profile.all()
    edit_sales = last_version.content_profile.all()
    if edit_sales.count() > public_sales.count():
        return True
    else:
        for sale_content in edit_sales:
            if sale_content.last_version:
                return True
            audios = sale_content.last_published_version.album.all()
            for audio in audios:
                if audio.last_version:
                    return True
    return False


class Command(BaseCommand):
    help = "update really new profile"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        before_profile_last_version()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
