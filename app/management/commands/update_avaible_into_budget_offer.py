from django.core.management.base import BaseCommand
from django.db.models import Sum, Prefetch, Q

from accounts.models import ProductUser, AuthUser
from app.models import OfferCreator


# update available budget for artist
def update_available_into_budget_offer():
    try:
        product_users = ProductUser.objects.filter(user__role=AuthUser.CREATOR)
        product_users.update(budget_offer=0)
        for product_user in product_users:

            user = product_user.user
            project = product_user.product
            offers = project.product_offers.exclude(status=OfferCreator.STATUS_REJECT)
            # offer not done payment
            offer_creators = offers.filter(creator=user, status__in=['2', '3', '4'],
                                           creator_payment_request_id__isnull=True, payment_status=False,
                                           legaxy_payment=False)
            budget_creators = offer_creators.aggregate(Sum('reward')).get('reward__sum', 0) if offer_creators else 0

            # offer done payment
            done_offer_creators = offers.filter(Q(creator=user) & Q(status__in=['2', '3', '4']) &
                                                (Q(creator_payment_request_id__isnull=False) | Q(
                                                    payment_status=True) | Q(legaxy_payment=True)))
            done_budget_creators = done_offer_creators.aggregate(Sum('reward')).get('reward__sum',
                                                                                    0) if done_offer_creators else 0

            # offer đi
            all_offer_admins = offers.filter(admin=user)
            budget_admins = all_offer_admins.aggregate(Sum('reward')).get('reward__sum',
                                                                                    0) if all_offer_admins else 0
            budget_offer = 0
            budget_by_offer = budget_creators

            budget_offer = done_budget_creators - budget_admins

            if budget_offer < 0:
                budget_by_offer += budget_offer

            offer_project = project.offer_product.first()
            if product_user.is_super_producer and offer_project.condition in ['3', '4', '5', '6']:
                budget_by_offer += project.total_budget - round((project.total_budget * product_user.usage_fee / 100))

            product_user.budget_offer = budget_by_offer
            product_user.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update avaible budget for artist"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_available_into_budget_offer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
