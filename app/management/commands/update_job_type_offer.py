from django.core.management.base import BaseCommand
from django.db.models import Sum, Prefetch, Q

from app.models import OfferCreator
from django.utils.translation import gettext as _


def update_job_type_offer():
    try:
        mapping = {
            '1': _('作編曲'),
            '2': _('編曲'),
            '3': _('作詞'),
            '4': _('SFX　効果音'),
            '5': _('レコーディング　ミキシング'),
            '6': _('キャラクターボイス　ナレーション'),
            '7': _('歌唱'),
            '8': _('演奏'),
            '9': _('進行管理'),
        }
        offers = OfferCreator.original_objects.all()
        for offer in offers:
            if len(offer.contract) == 1 and offer.contract in '123456789':
                temp_contract = mapping.get(offer.contract, "")
                OfferCreator.original_objects.filter(pk=offer.pk).update(contract=temp_contract)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update value for job type offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_job_type_offer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
