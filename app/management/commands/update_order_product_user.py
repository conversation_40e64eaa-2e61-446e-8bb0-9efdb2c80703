
from django.core.management.base import BaseCommand

from accounts.models import AuthUser


def update_order_product_user():
    try:
        users = AuthUser.objects.all()
        for user in users:
            product_users = user.productuser_set.all().order_by('order', 'product__created')
            for index, pu in enumerate(product_users):
                pu.order = index
                pu.save()
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "update order product user"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_order_product_user()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))

