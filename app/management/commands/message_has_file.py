from django.core.management.base import BaseCommand
from app.models import OfferMessage


class Command(BaseCommand):
    help = "add table variation to link file pdf with offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        for m in OfferMessage.objects.all():
            if m.files.exists():
                m.has_file = True
                m.save()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
