
from django.core.management.base import BaseCommand

from app.models import Plan<PERSON>ffer, VariationOffer, OfferProduct
from accounts.models import <PERSON>th<PERSON><PERSON>


def create_variation_offer():
    master_admin = AuthUser.objects.filter(role='master_admin').first()
    try:
        for plan in PlanOffer.objects.all():
            offer = plan.offer_product
            variation, created = VariationOffer.objects.get_or_create(owner=master_admin, offer=offer, plan=plan.file,
                                                      plan_name=plan.real_name, is_chosen=plan.status)
            if offer.contact:
                variation.contract = offer.contact
                variation.contract_name = offer.contact_real_name
            if offer.bill:
                variation.bill = offer.bill
                variation.bill_name = offer.bill_real_name
            variation.save()
        for offer in OfferProduct.objects.exclude(plan_offer__isnull=False):
            contract = offer.contact
            contract_name = offer.contact_real_name
            bill = offer.bill
            bill_name = offer.bill_real_name
            if contract and bill:
                VariationOffer.objects.create(owner=master_admin, offer=offer, contract=contract,
                                              contract_name=contract_name, bill=bill, bill_name=bill_name,
                                              is_chosen='2')
            elif contract:
                VariationOffer.objects.create(owner=master_admin, offer=offer, contract=contract,
                                              contract_name=contract_name,
                                              is_chosen='2')
            elif bill:
                VariationOffer.objects.create(owner=master_admin, offer=offer, bill=bill, bill_name=bill_name,
                                              is_chosen='2')

        offers = OfferProduct.objects.filter(project__isnull=False)
        for offer in offers:
            if offer.variation_offer.filter().exists() and not offer.variation_offer.filter(is_chosen='2').exists():
                offer.variation_offer.first().update(is_chosen='2')

    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "add table variation to link file pdf with offer"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        create_variation_offer()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
