
from django.core.management.base import BaseCommand

from app.models import Product
from accounts.models import ProductUser, AuthUser


def update_only_address_default_true():
    try:
        AuthUser.objects.all().update(only_address=True)
    except:
        print("Error! Try again!")


class Command(BaseCommand):
    help = "set default only address is True"

    def __init__(self, *args, **kwargs):
        super(Command, self).__init__(*args, **kwargs)

    def handle(self, *args, **options):
        self.stdout.write("__________start__________")
        update_only_address_default_true()
        self.stdout.write(self.style.SUCCESS("__________successed!__________"))
