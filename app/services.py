# 01EG89H6SS2141VNGDDEHBMV4Q
import datetime
import re
import time
import logging
import math
from operator import itemgetter

import jwt
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from dateutil.relativedelta import relativedelta
from django.db import transaction
from django.core import serializers

from django.db.models import Q, Sum, Prefetch, FloatField, IntegerField
from django.db.models.aggregates import Max
from django.db.models.expressions import Case, When, F, Value, ExpressionWrapper, Subquery, OuterRef, Exists
from django.http import HttpResponseBadRequest
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils.translation import gettext as _
from django.conf import settings

from accounts.models import ProductUser, CreatorListCreator, Creator, AuthUser
from accounts.tasks import update_product_user_order
from app import models
from app.templatetags.util import get_display_fullname
from app.models import (SceneCommentFile, ProductCommentFile, MessageFile, OfferCreator,
                        OfferProduct, Product, ProductMessageFile, ProductMessage, MessageReceiver, BlockListArtist,
                        OfferProject, OfferUser, OfferMessage, OfferMessageReceiver, SelectionGallery, TopicGallery,
                        SelectionOffer, HashTag, TopicTag, Category, TopicCategory, SaleContent, SaleContentSelection,
                        SaleContentListWork, OrderData, Scene, ReviewOffer)
from landingPage.models import ContactInfo
from voice.constants import CONST_REGEX_EMAIL
from . import tasks
from app.util import get_total_diff_time
from .tasks import add_member_into_offer_creator, add_remove_member_into_offer_product, sendFileToACRFileScanning
from django.db.models import Prefetch, Q, Case, When, Value, CharField, Count, Subquery

SECRET = settings.SECRET_KEY

def get_offers_in_project(project, user, project_user, offer_status, type_model):
    if project_user.position in [ProductUser.MASTERADMIN, ProductUser.PRODUCER, ProductUser.OWNER]:
        offer_creators = OfferUser.objects.filter(position__in=[OfferUser.ADMIN, OfferUser.CREATOR], user=user,
                                                  offer__project=project).values_list('offer_id')
        offers = OfferProject.objects.filter(Q(project=project) & \
                                             (Q(type_offer='2') | \
                                              (Q(type_offer='1') & Q(
                                                  pk__in=offer_creators.values_list('offer_id'))))).distinct()

    else:
        offers = OfferProject.objects.filter(
            Q(project=project) & Q(type_offer='1') & (Q(offer_creator__admin=user) | Q(offer_creator__creator=user)))
    if offer_status == 'processing':
        offers = offers.filter(offer_status__in=OfferProject.STATUS_OFFER_ACTIVE).distinct()
    else:
        offers = offers.filter(
            Q(offer_status__in=OfferProject.STATUS_IN_PROGRESS) | 
            Q(pk__in=project.get_unread_offer_message(user).values_list('pk', flat=True))
        ).exclude(
            Q(type_offer='1') & Q(offer_creator__status=OfferCreator.STATUS_CLOSED)
        ).distinct()
    return offers.exclude(offer_status=OfferProject.STATUS_DELETED).order_by('-modified')

def get_offers_in_project_refactor(project, user, project_user, offer_status, type_model):
    if project_user.position in [ProductUser.MASTERADMIN, ProductUser.PRODUCER, ProductUser.OWNER]:
        offer_ids = OfferUser.objects.filter(position__in=[OfferUser.ADMIN, OfferUser.CREATOR], user=user,
                                                  offer__project=project).values_list('offer_id', flat=True)
        offers = OfferProject.objects.filter(
            Q(project=project) & (Q(type_offer='2') | (Q(type_offer='1') & Q(pk__in=offer_ids)))
        )

    else:
        offers = OfferProject.objects.filter(
            Q(project=project) & 
            Q(type_offer='1') & 
            (Q(offer_creator__admin=user) | Q(offer_creator__creator=user))
        )
    
    if offer_status == 'processing':
        offers = offers.filter(offer_status__in=OfferProject.STATUS_OFFER_ACTIVE).distinct()
    else:
        unread_offers = project.get_unread_offer_message(user).values_list('pk', flat=True)
        offers = offers.filter(
            Q(offer_status__in=OfferProject.STATUS_IN_PROGRESS) | 
            Q(pk__in=unread_offers)
        ).exclude(
            Q(type_offer='1') & Q(offer_creator__status=OfferCreator.STATUS_CLOSED)
        ).distinct()
        offer_review = ReviewOffer.objects.filter(user_id=str(user.pk)).values_list('offer_id', flat=True)
        new_offer_reviews = [str(id).replace('-', '') for id in offer_review]
        offers = offers.exclude(offer_id__in=new_offer_reviews)
        

    return offers.exclude(offer_status=OfferProject.STATUS_DELETED).order_by('-modified')

def sort_offers(offers, user):
    offers = offers.annotate(admin_pk=Subquery(
        OfferUser.objects.filter(offer=OuterRef('pk'), user=user, position='admin').values('pk')[:1]
    )).annotate(
        budget_or_reward=Case(
            When(type_offer=2, then='offer_product__project__total_budget'),
            When(type_offer=1, then='offer_creator__reward'),
            default=Value(0),
            output_field=FloatField()
        ),
        offer_side=Case(
            When(
                admin_pk__isnull=False, then=Value(1),
            ),
            When(Q(offer_creator__isnull=False) & Q(offer_creator__creator=user), then=Value(2)),
            default=Value(0),
            output_field=IntegerField()
        )
    ).order_by('-type_offer', '-offer_side', '-budget_or_reward', '-created')

    return offers

def check_budget_admin(user, project, offer_id, reward, get_budget=False):
    offer_reward = 0
    if offer_id:
        offer = OfferCreator.objects.filter(pk=offer_id).first()
        if offer:
            offer_reward = offer.reward
    product_user = ProductUser.objects.filter(product=project, user=user).first()

    if user.role == AuthUser.CREATOR and reward > 0 and reward > product_user.budget_offer + offer_reward and get_budget:
        return (product_user.budget_offer + offer_reward)
    if user.role == AuthUser.CREATOR:
        return not (reward > 0 and reward > product_user.budget_offer + offer_reward)
    elif user.role == AuthUser.MASTERADMIN:
        return True
    return False


def get_list_creators(user, creator_lists):
    if user.is_authenticated:
        if user.role == AuthUser.CURATOR:
            creator_lists = creator_lists.order_by('-order').prefetch_related(
                Prefetch('creatorlistcreator_set',
                         queryset=CreatorListCreator.objects.filter(
                             Q(creator__user__is_active=True)
                         ).order_by('-order')))
        else:
            creator_lists = creator_lists.exclude(is_default=True).order_by('-order').prefetch_related(
                Prefetch('creatorlistcreator_set',
                         queryset=CreatorListCreator.objects.filter(
                             (Q(creator__user__is_active=True) &
                              (Q(creator__show_profile__in=[Creator.PUBLIC, Creator.PRIVATE]) |
                              (Q(creator__show_profile=Creator.PROJECT) &
                               Q(creator__user__products__in=user.products.filter(productuser__is_invited=False))))) |
                             Q(creator=user.user_creator.first())
                         ).order_by('-order')))
    else:
        creator_lists = creator_lists.exclude(is_default=True).order_by('-order').prefetch_related(
            Prefetch('creatorlistcreator_set', queryset=CreatorListCreator.objects
                     .filter(creator__user__is_active=True, creator__show_profile='public').order_by('-order')
                     ))
    return creator_lists


def update_peak_in_comment(comment_id, peaks, type):
    mapping_comment_class = {
        'messenger': MessageFile,
        'messenger_owner': ProductMessageFile,
        'scene': SceneCommentFile,
        'project': ProductCommentFile,
    }
    comment_model = mapping_comment_class.get(type, mapping_comment_class['project'])
    comment = comment_model.objects.filter(pk=comment_id).first()
    if not comment:
        comment = MessageFile.objects.filter(pk=comment_id).first()
    if comment and peaks:
        comment.peaks = peaks
        comment.save()
        return True
    return False


def handle_writer_csv_file(type, target_id, csvwriter, product_scene_name):

    def write_error_to_csv(message):
        csvwriter.writerow([
            product_scene_name,
            target_obj.real_name,
            uploaded_time,
            '', '', '', '', '', '', message
        ])

    processing_message = _('Checking. Please wait a moment and try exporting again.')
    no_result_message = _('No collation result')

    model = {'scene': models.Scene.objects, 'file': models.SceneCommentFile.objects, 'pdfile': models.ProductCommentFile.objects}
    target_model = model.get(type)
    target_obj = target_model.get(pk=target_id)

    if target_obj.is_audio_file() not in ['audio', 'video']:
        return

    uploaded_time = target_obj.created.strftime('%y/%m/%d %H:%M')

    if target_obj.acr_status in ['1', '2']:
        sendFileToACRFileScanning.delay(str(target_obj.pk), target_obj.__class__.__name__)

        write_error_to_csv(processing_message)
        return

    elif target_obj.acr_status == '4':
        write_error_to_csv(no_result_message)
        return
    elif target_obj.acr_status == '3':
       
        try:
            import json
            result = json.loads(target_obj.acr_filescanning)
            try:
                for item in result['music']:
                    artists = ''
                    for artist in item['result']['artists']:
                        if not artists == '':
                            artists = artists + ' & ' + artist['name']
                        else:
                            artists = artists + artist['name']
                    played = int(item['played_duration'])
                    begin_time = int(math.floor(float(item['result']['db_begin_time_offset_ms'])/1000 + 0.5))
                    duration = str(datetime.timedelta(seconds = played))
                    start_second = str(datetime.timedelta(seconds = begin_time))
                    end_time = begin_time + played
                    end_second =  str(datetime.timedelta(seconds = end_time))
                    csvwriter.writerow([
                        product_scene_name,
                        target_obj.real_name,
                        uploaded_time,
                        '原盤',
                        item['result']['score'],
                        item['result']['title'],
                        artists,
                        duration,
                        start_second + ' - ' + end_second,
                        '',
                    ])
            except:
                pass
            try:
                for item in result['cover_songs']:
                    artists = ''
                    for artist in item['result']['artists']:
                        if not artists == '':
                            artists = artists + ' & ' + artist['name']
                        else:
                            artists = artists + artist['name']
                    csvwriter.writerow([
                        product_scene_name,
                        target_obj.real_name,
                        uploaded_time,
                        '複製（カバーソング）',
                        '',
                        item['result']['title'],
                        artists,
                        '',
                        '',
                        '',
                    ])
            except:
                pass

            try:
                for item in result['custom_files']:
                    artists = ''
                    played = int(item['played_duration'])
                    begin_time = int(math.floor(float(item['result']['db_begin_time_offset_ms'])/1000 + 0.5))
                    duration = str(datetime.timedelta(seconds = played))
                    start_second = str(datetime.timedelta(seconds = begin_time))
                    end_time = begin_time + played
                    end_second =  str(datetime.timedelta(seconds = end_time))
                    csvwriter.writerow([
                        product_scene_name,
                        target_obj.real_name,
                        uploaded_time,
                        'カスタムコンテンツ',
                        item['result']['score'],
                        item['result']['title'],
                        artists,
                        duration,
                        start_second + ' - ' + end_second,
                        '',
                    ])
            except:
                pass
        except:
            write_error_to_csv('エラーが発生しました')


class GetSceneWattingsFeedbackService:
    def __init__(self, user, product) -> None:
        self.product = product
        self.user = user
        self.group_scene = {}

    def process(self):
        try:
            product_scenes = self.__get_product_scenes()
            self.scene_titles = self.__get_scene_titles(product_scenes)
            scenes_items = self.__get_watting_feedback_scene_ids()
            sorted_scenes_items = sorted(
                scenes_items, key=itemgetter('datetime'), reverse=True
            )
            pk_list = [scene['id'] for scene in sorted_scenes_items]
            preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(pk_list)])
            queryset = (
                models.SceneTitle.objects.filter(pk__in=pk_list, scene_title__isnull=False).distinct()
                .order_by(preserved)
                .prefetch_related(
                    Prefetch(
                        'scene_title',
                        queryset=models.Scene.objects.filter(
                            product_scene__isnull=False, version=None
                        )
                        .exclude(Q(movie='') | Q(movie__isnull=True))
                        .order_by('-variation__order')
                        .select_related('product_scene')
                        .prefetch_related(
                            Prefetch(
                                'other_versions',
                                queryset=models.Scene.objects.order_by('-created'),
                            )
                        ),
                    )
                )
                .prefetch_related(
                    Prefetch(
                        'owner_rating_title',
                        queryset=models.RatingSceneTitle.objects.filter(
                            user_id=self.user.id
                        ),
                    )
                )
            )

            return queryset
        except Exception as e:
            logging.error(e)
            raise e

    def process_refactor(self):
        try:
            product_scenes = self.__get_product_scenes()
            self.scene_titles = self.__get_scene_titles(product_scenes)
            scenes_items = self.__get_watting_feedback_scene_ids()
            sorted_scenes_items = sorted(
                scenes_items, key=itemgetter('datetime'), reverse=True
            )
            pk_list = [scene['id'] for scene in sorted_scenes_items]
            preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(pk_list)])
            queryset = (
                models.SceneTitle.objects.filter(pk__in=pk_list, scene_title__isnull=False).distinct()
                .order_by(preserved)
                .select_related("last_version", "product_scene")
                .prefetch_related(
                    Prefetch(
                        'scene_title',
                        queryset=models.Scene.objects.filter(
                            product_scene__isnull=False, version=None
                        )
                        .exclude(Q(movie='') | Q(movie__isnull=True))
                        .order_by('-variation__order')
                        .select_related("product_scene")
                        .prefetch_related(
                            Prefetch(
                                'other_versions',
                                queryset=models.Scene.objects.order_by('-created'),
                            )
                        ),
                    ),
                    Prefetch(
                        'owner_rating_title',
                        queryset=models.RatingSceneTitle.objects.filter(
                            user_id=self.user.id
                        ),
                    ),
                    Prefetch("title_taken_scene__taken_scenes"),
                )
            )

            return queryset
        except Exception as e:
            logging.error(e)
            raise e

    def __get_product_scenes(self):
        return self.product.scene_list.all()

    def __get_scene_titles(self, product_scenes):
        return (
            models.SceneTitle.objects.filter(product_scene__in=product_scenes)
            .exclude(status__in=['5', '6'])
            .order_by('-updated_at')
        )

    def __get_scene_and_time_comment_newest(self, scene_titles):
        return (
            models.SceneComment.objects.filter(scene_title__in=scene_titles)
            .values('scene_title_id', 'user__role')
            .annotate(Max('created'))
            .order_by()
        )

    def __get_scene_and_time_comment_updated_newest(self, scene_titles):
        return (
            models.SceneComment.objects.filter(scene_title__in=scene_titles)
            .values('scene_title_id', 'user__role')
            .annotate(Max('updated_at'))
            .order_by()
        )

    def __group_scene_max_created_comment(self):
        scenes = self.__get_scene_and_time_comment_newest(self.scene_titles)

        for scene in scenes:
            self.group_scene.setdefault(str(scene.get('scene_title_id')), {})
            self.group_scene[str(scene.get('scene_title_id'))][
                scene.get('user__role')
            ] = scene.get('created__max')

    def __group_scene_max_updated_comment(self):
        scenes = self.__get_scene_and_time_comment_updated_newest(self.scene_titles)

        for scene in scenes:
            if scene.get('updated_at__max'):
                self.group_scene.setdefault(str(scene.get('scene_title_id')), {})
                self.group_scene[str(scene.get('scene_title_id'))][
                    f'{scene.get("user__role")}_updated'
                ] = scene.get('updated_at__max')

    def __group_scene_created_updated(self):
        for scene in self.scene_titles:
            pk = str(scene.pk)
            self.group_scene.setdefault(pk, {})
            self.group_scene[pk]['updated'] = scene.admin_updated_at
            self.group_scene[pk]['created'] = scene.created

    def __get_watting_feedback_scene_ids(self):
        self.__group_scene_created_updated()
        self.__group_scene_max_created_comment()
        self.__group_scene_max_updated_comment()

        scenes_items = []
        for key, value in self.group_scene.items():
            last_comment_admin = value.get('admin')
            last_comment_master_client = value.get('master_client')
            last_comment_master_client_updated = value.get('master_client_updated')
            last_comment_admin_updated = value.get('admin_updated')
            updated = value.get('updated')
            created = value.get('created')

            lastest_comment_master_client_time = self.max_with_filter_none([
                last_comment_master_client_updated,
                last_comment_master_client
            ])

            lastest_admin_updated_time = self.max_with_filter_none([
                updated,
                created,
                last_comment_admin,
                last_comment_admin_updated
            ])

            if ((not lastest_admin_updated_time) or
                (
                    lastest_admin_updated_time
                    and lastest_comment_master_client_time
                    and lastest_comment_master_client_time > lastest_admin_updated_time
                )
            ):
                continue

            scenes_items.append({
                'id': key,
                'datetime': lastest_admin_updated_time
            })

        return scenes_items

    def max_with_filter_none(self, arr):
        arr_filter = list(filter(lambda item: item is not None, arr))

        if len(arr_filter) < 1:
            return

        return max(arr_filter)


def send_message_update_scene(product):
    user_ids = ProductUser.objects.filter(
        product=product, position__in=[ProductUser.DIRECTOR, ProductUser.PRODUCER]
    ).values_list('user__pk', flat=True)
    channel_layer = get_channel_layer()

    for user_id in user_ids:
        async_to_sync(channel_layer.group_send)(
            '{}'.format(user_id),
            {
                'type': 'scene_message',
                'action': 'update_scene',
                'project_id': str(product.pk),
            },
        )


def update_menu_project_detail(product):
    user_ids = ProductUser.objects.filter(
        product=product, user__role=AuthUser.MASTERCLIENT
    ).values_list('user__pk', flat=True)
    channel_layer = get_channel_layer()
    is_show = product.check_has_scene_in_project()

    for user_id in user_ids:
        async_to_sync(channel_layer.group_send)(
            '{}'.format(user_id),
            {
                'type': 'scene_message',
                'action': 'show_menu',
                'project_id': str(product.pk),
                'is_show': is_show
            },
        )


def save_form_create_offer_edit_service(form, form_contract, request):
    topic_id = request.POST.get('topic_id')
    topic = TopicGallery.objects.get(pk=topic_id)
    if topic.user:
        # create profile by topic profile
        return save_form_create_offer_by_topic_artist(form, form_contract, topic, request)

    instance = form.save()
    offer = OfferProduct.objects.get(pk=instance.pk)
    list_file_id = request.POST.get('list_file_id')
    list_folder_id = request.POST.get('list_folder_id')
    offer.topic = topic
    offer.save()

    import json
    selections = json.loads(request.POST.get('selections'))

    # update selection for offer
    for selection in selections:
        id = selection.get('id')
        selector_checkbox = selection.get('selector_checkbox')
        selection_gallery = SelectionGallery.objects.filter(pk=id).first()
        SelectionOffer.objects.create(offer=offer, selection=selection_gallery, work_name=selection.get('artist_name'),
                                      selection_content=selector_checkbox,
                                      toggle_content=json.dumps(selection.get('toggles_on', {})))

    # create first message
    message = create_first_message_in_offer(offer)

    # update file/folder for offer
    if list_folder_id:
        tasks.update_folder(list_folder_id, "offer_product", offer.pk)
    if list_file_id:
        list_file_id = list_file_id.split(",")
    if list_file_id:
        list_files = ProductMessageFile.objects.filter(pk__in=list_file_id)
        for f in list_files:
            f.offer = offer
            f.message = message
            f.save()

    url_offer = reverse("app:direct_inbox") + '?offer=' + str(offer.pk)
    url_mail = url_offer
    success_url = url_offer
    master_client = None
    email_value = ''
    if form_contract:
        contract_info = form_contract.save()
        contract_info.offer = offer
        contract_info.save()
        email_value = contract_info.email
        user = AuthUser.objects.filter(email=email_value).first()
        if user:
            master_client = user
        if user and user.role == AuthUser.MASTERCLIENT:
            # account is master client
            create_project_service(master_client, offer)
        if not user:
            # no account -> create acc, project
            user_created = AuthUser.objects.filter(email=email_value).first()
            if not user_created:
                user_name = email_value
                if AuthUser.objects.filter(username=email_value).exists():
                    count_user = AuthUser.objects.filter(username=email_value).count() + 1
                    user_name = f'{email_value}+{count_user}'
                user_created = AuthUser.objects.create(email=email_value, username=user_name)
            if user_created and user_created.role == AuthUser.MASTERCLIENT:
                master_client = user_created
                master_clients = AuthUser.objects.filter(email=email_value)
                master_clients.update(username=email_value, fullname=contract_info.fullname,
                                      enterprise=contract_info.enterprise,
                                      company_url=contract_info.company_url, phone=contract_info.phone,
                                      position=contract_info.job_type, is_active=False, is_verify=False)
                url_mail = create_project_for_user_has_not_account_service(user_created, offer)

        success_url = '/'
    elif request.user:
        master_client = request.user
        email_value = master_client.email
        create_project_service(master_client, offer)

    send_mail_create_offer_service(request, email_value, offer, url_mail, url_offer)
    if master_client and message:
        message.user = master_client
        message.save()
        receivers = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).exclude(pk=master_client.pk)
        if receivers.exists():
            for receiver in receivers:
                MessageReceiver.objects.create(message=message, user=receiver, seen_date=None)

    return success_url


def create_project_for_user_has_not_account_service(master_client, offer, add_by_sale=False, artist=None, sale_name='',
                                                    sale=None, contact_artist=False):
    if not add_by_sale and contact_artist:
        create_project_by_contact_artist_service(master_client, artist, offer)
    elif not add_by_sale:
        create_project_service(master_client, offer)
    else:
        create_project_by_sale_service(master_client, artist, offer, sale_name, sale)
    # send mail to login
    host = settings.HOST
    url = '/top/invite_user'
    jwt_token = generate_jwt_token_invite_message(str(offer.project.pk), str(offer.pk), master_client.id,
                                                  master_client.email)
    return f'/top/invite_user?jwt={jwt_token}'


def create_project_service(master_client, offer):
    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
    # create project
    project = Product.objects.create(name=offer.topic.title, start_time=offer.start_time, end_time=offer.end_time,
                                     client_name=master_client.enterprise, total_budget=offer.budget)
    # create project user
    for master_admin in master_admins:
        user_index = update_product_user_order(master_admin)
        ProductUser.objects.create(user=master_admin, product=project, order=user_index,
                                   position=ProductUser.MASTERADMIN)
    if master_client:
        user_index = update_product_user_order(master_client)
        ProductUser.objects.create(user=master_client, product=project, order=user_index,
                                   position=ProductUser.OWNER,
                                   is_owner='1')
    offer.project = project
    offer.save()
    add_remove_member_into_offer_product(offer)
    message_content = _('Creating a quote ... Please wait a moment now.')
    create_system_message(master_client, 'messenger_owner', str(offer.offer.pk), message_content, system_message=None,
                          type_system=ProductMessage.OWNER_SYSTEM_MESSAGE)


def send_mail_create_offer_service(request, email_value, offer, url, url_offer):
    # send mail for master admin, master client, producer
    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
    list_user_emails = list(master_admins.values_list('email', flat=True))
    list_user_emails = list(set(list_user_emails))
    scheme = request.scheme
    host = settings.HOST
    path = "{host}{url}".format(host=host, url=url)
    path_offer = "{host}{url}".format(host=host, url=url_offer)
    tasks.send_email_when_create_offer.delay(offer.pk, [email_value], scheme, host, path, 'master_client', email_value)
    tasks.send_email_when_create_offer.delay(offer.pk, list_user_emails, scheme, host, path_offer, 'master_admin',
                                             email_value)


def generate_jwt_token_invite_message(product_id, offer_id, user_invited_id, user_invited_email):
    data = {'user_invited_pk': user_invited_id, 'user_invited_email': user_invited_email, 'product_pk': product_id,
            'offer_pk': offer_id}
    payload = {'exp': int(time.time()) + 86400, 'data': data}
    return jwt.encode(payload, SECRET, algorithm='HS256')


def create_first_message_in_offer(offer, add_by_sale=False, sale_type='1', sale_name='', contact_artist=False):
    message_content = ''
    deadline_value = offer.deadline.strftime("%y/%-m/%-d %H:%M") if offer.deadline else 'なし'
    range_date = 'なし' if not offer.start_time or not offer.end_time else \
        f'{offer.start_time.strftime("%y/%-m/%-d")} - {offer.end_time.strftime("%y/%-m/%-d")}'

    if not add_by_sale and contact_artist:
        message_content = f'作品名: {sale_name}\n \
                        メッセージ:\n{offer.description}\n \n \
                        制作期間: {range_date}\n \
                        最終期限: {deadline_value}\n 権利の取り扱い: {offer.get_contract_type_display()}\n \
                        契約書: {offer.get_ownership_type_display()}\n 実績公開: {offer.get_disclosure_rule_display()}\n'
    elif not add_by_sale:
        message_selection = ''
        for selection in offer.selections.all():
            message_selection += f'{selection.selection.title} \n {selection.work_name} \n {selection.selection_content}\n'
            for option in selection.get_toggle_content():
                message_selection += f'{option["detail"]}\n'

        message_content = f'{message_selection} \n \
        メッセージ:\n{offer.description}\n \n 希望バジェット: {int(offer.budget):,} 円（税抜）\n \
        制作期間: {range_date}\n \
        最終期限: {deadline_value}\n 権利の取り扱い: {offer.get_contract_type_display()}\n \
        契約書: {offer.get_ownership_type_display()}\n 実績公開: {offer.get_disclosure_rule_display()}\n'
    else:
        if sale_type != '4':
            message_content = f'作品名: {sale_name}\n \
                    メッセージ:\n{offer.description}\n \n 希望バジェット: {int(offer.budget):,} 円（税抜）\n'

        else:
            message_content = f'作品名: {sale_name}\n \
                メッセージ:\n{offer.description}\n \n 希望バジェット: {int(offer.budget):,} 円（税抜）\n \
                制作期間: {range_date}\n \
                最終期限: {deadline_value}\n 権利の取り扱い: {offer.get_contract_type_display()}\n \
                契約書: {offer.get_ownership_type_display()}\n 実績公開: {offer.get_disclosure_rule_display()}\n'

    message = ProductMessage.objects.create(comment=message_content, offer=offer, is_first_message=True)
    return message


def save_form_project_setting_modal_service(project, request):
    # Block list
    artist_blocked = request.POST.get('list_artist_block')
    if artist_blocked:
        artist_blocked = artist_blocked.split(",")
        artist_added = AuthUser.objects.filter(Q(user_creator__pk__in=artist_blocked),
                                               ~Q(pk__in=project.artist_blocked.all().values_list('user__pk', flat=True)))
        artist_removes = BlockListArtist.objects.filter(~Q(user__user_creator__pk__in=artist_blocked),
                                Q(pk__in=project.artist_blocked.all().values_list('pk', flat=True)))
        if artist_removes.exists():
            for artist_remove in artist_removes:
                artist_remove.delete()
        if artist_added.exists():
            for artist in artist_added:
                artist_block, created = BlockListArtist.objects.get_or_create(user=artist, project=project)
    else:
        artist_removes = BlockListArtist.objects.filter(pk__in=project.artist_blocked.all().values_list('pk', flat=True))
        for artist_remove in artist_removes:
            artist_remove.delete()

    # Image
    new_image = ''
    delete_image = False
    logo = request.FILES.get('id_logo')
    image = request.FILES.get('id_image')
    is_delete_file_logo = request.POST.get('is_delete_file_logo')
    is_delete_file_image = request.POST.get('is_delete_file_banner')
    logo_name = request.POST.get('id_logo_name')
    image_name = request.POST.get('id_image_name')
    if logo:
        project.logo = logo
        project.logo_name = logo_name
        project.x_logo = float(request.POST.get('x_logo', 0))
        project.y_logo = float(request.POST.get('y_logo', 0))
        project.height_logo = float(request.POST.get('height_logo', 0))
        project.width_logo = float(request.POST.get('width_logo', 0))
        project.save()
    elif not logo and is_delete_file_logo == 'true':
        project.logo = None
        project.logo_name = None
        project.save()
    if not image and is_delete_file_image == 'true':
        project.image = None
        project.image_name = None
        project.image_resized = None
        delete_image = True
        project.save()
        return delete_image
    elif image:
        project.image = image
        project.image_name = image_name
        project.x = float(request.POST.get('x', 0))
        project.y = float(request.POST.get('y', 0))
        project.height = float(request.POST.get('height', 0))
        project.width = float(request.POST.get('width', 0))
        project.save()
        new_image = project.image.url
        return new_image
    return


def get_link_to_preview(file):
    if not file:
        return ''

    from django.conf import settings
    import boto3

    s3 = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                      aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

    import urllib.parse
    url = s3.generate_presigned_url(
        ClientMethod='get_object',
        Params={
            'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
            'Key': file.name,
            'ResponseContentType': 'application/pdf'
        }
    )
    return url


def create_super_producer(product_user, artist, project):
    #TODO
    product_user.current_budget = project.total_budget * (100 - artist.get_usage_fee_for_user())
    product_user.usage_fee = artist.get_usage_fee_for_user()
    product_user.is_super_producer = True
    product_user.save()


def create_offer_creator_by_master_client(master_client, artist, offer_product):
    # auto add product user for owner
    ProductUser.objects.create(user=master_client, position=ProductUser.OWNER, is_owner='1',
                               product=offer_product.project)

    # add offer project for offer product
    offer, created = OfferProject.objects.get_or_create(offer_product=offer_product,
                                                        project=offer_product.project,
                                                        type_offer=OfferProject.OFFER_PRODUCT)

    OfferUser.objects.get_or_create(offer=offer, user=master_client, position=OfferUser.OWNER)

    # add master admin into project
    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN)
    for master_admin in master_admins:
        OfferUser.objects.get_or_create(offer=offer, user=master_admin, position=OfferUser.MASTER_ADMIN)
        ProductUser.objects.get_or_create(product=offer_product.project, user=master_admin,
                                          position=ProductUser.MASTERADMIN)

    # add user into project
    creator = artist.user_creator.first()
    if creator.is_direct:
        ProductUser.objects.create(user=artist, position=ProductUser.PRODUCER,
                                   product=offer_product.project)
        # ProductUser.objects.create(user=artist, position=ProductUser.PRODUCER,
        #                            product=offer_product.project, is_super_producer=True, usage_fee=creator.usage_fee)
        OfferUser.objects.get_or_create(offer=offer, user=artist, position=OfferUser.MASTER_ADMIN)
    return


def save_form_create_offer_service(form, form_contract, request):
    instance = form.save()
    offer = OfferProduct.objects.get(pk=instance.pk)
    producer_id = request.POST.get('artist_id')
    producer = AuthUser.objects.filter(pk=producer_id).first()
    message = create_first_message_in_offer(offer)
    url_offer = reverse("app:direct_inbox") + '?offer=' + str(offer.pk)
    url_mail = url_offer
    success_url = url_offer
    master_client = None
    email_value = ''
    if form_contract:
        contract_info = form_contract.save()
        contract_info.offer = offer
        contract_info.save()
        email_value = contract_info.email
        user = AuthUser.objects.filter(email=email_value).first()
        if user:
            master_client = user
        if user and user.role == AuthUser.MASTERCLIENT:
            # account is master client
            create_project_by_owner_service(master_client, producer, offer)
        if not user:
            # no account -> create acc, project
            user_created = AuthUser.objects.filter(email=email_value).first()
            if not user_created:
                user_name = email_value
                if AuthUser.objects.filter(username=email_value).exists():
                    count_user = AuthUser.objects.filter(username=email_value).count() + 1
                    user_name = f'{email_value}+{count_user}'
                user_created = AuthUser.objects.create(email=email_value, username=user_name)
            if user_created and user_created.role == AuthUser.MASTERCLIENT:
                master_client = user_created
                master_clients = AuthUser.objects.filter(email=email_value)
                master_clients.update(username=email_value, fullname=contract_info.fullname,
                                      enterprise=contract_info.enterprise,
                                      company_url=contract_info.company_url, phone=contract_info.phone,
                                      position=contract_info.job_type, is_active=False, is_verify=False)
                url_mail = create_project_for_owner_has_not_account_service(user_created, producer, offer)

        success_url = '/'
    elif request.user:
        master_client = request.user
        email_value = master_client.email
        create_project_by_owner_service(master_client, producer, offer)

    send_mail_create_offer_by_owner_service(request, email_value, producer.email, offer, url_mail, url_offer)

    if master_client and message:
        message.user = master_client
        message.save()
        MessageReceiver.objects.create(message=message, user=producer, seen_date=None)

    return success_url


def create_project_by_owner_service(master_client, producer, offer_product):
    # create project
    project = Product.objects.create(name=offer_product.topic.title, start_time=offer_product.start_time, end_time=offer_product.end_time,
                                     client_name=master_client.enterprise, total_budget=offer_product.budget)

    offer_product.project = project
    offer_product.artist = producer
    offer_product.save()

    # create offer user
    create_offer_creator_by_master_client(master_client, producer, offer_product)


def create_project_for_owner_has_not_account_service(master_client, producer, offer):
    create_project_by_owner_service(master_client, producer, offer)
    # send mail to login
    host = settings.HOST
    url = '/top/invite_user'
    jwt_token = generate_jwt_token_invite_message(str(offer.project.pk), str(offer.pk), master_client.id,
                                                  master_client.email)
    return f'/top/invite_user?jwt={jwt_token}'


def send_mail_create_offer_by_owner_service(request, album_name, email_value, producer, offer, url, url_offer):
    scheme = request.scheme
    host = settings.HOST
    path = "{host}{url}".format(host=host, url=url)
    path_offer = "{host}{url}".format(host=host, url=url_offer)

    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
    list_user__admin_emails = list(master_admins.values_list('email', flat=True))
    list_user__admin_emails = list(set(list_user__admin_emails))
    tasks.send_email_when_create_offer_by_sale.delay(offer.pk, [email_value], scheme, host, path, 'master_client',
                                                     email_value, album_name, producer.email)

    tasks.send_email_when_create_offer_by_sale.delay(offer.pk, list_user__admin_emails, scheme, host, path_offer,
                                                     'master_admin',
                                                     email_value, album_name, producer.email)

    if producer.user_creator.last().is_direct:
        tasks.send_email_when_create_offer_by_sale.delay(offer.pk, [producer.email], scheme, host, path_offer,
                                                         'producer', email_value, album_name, producer.email)


def get_budget_creator_can_assign(producer, project):
    #TODO
    creator = producer.user_creator.first()
    return int(project.total_budget * (100 - producer.get_usage_fee_for_user())/100)


def add_change_offer_creator_from_admin_to_producer(producer, project):
    try:
        with transaction.atomic():
            # add producer into offer product
            product_user, created = ProductUser.objects.get_or_create(user=producer, product=project)
            product_user.position = ProductUser.PRODUCER
            product_user.order_user = product_user.get_max_order_user()
            product_user.save()
            if project.offer_product.first():
                offer_user, created = OfferUser.objects.get_or_create(user=producer,
                                                                    offer=project.offer_product.first().offer)
                offer_user.position = OfferUser.MASTER_ADMIN
                offer_user.save()
            return 1
    except Exception as e:
        logging.error(e)
    return 0

def add_admin_to_project(producer, project):
    try:
        with transaction.atomic():
            product_user, created = ProductUser.objects.get_or_create(user=producer, product=project)
            product_user.position = ProductUser.MASTERADMIN
            product_user.order = update_product_user_order(producer)
            product_user.order_user = product_user.get_max_order_user()
            product_user.save()
            offer_user, created = OfferUser.objects.get_or_create(user=producer,
                                                                  offer=project.offer_product.first().offer)
            offer_user.position = OfferUser.MASTER_ADMIN
            offer_user.save()
            for offer in project.product_offers.filter(admin__role=AuthUser.MASTERADMIN):
                add_member_into_offer_creator(offer)
            return 1
    except Exception as e:
        logging.error(e)
    return 0


def send_socket_when_add_first_producer(product):
    pu = ProductUser.objects.filter(
        product=product, position__in=[ProductUser.MASTERADMIN, ProductUser.PRODUCER]
    )
    users = AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    channel_layer = get_channel_layer()

    new_offer_data = {
        'type': 'offer_message',
        'action': 'add_first_producer',
        'project_id': str(product.pk),
    }

    for user in users:
        new_offer_data['unread_message_count'] = product.count_unread_offer_message_admin(user)
        async_to_sync(channel_layer.group_send)(
            '{}'.format(user.pk),
            new_offer_data
        )
    return


def create_system_message(user, type_message, offer_id, message_content, system_message=None,
                          type_system=ProductMessage.SYSTEM_MESSAGE):
    real_offer = OfferProject.objects.filter(pk=offer_id).first()
    offer = real_offer.offer_product if type_message == 'messenger_owner' else real_offer.offer_creator
    if offer:
        project = offer.project
        if type_message == 'messenger_owner':
            message = models.ProductMessage.objects.create(comment=message_content,
                                                           type_message=type_system, offer=offer,
                                                           user=user)
            list_user = project.get_member_offer_project()
            if message.type_message == ProductMessage.OWNER_SYSTEM_MESSAGE:
                list_user = list_user.filter(role=AuthUser.MASTERCLIENT)
        else:
            message = models.OfferMessage.objects.create(content=message_content,
                                                         type_message=type_system, offer=offer,
                                                         system_message=system_message, owner=user,
                                                         user=user)

            list_user = offer.get_member_offer()

        data = list()
        data.append(message)
        channel_layer = get_channel_layer()
        for user in list_user:
            new_offer_message = {
                'type': 'offer_message',
                'message': serializers.serialize('json', data),
                'message_id': str(message.pk),
                'type_message': 'system_message',
                'action': 'new_message',
                'offer_id': str(real_offer.pk),
                'project_id': str(project.pk),
            }
            if real_offer.offer_creator and real_offer.offer_creator.status:
                new_offer_message["status"] = real_offer.offer_creator.status
                if real_offer.offer_creator.status == '4':
                    new_offer_message['user_name'] = user.get_display_name()
                    new_offer_message['creator_name'] = get_display_fullname(offer.creator.pk)
                    new_offer_message['admin_name'] = get_display_fullname(offer.admin.pk)
                    new_offer_message['position'] = user.position
            new_offer_message['message_system_html'] = render_to_string('messenger/_item_system_message.html',
                                                                        {'message': message,
                                                                         'type_message': type_message,
                                                                         'user': user})
            new_offer_message["unread_message_count"] = OfferMessageReceiver.count_unread_offer(str(project.pk), str(user.pk))
            new_offer_message["unread_message_offer"] = offer.count_unread_message_in_offer(
                user
            )
            new_offer_message['infor_html'] = render_to_string('direct/_floating_icon_DM.html', {'offer': offer, 'user': user}),

            async_to_sync(channel_layer.group_send)(
                '{}'.format(user.pk),
                new_offer_message,
            )
    return


def update_topic_service(request, topic, form):
    thumbnail = request.FILES.get('thumbnail')
    video = request.FILES.get('video')
    file = request.FILES.get('file')
    image = request.FILES.get('image')
    is_delete_video = request.POST.get('is_delete_video')
    is_delete_file = request.POST.get('is_delete_file')

    topic = form.save()
    if thumbnail:
        topic.thumbnail = thumbnail
        thumbnail_real_name = request.POST.get('thumbnail_real_name', thumbnail.name)
        topic.thumbnail_real_name = thumbnail_real_name
    if file:
        file_real_name = request.POST.get('file_real_name', file.name)
        topic.file = file
        topic.file_real_name = file_real_name
    elif is_delete_file == 'true':
        topic.file = None
        topic.file_real_name = None
    if video:
        topic.video = video
        video_real_name = request.POST.get('video_real_name', video.name)
        topic.video_real_name = video_real_name
    elif is_delete_video == 'true':
        topic.video = None
        topic.video_real_name = None
    if image:
        topic.image = image
        topic.image_real_name = request.POST.get('image_real_name', image.name)
    topic.save()

    # Tag
    hash_tags = request.POST.get('hashtag')
    list_tag = re.findall("#[々〆〤一-龠ぁ-ゔァ-ヴーａ-ｚＡ-Ｚ０-９a-zA-Z0-9]{1,60}", hash_tags)
    list_new_tag = []
    for v in list_tag:
        v = v.replace('#', '')
        list_new_tag.append(v)
        tag = HashTag.objects.filter(tag_name=v, tag_name__contains=v).first()
        if not tag:
            tag = HashTag.objects.create(tag_name=v)
        if not TopicTag.objects.filter(topic=topic, tag=tag).exists():
            TopicTag.objects.create(topic=topic, tag=tag)

    TopicTag.objects.filter(
        Q(topic=topic) & ~Q(tag__tag_name__in=list_new_tag)).delete()

    # Category
    categories = request.POST.get('category')
    list_category = categories.split()
    list_new_categories = []
    for v in list_category:
        # v = v.lower()
        list_new_categories.append(v)
        category = Category.objects.filter(category_name=v, category_name__contains=v).first()
        if not category:
            category = Category.objects.create(category_name=v)
        if not TopicCategory.objects.filter(topic=topic, category=category).exists():
            TopicCategory.objects.create(topic=topic, category=category)
    TopicCategory.objects.filter(
        Q(topic=topic) & ~Q(category__category_name__in=list_new_categories)).delete()

    # selections
    list_selection_delete = request.POST.get('list_selection_delete')
    if list_selection_delete:
        list_selection_delete = list_selection_delete.split(',')
        SelectionGallery.objects.filter(pk__in=list_selection_delete, topic=topic).delete()

    import json
    selections = json.loads(request.POST.get('selections', '[]'))

    for selection in selections:
        title = selection.get('title')
        description = selection.get('description')
        index = selection.get('order')
        selection_id = selection.get('id')
        selection_topic = None
        if selection_id:
            selection_topic = SelectionGallery.objects.filter(pk=selection_id).first()
        if not selection_topic:
            selection_topic = SelectionGallery.objects.create(topic=topic)
        if selection_topic:
            SelectionGallery.objects.filter(pk=selection_topic.pk).update(title=title, description=description,
                                                                          order=index,
                                                                          selection_content=json.dumps(
                                                                              selection.get('selectors_checkbox', {})),
                                                                          toggle_content=json.dumps(
                                                                              selection.get('toggles', {})))
        list_sale = selection.get('sale_arr')
        list_sale_id = []
        for sale in list_sale:
            sale_id = sale.get('sale_id')
            list_sale_id.append(sale_id)
            order = sale.get('order')
            default_thumbnail = sale.get('default_thumbnail', 'C4C4C4')
            sale_content = SaleContent.objects.filter(pk=sale_id).first()
            if sale_content:
                sale_selection = None
                if sale_content not in selection_topic.sale_contents.all():
                    sale_selection = SaleContentSelection.objects.create(sale_content=sale_content,
                                                                         selection=selection_topic)
                else:
                    sale_selection = selection_topic.salecontentselection_set.filter(sale_content=sale_content).first()
                if sale_selection:
                    sale_selection.default_thumbnail = default_thumbnail
                    sale_selection.order = order
                    sale_selection.save()
        SaleContentSelection.objects.filter(
            Q(selection=selection_topic) & ~Q(sale_content_id__in=list_sale_id)).delete()
    return


def create_offer_creator_for_master_producer(producer, project, project_user):
    owner = project.get_owner()
    master_admin = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).first()
    reward_soremo = round(project.total_budget * project_user.usage_fee / 100, 0)
    existed_offer_from = OfferCreator.original_objects.filter(type='2', creator=producer, project=project)
    if not existed_offer_from.exists():
        OfferCreator.original_objects.create(type='2', admin=owner, creator=producer, reward=project.total_budget,
                                            status='2', project=project, accept_time=datetime.datetime.now(),
                                            scenes=project.name, contract='プロジェクトオーナー')

    existed_offer_to = OfferCreator.original_objects.filter(type='2', admin=producer, project=project)
    if not existed_offer_to.exists():
        name_offer_2 = f'サービス利用料（{project_user.usage_fee}%）'
        OfferCreator.original_objects.create(type='2', admin=producer, creator=master_admin, reward=reward_soremo,
                                            status='2', project=project, accept_time=datetime.datetime.now(),
                                            scenes=name_offer_2, contract='手数料')
    AuthUser.change_balance(producer.pk, [('balance_expected', project.total_budget - reward_soremo)])
    ProductUser.update_available_budget(project_user, project.total_budget - reward_soremo)
    ProductUser.update_rewarded(project_user, project.total_budget)


def done_offer_product(project, producer):
    OfferCreator.original_objects.filter(
        Q(type='2') & Q(project=project) & (Q(admin=producer) | Q(creator=producer))).update(status='4',
                                                                                             check_time=datetime.datetime.now())
    offer_1 = OfferCreator.original_objects.filter(type='2', project=project, creator=producer).last()
    offer_2 = OfferCreator.original_objects.filter(type='2', project=project, admin=producer).last()
    if offer_1 and offer_2:
        AuthUser.change_balance(producer.pk,
                                [('balance_available', offer_1.reward - offer_2.reward),
                                 ('balance_expected', offer_2.reward - offer_1.reward),
                                 ('balance_reward', offer_1.reward - offer_2.reward),
                                 ('total_point', offer_1.reward - offer_2.reward)])


def save_form_create_offer_project_by_sale_service(form, form_contract, request):
    instance = form.save()
    offer = OfferProduct.objects.get(pk=instance.pk)
    artist_id = request.POST.get('artist_id')
    sale_id = request.POST.get('sale_id')
    sale_name = request.POST.get('sale_name', '').strip()

    artist = AuthUser.objects.filter(pk=artist_id, role=AuthUser.CREATOR).first()
    sale = SaleContent.objects.filter(pk=sale_id).first()
    offer.artist = artist
    offer.sale = sale
    offer.sale_name = sale_name
    offer.save()
    OrderData.objects.filter(pk=request.POST.get('data_id'), offer__isnull=True).update(offer=offer)

    # create first message
    message = create_first_message_in_offer(offer, True, sale.last_published_version.sale_type, sale_name)

    url_offer = reverse("app:direct_inbox") + '?offer=' + str(offer.pk)
    url_mail = url_offer
    success_url = url_offer
    master_client = None
    email_value = ''

    if form_contract:
        contract_info = form_contract.save()
        contract_info.offer = offer
        contract_info.save()
        email_value = contract_info.email
        user = AuthUser.objects.filter(email=email_value).first()
        if user:
            master_client = user
        if user and user.role == AuthUser.MASTERCLIENT:
            # account is master client
            create_project_by_sale_service(master_client, artist, offer, sale_name, sale)
        if not user:
            # no account -> create acc, project
            user_created = AuthUser.objects.filter(email=email_value).first()
            if not user_created:
                user_name = email_value
                if AuthUser.objects.filter(username=email_value).exists():
                    count_user = AuthUser.objects.filter(username=email_value).count() + 1
                    user_name = f'{email_value}+{count_user}'
                user_created = AuthUser.objects.create(email=email_value, username=user_name)
            if user_created and user_created.role == AuthUser.MASTERCLIENT:
                master_client = user_created
                master_clients = AuthUser.objects.filter(email=email_value)
                master_clients.update(username=email_value, fullname=contract_info.fullname,
                                      enterprise=contract_info.enterprise,
                                      company_url=contract_info.company_url, phone=contract_info.phone,
                                      position=contract_info.job_type, is_active=False, is_verify=False)
                url_mail = create_project_for_user_has_not_account_service(user_created, offer, True, artist, sale_name,
                                                                           sale)

        success_url = '/'

    elif request.user:
        master_client = request.user
        email_value = master_client.email
        create_project_by_sale_service(master_client, artist, offer, sale_name, sale)

    # send mail
    send_mail_create_offer_by_owner_service(request, sale_name.split('/')[0], email_value, artist, offer, url_mail,
                                            url_offer)

    if master_client and message:
        message.user = master_client
        message.save()
        receivers = None
        if artist.user_creator.last().is_direct:
            receivers = AuthUser.objects.filter(role=AuthUser.CREATOR, pk=artist_id).exclude(pk=master_client.pk)
        else:
            receivers = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).exclude(pk=master_client.pk)

        if receivers.exists():
            for receiver in receivers:
                MessageReceiver.objects.create(message=message, user=receiver, seen_date=None)

    return success_url


def create_project_by_sale_service(master_client, artist, offer, sale_name, sale):
    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
    # create project
    project_name = f'{sale_name.split("/")[0]}の{sale.get_custom_sale_type()}'
    project = Product.objects.create(name=project_name, start_time=offer.start_time, end_time=offer.end_time,
                                     client_name=master_client.enterprise, total_budget=offer.budget)

    # create project user
    if master_client:
        user_index = update_product_user_order(master_client)
        ProductUser.objects.create(user=master_client, product=project, order=user_index,
                                   position=ProductUser.OWNER,
                                   is_owner='1')
    if artist.user_creator.last().is_direct:
        user_index = update_product_user_order(artist)
        ProductUser.objects.create(user=artist, product=project, order=user_index,
                                   position=ProductUser.PRODUCER, is_super_producer=True)
        project.contact_artist = True
        project.save()
    else:
        for master_admin in master_admins:
            user_index = update_product_user_order(master_admin)
            ProductUser.objects.create(user=master_admin, product=project, order=user_index,
                                       position=ProductUser.MASTERADMIN)

    offer.project = project
    offer.save()
    add_remove_member_into_offer_product(offer, not artist.user_creator.last().is_direct, artist)
    message_content = _('Creating a quote ... Please wait a moment now.')
    create_system_message(master_client, 'messenger_owner', str(offer.offer.pk), message_content, system_message=None,
                          type_system=ProductMessage.OWNER_SYSTEM_MESSAGE)


def save_form_create_offer_project_by_contact_artist_service(form, form_contract, request):
    order_datas = OrderData.objects.filter(pk=request.POST.get('data_id'), offer__isnull=True)
    instance = form.save()
    offer = OfferProduct.objects.get(pk=instance.pk)
    artist_id = request.POST.get('artist_id')
    artist = AuthUser.objects.filter(pk=artist_id, role=AuthUser.CREATOR).first()
    offer.artist = artist
    offer.save()

    artist_name = artist.get_display_name()

    # create first message
    message = create_first_message_in_offer(offer, False, '', artist_name, True)

    if order_datas.exists():
        order_data = order_datas[0]
        # update file/folder for offer
        if order_data.files.exists():
            order_data.files.filter(message__isnull=True).update(message=message, offer=offer)
        if order_data.folders.exists():
            order_data.folders.filter(message__isnull=True).update(message=message, offer=offer)
        order_datas.update(offer=offer)

    url_offer = reverse("app:direct_inbox") + '?offer=' + str(offer.pk)
    url_mail = url_offer
    success_url = url_offer
    master_client = None
    email_value = ''

    if form_contract:
        contract_info = form_contract.save()
        contract_info.offer = offer
        contract_info.save()
        email_value = contract_info.email
        user = AuthUser.objects.filter(email=email_value).first()
        if user:
            master_client = user
        if user and user.role == AuthUser.MASTERCLIENT:
            # account is master client
            create_project_by_contact_artist_service(master_client, artist, offer)
        if not user:
            # no account -> create acc, project
            user_created = AuthUser.objects.filter(email=email_value).first()
            if not user_created:
                user_name = email_value
                if AuthUser.objects.filter(username=email_value).exists():
                    count_user = AuthUser.objects.filter(username=email_value).count() + 1
                    user_name = f'{email_value}+{count_user}'
                user_created = AuthUser.objects.create(email=email_value, username=user_name)
            if user_created and user_created.role == AuthUser.MASTERCLIENT:
                master_client = user_created
                master_clients = AuthUser.objects.filter(email=email_value)
                master_clients.update(username=email_value, fullname=contract_info.fullname,
                                      enterprise=contract_info.enterprise,
                                      company_url=contract_info.company_url, phone=contract_info.phone,
                                      position=contract_info.job_type, is_active=False, is_verify=False)
                url_mail = create_project_for_user_has_not_account_service(user_created, offer, False, artist, artist_name,
                                                                           None, True)

        success_url = '/'

    elif request.user:
        master_client = request.user
        email_value = master_client.email
        create_project_by_contact_artist_service(master_client, artist, offer)

    # send mail
    send_mail_create_offer_contact_artist_service(request, email_value, artist, offer, url_mail, url_offer)

    if master_client and message:
        message.user = master_client
        message.save()
        receivers = None
        if artist.user_creator.last().is_direct:
            receivers = AuthUser.objects.filter(role=AuthUser.CREATOR, pk=artist_id).exclude(pk=master_client.pk)
        else:
            receivers = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).exclude(pk=master_client.pk)

        if receivers.exists():
            for receiver in receivers:
                MessageReceiver.objects.create(message=message, user=receiver, seen_date=None)

    return success_url


def create_project_by_contact_artist_service(master_client, artist, offer):
    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
    # create project
    project_name = f'{artist.get_display_name()}'

    if offer.topic and offer.topic.user:
        project_name = f'{offer.topic.title}'
    project = Product.objects.create(name=project_name, start_time=offer.start_time, end_time=offer.end_time,
                                     client_name=master_client.enterprise, total_budget=offer.budget)

    # create project user
    if master_client:
        user_index = update_product_user_order(master_client)
        ProductUser.objects.create(user=master_client, product=project, order=user_index,
                                   position=ProductUser.OWNER,
                                   is_owner='1')
    # TODO
    if artist.user_creator.last().is_direct:
        user_index = update_product_user_order(artist)
        ProductUser.objects.create(user=artist, product=project, order=user_index,
                                   position=ProductUser.PRODUCER, is_super_producer=True)
        project.contact_artist = True
        project.save()
    else:
        for master_admin in master_admins:
            user_index = update_product_user_order(master_admin)
            ProductUser.objects.create(user=master_admin, product=project, order=user_index,
                                       position=ProductUser.MASTERADMIN)

    offer.project = project
    offer.save()
    # TODO
    add_remove_member_into_offer_product(offer, not artist.user_creator.last().is_direct, artist)
    message_content = _('Creating a quote ... Please wait a moment now.')
    create_system_message(master_client, 'messenger_owner', str(offer.offer.pk), message_content, system_message=None,
                          type_system=ProductMessage.OWNER_SYSTEM_MESSAGE)


def send_mail_create_offer_contact_artist_service(request, email_value, producer, offer, url, url_offer):
    scheme = request.scheme
    host = settings.HOST
    path = "{host}{url}".format(host=host, url=url)
    path_offer = "{host}{url}".format(host=host, url=url_offer)

    master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
    list_user__admin_emails = list(master_admins.values_list('email', flat=True))
    list_user__admin_emails = list(set(list_user__admin_emails))
    if not offer.topic:
        tasks.send_email_when_create_offer_by_contact_artist.delay(offer.pk, [email_value], scheme, host, path,
                                                                   'master_client', email_value, producer.email)

        tasks.send_email_when_create_offer_by_contact_artist.delay(offer.pk, list_user__admin_emails, scheme, host,
                                                                   path_offer, 'master_admin', email_value,
                                                                   producer.email)
    else:
        tasks.send_email_when_create_offer.delay(offer.pk, [email_value], scheme, host, path, 'master_client',
                                                 email_value)
        tasks.send_email_when_create_offer.delay(offer.pk, list_user__admin_emails, scheme, host, path_offer,
                                                 'master_admin', email_value)
    #TODO
    if producer.user_creator.last().is_direct:
        if not offer.topic:
            tasks.send_email_when_create_offer_by_contact_artist.delay(offer.pk, [producer.email], scheme, host,
                                                                       path_offer,
                                                                       'producer', email_value, producer.email)
        else:
            tasks.send_email_when_create_offer.delay(offer.pk, [producer.email], scheme, host, path_offer,
                                                     'master_admin', email_value)


def update_master_producer(item, project, current_master_producer):
    new_master_producer = item.user
    if new_master_producer.role == AuthUser.CREATOR:
        item.is_super_producer = True
        #TODO
        usage_fee = new_master_producer.get_usage_fee_for_user()
        item.usage_fee = usage_fee
        reward_soremo = round(project.total_budget * usage_fee / 100, 0)
        name_offer_2 = f'利用料{usage_fee}%'
        new_budget = project.total_budget - reward_soremo

        if current_master_producer and item != current_master_producer:
            if project.offer_product.first().can_be_done():
                # assign 2 task
                offer_soremo = OfferCreator.original_objects.filter(type='2',
                                                                    admin=current_master_producer.user,
                                                                    project=project).first()
                offer_owner = OfferCreator.original_objects.filter(type='2',
                                                                   creator=current_master_producer.user,
                                                                   project=project).first()
                budget = offer_owner.reward - offer_soremo.reward

                # update available budget
                ProductUser.update_available_budget(item, new_budget)
                ProductUser.update_available_budget(current_master_producer, -1 * budget)
                ProductUser.update_rewarded(item, offer_owner.reward)
                ProductUser.update_rewarded(current_master_producer, -1 * offer_owner.reward)

                # update scenes, reward for new master producer
                OfferCreator.original_objects.filter(type='2',
                                                     admin=current_master_producer.user, project=project).update(
                    admin=new_master_producer, scenes=name_offer_2, reward=reward_soremo)
                OfferCreator.original_objects.filter(type='2', project=project,
                                                     creator=current_master_producer.user).update(
                    creator=new_master_producer)

                if offer_soremo.status == '2':
                    # update balance_expected
                    AuthUser.change_balance(new_master_producer.pk,
                                            [('balance_expected', new_budget)])
                    AuthUser.change_balance(current_master_producer.user.pk,
                                            [('balance_expected', -1 * budget)])
        elif not current_master_producer:
            if project.offer_product.first().can_be_done():
                owner = project.get_owner()
                master_admin = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).first()
                OfferCreator.original_objects.create(type='2', admin=owner, creator=new_master_producer,
                                                     reward=project.total_budget,
                                                     status='2', project=project,
                                                     accept_time=datetime.datetime.now(),
                                                     scenes=project.name)
                OfferCreator.original_objects.create(type='2', admin=new_master_producer,
                                                     creator=master_admin,
                                                     reward=reward_soremo,
                                                     status='2', project=project,
                                                     accept_time=datetime.datetime.now(),
                                                     scenes=name_offer_2)

                AuthUser.change_balance(new_master_producer.pk,
                                        [('balance_expected', new_budget)])

                ProductUser.update_available_budget(item, new_budget)
                ProductUser.update_rewarded(item, project.total_budget)
    else:
        # update budget offer
        if current_master_producer:
            master_producer = current_master_producer.user
            if project.offer_product.first().can_be_done():
                offer_owner = OfferCreator.original_objects.filter(type='2', creator=master_producer,
                                                                   project=project).first()
                offer_soremo = OfferCreator.original_objects.filter(type='2', admin=master_producer,
                                                                    project=project).first()
                ProductUser.update_available_budget(current_master_producer,
                                                    -1 * (offer_owner.reward - offer_soremo.reward))
                ProductUser.update_rewarded(current_master_producer, -1 * offer_owner.reward)

                if offer_owner.status == '2':
                    AuthUser.change_balance(current_master_producer.user.pk, [
                        ('balance_expected', -1 * (offer_owner.reward - offer_soremo.reward))])
                # delete 2 task
                OfferCreator.original_objects.filter(type='2', project=project).delete()
    return


def update_budget_offer(product_users):
    # product_users.update(budget_offer=0)
    for product_user in product_users:
        current_user = product_user.user
        project = product_user.product
        offers = get_sub_offers(project, current_user)
        budget_offer = offers.aggregate(total_reward=Sum(
            Case(When(admin=current_user, then=-1 * F('reward')),
                 default='reward'))).get('total_reward', 0) if offers else 0
        product_user.budget_offer = budget_offer
        product_user.save()


def get_sub_offers(project, current_user):
    return OfferCreator.original_objects.filter(project=project).filter(
        (Q(status__in=OfferCreator.STATUS_OFFER_ACTIVE) & Q(admin=current_user)) | (
                Q(status__in=['2', '3', '4']) & Q(creator=current_user))).exclude(
        (Q(payment_status=True) & Q(creator_payment_request_id__isnull=True) & Q(
            admin_payment_request_id__isnull=True)) | (Q(payment_status=True) & Q(creator=current_user)) | \
        (Q(admin=current_user) & Q(admin_payment_request_id__isnull=False)) | \
        (Q(creator=current_user) & Q(creator_payment_request_id__isnull=False)))


def check_sale_content_type(sale):
    sale_version = sale.last_published_version
    if sale.last_version:
        sale_version = sale.last_version
    if sale_version and not sale_version.price and not sale_version.max_price \
            and sale_version.sale_type == '4':
        return False
    return True


def save_form_create_offer_by_topic_artist(form, form_contract, topic, request):
    instance = form.save()
    offer = OfferProduct.objects.get(pk=instance.pk)
    list_file_id = request.POST.get('list_file_id')
    list_folder_id = request.POST.get('list_folder_id')
    offer.topic = topic
    offer.artist = topic.user
    offer.save()
    artist = topic.user

    import json
    selections = json.loads(request.POST.get('selections'))

    # update selection for offer
    for selection in selections:
        id = selection.get('id')
        selector_checkbox = selection.get('selector_checkbox')
        selection_gallery = SelectionGallery.objects.filter(pk=id).first()
        SelectionOffer.objects.create(offer=offer, selection=selection_gallery, work_name=selection.get('artist_name'),
                                      selection_content=selector_checkbox,
                                      toggle_content=json.dumps(selection.get('toggles_on', {})))

    # create first message
    # TODO
    message = create_first_message_in_offer(offer, False, '', artist.get_display_name(), True)

    # update file/folder for offer
    if list_folder_id:
        tasks.update_folder(list_folder_id, "offer_product", offer.pk)
    if list_file_id:
        list_file_id = list_file_id.split(",")
    if list_file_id:
        list_files = ProductMessageFile.objects.filter(pk__in=list_file_id)
        for f in list_files:
            f.offer = offer
            f.message = message
            f.save()

    url_offer = reverse("app:direct_inbox") + '?offer=' + str(offer.pk)
    url_mail = url_offer
    success_url = url_offer
    master_client = None
    email_value = ''
    if form_contract:
        contract_info = form_contract.save()
        contract_info.offer = offer
        contract_info.save()
        email_value = contract_info.email
        user = AuthUser.objects.filter(email=email_value).first()
        if user:
            master_client = user
        if user and user.role == AuthUser.MASTERCLIENT:
            # account is master client
            create_project_by_contact_artist_service(master_client, artist, offer)
        if not user:
            # no account -> create acc, project
            user_created = AuthUser.objects.filter(email=email_value).first()
            if not user_created:
                user_name = email_value
                if AuthUser.objects.filter(username=email_value).exists():
                    count_user = AuthUser.objects.filter(username=email_value).count() + 1
                    user_name = f'{email_value}+{count_user}'
                user_created = AuthUser.objects.create(email=email_value, username=user_name)
            if user_created and user_created.role == AuthUser.MASTERCLIENT:
                master_client = user_created
                master_clients = AuthUser.objects.filter(email=email_value)
                master_clients.update(username=email_value, fullname=contract_info.fullname,
                                      enterprise=contract_info.enterprise,
                                      company_url=contract_info.company_url, phone=contract_info.phone,
                                      position=contract_info.job_type, is_active=False, is_verify=False)

                url_mail = create_project_for_user_has_not_account_service(user_created, offer, False, artist,
                                                                           artist.get_display_name(),
                                                                           None, True)

        success_url = artist.get_link_profile()
    elif request.user:
        master_client = request.user
        email_value = master_client.email
        create_project_by_contact_artist_service(master_client, artist, offer)

    send_mail_create_offer_contact_artist_service(request, email_value, artist, offer, url_mail, url_offer)

    if master_client and message:
        message.user = master_client
        message.save()
        receivers = None
        if artist.user_creator.last().is_direct:
            receivers = AuthUser.objects.filter(role=AuthUser.CREATOR, pk=artist.pk).exclude(pk=master_client.pk)
        else:
            receivers = AuthUser.objects.filter(role=AuthUser.MASTERADMIN).exclude(pk=master_client.pk)

        if receivers.exists():
            for receiver in receivers:
                MessageReceiver.objects.create(message=message, user=receiver, seen_date=None)

    return success_url

def save_mgk_form_contact_service(form):
    instance = form.save()
    contact = ContactInfo.objects.get(pk=instance.pk)
    host = settings.HOST
    tasks.send_email_when_user_contact_mgk.delay(contact.email, contact.pk, host)
