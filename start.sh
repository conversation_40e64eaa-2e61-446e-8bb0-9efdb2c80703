#!/bin/bash

# shellcheck disable=SC2164
cd /code/

[ -f celerybeat.pid ] && rm celerybeat.pid

exec mkdir logs &
exec touch logs/infos.log &

exec celery -A voice.celery worker -l info -f logs/celery.log &
exec celery -A voice.celery beat -l info -f logs/celery-beat.log &

exec python3 manage.py collectstatic --noinput &
exec python3 manage.py compilemessages &
exec python3 manage.py migrate &
exec uvicorn voice.routing:application --host 0.0.0.0 --port 8000 --reload --reload-include '*.html'