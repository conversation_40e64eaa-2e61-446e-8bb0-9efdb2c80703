#!/usr/bin/env python3
"""
Test script for HLS MediaConvert integration
Run this script to verify that the HLS integration is working correctly
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'voice.settings')
django.setup()

from app.models import MediaConvertJob
from app.templatetags.util import get_video_url_with_fallback, get_converted_file_url

def test_template_filter():
    """Test the template filter functionality"""
    print("Testing template filter...")
    
    # Test with None input
    result = get_video_url_with_fallback(None)
    print(f"None input: {result}")
    assert result['url'] is None
    
    # Test with sample URL
    sample_url = "test/sample_video.mp4"
    result = get_video_url_with_fallback(None, sample_url)
    print(f"Sample URL: {result}")
    assert result['url'] == sample_url
    assert result['is_hls'] == False
    
    print("✅ Template filter tests passed")

def test_converted_file_url():
    """Test the converted file URL generation"""
    print("Testing converted file URL generation...")
    
    # Test with None
    result = get_converted_file_url(None)
    print(f"None input: {result}")
    assert result is None
    
    # Test with sample key
    sample_key = "converted/sample.m3u8"
    result = get_converted_file_url(sample_key)
    print(f"Sample key result: {result}")
    
    print("✅ Converted file URL tests passed")

def test_mediaconvert_jobs():
    """Test MediaConvert job queries"""
    print("Testing MediaConvert job queries...")
    
    # Check if we have any MediaConvert jobs
    job_count = MediaConvertJob.objects.count()
    print(f"Total MediaConvert jobs: {job_count}")
    
    # Check completed jobs
    completed_jobs = MediaConvertJob.objects.filter(status='completed').count()
    print(f"Completed MediaConvert jobs: {completed_jobs}")
    
    # Show sample jobs if any exist
    sample_jobs = MediaConvertJob.objects.all()[:5]
    for job in sample_jobs:
        print(f"Job: {job.original_object_key} -> {job.converted_media_key} ({job.status})")
    
    print("✅ MediaConvert job queries completed")

def test_api_endpoint():
    """Test the API endpoint functionality"""
    print("Testing API endpoint...")
    
    from django.test import Client
    from django.urls import reverse
    
    client = Client()
    
    # Test without parameters
    response = client.get('/get_video_url_with_fallback')
    print(f"No params response: {response.status_code}")
    assert response.status_code == 400
    
    # Test with sample URL
    response = client.get('/get_video_url_with_fallback', {
        'original_url': 'test/sample.mp4'
    })
    print(f"Sample URL response: {response.status_code}")
    assert response.status_code == 200
    
    data = response.json()
    print(f"Response data: {data}")
    
    print("✅ API endpoint tests passed")

def main():
    """Run all tests"""
    print("🚀 Starting HLS MediaConvert Integration Tests")
    print("=" * 50)
    
    try:
        test_template_filter()
        print()
        
        test_converted_file_url()
        print()
        
        test_mediaconvert_jobs()
        print()
        
        test_api_endpoint()
        print()
        
        print("=" * 50)
        print("🎉 All tests passed! HLS integration is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
