# MediaConvert Integration - Complete Implementation

## Tổng quan
Tài liệu này mô tả việc tích hợp AWS MediaConvert cho tất cả video trong hệ thống, bao gồm performance optimization để không load file gốc khi có HLS data.

## Core Components

### 1. Template Filter
**File**: `app/templatetags/util.py`
```python
def get_video_url_with_fallback(video_field, original_url=None, skip_original_if_hls=True):
    """
    Get video URL with MediaConvert fallback
    
    Args:
        video_field: Django FileField instance or URL string
        original_url: Original video URL if video_field is None
        skip_original_if_hls: If True, don't include original URL when HLS is available (performance optimization)
    
    Returns: dict with 'url', 'is_hls', 'fallback_url'
    """
```

### 2. API Endpoint
**File**: `app/views.py`
```python
def get_video_url_with_fallback(request):
    """
    API endpoint để lấy video URL với MediaConvert fallback
    GET /get_video_url_with_fallback?original_url=<url>&skip_original_if_hls=true
    Returns: JSON với 'url', 'is_hls', 'fallback_url'
    """
```

### 3. JavaScript Utilities
**File**: `app/static/js/mediaconvert-utils.js`
- HLS initialization và error handling
- Auto-initialization cho video elements mới
- Performance optimization

## Performance Optimization

### 1. Skip Original File Loading
Khi có HLS data, hệ thống sẽ không load file video gốc để tối ưu performance:

```python
# Template filter với skip_original_if_hls=True (mặc định)
{% with video_info=video_field|get_video_url_with_fallback %}
<video data-video-src="{{ video_info.url }}"
       data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
       data-fallback-src="{{ video_info.fallback_url }}">
    <source src="{{ video_info.url }}" type="video/mp4"/>
</video>
{% endwith %}
```

**Lợi ích:**
- Giảm bandwidth usage
- Tăng tốc độ load page
- Tránh load file video gốc không cần thiết

### 2. JavaScript Error Handling
Cập nhật error handling cho trường hợp không có fallback URL:

```javascript
// Trong mediaconvert-utils.js
hls.on(Hls.Events.ERROR, function(event, data) {
    if (data.fatal) {
        if (fallbackSrc && fallbackSrc !== 'undefined' && fallbackSrc !== 'null') {
            // Fallback to original video
            video.src = fallbackSrc;
        } else {
            // No fallback available, reload HLS
            setTimeout(() => {
                if (video._hlsInstance) {
                    video._hlsInstance.loadSource(videoSrc);
                }
            }, 2000);
        }
    }
});
```

## Implementation Status

### ✅ Template Files (100% Complete)
- `app/templates/creator/topic_detail.html`
- `app/templates/scene/detail.html`
- `app/templates/scene/list.html`
- `app/templates/collection/bookmarked_scene.html`
- `app/templates/creator/_detail_topic_in_modal.html`
- `app/templates/messenger/index.html`
- `app/templates/product/order_file_delete.html`
- `app/templates/scene/change.html`
- `app/templates/scene/delete.html`
- `app/templates/top/_cscene.html`
- `app/templates/top/_item_received_2.html`
- `app/templates/top/_item_send_2.html`
- `app/templates/top/_list_update_video.html`
- `app/templates/top/_process_bottom_video.html`
- `app/templates/collection/_item_add.html`
- `app/templates/collection/_sale.html`
- `app/templates/creator/_item_sale_content_item.html`
- `app/templates/top/_carousel_all_scene.html`
- `app/templates/top/_item_received.html`
- `app/templates/top/_item_send.html`
- `app/templates/top/_video_item_component.html`
- `app/templates/top/_process_top_video.html`
- `app/templates/top/_list_update_video_refactor.html`
- `app/templates/top/_cscene_delivery.html`
- `app/templates/top/_project_video.html`
- `app/templates/top/_video-modal__videos.html`
- `app/templates/product/order.html`
- `app/templates/product/upload.html`
- `app/templates/scene/upload.html`
- `app/templates/scene/show.html`
- `app/templates/messenger/_modal_open_file.html`
- `accounts/templates/accounts/creator/_sale_content.html`

### ✅ JavaScript Files (100% Complete)
- `app/static/js/album_preview.js`
- `app/static/js/creator_profile.js`
- `app/static/js/main.js`
- `app/static/js/top_page_admin.js`
- `app/static/js/mediaconvert-utils.js` (new)

### ✅ API Endpoints (100% Complete)
- `app/views.py` - `get_video_url_with_fallback` endpoint
- `app/urls.py` - URL routing

## Usage Patterns

### Template Pattern
```html
{% with video_info=video_field|get_video_url_with_fallback %}
<video data-video-src="{{ video_info.url }}"
       data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
       data-fallback-src="{{ video_info.fallback_url }}">
    <source src="{{ video_info.url }}" type="video/mp4"/>
</video>
{% endwith %}
```

### JavaScript Pattern
```javascript
// API call
const response = await $.ajax({
    type: "GET",
    url: "/get_video_url_with_fallback",
    data: {
        original_url: src,
        skip_original_if_hls: 'true'
    }
});

// Video element creation
const video = $('<video>', {
    'data-video-src': response.url,
    'data-is-hls': response.is_hls,
    'data-fallback-src': response.fallback_url
});
```

## Testing Strategy

### 1. Manual Testing
- Kiểm tra video load với HLS
- Kiểm tra fallback khi HLS fail
- Kiểm tra performance (không load file gốc)

### 2. Browser Testing
- Chrome, Firefox, Safari
- Mobile browsers
- HLS.js compatibility

### 3. Error Scenarios
- Network issues
- HLS server down
- Invalid video files

## Migration Checklist

### ✅ Template Files (100% Complete)
- [x] `app/templates/creator/topic_detail.html`
- [x] `app/templates/scene/detail.html`
- [x] `app/templates/scene/list.html`
- [x] `app/templates/collection/bookmarked_scene.html`
- [x] `app/templates/creator/_detail_topic_in_modal.html`
- [x] `app/templates/messenger/index.html`
- [x] `app/templates/product/order_file_delete.html`
- [x] `app/templates/scene/change.html`
- [x] `app/templates/scene/delete.html`
- [x] `app/templates/top/_cscene.html`
- [x] `app/templates/top/_item_received_2.html`
- [x] `app/templates/top/_item_send_2.html`
- [x] `app/templates/top/_list_update_video.html`
- [x] `app/templates/top/_process_bottom_video.html`

### ✅ JavaScript Files (100% Complete)
- [x] `app/static/js/album_preview.js`
- [x] `app/static/js/creator_profile.js`
- [x] `app/static/js/main.js`
- [x] `app/static/js/top_page_admin.js`
- [x] `app/static/js/mediaconvert-utils.js`

### ✅ API Endpoints (100% Complete)
- [x] `app/views.py` - `get_video_url_with_fallback`
- [x] `app/urls.py` - URL routing

## Performance Metrics

### Before Optimization
- Load cả file video gốc và HLS
- High bandwidth usage
- Slow page load

### After Optimization
- Chỉ load HLS khi có data
- Reduced bandwidth usage
- Faster page load
- Better user experience

## Conclusion

✅ **100% COMPLETION ACHIEVED**

Tất cả video trong hệ thống đã được tích hợp MediaConvert với:
- Performance optimization
- Error handling robust
- Fallback mechanisms
- HLS support
- Reduced bandwidth usage

**Next Steps:**
- Monitor performance metrics
- Test error scenarios
- Optimize further if needed 