# define base image as python slim-buster.
FROM ubuntu:22.04 as base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV DJANGO_SETTINGS_MODULE=voice.settings
ENV DEBIAN_FRONTEND noninteractive

### Install `wkhtmltopdf` and `xvfb` for generate pdf
RUN apt-get update && \
    apt-get install -y software-properties-common && \
    add-apt-repository -y ppa:deadsnakes/ppa &&  \
    apt-get update

# Add repository with Python 3.6
RUN apt-get install -y \
    git \
    curl \
    wget \
    build-essential \
    zlib1g-dev \
    liblzma-dev \
    libfreetype6-dev \
    libssl-dev \
    libpng-dev \
    libjpeg-turbo8-dev \
    icc-profiles-free \
    libxrender1 \
    libxext6 \
    libx11-6 \
    fontconfig \
    xfonts-base \
    xfonts-75dpi \
    libmysqlclient-dev \
    libpq-dev \
    gettext \
    poppler-utils
    
RUN apt-get install -y ffmpeg

RUN wget https://github.com/wkhtmltopdf/packaging/releases/download/********-2/wkhtmltox_********-2.jammy_amd64.deb && \
    dpkg -i wkhtmltox_********-2.jammy_amd64.deb && \
    apt-get install -f


# Install Python 3 and related packages
RUN apt-get install -y python3.13 python3-venv python3-pip python3-distutils

### Install vlc media
RUN apt-get install -y vlc --no-install-recommends \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /code

# copy the source code into /code and move into that directory.
WORKDIR /code

### Install the required packages
COPY requirements.txt .
# tmp comment
# RUN pip3 install --upgrade --no-cache-dir pip setuptools
RUN pip install --upgrade setuptools

# tmp for quick debug, after ok, move to above code block
RUN apt-get update
RUN apt-get install -y libffi-dev
RUN apt-get install -y pkg-config
RUN apt-get install -y xvfb
RUN apt-get install -y redis-server

RUN pip3 install --no-cache-dir -r requirements.txt --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org

COPY . /code

## end builder stage.

## start base stage.

# this is the image this is run.
FROM base

EXPOSE 8000

#CMD exec uwsgi --http :80 --module config.wsgi
CMD ["chmod", "+x", "/code/start.sh"]

# default entry point.
ENTRYPOINT ["sh", "/code/start.sh"]
## end base stage.
