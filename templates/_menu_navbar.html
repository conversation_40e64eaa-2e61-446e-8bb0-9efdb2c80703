{% load static %}
{% load compress %}
{% load user_agents %}
{% load util %}
{% load i18n %}

{% if user.is_authenticated %}
  <div class="sheader-container">
    <div class="smenu-pc">
      <div class="mcontainer">
        <div class="sheader-pc">
          <div class="sheader-pc__left">
            <div class="sheader-pc-logo">
              <a class="logo" href="#">
              </a>
            </div>
          </div>
          <div class="sheader-pc__right">
            <div class="sheader-links {% if user.role == 'master_admin' %}nav-master-admin{% endif %}">

              {% if user.role == 'admin' or user.role == 'master_admin' or user.role == 'master_client' %}
                <a class="sheader-link text-link" href="{% url 'app:top_page' %}?force=true" data-show="project">PROJECT</a>
              {% endif %}

              {% if user.role in 'admin' %}
                <a class="sheader-link text-link {% if user.user_creator.exists and user.user_creator.first.last_version %} creator-profile-new {% if  user.user_creator.first.last_version.owner_id == user.pk %}owner-creator-profile-new{% else %}checker-creator-profile-new{% endif %}{% endif %}"
                   href="{{ user.get_link_profile }}"
                   data-show="profile">PROFILE</a>
              {% endif %}

              {% if user.role == 'curator' or user.role == 'master_client' %}
                <a class="sheader-link text-link" href="{% url 'app:creator_index' %}" data-show="gallery">GALLERY</a>
              {% endif %}

              {% if user.role == 'master_admin' %}
                <a class="sheader-link text-link" href="{% url 'accounts:accounts_list' %}" data-show="account">ACCOUNT</a>
              {% endif %}
              {% if user.role == 'curator' %}
                <a class="sheader-link text-link" href="{% url 'accounts:curator_setting' %}" data-show="account">ACCOUNT</a>
              {% endif %}
              {% if user.role == 'master_admin' %}
                <a class="sheader-link text-link" href="{% url 'mileages:index' %}" data-show="mileage">MILEAGE</a>
                <a class="sheader-link text-link" href="{% url 'app:updateinfo' %}" data-show="updateinfo">UPDATE INFO</a>
              {% else %}
                {% if user.role == 'admin' or user.role == 'master_admin' or user.role == 'master_client' %}
                  <a class="sheader-link" href="{% url 'app:get_bookmarked' %}" data-show="collection" style="margin-top: 3px;">
                    <span class="icon icon--sicon-bookmark fa-bookmark icon-bookmark-navbar"></span>
                  </a>
                {% endif %}
              {% endif %}
            </div>
            <div class="sheader-info">
              <a class="sheader-account-link" href="javascript:void(0)">
                <div class="{% if user.role == 'admin' %}mileage-popup{% endif %}" id="account-link">
                  <div class="c-avatar40">
                    <img src="{{ user|get_avatar:'medium' }}">
                  </div>
                </div>
              </a>
              <div class="sheader-dropdown bdropdown">
                <a class="bdropdown-toggle dropdown-toggle" href="javascript:void(0)" role="button" id="mthread-id"
                   data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="icon icon--sicon-dropdown"></i>
                </a>
                <div class="bdropdown-menu dropdown-menu" aria-labelledby="mthread-id">
                  {% if user.role != 'admin' %}
                    <a class="bdropdown-item" href="{% url 'accounts:accounts_update' user.id %}">アカウント情報</a>
                    <a class="bdropdown-item" href="{% url 'accounts:accounts_setting' user.id %}">利用設定</a>

                    {% if user.role == 'master_client' %}
                      <a class="bdropdown-item" href="{% url 'accounts:payment' user.id %}">クレジットカード情報を設定</a>
                      <a class="bdropdown-item" href="https://soremo.notion.site/Owner-Help-Center-76d42490929b46909d60f688413ac9a1" target="_blank">ヘルプセンター</a>
                    {% else %}
                      {% if user.role == 'master_admin' %}
                        <a class="bdropdown-item" href="{% url 'payments:payment_current_master_admin' %}">検収状況</a>
                      {% endif %}
                      <a class="bdropdown-item" href="https://soremo.notion.site/Partner-Help-Center-a294ba282ad9438a968a3cb9e1ef7c77" target="_blank">ヘルプセンター</a>
                    {% endif %}

                  {% else %}
                  {% if user.get_creator_id %}
                  <a class="bdropdown-item" href="{% url 'accounts:accounts_creator_info' user.get_creator_id %}">アカウント情報</a>
              {% else %}
                  <span>{% trans "No account information available" %}</span>
              {% endif %}
              {% if user.get_creator_id %}
              <a class="bdropdown-item" href="{% url 'accounts:accounts_creator_setting' user.get_creator_id %}">{% trans "usage setting" %}</a>
          {% else %}
              <span>{% trans "No usage setting available" %}</span>
          {% endif %}
                    <a class="bdropdown-item" href="{% url 'payments:payment_current_artist' %}">ウォレットの残高</a>
                    <a class="bdropdown-item" href="{% url 'accounts:payment' user.id %}">クレジットカード情報を設定</a>
                    <a class="bdropdown-item" href="https://soremo.notion.site/Partner-Help-Center-a294ba282ad9438a968a3cb9e1ef7c77" target="_blank">ヘルプセンター</a>
                  {% endif %}
                  <a class="u-border-top bdropdown-item btn-logout {% if user.role == 'admin' %}is-admin{% endif %}" href="{% url 'accounts:accounts_logout' %}">サインアウト</a>
                </div>
              </div>
                {% if not hide_milaeage_popup %}
                    {% include 'components/_modal.html' with position="top-right" body_html="_modal_mileage_popup_body.html" modal_id="modal_mileage_popup" modal_zindex="2000" %}
                {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% else %}
  <div class="sheader-container">
    <div class="smenu-pc">
      <div class="mcontainer">
        <div class="sheader-pc">
          <div class="sheader-pc__left">
            <div class="sheader-pc-logo">
              <a class="logo" href="#">
              </a>
            </div>
          </div>
          <div class="sheader-pc__right">
            <div class="sheader-links">
              <a class="sheader-link" href="{% url 'app:creator_index' %}" data-show="gallery">GALLERY</a>
            </div>
            <div class="sheader-info">
              <a class="sheader-account-link" href="{% url 'accounts:accounts_login' %}">
                <div class="avatar avatar--icon avatar--32 avatar--round" id="account-link">
                  <div class="avatar-icon">
                    <i class="icon icon--sicon-user"></i>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endif %}

<script src="{% url 'javascript-catalog' %}"></script>