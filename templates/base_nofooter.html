{% load static %}
{% load user_agents util compress %}
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    {% block title %}
      <title>{% if title_page %}{{ title_page }}{% else %}SOREMO{% endif %}</title>
    {% endblock %}
    <meta name="viewport"
          content="width=device-width, initial-scale=1, shrink-to-fit=no, maximum-scale=1, user_scalable=0"/>
    {% include '_gtag_head.html' %}
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />

    {% compress css %}
        {% if request.resolver_match.url_name == 'top_project_detail' %}
            <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/main.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/modal.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/base.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/components/modal.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/components/image.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail/main_new.css' %}"/>
        {% elif request.resolver_match.url_name == 'scene_title_detail' %}
            <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail/main.css' %}"/>
            <link rel="stylesheet" type="text/cs" href="{% static 'css/scene_detail/modal.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail/base.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail/components/modal.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail/components/image.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail/main_new.css' %}"/>
        {% else %}
            <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/base.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/components/modal.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/components/image.css' %}"/>
            <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
        {% endif %}
        <link rel="stylesheet" href="{% static 'css/soremo_style_2024.css' %}"/>
    {% endcompress %}
    <script>let is_logged_in = '{{ user.is_authenticated }}';</script>
    <script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}";</script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    {% comment %} {% include "config_font.html" %} {% endcomment %}
    {% block extrahead %}{% endblock %}
    <meta name="csrf-token" content="{{ csrf_token }}">
    <link rel="shortcut icon" href="{% static 'images/soremo-favi2_01.png' %}">
    <link rel="apple-touch-icon" href="{% static 'images/soremo-favi2_01.png' %}">
    <style>
        .account-link {
            margin-left: 20px;
        }
        @media (max-width: 992px) {
            .logo-wrap-sp {
                top: 7px;
            }
        }
    </style>
</head>

<body>
  {% include '_gtag_body.html' %}
<header class="sheader d-block-header">
  {% include '_menu_navbar_refactor.html' with user=user request=request %}
</header>

{% block content %}
{% endblock %}
</body>
<script src="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.0/croppie.min.js" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
        integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<!-- HLS.js for adaptive streaming support -->
<script src="https://cdn.jsdelivr.net/npm/hls.js@1.5.12/dist/hls.min.js"></script>
<!-- MediaConvert utilities -->
<script src="{% static 'js/mediaconvert-utils.js' %}"></script>
{% compress js inline %}
<script type="text/javascript" src="{% static 'js/init_socket_messenger.js' %}"></script>
<script>
  if (is_logged_in === 'True') {
      initSocket({{ user.id }});
  }
</script>
<script src="{% static 'js/main.js' %}"></script>
{% endcompress %}
{% block extra_script %}
{% endblock %}
</html>
