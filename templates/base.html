{% load bootstrap3 static compress %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE|default:"en-us" }}">
<head>
    <meta charset="UTF-8">
    <title>SOREMO</title>
    <script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}";</script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {% include '_gtag_head.html' %}
    <link rel="shortcut icon" href="{%  static 'images/favicon.svg' %}">
    {% bootstrap_css %}
    {% bootstrap_javascript jquery=1 %}
    {% compress css %}
        <style type="text/css">
            .border-1{
                border: 1px solid #ccc;
            }

            .round{
                border-radius: 0.5em;
            }

            .auto {
                margin-right: auto !important;
                margin-left: auto !important;
            }

            .mt-3 { margin-top: -3px !important; }
            .mt-5 { margin-top: -5px !important; }
            .mt-6 { margin-top: -6px !important; }
            .mt-8 { margin-top: -8px !important; }
            .mt-10 { margin-top: -10px !important; }
            .ml-3 { margin-left: -3px !important; }
            .ml-6 { margin-left: -6px !important; }
            .ml-7 { margin-left: -7px !important; }
            .ml-8 { margin-left: -8px !important; }
            .ml-10{ margin-left: -10px !important; }
            .mr-3 { margin-right: -3px !important; }

            .m0 { margin: 0 auto !important; }
            .m3 { margin: 3px auto !important; }
            .m5 { margin: 5px auto !important; }
            .m6 { margin: 6px auto !important; }
            .m8 { margin: 8px auto !important; }
            .m10 { margin: 10px auto !important; }
            .p0 { padding: 0 !important; }
            .p3 { padding: 3px !important; }
            .p5 { padding: 5px !important; }
            .p6 { padding: 6px !important; }
            .p8 { padding: 8px !important; }
            .p10 { padding: 10px !important; }
            .p15 { padding: 15px !important; }
            .p20 { padding: 20px !important; }
            .p30 { padding: 30px !important; }
            .p40 { padding: 40px !important; }
            .mt0, .mv0, .ma0 { margin-top:     0 !important; }
            .mr0, .mh0, .ma0 { margin-right:   0 !important; }
            .mb0, .mv0, .ma0 { margin-bottom:  0 !important; }
            .ml0, .mh0, .ma0 { margin-left:    0 !important; }
            .pt0, .pv0, .pa0 { padding-top:    0 !important; }
            .pr0, .ph0, .pa0 { padding-right:  0 !important; }
            .pb0, .pv0, .pa0 { padding-bottom: 0 !important; }
            .pl0, .ph0, .pa0 { padding-left:   0 !important; }
            .mt2, .mv2, .ma2 { margin-top:     2px !important; }
            .mr2, .mh2, .ma2 { margin-right:   2px !important; }
            .mb2, .mv2, .ma2 { margin-bottom:  2px !important; }
            .ml2, .mh2, .ma2 { margin-left:    2px !important; }
            .pt2, .pv2, .pa2 { padding-top:    2px !important; }
            .pr2, .ph2, .pa2 { padding-right:  2px !important; }
            .pb2, .pv2, .pa2 { padding-bottom: 2px !important; }
            .pl2, .ph2, .pa2 { padding-left:   2px !important; }
            .mt5, .mv5, .ma5 { margin-top:     5px !important; }
            .mr5, .mh5, .ma5 { margin-right:   5px !important; }
            .mb5, .mv5, .ma5 { margin-bottom:  5px !important; }
            .ml5, .mh5, .ma5 { margin-left:    5px !important; }
            .pt5, .pv5, .pa5 { padding-top:    5px !important; }
            .pr5, .ph5, .pa5 { padding-right:  5px !important; }
            .pb5, .pv5, .pa5 { padding-bottom: 5px !important; }
            .pl5, .ph5, .pa5 { padding-left:   5px !important; }
            .pt8, .pv8, .pa8 { padding-top:    8px !important; }
            .pr8, .ph8, .pa8 { padding-right:  8px !important; }
            .pb8, .pv8, .pa8 { padding-bottom: 8px !important; }
            .pl8, .ph8, .pa8 { padding-left:   8px !important; }
            .mt10, .mv10, .ma10 { margin-top:     10px !important; }
            .mr10, .mh10, .ma10 { margin-right:   10px !important; }
            .mb10, .mv10, .ma10 { margin-bottom:  10px !important; }
            .ml10, .mh10, .ma10 { margin-left:    10px !important; }
            .pt10, .pv10, .pa10 { padding-top:    10px !important; }
            .pr10, .ph10, .pa10 { padding-right:  10px !important; }
            .pb10, .pv10, .pa10 { padding-bottom: 10px !important; }
            .pl10, .ph10, .pa10 { padding-left:   10px !important; }
            .mt15, .mv15, .ma15 { margin-top:     15px !important; }
            .mr15, .mh15, .ma15 { margin-right:   15px !important; }
            .mb15, .mv15, .ma15 { margin-bottom:  15px !important; }
            .ml15, .mh15, .ma15 { margin-left:    15px !important; }
            .pt15, .pv15, .pa15 { padding-top:    15px !important; }
            .pr15, .ph15, .pa15 { padding-right:  15px !important; }
            .pb15, .pv15, .pa15 { padding-bottom: 15px !important; }
            .pl15, .ph15, .pa15 { padding-left:   15px !important; }
            .mt20, .mv20, .ma20 { margin-top:     20px !important; }
            .mr20, .mh20, .ma20 { margin-right:   20px !important; }
            .mb20, .mv20, .ma20 { margin-bottom:  20px !important; }
            .ml20, .mh20, .ma20 { margin-left:    20px !important; }
            .pt20, .pv20, .pa20 { padding-top:    20px !important; }
            .pr20, .ph20, .pa20 { padding-right:  20px !important; }
            .pb20, .pv20, .pa20 { padding-bottom: 20px !important; }
            .pl20, .ph20, .pa20 { padding-left:   20px !important; }
            .mt30, .mv30, .ma30 { margin-top:     30px !important; }
            .mr30, .mh30, .ma30 { margin-right:   30px !important; }
            .mb30, .mv30, .ma30 { margin-bottom:  30px !important; }
            .ml30, .mh30, .ma30 { margin-left:    30px !important; }
            .pt30, .pv30, .pa30 { padding-top:    30px !important; }
            .pr30, .ph30, .pa30 { padding-right:  30px !important; }
            .pb30, .pv30, .pa30 { padding-bottom: 30px !important; }
            .pl30, .ph30, .pa30 { padding-left:   30px !important; }
            .mt40, .mv40, .ma40 { margin-top:     40px !important; }
            .mr40, .mh40, .ma40 { margin-right:   40px !important; }
            .mb40, .mv40, .ma40 { margin-bottom:  40px !important; }
            .ml40, .mh40, .ma40 { margin-left:    40px !important; }
            .pt40, .pv40, .pa40 { padding-top:    40px !important; }
            .pr40, .ph40, .pa40 { padding-right:  40px !important; }
            .pb40, .pv40, .pa40 { padding-bottom: 40px !important; }
            .pl40, .ph40, .pa40 { padding-left:   40px !important; }
            .bt0, .ba0 { border-top: none !important; }
            .br0, .ba0 { border-right: none !important; }
            .bb0, .ba0 { border-bottom: none !important; }
            .bl0, .ba0 { border-left: none !important; }

            .lh10 { line-height: 1.0; }
            .lh11 { line-height: 1.1; }
            .lh12 { line-height: 1.2; }
            .lh13 { line-height: 1.3; }
            .lh14 { line-height: 1.4; }
            .lh15 { line-height: 1.5; }
            .lh16 { line-height: 1.6; }
            .lh17 { line-height: 1.7; }
            .lh18 { line-height: 1.8; }
            .lh19 { line-height: 1.9; }
            .lh20 { line-height: 2.0; }

            .w300 { width: 300px !important;}

            .navbar-default {
                background-color: #ffffff;
                border-color: #e7e7e7;
            }
            .navbar-default .navbar-brand {
                color: #1e1e1e;
            }
            .navbar-default .navbar-brand:hover,
            .navbar-default .navbar-brand:focus {
                color: #151515;
            }
            .navbar-default .navbar-text {
                color: #1e1e1e;
            }
            .navbar-default .navbar-nav > li > a {
                color: #1e1e1e;
            }
            .navbar-default .navbar-nav > li > a:hover,
            .navbar-default .navbar-nav > li > a:focus {
                color: #151515;
            }
            .navbar-default .navbar-nav > .active > a,
            .navbar-default .navbar-nav > .active > a:hover,
            .navbar-default .navbar-nav > .active > a:focus {
                color: #151515;
                background-color: #e7e7e7;
            }
            .navbar-default .navbar-nav > .open > a,
            .navbar-default .navbar-nav > .open > a:hover,
            .navbar-default .navbar-nav > .open > a:focus {
                color: #151515;
                background-color: #e7e7e7;
            }
            .navbar-default .navbar-toggle {
                border-color: #e7e7e7;
            }
            .navbar-default .navbar-toggle:hover,
            .navbar-default .navbar-toggle:focus {
                background-color: #e7e7e7;
            }
            .navbar-default .navbar-toggle .icon-bar {
                background-color: #1e1e1e;
            }
            .navbar-default .navbar-collapse,
            .navbar-default .navbar-form {
                border-color: #1e1e1e;
            }
            .navbar-default .navbar-link {
                color: #1e1e1e;
            }
            .navbar-default .navbar-link:hover {
                color: #151515;
            }

            @media (max-width: 767px) {
                .navbar-default .navbar-nav .open .dropdown-menu > li > a {
                    color: #1e1e1e;
                }
                .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
                .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
                    color: #151515;
                }
                .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
                .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
                .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
                    color: #151515;
                    background-color: #e7e7e7;
                }
            }

            .project {
                padding: 10px;
                margin: 3px;
                border-top: solid 1px #999;
                border-bottom: solid 1px #999;
            }
        </style>
    {% endcompress %}
    {% block extrahead %}{% endblock %}
</head>
<body>
{% include '_gtag_body.html' %}
<div class="container">
    <nav class="navbar navbar-default">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#gnavi">
                <span class="sr-only">メニュー</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a href="{% url 'app:index' %}" class="navbar-brand">SOREMO</a>
        </div>
        <div id="gnavi" class="collapse navbar-collapse">
            <ul class="nav navbar-nav navbar-right">
                {% if user.is_authenticated %}
                    {% if user.role == 'master_admin' or user.role == 'curator' %}
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button">管理メニュー<span class="caret"></span></a>
                        <ul class="dropdown-menu" role="menu">
                            {% if user.role in 'master_admin' %}
{#                            <li><a href="{% url 'app:product_create' %}">プロジェクト登録</a></li>#}
                            {% endif %}
                            <li><a href="{% url 'accounts:accounts_regist' %}">アカウント追加</a></li>
                            <li><a href="{% url 'accounts:accounts_list' %}">アカウント管理</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button">{{ user }}様<span class="caret"></span></a>
                        <ul class="dropdown-menu" role="menu">
                            <li><a href="{% url 'accounts:accounts_update' user.id %}">情報変更</a></li>
                            <li><a href="{% url 'accounts:accounts_logout' %}">ログアウト</a></li>
                        </ul>
                    </li>
                {% else %}
                    <li><a href="{% url 'accounts:accounts_login' %}">ログイン</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>
    {% block content %}
        {{ content }}
    {% endblock %}
</div>
</body>
<script>let is_logged_in = '{{ user.is_authenticated }}';</script>
</html>
