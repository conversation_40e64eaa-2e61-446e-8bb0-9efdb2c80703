{% load static %}
{% load util %}

{% block extrahead %}
{% endblock %}

{% block content %}
    {% if not type or type not in 'date-time, date, datetime, datetime-local, month, time, week, select, select-search, select-input, text-area, rangetime' %}
        <div class="form-group component-input custom-input-component {% if error_text %}error-input{% endif %}" style="display: flex; flex-direction: column;">
            <input class="form-control input-normal {% if rightText %}right-text{% endif %} {% if className %}{{className}}{% endif %} {% if disabled %}{{disabled}}{% endif %} {% if error_text %}error{% endif %}"
            {% if attribute %}{{attribute}}{% endif %}{% if type %}type="{{type}}"{% endif %} {% if value %}value="{{value}}"{% endif %}
            {% if placeholder %}placeholder="{{placeholder}}"{% endif %}>
            <svg class="error-icon-input{% if rightText %}-right-text{% endif %} " xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 22 18" fill="none">
                <path d="M1.72992 18.0025H20.2599C21.0299 18.0025 21.5099 17.1725 21.1299 16.5025L11.8599 0.5025C11.4699 -0.1675 10.5099 -0.1675 10.1299 0.5025L0.859922 16.5025C0.479922 17.1725 0.959922 18.0025 1.72992 18.0025ZM11.9999 15.0025H9.99992V13.0025H11.9999V15.0025ZM10.9999 11.0025C10.4499 11.0025 9.99992 10.5525 9.99992 10.0025V8.0025C9.99992 7.4525 10.4499 7.0025 10.9999 7.0025C11.5499 7.0025 11.9999 7.4525 11.9999 8.0025V10.0025C11.9999 10.5525 11.5499 11.0025 10.9999 11.0025Z" fill="#2CC84D"/>
            </svg>
            {% if error_text %}
                <span class="error-input">{{error_text}}</span>
            {% endif %}
        </div>
    {% elif type in 'select, select-search' %}
        <div class="form-group component-input component-search-select custom-input-component">
            <div class="sselect-wrapper select-white component-select {% if className %}{{className}}{% endif %} {% if type == 'select-search' %}select-search{% endif %}">
                {% if type == 'select-search' %}
                    <svg class='icon-search-input' xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="#A7A8A9">
                        <path d="M12.5 10.9999H11.71L11.43 10.7299C12.63 9.32989 13.25 7.41989 12.91 5.38989C12.44 2.60989 10.12 0.389893 7.32001 0.0498932C3.09001 -0.470107 -0.469985 3.08989 0.0500152 7.31989C0.390015 10.1199 2.61002 12.4399 5.39002 12.9099C7.42002 13.2499 9.33002 12.6299 10.73 11.4299L11 11.7099V12.4999L15.25 16.7499C15.66 17.1599 16.33 17.1599 16.74 16.7499C17.15 16.3399 17.15 15.6699 16.74 15.2599L12.5 10.9999ZM6.50002 10.9999C4.01002 10.9999 2.00002 8.98989 2.00002 6.49989C2.00002 4.00989 4.01002 1.99989 6.50002 1.99989C8.99002 1.99989 11 4.00989 11 6.49989C11 8.98989 8.99002 10.9999 6.50002 10.9999Z" fill="#A7A8A9"/>
                    </svg>
                    <svg class='icon-close-input' xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                        <path d="M9.19998 0.806769C8.93998 0.546769 8.51998 0.546769 8.25998 0.806769L4.99998 4.0601L1.73998 0.800103C1.47998 0.540103 1.05998 0.540103 0.79998 0.800103C0.53998 1.0601 0.53998 1.4801 0.79998 1.7401L4.05998 5.0001L0.79998 8.2601C0.53998 8.5201 0.53998 8.9401 0.79998 9.2001C1.05998 9.4601 1.47998 9.4601 1.73998 9.2001L4.99998 5.9401L8.25998 9.2001C8.51998 9.4601 8.93998 9.4601 9.19998 9.2001C9.45998 8.9401 9.45998 8.5201 9.19998 8.2601L5.93998 5.0001L9.19998 1.7401C9.45331 1.48677 9.45331 1.0601 9.19998 0.806769Z" fill="#A7A8A9"/>
                    </svg>
                {% endif %}
                <select class="select" placeholder="{% if placeholder %}{{placeholder}}{% else %}Select Here{% endif %}" 
                    data-search="{% if type == 'select-search' %}true{% else %}false{% endif %}"
                    {% if disabled %}{{disabled}}{% endif %}{% if attribute %}{{attribute}}{% endif %}
                    data-search-text="{% if data_search_text %}{{data_search_text}}{% else %}Enter the keyword{% endif %}" value="{{value}}">
                    {% if options %}
                        {% for option in options %}
                            <option value="{{option.value}}">{{option.name}}</option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
        </div>
    {% elif type == 'select-input' %}
        <div class="form-group component-input-select custom-input-component" data-scroll="{{dataScroll}}">
            <div class="input-select-container">
                {% comment %} <svg class="click-open-menu-select-input" xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 8 5" fill="#F0F0F0">
                    <path d="M0.710051 1.71L3.30005 4.3C3.69005 4.69 4.32005 4.69 4.71005 4.3L7.30005 1.71C7.93005 1.08 7.48005 0 6.59005 0H1.41005C0.520051 0 0.0800515 1.08 0.710051 1.71Z" fill="#F0F0F0"/>
                </svg> {% endcomment %}
                <input class="form-control input-select {% if className %}{{className}}{% endif %} {% if disabled %}{{disabled}}{% endif %} {% if error_text %}error{% endif %}"
                    type="text" {% if attribute %}{{attribute}}{% endif %}
                    autocomplete="off" value="{% if data_selected_value %}{% for option in options %}{% if option.value == data_selected_value %}{{option.name}}{% endif %}{% endfor%}{% else %}{{value}}{% endif %}"
                    {% if placeholder %}placeholder="{{placeholder}}"{% endif %} data-value="{% if data_selected_value %}{{data_selected_value}}{% else %}{{value}}{% endif %}" 
                    data-type="{% if data_type %}select{% else %}input{% endif %}">
                    <span class="material-symbols-rounded click-open-menu-select-input">arrow_drop_down</span>
                    {% if options %}
                    <ul>
                        {% for option in options %}
                            <li class="list-input-select {% if data_selected_value == option.value %}selected{% endif %}" data-value="{{ option.value }}" data-name="{{ option.name }}">
                                <span>{{option.name}}</span>
                            </li>
                        {% endfor %}
                    </ul>
                    {% endif %}

            </div>
        </input>
        </div>
    {% elif type == 'text-area' %}
        <div class="form-group component-input-text-area custom-input-component">
            <textarea class="form-control component-text-area {% if className %}{{className}}{% endif %} {% if disabled %}{{disabled}}{% endif %} {% if error_text %}error{% endif %}" 
            {% if placeholder %}placeholder="{{placeholder}}"{% endif %} autocomplete="off"
            {% if attribute %}{{attribute}}{% endif %} {% if value %}value="{{value}}"{% endif %}
            {% if max_length %}maxlength="{{max_length}}"{% endif %}>{% if value %}{{value}}{% endif %}</textarea>
            {% if count_length and max_length %}
                <div class="count-length">
                    <span class="count-box-container">
                        <span id="count-text-area">0 </span>/<span>{{max_length}}</span>
                    </span>
                </div>
            {% endif %}
            {% if error_text %}
                <span class="error-input">{{error_text}}</span>
            {% endif %}
        </div>
    {% elif type in 'datetime, time, rangetime' %}
        <div class="form-group component-datetime custom-input-component">
            <div class="component-datetime-container {% if type == 'datetime' %}calendar-datetime{% elif type == 'time' %}calendar-time{% elif type == 'rangetime' %}calendar-rangetime{% endif %}">
                <input type="text" placeholder="{% if placeholder %}{{placeholder}}{% else %}       {% endif %}" 
                class="form-control pointer {% if type == 'datetime' %}calendar-datetime-picker{% elif type == 'time' %}calendar-time-picker{% elif type == 'rangetime' %}calendar-rangetime-picker{% endif %} {% if className %}{{className}}{% endif %} {% if disabled %}{{disabled}}{% endif %} {% if error_text %}error{% endif %}" 
                {% if id %}id="{{id}}"{% endif %} {% if attribute %}{{attribute}}{% endif %} {% if attribute %}{{attribute}}{% endif %} 
                value="{% if value %}{{value}}{% else %}{% endif %}" onfocus="this.focus()" onblur="this.blur()" readonly="readonly"/>
                {% if type == 'datetime' %}
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20" fill="none">
                        <path d="M13 11H10C9.45 11 9 11.45 9 12V15C9 15.55 9.45 16 10 16H13C13.55 16 14 15.55 14 15V12C14 11.45 13.55 11 13 11ZM13 1V2H5V1C5 0.45 4.55 0 4 0C3.45 0 3 0.45 3 1V2H2C0.89 2 0.00999999 2.9 0.00999999 4L0 18C0 19.1 0.89 20 2 20H16C17.1 20 18 19.1 18 18V4C18 2.9 17.1 2 16 2H15V1C15 0.45 14.55 0 14 0C13.45 0 13 0.45 13 1ZM15 18H3C2.45 18 2 17.55 2 17V7H16V17C16 17.55 15.55 18 15 18Z" fill="#A7A8A9"/>
                    </svg>
                {% elif type == 'time' %}
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M9.99 0C4.47 0 0 4.48 0 10C0 15.52 4.47 20 9.99 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 9.99 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18ZM9.78 5H9.72C9.32 5 9 5.32 9 5.72V10.44C9 10.79 9.18 11.12 9.49 11.3L13.64 13.79C13.98 13.99 14.42 13.89 14.62 13.55C14.83 13.21 14.72 12.76 14.37 12.56L10.5 10.26V5.72C10.5 5.32 10.18 5 9.78 5Z" fill="#A7A8A9"/>
                    </svg>
                {% elif type == 'rangetime' %}
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20" fill="none">
                        <path d="M16 2H15V1C15 0.45 14.55 0 14 0C13.45 0 13 0.45 13 1V2H5V1C5 0.45 4.55 0 4 0C3.45 0 3 0.45 3 1V2H2C0.89 2 0.00999999 2.9 0.00999999 4L0 18C0 19.1 0.89 20 2 20H16C17.1 20 18 19.1 18 18V4C18 2.9 17.1 2 16 2ZM16 17C16 17.55 15.55 18 15 18H3C2.45 18 2 17.55 2 17V7H16V17ZM4 9H6V11H4V9ZM8 9H10V11H8V9ZM12 9H14V11H12V9Z" fill="#A7A8A9"/>
                    </svg>
                {% endif %}
            </div>
            {% if error_text %}
                <span class="error-input">{{error_text}}</span>
            {% endif %}
        </div>
    {% endif %}
{% endblock content %}
