{% load static %}
{% load util compress %}

{% block extrahead %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
    {% compress css %}
            <link rel="stylesheet" type="text/css" href="{% static 'css/tab_component.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
    <div class="component-tab-container {% if type == 'segment' %}segment{% endif %} {% if className %}{{className}}{% endif %}" {% if attribute %}{{attribute}}{% endif %}>
        <ul class="nav component-tab {% if type == 'segment' %}segment{% endif %}">
            {% for tab in options %}
                <li class="nav-item {% if selected_tab and selected_tab == tab.value %}active{% elif not selected_tab and forloop.counter0 == 0 %}active{% endif %}">
                    <a class="nav-link" data-target="#{{tab.value}}">{{ tab.name }}</a>
                    <div class="notification notification--blue notification--round skill-selected hide"></div>
                </li>
            {% endfor %}
        </ul>
        <div class="tab-content">
            {% if filename %}
                {% for file in filename %}
                    {% with options|get_data_array_index:forloop.counter0 as option %}
                        <div class="tab-pane {% if selected_tab and selected_tab == option.value %}active{% elif not selected_tab and forloop.counter0 == 0 %}active{% endif %}" id="{{option.value}}">
                                {% include filename|get_data_array_index:forloop.counter0 with index=forloop.counter0 %}
                        </div>
                    {% endwith %}
                {% endfor %}
            {% else %}
                {% for tab in options %}
                    <div class="tab-pane {% if forloop.counter0 == 0 %}active{% endif %}" id="{{tab.value}}">
                        {{tab.name}}
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
{% endblock content %}
