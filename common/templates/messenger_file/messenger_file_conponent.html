{% load static %}
{% load util compress %}

{% block extrahead %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/messenger_file_conponent.css' %}"/>
    {% endcompress %}
{% endblock %}
<div class="mmessage-container refactor">
    <div class="messenger-file-component-container {% if className and "hide" in className%}hide{% endif %}">
        {% if text_offer %}<p class="text-offer">{{ text_offer }}</p>{% endif %}
        {% if icon_start or file_icon_start %}
            {% if not icon_end and not file_icon_end %}
                <div class="messenger-file-component-content type-two {% if className %}{{ className }}{% endif %}" {% if attribute %}{{ attribute }}{% endif %}>
                    <div class="content-left">
                        {% if file_icon_start and not icon_start %}
                            {% include file_icon_start %}
                        {% elif icon_start %}
                            <i class="icon icon-start icon--sicon-{{icon_start}}"></i>
                        {% endif %}
                        {% if text_left %}
                            <div class="text-content-left">{{text_left}}</div>
                        {% endif %}
                    </div>
                    <div class="content-middle">
                        <div class="text-top">{% if text_top %}{{text_top}}{% endif %}</div>
                        {% if text_bottom %}<div class="text-bottom">{{text_bottom}}</div>{% endif %}
                    </div>
                </div>
            {% elif icon_end or file_icon_end %}
                <div class="messenger-file-component-content type-three {% if className %}{{ className }}{% endif %}" {% if attribute %}{{ attribute }}{% endif %}>
                    <div class="content-left">
                        {% if file_icon_start and not icon_start %}
                            {% include file_icon_start %}
                        {% elif icon_start %}
                            <i class="icon icon-start icon--sicon-{{icon_start}}"></i>
                        {% endif %}
                        {% if text_left %}
                            <div class="text-content-left">{{text_left}}</div>
                        {% endif %}
                    </div>
                    <div class="content-middle">
                        <div class="text-top">{% if text_top %}{{text_top}}{% endif %}</div>
                        {% if text_bottom %}<div class="text-bottom">{{text_bottom}}</div>{% endif %}
                    </div>
                    <div class="content-right">
                        {% if file_icon_end and not icon_end %}
                            {% include file_icon_end %}
                        {% elif icon_end %}
                            <i class="icon icon-start icon--sicon-{{icon_end}}"></i>
                        {% endif %}
                        {% if text_right %}
                            <div class="text-content-left">{{text_right}}</div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>
