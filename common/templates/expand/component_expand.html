{% load static %}
{% load util compress %}

{% block extrahead %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/expand_components.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
<div class="component-expand-container {% if className %}{{className}}{% endif %} collapse-component">
    <div class="expand-top-action">
        <div class="expand-top-container">
            <div class="expand-label">{{label}}</div>
            {% if hint %}
                <div class="expand-hint">[{{hint}}]</div>
            {% endif %}
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M5.28997 0.70998L0.699971 5.29998C0.309971 5.68998 0.309971 6.31998 0.699971 6.70998C1.08997 7.09998 1.71997 7.09998 2.10997 6.70998L5.99997 2.82998L9.87997 6.70998C10.27 7.09998 10.9 7.09998 11.29 6.70998C11.68 6.31998 11.68 5.68998 11.29 5.29998L6.69997 0.70998C6.31997 0.31998 5.67997 0.31998 5.28997 0.70998Z" fill="#A7A8A9"/>
            </svg> 
        </div>
    </div>
    <div class="component-expand-content collapse">
       {% if filename %}
            {% include filename with data=data %}
       {% endif %}

    </div>
</div>
{% endblock %}
