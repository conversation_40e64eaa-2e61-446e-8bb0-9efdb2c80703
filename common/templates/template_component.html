{% load static %}
{% load util compress %}

{% block extrahead %}
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/button_components.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/expand_components.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/input_component.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/messenger_file_conponent.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/tab_component.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/temp_component.css' %}"/>
    {% endcompress %}
{% endblock %}

{% block content %}
{% include 'config_font.html' %}
<div class="components-container">
    <div class="component-subcontainer">
        <h2>Check new components!</h2>

        <h3>Large button</h3>
        <hr/>      
        <div class="large-button">
            {% include 'buttons/conponent_button.html' with className='btn--primary large' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--primary large' value='契約書を見る' icon_start="clip" %}
            {% include 'buttons/conponent_button.html' with className='btn--primary large' value='契約書を見る' icon_end="next" %}
            {% include 'buttons/conponent_button.html' with className='btn--primary large' value='Disabled' disabled='disabled' %}
        </div>
        <div class="large-button">
            {% include 'buttons/conponent_button.html' with className='btn--secondary large' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--secondary large' value='Disabled' disabled='disabled' %}
        </div>
        <div class="large-button">
            {% include 'buttons/conponent_button.html' with className='btn--tertiary large' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--tertiary large' value='Disabled' disabled='disabled' %}
        </div>

        <h3>Medium button</h3>
        <hr/>

        <div class="medium-button">
            {% include 'buttons/conponent_button.html' with className='btn--primary medium' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--primary medium' value='契約書を見る' icon_start="clip" %}
            {% include 'buttons/conponent_button.html' with className='btn--primary medium' value='契約書を見る' icon_end="next" %}
            {% include 'buttons/conponent_button.html' with className='btn--primary medium' value='Disabled' disabled='disabled' %}
        </div>
        <div class="medium-button">
            {% include 'buttons/conponent_button.html' with className='btn--secondary medium' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--secondary medium' value='Disabled' disabled='disabled' %}
        </div>
        <div class="medium-button">
            {% include 'buttons/conponent_button.html' with className='btn--tertiary medium' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--tertiary medium' value='Disabled' disabled='disabled' %}
        </div>

        <h3>Small button</h3>
        <hr/>

        <div class="small-button">
            {% include 'buttons/conponent_button.html' with className='btn--primary small' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--primary small' value='契約書を見る' icon_start="clip" %}
            {% include 'buttons/conponent_button.html' with className='btn--primary small' value='契約書を見る' icon_end="next" %}
            {% include 'buttons/conponent_button.html' with className='btn--primary small' value='Disabled' disabled='disabled' %}
        </div>
        <div class="small-button">
            {% include 'buttons/conponent_button.html' with className='btn--secondary small' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--secondary small' value='Disabled' disabled='disabled' %}
        </div>
        <div class="small-button">
            {% include 'buttons/conponent_button.html' with className='btn--tertiary small' value='契約書を見る' %}
            {% include 'buttons/conponent_button.html' with className='btn--tertiary small' value='Disabled' disabled='disabled' %}
        </div>
        <hr/>
        <div class="large-button">
            {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' disabled='disabled' %}
            {% include 'input_box/component_input.html' with value='MinhTuan' placeholder='Placeholder' %}
            {% include 'input_box/component_input.html' with value='MinhTuan' placeholder='Placeholder' error_text='ここにエラーメッセージが入ります。' %}
            {% include 'input_box/component_input.html' with value='MinhTuan' placeholder='Placeholder' rightText='true' %}
            {% include 'input_box/component_input.html' with value='MinhTuan' rightText='true' placeholder='Placeholder' error_text='ここにエラーメッセージが入ります。' %}
        </div>
        <div class="large-button">
            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='select' options=options %}
            {% endwith %}

            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='select-search' options=options %}
            {% endwith %}

            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='select-input' options=options %}
            {% endwith %}

            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='select-input' options=options error_text='ここにエラーメッセージが入ります。' %}
            {% endwith %}
        </div>

        <div class="large-button">
            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='select' options=options disabled='disabled' %}
            {% endwith %}

            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='select-search' options=options disabled='disabled' %}
            {% endwith %}

            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='select-input' options=options disabled='disabled' %}
            {% endwith %}
        </div>

        <div class="large-button">
            {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='text-area' max_length='100' count_length='true' %}
            {% include 'input_box/component_input.html' with value='' placeholder='Placeholder' type='text-area' max_length='100' count_length='true' disabled='disabled' %}
            {% include 'input_box/component_input.html' with value='更新日' placeholder='Placeholder' type='text-area' max_length='100' count_length='true' error_text='ここにエラーメッセージが入ります。' %}
        </div>

        <div style="margin-bottom: 40px;">
            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'tabs/component_tab.html' with options=options filename='tabs/tab1.html, tabs/tab2.html, tabs/tab3.html, tabs/tab4.html'|create_option_component_select:'' %}
            {% endwith %}

            <br/>
            <hr/>

            {% with '更新日, 開始日, 評価, 手動'|create_option_component_select:'modified, created, rating, order' as options %}
                {% include 'tabs/component_tab.html' with options=options type='segment' filename='tabs/tab1.html, tabs/tab2.html, tabs/tab3.html, tabs/tab4.html'|create_option_component_select:'' %}
            {% endwith %}
        </div>

        <div class="large-button">
            {% include 'input_box/component_input.html' with value='' type='datetime' disabled='disabled' %}
            {% include 'input_box/component_input.html' with value='' type='datetime' error_text='ここにエラーメッセージが入ります。' %}
            {% include 'input_box/component_input.html' with value='' type='datetime' %}
        </div>

        <div class="large-button">
            {% include 'input_box/component_input.html' with value='' type='time' placeholder='hh:mm' disabled='disabled' %}
            {% include 'input_box/component_input.html' with value='' type='time' placeholder='hh:mm' %}
            {% include 'input_box/component_input.html' with value='' type='time' placeholder='hh:mm' error_text='ここにエラーメッセージが入ります。' %}
        </div>

        <div class="large-button">
            {% include 'input_box/component_input.html' with value='' type='rangetime' placeholder='yyyy/mm/dd - yyyy/mm/dd' disabled='disabled' %}
            {% include 'input_box/component_input.html' with value='' type='rangetime' placeholder='yyyy/mm/dd - yyyy/mm/dd' %}
            {% include 'input_box/component_input.html' with value='' type='rangetime' placeholder='yyyy/mm/dd - yyyy/mm/dd' error_text='ここにエラーメッセージが入ります。' %}
        </div>

        <div>
            {% include 'expand/component_expand.html' with data='123' label='Label 18' hint='任意' className='label18' filename='tabs/tab1.html' %}
            {% include 'expand/component_expand.html' with data='123' label='Label 16' hint='任意' className='label16' filename='tabs/tab1.html' %}
            {% include 'expand/component_expand.html' with data='123' label='Label 13' hint='任意' className='label13' filename='tabs/tab1.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_script %}
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/component_input.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/component_tab.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/component_expand.js' %}"></script>
    {% endcompress %}
    <script src="{% url 'javascript-catalog' %}"></script>
{% endblock %}
