{% load static %}
{% load util %}

{% block content %}
{% if not type or type != 'icon-button' %}
<a class="btn component-button {% if className %}{{className}}{% endif %} {% if disabled %}{{disabled}}{% endif %}" 
    {% if attribute %}{{attribute}}{% endif %} {% if href %}href="{{href}}"{% endif %}>
    <div class="btn-content">
        {% if icon_start %}
            <i class="icon icon-start icon--sicon-{{icon_start}}"></i>
        {% elif icon_class_start %}
            <i class="{{icon_class_start}}"></i>
        {% elif icon_file_start %}
            {% include icon_file_start %}
        {% endif %}
        <span class="btn-text">{% if value %}{{value}}{% else %}Button{% endif %}</span>
        {% if icon_end %}
            <i class="icon icon-end icon--sicon-{{icon_end}}"></i>
        {% elif icon_class_end %}
            <i class="{{icon_class_end}}"></i>
        {% elif icon_file_end %}
            {% include icon_file_end %}
        {% endif %}
    </div>
</a>
{% else %}
<a class="btn {% if className %}{{className}}{% endif %} {% if disabled %}{{disabled}}{% endif %}" 
    {% if attribute %}{{attribute}}{% endif %} {% if href %}href="{{href}}"{% endif %}>
    <div class="btn-content">
        {% if icon_name %}
            <i class="icon icon-start icon--sicon-{{icon_name}}"></i>
        {% elif icon_class %}
            <i class="{{icon_class}}"></i>
        {% endif %}
        <span class="btn-text">{% if value %}{{value}}{% else %}Button{% endif %}</span>
    </div>
</a>
{% endif %}
{% endblock %}
