import io

from PIL import Image
from django.core.files.uploadedfile import InMemoryUploadedFile


class BaseModelCropImg(object):
    def crop_img(self, attr_name, x, y, w, h, attr_resets=None):
        img = Image.open(io.BytesIO(getattr(self, attr_name).read())).convert('RGBA')
        area = (x, y, w + x, h + y)
        crop_image = img.crop(area)
        output = io.BytesIO()
        white_background = Image.new('RGBA', crop_image.size, 'WHITE')
        white_background.paste(crop_image, (0, 0), crop_image)
        white_background.convert('RGB').save(output, format='PNG')
        setattr(self, attr_name, InMemoryUploadedFile(output, 'FileField', getattr(self, attr_name).name,
                                                      'image/png', output.getbuffer().nbytes, None))
        if attr_resets:
            for attr in attr_resets:
                setattr(self, attr, None)
        return img, output
