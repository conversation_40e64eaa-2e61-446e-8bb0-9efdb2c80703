import io

from PIL import Image
from django.core.files.base import ContentFile


def make_new_version_of_img(prefix, attr_name_target, attr_name_origin, size, obj):
    origin_img = getattr(obj, attr_name_origin)
    original_name = origin_img.name
    file_name = f'{prefix}_{original_name[7:]}'
    avatar = Image.open(origin_img)
    target_image = avatar.resize(size, Image.ANTIALIAS)
    buffer = io.BytesIO()
    target_image.save(fp=buffer, format='PNG')
    attr_value = getattr(obj, attr_name_target)
    attr_value.save(file_name, content=ContentFile(buffer.getvalue()))
    buffer.close()
    avatar.close()
