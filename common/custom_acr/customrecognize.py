import os, sys
import ast, copy, json
from django.conf import settings
from acrcloud.recognizer import ACRCloudRecognizer
from acrcloud.recognizer import ACRCloudRecognizeType

config = {
    'host': settings.ACR_HOST,
    'access_key': settings.ACR_ACCESS_KEY,
    'access_secret': settings.ACR_ACCESS_SECRET,
    'recognize_type': ACRCloudRecognizeType.ACR_OPT_REC_AUDIO,
    'debug': False,
    'timeout': 10  # seconds
}
ERROR_CONFIG_MESSAGE = "認識できません！ 設定を確認して再試行してください！"
ERROR_READFILE_MESSAGE = "Cannot read file of object!"

TEST_BUFFER = None


def get_test_buffer():
    try:
        return open('common/custom_acr/check_config_data.mp3', 'rb').read()
    except:
        return None


def return_db_offsets(temp_results):
    # sorted
    temp_results = sorted(temp_results, key = lambda i: (i['acrid'], i['play_offset_ms']))

    result_dict = {}
    db_duration_dict = {}
    db_sample_time_dict = {}
    play_offsets = [0, 0]
    acrid = 0
    results_length = len(temp_results)
    for result in temp_results:
        db_duration = result.get("duration_ms", None)
        if db_duration is not None:
            db_duration = int(db_duration)
        if acrid == 0:
            acrid = result.get('acrid', None)
        if db_duration_dict.get(acrid, None) is None:
            db_duration_dict[acrid] = int(result.get("duration_ms", None))
        play_offset = result.get("play_offset_ms")
        if result.get('acrid', None) not in result_dict.keys():
            sample_time = [0, 0]
            if play_offsets[1] != 0:
                if (db_duration is not None) and db_duration_dict[acrid] < play_offsets[1]:
                    play_offsets[1] = db_duration_dict[acrid]
                result_dict[acrid].append(tuple(play_offsets))
                play_offsets = [0, 0]
            acrid = result.get('acrid', None)
            play_offsets[0] = play_offset//10000 * 10000
            play_offsets[1] = play_offset
            result_dict[acrid] = []
            if result == temp_results[results_length - 1]:
                acrid = result.get('acrid', None)
                if db_duration_dict.get(acrid, None) is None:
                    db_duration_dict[acrid] = int(result.get("duration_ms", None))
                if (db_duration is not None) and db_duration_dict[acrid] < play_offsets[1]:
                    play_offsets[1] = db_duration_dict[acrid]
                result_dict[acrid].append(tuple(play_offsets))
            if db_sample_time_dict.get(acrid, None) is None:
                sample_time[0] = (int(result.get('play_offset_ms')) // 10000) * 10000 + int(
                    result.get('sample_begin_time_offset_ms'))
                sample_time[1] = (int(result.get('play_offset_ms')) // 10000) * 10000 + int(
                    result.get('sample_end_time_offset_ms'))
                db_sample_time_dict[acrid] = sample_time
            continue

        delta_offset = play_offset - play_offsets[1]

        if result.get('sample_end_time_offset_ms'):
            db_sample_time_dict[acrid][1] = (db_sample_time_dict[acrid][1] // 10000 + 1) * 10000 + int(
                    result.get('sample_end_time_offset_ms'))

        if 0 < delta_offset < 11000:
            play_offsets[1] = play_offset
        else:
            if (db_duration is not None) and db_duration_dict[acrid] < play_offsets[1]:
                play_offsets[1] = db_duration_dict[acrid]
            result_dict[acrid].append(tuple(play_offsets))
            play_offsets = [0, 0]
            play_offsets[0] = play_offset//10000 * 10000
            play_offsets[1] = play_offset

        if result == temp_results[results_length - 1]:
            if (db_duration is not None) and db_duration_dict[acrid] < play_offsets[1]:
                play_offsets[1] = db_duration_dict[acrid]
            result_dict[acrid].append(tuple(play_offsets))

    final_results = []
    for key in result_dict.keys():
        for result in temp_results:
            if result['acrid'] == key:
                temp_result = result
                temp_result['play_offset_ms'] = result_dict[key]
                temp_result['sample_begin_time_offset_ms'] = db_sample_time_dict[key][0]
                temp_result['sample_end_time_offset_ms'] = db_sample_time_dict[key][1]
                final_results.append(temp_result)
                break
    return final_results


def return_local_offsets(temp_results, local_duration=-1):
    results = temp_results
    result_dict = {}
    results_length = len(results)
    for i in range(results_length):
        for record in results[i]:
            acrid = record.get('acrid', None)
            if record.get('acrid', None) not in result_dict.keys():
                play_offsets = [i * 10, (i + 1) * 10]
                if 0 < local_duration < play_offsets[1]:
                    play_offsets[1] = local_duration
                result_dict[acrid] = []
                result_dict[acrid].append(play_offsets)
            else:
                new_play_offsets = [i * 10, (i + 1) * 10]
                if 0 < local_duration < new_play_offsets[1]:
                    new_play_offsets[1] = local_duration
                if 0 < local_duration < new_play_offsets[0]:
                    new_play_offsets[0] = local_duration
                play_offsets_list = result_dict[acrid]
                length_play_offsets_list = len(play_offsets_list)
                last_play_offsets = play_offsets_list[length_play_offsets_list - 1]
                if last_play_offsets[1] < new_play_offsets[0]:
                    result_dict[acrid].append(new_play_offsets)
                else:
                    result_dict[acrid][length_play_offsets_list - 1][1] = new_play_offsets[1]

    final_results = []
    for key in result_dict.keys():
        for result in results:
            found = False
            for record in result:
                if record['acrid'] == key:
                    record['play_offset_ms'] = result_dict[key]
                    final_results.append(record)
                    found = True
                    break
            if found:
                break
    return final_results


def recognize_by_filebuffer_db_offsets(re_config, buffer, start_second=0, rec_length=10):
    re = ACRCloudRecognizer(re_config)
    results = []
    status_code = 0
    i = 0
    while status_code == 0:
        tmp_results = ast.literal_eval(re.recognize_by_filebuffer(buffer, start_second + i * 10, rec_length))
        status_code = tmp_results.get("status").get("code", -1)
        if status_code == 0:
            results = results + tmp_results["metadata"].get("custom_files", [])
        i += 1

    return return_db_offsets(results)


def recognize_by_filebuffer(re_config, buffer, start_second=0, rec_length=10):
    re = ACRCloudRecognizer(re_config)
    results_local = []
    results_db = []
    status_code = 0
    i = 0
    temp_duration = -1
    while status_code == 0 or status_code == 2006 or status_code == 1001:
        raw_result = re.recognize_by_filebuffer(buffer, start_second + i * 10, rec_length)
        try:
            tmp_results = ast.literal_eval(raw_result)
        except:
            tmp_results = json.loads(raw_result)

        status_code = tmp_results.get("status").get("code", -1)
        if status_code == 3014:
            raise ValueError(ERROR_CONFIG_MESSAGE)
        if status_code == 0:
            results_local.append(tmp_results["metadata"].get("custom_files", []))
            results_db = results_db + copy.deepcopy(tmp_results["metadata"].get("custom_files", []))
            temp_duration = start_second + i * 10 + rec_length
        else:
            if status_code == 2006 or status_code == 1001:
                # the audio/video file may be muted
                temp_duration = start_second + i * 10 + rec_length
                i += 1
                continue
            #find real duration of audio/video file
            temp_duration -= 10
            for div_rec_length in range(rec_length-1, -1, -1):
                tmp_results = ast.literal_eval(re.recognize_by_filebuffer(buffer, start_second + (i - 1) * 10 + div_rec_length, rec_length))
                status_code = tmp_results.get("status").get("code", -1)
                if status_code == 0 or status_code == 2006 or status_code == 1001:
                    temp_duration += (div_rec_length + 1)
                    status_code = -1
                    break

        i += 1

    db_offsets_results = return_db_offsets(results_db)
    local_offsets_results = return_local_offsets(results_local, local_duration=temp_duration)

    for i in range(len(db_offsets_results)):
        db_result = db_offsets_results[i]
        for local_result in local_offsets_results:
            local_acrid = local_result.get("acrid", None)
            db_acrid = db_result.get("acrid", None)
            if local_acrid and db_acrid and local_acrid == db_acrid:
                db_offsets_results[i]["sample_play_offset_s"] = local_result.get("play_offset_ms", [])

    return db_offsets_results


def recognize_by_filebuffer_with_duration_db_offsets(re_config, buffer, duration, start_second=0, rec_length=10):
    re = ACRCloudRecognizer(re_config)
    count_loop = int((duration - start_second) / rec_length)
    div_length = (duration - start_second) % rec_length
    results = []
    for i in range(count_loop):
        tmp_results = ast.literal_eval(re.recognize_by_filebuffer(buffer, start_second + i * rec_length, rec_length))
        status_code = tmp_results.get("status").get("code", -1)
        if status_code == 0:
            results = results + tmp_results["metadata"].get("custom_files", [])

    if div_length > 0:
        tmp_results = ast.literal_eval(
            re.recognize_by_filebuffer(buffer, start_second + count_loop * rec_length, div_length))
        status_code = tmp_results.get("status").get("code", -1)
        if status_code == 0:
            results = results + tmp_results["metadata"].get("custom_files",[])

    return return_db_offsets(results)


def recognize_by_filebuffer_with_duration(re_config, buffer, duration, start_second=0, rec_length=10):
    re = ACRCloudRecognizer(re_config)
    count_loop = int((duration - start_second) / rec_length)
    div_length = (duration - start_second) % rec_length
    results_local = []
    results_db = []
    for i in range(count_loop):
        tmp_results = ast.literal_eval(re.recognize_by_filebuffer(buffer, start_second + i * rec_length, rec_length))
        status_code = tmp_results.get("status").get("code", -1)
        if status_code == 3014:
            raise ValueError(ERROR_CONFIG_MESSAGE)
        if status_code == 0:
            results_local.append(tmp_results["metadata"].get("custom_files", []))
            results_db = results_db + copy.deepcopy(tmp_results["metadata"].get("custom_files", []))

    if div_length > 0:
        tmp_results = ast.literal_eval(
            re.recognize_by_filebuffer(buffer, start_second + count_loop * rec_length, div_length))
        status_code = tmp_results.get("status").get("code", -1)
        if status_code == 3014:
            raise ValueError(ERROR_CONFIG_MESSAGE)
        if status_code == 0:
            results_local.append(tmp_results["metadata"].get("custom_files", []))
            results_db = results_db + copy.deepcopy(tmp_results["metadata"].get("custom_files", []))

    db_offsets_results = return_db_offsets(results_db)
    local_offsets_results = return_local_offsets(results_local, duration)

    for i in range(len(db_offsets_results)):
        db_result = db_offsets_results[i]
        for local_result in local_offsets_results:
            local_acrid = local_result.get("acrid", None)
            db_acrid = db_result.get("acrid", None)
            if local_acrid and db_acrid and local_acrid == db_acrid:
                db_offsets_results[i]["sample_play_offset_s"] = local_result.get("play_offset_ms", [])

    return db_offsets_results


def check_config(re_config, buffer, start_second=0, rec_length=10):
    try:
        re = ACRCloudRecognizer(re_config)
        tmp_results = ast.literal_eval(re.recognize_by_filebuffer(buffer, start_second, rec_length))
        status_code = tmp_results.get("status").get("code", -1)
        if status_code == 3014 or status_code == 3000 or status_code == -1:
            return False
        return True
    except Exception as e:
        return False
