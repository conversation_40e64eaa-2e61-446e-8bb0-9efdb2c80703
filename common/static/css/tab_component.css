
/* :root {
  --black1-color: #000000;
  --black2-color: #53565a;
  --grey1-color: #a7a8a9;
  --grey2-color: #d3d3d3;
  --grey3-color: #f0f0f0;
  --white-color: #ffffff;
  --blue-color: #009ace;
  --blue-color-hover: #0076a5;
  --background-color: #fcfcfc;
  --error-color: #2cc84d;
  --font-size-40: 40px;
  --line-height-60: 60px;
  --font-size-32: 32px;
  --line-height-48: 48px;
  --font-size-24: 24px;
  --line-height-36: 36px;
  --font-size-18: 18px;
  --line-height-27: 27px;
  --font-size-16: 16px;
  --line-height-24: 24px;
  --font-size-13: 13px;
  --line-height-20: 20px;
  --font-size-11: 11px;
  --line-height-17: 17px;
  --font-size-8: 8px;
  --line-height-12: 12px;
  --font-weight-300: 300;
  --font-weight-400: 400;
  --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
  --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
} */

.component-tab {
  display: flex;
  flex-wrap: wrap;
}

.component-tab.segment {
  display: flex;
  flex-wrap: nowrap;
}

.component-tab .nav-item {
  position: relative;
}

.component-tab.segment .nav-item {
  position: relative;
  width: 100%;
  border: 1px solid var(--soremo-border);
  background-color: var(--background-color);
  padding: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.component-tab.segment .nav-item:first-child {
  border-top-left-radius: 6px;
}

.component-tab.segment .nav-item:last-child {
  border-top-right-radius: 6px;
}

.component-tab.segment .nav-item:not(:last-child) {
  border-right: none;
}

.nav.component-tab.segment .nav-item.active, .nav.component-tab.segment .nav-item.active:hover, .nav.component-tab.segment .nav-item:hover, .nav.component-tab.segment .nav-item.active:focus {
  color: var(--white-color);
  border-bottom: 1px solid #009ACE;
  font-weight: 400;
  background-color: var(--blue-color);
  cursor: pointer;
}

.nav.component-tab.segment .nav-item.active a.nav-link, .nav.component-tab.segment .nav-item.active:hover a.nav-link, .nav.component-tab.segment .nav-item:hover a.nav-link, .nav.component-tab.segment .nav-item.active:focus a.nav-link {
  color: var(--white-color);
  border-bottom: none;
  font-weight: 300;
  background-color: var(--blue-color);
}

.nav.component-tab.segment .nav-item a.nav-link {
  color: var(--grey1-color);
  border-bottom: none;
  cursor: pointer;
  font-weight: 300;
  font-size: 13px;
  padding: 0;
  margin: 0;
  line-height: 200%;
  background-color: var(--background-color);
  font-family: var(--font-family-R);
}

.nav.component-tab .nav-item a.nav-link {
  color: #000000;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: 300;
  font-size: 13px;
  padding: 0;
  margin: 0 32px 0 0;
  line-height: 200%;
 
}

.nav.component-tab .nav-item.active a.nav-link, .nav.component-tab .nav-item.active a.nav-link:hover, .nav.component-tab .nav-item a.nav-link:hover, .nav.component-tab .nav-item.active a.nav-link:focus {
  color: #009ACE;
  border-bottom: 2px solid #009ACE;
  font-weight: 400;
}

.component-tab-container .tab-content {
  margin-top: 8px;
}

.component-tab-container.segment .tab-content {
  border-width: 0px 1px 1px 1px;
  border-style: solid;
  border-color: var(--soremo-border);
  border-radius: 0px 0px 4px 4px;
  padding: 12px 16px;
  background-color: var(--background-color);
  margin-top: 0;
}

.nav.component-tab .nav-item a.nav-link,
.nav.component-tab .nav-item.active a.nav-link,
.nav.component-tab .nav-item.active a.nav-link:hover,
.nav.component-tab .nav-item a.nav-link:hover,
.nav.component-tab .nav-item.active a.nav-link:focus {
  background-color: transparent !important;
}
