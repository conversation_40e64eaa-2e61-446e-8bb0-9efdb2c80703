@import url("https://fonts.googleapis.com/earlyaccess/notosansjapanese.css");
    
:root {
    --app-height: 100%; 
    --bg-album: #a7a8a9;
}

body {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'A+mfCv-AXISラウンド 50 R StdN', 'A+mfCv-AXISラウンド 50 M StdN' !important;
}

.components-container {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    margin-bottom: 200px;
}

.component-subcontainer {
    width: 70%;
}

.component-subcontainer h2 {
    padding-bottom: 50px;
}

.large-button, .medium-button, .small-button {
    display: flex;
    flex-direction: row;
    /* justify-content: space-around; */
    margin-bottom: 40px;
}

.large-button .btn, .medium-button .btn , .small-button .btn, .large-button .form-group {
    margin-left: 20px;
}
