@import url("https://fonts.googleapis.com/earlyaccess/notosansjapanese.css");

:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
    --font-size-40: 40px;
    --line-height-60: 60px;
    --font-size-32: 32px;
    --line-height-48: 48px;
    --font-size-24: 24px;
    --line-height-36: 36px;
    --font-size-18: 18px;
    --line-height-27: 27px;
    --font-size-16: 16px;
    --line-height-24: 24px;
    --font-size-13: 13px;
    --line-height-20: 20px;
    --font-size-11: 11px;
    --line-height-17: 17px;
    --font-size-8: 8px;
    --line-height-12: 12px;
    --font-weight-300: 300;
    --font-weight-400: 400;
    --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
    --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
}

.messenger-file-component-container {
    width: 100%;
}

.messenger-file-component-content {
    display: flex;
    width: 100%;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 8px;
}

.messenger-file-component-content.type-two {
    justify-content: center;
    align-items: center;
    background-color: var(--blue-color);
    color: var(--white-color);
}

.messenger-file-component-content.type-three {
    justify-content: space-between;
    align-items: center;
    background-color: var(--white-color);
    color: var(--blue-color);
    border: 1px solid var(--blue-color);
    padding:  16px 24px;
}

.messenger-file-component-content.disable {
    background-color: var(--soremo-border) !important;
    color: var(--grey1-color) !important;
    pointer-events: none;
}

.messenger-file-component-content.disable.open-file-preview-artist-contract-only {
    pointer-events: all !important;
}

.messenger-file-component-content.disable svg.icon-document path {
    fill: var(--grey1-color) !important;
} 

.messenger-file-component-content.type-two:hover {
    background-color: var(--soremo-deep-blue);
    color: var(--white-color);
}

.messenger-file-component-content:hover {
    cursor: pointer;
}

.messenger-file-component-content .content-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.messenger-file-component-content .content-middle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.messenger-file-component-content.type-two .content-middle {
    margin-left: 16px;
}

.messenger-file-component-content .content-middle .text-top {
    font-family: var(--font-family-R);
    line-height: 100%;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.messenger-file-component-content .content-middle .text-bottom {
   
    line-height: 100%;
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
    margin-top: 6px;
}

.messenger-file-component-content .content-left .text-content-left {
   
    font-size: 8px;
    font-weight: 300;
    line-height: 100%;
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.messenger-file-component-content .content-left i {
    font-size: 20px;
    margin-bottom: 2px;
}

.messenger-file-component-content.disable.open-file-preview-only {
    pointer-events: all;
    cursor: pointer;
}
