@import url("https://fonts.googleapis.com/earlyaccess/notosansjapanese.css");

:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
    --font-size-40: 40px;
    --line-height-60: 60px;
    --font-size-32: 32px;
    --line-height-48: 48px;
    --font-size-24: 24px;
    --line-height-36: 36px;
    --font-size-18: 18px;
    --line-height-27: 27px;
    --font-size-16: 16px;
    --line-height-24: 24px;
    --font-size-13: 13px;
    --line-height-20: 20px;
    --font-size-11: 11px;
    --line-height-17: 17px;
    --font-size-8: 8px;
    --line-height-12: 12px;
    --font-weight-300: 300;
    --font-weight-400: 400;
    --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
    --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
}

.component-expand-container {
    margin-bottom: 24px;
    background: #FCFCFC;
    border: 1px solid #F0F0F0;
    border-radius: 4px;
}

.component-expand-container.label18 .expand-top-action .expand-label {
    font-family: var(--font-family-R);
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
}

.component-expand-container.label16 .expand-top-action .expand-label {
    font-family: var(--font-family-R);
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 200%;
}

.component-expand-container.label13 .expand-top-action .expand-label {
    font-family: var(--font-family-R);
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 200%;
}

.component-expand-container .expand-top-action:hover {
    background-color: #eeeeee;
}

.component-expand-container .expand-top-action .expand-top-container {
    position: relative;
    padding: 16px 8px;
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    /* margin: 5px 8px; */
}

.component-expand-container .component-expand-content {
    margin: 0 8px;
    border-top: 1px solid var(--soremo-border);
    padding-top: 16px;
}

.component-expand-container .expand-top-action .expand-label {
    font-family: var(--font-family-R);
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 2.5px;
    color: var(--black1-color);
}


.component-expand-container .expand-top-action .expand-hint {
    font-family: var(--font-family-R);
    font-style: normal;
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
    color: var(--grey1-color);
    margin-left: 4px;
}

.component-expand-container.collapse-component .expand-top-action svg {
   transform: rotate(-180deg) translate(0, 50%);
   transform-origin: center;
   transition: opacity 50ms linear 0ms, transform 200ms ease-in-out 0ms;
}

.component-expand-container.expand-component .expand-top-action svg {
    transform: translate(0, -50%);
    transform-origin: center;
    transition: opacity 50ms linear 0ms, transform 200ms ease-in-out 0ms;
}

.component-expand-container .expand-top-action svg {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translate(0, -50%);
    z-index: 1;
}
