if(window.initStatus === undefined) {
    window.initStatus = false;
}
$(document).ready(function () {
    console.log('loaded')
    $(document).find('.sselect-wrapper').each(function () {
        var search = $(this).find('select').attr('data-search');
        console.log(search);
        var searchText = $(this).find('select').attr('data-search-text');

        $(this).find('select').SumoSelect({
            search: (search == 'true') ? true : false,
            searchText: searchText,
            showTitle: false,
            forceCustomRendering: true,
        });
    });
    $(document).on('mouseenter', '.sselect-wrapper .CaptionCont.SelectBox', function () {
        $(this).removeAttr('title');
    });

    $(document).on(
        'focus focusout', '.form-group.component-input-select input.input-select',
        function (e) {
            let domList = $(this).parent().find('ul');
            $(this).parent().toggleClass('is-focus');
            if (e.type == 'focusin' || e.type == 'focus') {
                if(!$(this).val()) {
                    setTimeout(() => {
                        domList.css({
                            'display': 'block',
                            'pointer-events': 'all',
                        });
                    }, 300);
                    domList.find('.is-hover').removeClass('is-hover');
                    domList.find('.selected').addClass('is-hover');
                }
            } else {
                setTimeout(() => {
                    domList.css({ display: 'none', 'pointer-events': 'none' });
                }, 300);
            }
        }
    );


    $(document).on('click', '.click-open-menu-select-input', function()  {
      let dom = $(this).parents(".input-select-container").find("ul");
      let input = $(this).parents(".input-select-container").find("input");

      if (dom.css("display") == "block") {
        input.focus();
        setTimeout(() => {
        dom.hide();
        }, 300);
      } else {
        input.focus();
        if (input.val()) {
            setTimeout(() => {
            dom.show();
        }, 300);
      }
    }})

    $(document).on('keydown', 'input.input-select', function(e) {
        if (e.which == 40) { // 40は下キーのキーコード
            let ul = $(this).parents(".input-select-container").find("ul");
            if (ul.css("display") === "none") {
                ul.show();
            }
        }
    });

    if(!initStatus) {
        $(document).on('keyup', '.form-group.component-input-select input.input-select',function(e) {
            const domList = $(this).parent().find('ul');
            if(parseInt(e.keyCode) === 40 || parseInt(e.keyCode) === 38 || e.keyCode === 13) {
                if(domList.css('display') == 'block') {
                    // domList.children().unbind('mouseenter mouseleave');
                    let indexHover = domList.find('.is-hover').index();
                    if(parseInt(e.keyCode) === 40 || parseInt(e.keyCode) === 38) {
                        switch (indexHover) {
                            case -1:
                                if (e.keyCode === 38) {
                                $(domList.children()[domList.children().length-1]).addClass('is-hover');
                                scrollChildWhenKeyup(domList.children().length-1, domList)
                                indexHover = domList.children().length - 1;
                                } else {
                                    $(domList.children()[0]).addClass('is-hover');
                                scrollChildWhenKeyup(0, domList)
                                indexHover = 0;
                                }
                                break;
                            case 0:
                                domList.find('.is-hover').removeClass('is-hover')
                                if (e.keyCode === 38) {
                                    $(domList.children()[domList.children().length-1]).addClass('is-hover');
                                    scrollChildWhenKeyup(domList.children().length-1, domList)
                                    indexHover = domList.children().length - 1;
                                } else {
                                    $(domList.children()[indexHover+1]).addClass('is-hover');
                                    scrollChildWhenKeyup(indexHover + 1, domList)
                                    indexHover = indexHover + 1;
                                }
                                break;
                            case domList.children().length - 1:
                                domList.find('.is-hover').removeClass('is-hover')
                                if (e.keyCode === 38) {
            
                                    $(domList.children()[indexHover - 1]).addClass('is-hover');
                                    scrollChildWhenKeyup(indexHover - 1, domList)
                                    indexHover = indexHover - 1;
                                } else {
                                    $(domList.children()[0]).addClass('is-hover');
                                    scrollChildWhenKeyup(0, domList)
                                    indexHover = 0;
                                }
                                break;
                            default:
                                domList.find('.is-hover').removeClass('is-hover')
                                if (e.keyCode === 38) {
                                    $(domList.children()[indexHover - 1]).addClass('is-hover');
                                    scrollChildWhenKeyup(indexHover - 1, domList)
                                    indexHover = indexHover - 1;
                                } else {
                                    $(domList.children()[indexHover + 1]).addClass('is-hover');
                                    scrollChildWhenKeyup(indexHover + 1, domList)
                                    indexHover = indexHover + 1;
                                }
                                break;
                        }
                    } else if (e.keyCode === 13) {
                        if(indexHover !== -1) {
                            $(domList.children()[indexHover]).click();
                            $(this).blur();
                        }
                    }
                }
            } else {
                if(!$(this).val()) {
                    let dom = $(this).parents('.input-select-container').find('ul')
                    dom.css({
                        'display': 'block',
                        'pointer-events': 'all',
                    });
                } else {
                    let dom = $(this).parents('.input-select-container').find('ul')
                    dom.css({
                        'display': 'none',
                        'pointer-events': 'none',
                    });
                }
            }
        });
    }
    initStatus = true;

function scrollChildWhenKeyup (index, domList) {
    if(domList.children()[index].offsetTop < domList[0].scrollTop) {
        domList.animate({ scrollTop: domList.children()[index].offsetTop}, { duration: 100, easing: 'swing' });
    } else if (domList.children()[index].offsetTop + domList.children()[index].clientHeight > domList[0].scrollTop + domList[0].clientHeight) {
        domList.animate({ scrollTop: domList.children()[index].offsetTop - domList[0].clientHeight + domList.children()[index].clientHeight  }, { duration: 100, easing: 'swing' });
    }
}

    $(document).on('click', '.form-group.component-input-select .list-input-select', function (e) {
        $(this).parents('.component-input-select').find('.input-select').attr('data-value', $(this).attr('data-value'));
        $(this).parents('.component-input-select').find('.input-select').val($(this).attr('data-name'));
        $(this).parents('.component-input-select').find('.input-select').attr('data-type', 'select');
        $(this).parents('.component-input-select').find('li.selected').removeClass('selected is-hover');
        $(this).parents('.component-input-select').find('li.is-hover').removeClass('is-hover'); 
        $(this).addClass('selected is-hover');
    })

    $(document).on('input', '.form-group.component-input-select .input-select', function(e) {
        $(this).parents('.component-input-select').find('.input-select').attr('data-value', e.target.value)
        $(this).parents('.component-input-select').find('li.selected').removeClass('selected')
        $(this).attr('data-type', 'input');
    })

    $(document).on('change', '.form-group.component-input-select .input-select', function(e) {
        $(this).parents('.component-input-select').find('.input-select').attr('data-value', e.target.value)
        $(this).parents('.component-input-select').find('li.selected').removeClass('selected')
    })

    $(document).on('focus focusout', '.sselect-wrapper .search-txt', function(e) {
        let domIcon = $(this).parents('.component-select').find('svg.icon-close-input')
        if(e.type == 'focus') {
            domIcon.css('display', 'block');
        } else {
            domIcon.css('display', 'none');
        }
    })

    $(document).on('input', '.form-group.component-input-text-area textarea', function(e){
        checkLengthValueTextArea($(this));
    })

    $('.form-group.component-input-text-area textarea').each(function() {
        checkLengthValueTextArea($(this));
    })

    $(document).find('.calendar-time-picker').each(function () { 
        $(this).datetimepicker({ format: 'HH:mm', ignoreReadonly: true, });
    })
    console.log('init_daterange')
    // $(document).find('.calendar-rangetime-picker:not(.custom-datepicker)').daterangepicker({
    //     startDate: new Date(),
    //     endDate: moment(new Date()).add(1, 'weeks'),
    // });

    // scorllWhenSelectInput();
    setValueInputSelect();
    setValueSelect();
});

function setValueSelect () {
    $(document).find('.component-search-select select').each(function() {
        let valueSelect = $(this).attr('value');
        console.log("val project:", !!valueSelect);
        if(!!valueSelect && valueSelect!=='None' && valueSelect!=="undefined") {
            $(this).val(valueSelect.toString());
            let name = $(document).find($(this)).find('option[value="'+valueSelect.toString()+'"]').text();
            $(document).find($(this)).parent().find('.optWrapper ul li.selected').removeClass('selected')
            $(document).find($(this)).parent().find('.optWrapper ul li:contains("'+name+'")').addClass('selected')
            $(document).find($(this)).parent().find('.CaptionCont span').text(name)
        }
    })
}

function checkLengthValueTextArea(domElement) {
    let length = domElement.val().length
    domElement.parent().find('#count-text-area').text(length)
}

// function scorllWhenSelectInput() {
//     let scrollData = $(document).find('.form-group.component-input-select').attr('data-scroll')
//     if(!scrollData) {
//         $(document).on('scroll', function() {
//             $(this).find('.form-group.component-input-select .is-focus ul').css('display', 'none');
//             $(this).find('.form-group.component-input-select .is-focus .input-select').blur();
//         });
//     } else {
//         $(`${scrollData}`).on('scroll', function() {
//             $(this).find('.form-group.component-input-select .is-focus ul').css('display', 'none');
//             $(this).find('.form-group.component-input-select .is-focus .input-select').blur();
//         })
//     }
// }

function setValueInputSelect() {
    $(document).find('.form-group.component-input-select input.input-select').each(function() {
        let dataValue = $(this).attr('data-value');
        $(this).parent().find('ul li').each(function() {
            if($(this).attr('data-value')==dataValue) {
                $(this).parent().find('.selected').removeClass('selected');
                $(this).addClass('selected');
                $(this).parents('.form-group.component-input-select').find('input.input-select').val($(this).text().trim())
                return;
            }
        })
    })
}
