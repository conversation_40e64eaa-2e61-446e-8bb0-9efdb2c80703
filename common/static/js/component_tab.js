$(document).ready(function () {
    console.log('loaded tab')
    $(document).on('click', '.component-tab-container ul.nav.component-tab li .nav-link', function(e) {
        clickTabFnc($(this));
    })

    $(document).on('click', '.component-tab-container ul.nav.component-tab.segment li', function(e) {
        clickTabSegmentFnc($(this));
    })
});

function clickTabFnc(domElement) {
    let active_tab_button = domElement.closest('.component-tab').find('li.active').first()
    active_tab_button.removeClass('active')
    let active_tab_button_target = active_tab_button.find('.nav-link').attr('data-target')
    $(active_tab_button_target).removeClass('active')
    domElement.parent().addClass('active');
    $(domElement.attr('data-target')).addClass('active')
}

function clickTabSegmentFnc(domElement) {
    let current_tab = domElement.closest('.component-tab.segment').find('.active')
    current_tab.removeClass('active')
    $(current_tab.find('.nav-link').attr('data-target')).removeClass('active')
    domElement.addClass('active');
    $(domElement.find('.nav-link').attr('data-target')).addClass('active')
}
