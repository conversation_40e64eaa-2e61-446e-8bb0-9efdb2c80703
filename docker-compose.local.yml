services:
  api:
    build: .
    volumes:
      - .:/code
    ports:
      - "8010:8000"
    depends_on:
      - db
      - redis
    restart: always
    container_name: api
    networks:
      - backend_soremo_network

  redis:
    image: "bitnami/redis:5.0"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL
      - REDIS_AOF_ENABLED=no
    ports:
      - "6380:6379"
    volumes:
      - cache:/bitnami/redis/data
    container_name: redis
    networks:
      - backend_soremo_network

  db:
    image: mysql:8.0
    command: ["mysqld", "--character-set-server=utf8mb4", "--collation-server=utf8mb4_unicode_ci",]
    ports:
      - "3308:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: voice
    volumes:
      - mysql:/var/lib/mysql
    restart: always
    container_name: mysql-main
    networks:
      - backend_soremo_network

  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025" # SMTP port
      - "8025:8025" # Web UI port
    networks:
      - backend_soremo_network

volumes:
  cache: {}
  mysql: {}

networks:
  backend_soremo_network:
    name: backend_soremo_network
    driver: bridge