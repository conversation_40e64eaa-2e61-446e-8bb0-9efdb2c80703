{"extends": ["standard", "standard-jsx"], "rules": {"indent": ["error", "tab"], "quotes": ["error", "single"], "eqeqeq": ["error", "always", {"null": "ignore"}], "space-infix-ops": ["error", {"int32Hint": false}], "brace-style": ["1tbs", {"allowSingleLine": true}], "curly": "all", "no-multi-spaces": "error", "object-curly-spacing": ["error", "never", {"objectsInObjects": false}, {"arraysInObjects": false}], "comma-style": ["error", "last", {"exceptions": {"ArrayExpression": true, "ObjectExpression": true}}], "eol-last": ["error", "always"], "key-spacing": ["error", {"beforeColon": false, "afterColon": true, "mode": "strict"}]}}